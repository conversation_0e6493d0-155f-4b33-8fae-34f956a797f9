"""Service layer for the Claims Agent API.

This module defines the business logic layer that sits between the HTTP API
handlers and the core agent implementation. It uses a protocol-based approach
to define a clear service interface.
"""

import json
from datetime import datetime
from typing import Protocol, runtime_checkable

from loguru import logger

from claims_agent.interfaces.agents import AgentProtocol
from claims_agent.interfaces.legacy_models import CoverageDeterminationResponse
from claims_agent.interfaces.repositories import (
    CoverageRunRepositoryProtocol,
    CoverageNotesRepositoryProtocol,
)
from claims_agent.models.feedback import (
    CoverageRunWithNotes,
    FeedbackResponse,
    NoteFeedback,
)
from claims_agent.db.orm.generated import CoverageNotes
from claims_agent.instrumentation.helpers import (
    get_current_trace_id,
    trace_async_function,
)


# Template for formatting previous coverage notes for feedback refinement
FEEDBACK_COVERAGE_NOTE_TEMPLATE = """
**Coverage Note (Updated: {updated_at})**
```json
{content}
```
"""


@runtime_checkable
class ClaimsServiceProtocol(Protocol):
    """A protocol defining the interface for the claims service.

    This protocol establishes a clear contract for the operations that the
    service layer must provide. Using a protocol allows for dependency inversion,
    making it easier to test the API layer with mock services.
    """

    authorization: str | None

    async def health_check(self) -> tuple[bool, str]:
        """Check the health status of the service and its dependencies.

        Returns:
            A tuple of (is_healthy: bool, details: str) indicating service health.
        """
        ...

    async def determine_coverage(
        self,
        claim_id: str,
        authorization: str | None = None,
        as_of_date: datetime | None = None,
    ) -> CoverageDeterminationResponse:
        """Analyze claim coverage based on policy details.

        Args:
            claim_id: The ID of the claim to analyze.
            authorization: Optional authorization header to include in requests.
            as_of_date: Optional date to perform coverage determination as of
                (defaults to current time)

        Returns:
            A CoverageDeterminationResponse with verification results.

        Raises:
            AgentNotInitializedError: If the agent is not initialized.
            ClaimNotFoundError: If the claim is not found.
            Exception: If processing fails.
        """
        ...

    async def determine_coverage_with_storage(
        self,
        claim_id: str,
        created_by: str,
        authorization: str | None = None,
        as_of_date: datetime | None = None,
        trace_id: str | None = None,
    ) -> CoverageRunWithNotes:
        """Analyze claim coverage and store results in the database.

        This method performs coverage determination and stores the results
        in the database for later feedback processing.

        Args:
            claim_id: The ID of the claim to analyze.
            created_by: Email of the user creating the run.
            authorization: The authorization header from the API request.
            as_of_date: The optional date for the analysis.
            trace_id: Optional trace ID for debugging.

        Returns:
            A CoverageRunWithNotes with the stored results.
        """
        ...

    async def submit_feedback(
        self,
        feedback_list: list[NoteFeedback],
        updated_by: str,
    ) -> FeedbackResponse:
        """Submit feedback for coverage notes.

        Args:
            feedback_list: List of feedback for individual notes.
            updated_by: Email of the user providing feedback.

        Returns:
            A FeedbackResponse with the updated note IDs.
        """
        ...


class ClaimsService(ClaimsServiceProtocol):
    """The concrete implementation of the claims service.

    This class orchestrates the business logic for handling claims-related
    requests. It is instantiated by the dependency injection container, which
    injects the necessary dependencies, primarily the `AgentProtocol` and
    the repository classes.
    """

    def __init__(
        self,
        agent: AgentProtocol,
        coverage_run_repository: CoverageRunRepositoryProtocol,
        coverage_notes_repository: CoverageNotesRepositoryProtocol,
        authorization: str | None = None,
    ) -> None:
        """Initializes the claims service.

        The `injector` library creates an instance of this service for each
        request, injecting the singleton `AgentProtocol` instance and repository
        instances along with any request-specific data like the authorization header.

        Args:
            agent: The singleton `AgentProtocol` instance to use for processing.
            coverage_run_repository: Repository for coverage runs database operations.
            coverage_notes_repository: Repository for coverage notes database operations.
            authorization: The authorization token for the current API request.
        """
        self.agent = agent
        self.coverage_run_repository = coverage_run_repository
        self.coverage_notes_repository = coverage_notes_repository
        self.authorization = authorization

    async def health_check(self) -> tuple[bool, str]:
        """Check the health status of the service and its dependencies.

        Returns:
            A tuple of (is_healthy: bool, details: str) indicating service health.
        """
        try:
            # Check if agent is available and responsive
            if not self.agent:
                return False, "Agent is not available"

            # Try to perform a simple health check on the agent
            # This could be a simple method call or property check
            # For now, we'll check if the agent has the required methods
            if not hasattr(self.agent, "determine_coverage"):
                return False, "Agent is missing required methods"

            # Check if repositories are available
            if not self.coverage_run_repository:
                return False, "Coverage run repository is not available"

            if not self.coverage_notes_repository:
                return False, "Coverage notes repository is not available"

            # Additional health checks could be added here:
            # - Check MCP client connectivity
            # - Check LLM provider availability
            # - Check database connectivity
            # - Check external service dependencies

            return True, "Service is healthy"
        except Exception as e:
            logger.error(f"Health check failed: {str(e)}")
            return False, f"Health check failed: {str(e)}"

    async def determine_coverage(
        self,
        claim_id: str,
        authorization: str | None = None,
        as_of_date: datetime | None = None,
    ) -> CoverageDeterminationResponse:
        """Determines coverage for a claim by delegating to the agent.

        This method simply calls the `determine_coverage` method on the injected
        agent instance, passing through the necessary parameters from the API layer.

        Args:
            claim_id: The ID of the claim to analyze.
            authorization: The authorization header from the API request.
            as_of_date: The optional date for the analysis.

        Returns:
            The `CoverageDeterminationResponse` from the agent.
        """
        logger.info(f"Determining coverage for claim {claim_id}")
        # Use the self.agent instance which is injected rather than getting from AgentManager
        return await self.agent.determine_coverage(
            claim_id=claim_id,
            authorization=authorization,
            as_of_date=as_of_date,
            refined_notes=None,
        )

    @trace_async_function(name="ClaimsService.determine_coverage_with_storage")
    async def determine_coverage_with_storage(
        self,
        claim_id: str,
        created_by: str,
        authorization: str | None = None,
        as_of_date: datetime | None = None,
        trace_id: str | None = None,
    ) -> CoverageRunWithNotes:
        """Analyze claim coverage and store results in the database.

        This method performs coverage determination and stores the results
        in the database for later feedback processing. It follows the same
        status transition pattern as the original coverage determination flow.

        Args:
            claim_id: The ID of the claim to analyze.
            created_by: Email of the user creating the run.
            authorization: The authorization header from the API request.
            as_of_date: The optional date for the analysis.
            trace_id: Optional trace ID for debugging.

        Returns:
            A CoverageRunWithNotes with the stored results.
        """
        logger.info(f"Determining coverage with storage for claim {claim_id}")

        # Step 1: Create a new coverage run with status "requested"
        # Get current trace ID from the parent span and use it during creation
        current_trace_id = trace_id or get_current_trace_id()
        run_id = await self.coverage_run_repository.create_run(
            claim_id=claim_id,
            created_by=created_by,
            trace_id=current_trace_id,
        )

        try:
            # Step 2: Set status to "in_progress"
            await self.coverage_run_repository.set_in_progress(run_id)

            # Step 3: Fetch previous refined coverage notes for refinement
            previous_notes = (
                await self.coverage_notes_repository.get_recent_refined_coverage_notes(
                    claim_id=claim_id
                )
            )

            # Format previous notes for the prompt if they exist
            formatted_previous_notes = None
            if previous_notes:
                formatted_previous_notes = self._format_previous_notes_for_prompt(
                    previous_notes
                )
                logger.info(
                    f"Found {len(previous_notes)} previous refined notes for language consistency"
                )

            # Step 4: Perform coverage determination using the agent with refined_notes
            coverage_response = await self.agent.determine_coverage(
                claim_id=claim_id,
                authorization=authorization,
                as_of_date=as_of_date,
                refined_notes=formatted_previous_notes,
            )

            # Step 5: Store the verification items as coverage notes
            await self.coverage_notes_repository.create_notes_for_run(
                run_id=run_id,
                claim_id=claim_id,
                verification_items=coverage_response.verifications,
            )

            # Step 6: Set status to "succeeded"
            await self.coverage_run_repository.set_succeeded(run_id)

            # Step 7: Retrieve the complete run with notes to return
            run_with_notes = await self.coverage_run_repository.get_run_with_notes(
                run_id
            )

            if not run_with_notes:
                raise RuntimeError(
                    f"Failed to retrieve coverage run {run_id} after creation"
                )

            logger.info(
                f"Successfully created coverage run {run_id} with {len(run_with_notes.coverage_notes)} notes"
            )
            return run_with_notes

        except Exception as e:
            # Set status to "failed" on any error
            try:
                await self.coverage_run_repository.set_failed(run_id)
            except Exception as repo_error:
                logger.exception(
                    f"Failed to set run {run_id} status to failed: {str(repo_error)}"
                )

            logger.error(
                f"Error in determine_coverage_with_storage for run {run_id}: {str(e)}"
            )
            raise

    def _format_previous_notes_for_prompt(
        self, previous_notes: list[CoverageNotes]
    ) -> str:
        """Format previous refined coverage notes for inclusion in the refinement prompt.

        Args:
            previous_notes: List of CoverageNotes with refined content.

        Returns:
            Formatted string representation of the previous notes.
        """
        formatted_notes = []

        for note in previous_notes:
            if note.modified_content and note.updated_at:
                # Convert the modified_content (dict) to a formatted JSON string
                modified_content_json = json.dumps(note.modified_content, indent=2)

                formatted_note = FEEDBACK_COVERAGE_NOTE_TEMPLATE.format(
                    updated_at=note.updated_at.strftime("%Y-%m-%d %H:%M:%S UTC"),
                    content=modified_content_json,
                )
                formatted_notes.append(formatted_note)

        return (
            "\n".join(formatted_notes)
            if formatted_notes
            else "No previous refined examples available."
        )

    async def submit_feedback(
        self,
        feedback_list: list[NoteFeedback],
        updated_by: str,
    ) -> FeedbackResponse:
        """Submit feedback for coverage notes.

        Args:
            feedback_list: List of feedback for individual notes.
            updated_by: Email of the user providing feedback.

        Returns:
            A FeedbackResponse with the updated note IDs.
        """
        logger.info(f"Submitting feedback for {len(feedback_list)} notes")

        updated_note_ids = await self.coverage_notes_repository.upsert_feedback(
            feedback_list=feedback_list,
            updated_by=updated_by,
        )

        return FeedbackResponse(updated_notes=updated_note_ids)
