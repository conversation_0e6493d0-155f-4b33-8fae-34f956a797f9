from abc import abstractmethod
import datetime
from enum import Enum
from typing import Protocol, runtime_checkable, Optional
import uuid

from claims_agent.db.orm import LegacyCoverageDeterminations
from claims_agent.db.orm.generated import CoverageNotes
from claims_agent.interfaces.legacy_models import (
    CoverageDeterminationResponse as LegacyCoverageDeterminationResponse,
    VerificationItem,
)
from claims_agent.models.feedback import (
    CoverageRunWithNotes,
    NoteFeedback,
)
from pydantic import BaseModel


class CreateCoverageDeterminationRequest(BaseModel):
    """
    A request to create a coverage determination request.
    """

    request_id: uuid.UUID  # The ID of the request to create.
    claim_id: str  # The ID of the claim that the coverage determination is for.
    workflow_id: str  # The ID of the temporal workflow that will be used to generate the coverage determination.
    timestamp: datetime.datetime


class CoverageDeterminationError(BaseModel):
    kind: str
    message: str


class CoverageDeterminationStatus(str, Enum):
    REQUESTED = "requested"
    IN_PROGRESS = "in_progress"
    SUCCEEDED = "succeeded"
    FAILED = "failed"
    CANCELLED = "cancelled"


@runtime_checkable
class CoverageDeterminationRequestRepositoryProtocol(Protocol):
    @abstractmethod
    async def insert(self, request: CreateCoverageDeterminationRequest) -> None:
        """
        Insert a new coverage determination request into the database.

        Args:
            request: The request to insert.
        """

    @abstractmethod
    async def get(self, request_id: uuid.UUID) -> LegacyCoverageDeterminations:
        """
        Get a coverage determination request from the database.
        """

    @abstractmethod
    async def set_in_progress(
        self,
        request_id: uuid.UUID,
        timestamp: datetime.datetime,
    ) -> None:
        """
        Set the coverage determination request to the `in_progress` status.
        """

    @abstractmethod
    async def set_succeeded(
        self,
        request_id: uuid.UUID,
        timestamp: datetime.datetime,
        result: LegacyCoverageDeterminationResponse,
    ) -> None:
        """
        Set the coverage determination request to the `succeeded` status.
        """

    @abstractmethod
    async def set_failed(
        self,
        request_id: uuid.UUID,
        timestamp: datetime.datetime,
        error: CoverageDeterminationError,
    ) -> None:
        """
        Set the coverage determination request to the `failed` status.
        """

    @abstractmethod
    async def get_latest_successful(
        self,
        claim_id: str,
        max_age_seconds: int,
    ) -> LegacyCoverageDeterminationResponse | None:
        """
        Get the most recent successful coverage determination for a claim within the TTL window.

        Args:
            claim_id: The ID of the claim to look up.
            max_age_seconds: Maximum age in seconds for the determination to be considered fresh.

        Returns:
            The cached LegacyCoverageDeterminationResponse if found and fresh, None otherwise.
        """


@runtime_checkable
class CoverageRunRepositoryProtocol(Protocol):
    """Protocol for coverage run repository operations."""

    @abstractmethod
    async def create_run(
        self,
        claim_id: str,
        created_by: str,
        trace_id: Optional[str] = None,
    ) -> uuid.UUID:
        """Create a new coverage run.

        Args:
            claim_id: The claim ID being analyzed.
            created_by: Email of the user creating the run.
            trace_id: Optional trace ID for debugging.

        Returns:
            The UUID of the created run.
        """

    @abstractmethod
    async def set_in_progress(self, run_id: uuid.UUID) -> None:
        """Set the status of a coverage run to IN_PROGRESS.

        Args:
            run_id: The ID of the run to update.
        """

    @abstractmethod
    async def set_succeeded(self, run_id: uuid.UUID) -> None:
        """Set the status of a coverage run to SUCCEEDED.

        Args:
            run_id: The ID of the run to update.
        """

    @abstractmethod
    async def set_failed(self, run_id: uuid.UUID) -> None:
        """Set the status of a coverage run to FAILED.

        Args:
            run_id: The ID of the run to update.
        """

    @abstractmethod
    async def set_cancelled(self, run_id: uuid.UUID) -> None:
        """Set the status of a coverage run to CANCELLED.

        Args:
            run_id: The ID of the run to update.
        """

    @abstractmethod
    async def get_run_with_notes(
        self, run_id: uuid.UUID
    ) -> Optional[CoverageRunWithNotes]:
        """Get a coverage run with its notes.

        Args:
            run_id: The ID of the run to retrieve.

        Returns:
            The coverage run with notes, or None if not found.
        """

    @abstractmethod
    async def get_latest_run_for_claim(
        self, claim_id: str
    ) -> Optional[CoverageRunWithNotes]:
        """Get the latest coverage run for a claim.

        Args:
            claim_id: The claim ID to search for.

        Returns:
            The latest coverage run with notes, or None if not found.
        """

    @abstractmethod
    async def get_latest_successful_runs_for_claim(
        self, claim_id: str, limit: int = 1
    ) -> list[CoverageRunWithNotes]:
        """Get the latest successful coverage runs for a claim.

        Args:
            claim_id: The claim ID to search for.
            limit: Maximum number of runs to return (default: 1).

        Returns:
            List of successful coverage runs with notes, ordered by creation date desc.
        """


@runtime_checkable
class CoverageNotesRepositoryProtocol(Protocol):
    """Protocol for coverage notes repository operations."""

    @abstractmethod
    async def create_notes_for_run(
        self,
        run_id: uuid.UUID,
        claim_id: str,
        verification_items: list[VerificationItem],
    ) -> list[uuid.UUID]:
        """Create coverage notes for a coverage run.

        Args:
            run_id: The ID of the coverage run.
            claim_id: The claim ID being analyzed.
            verification_items: List of verification items to store as notes.

        Returns:
            List of note IDs that were created.
        """

    @abstractmethod
    async def upsert_feedback(
        self,
        feedback_list: list[NoteFeedback],
        updated_by: str,
    ) -> list[uuid.UUID]:
        """Upsert feedback for multiple coverage notes.

        Args:
            feedback_list: List of feedback for individual notes.
            updated_by: Email of the user providing feedback.

        Returns:
            List of note IDs that were updated.
        """

    @abstractmethod
    async def get_recent_refined_coverage_notes(
        self, claim_id: str, limit: int = 20, exclude_run_id: Optional[uuid.UUID] = None
    ) -> list[CoverageNotes]:
        """Get recent coverage notes with human-refined content for language consistency analysis.

        Only returns notes that have been refined through human feedback (modified_content is not null)
        for the specific claim.

        Args:
            claim_id: The claim ID to filter notes for.
            limit: Maximum number of notes to retrieve.
            exclude_run_id: Optional run ID to exclude from results (e.g., current run).

        Returns:
            List of recent refined coverage notes for the claim, ordered by most recent first.
        """
