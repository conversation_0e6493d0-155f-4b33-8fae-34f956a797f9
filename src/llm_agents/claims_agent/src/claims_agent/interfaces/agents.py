"""Agent interfaces and abstractions.

This module defines the interfaces that agent implementations must adhere to,
separating the contract from the implementation.
"""

from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, ClassVar, Protocol, runtime_checkable

from pydantic import BaseModel

from claims_agent.interfaces.legacy_models import (
    CoverageDeterminationResponse as LegacyCoverageDeterminationResponse,
)


class ProcessPromptResponse(BaseModel):
    """Structured response for the process_prompt method."""

    content: str = ""
    # Potentially add other fields like: tool_calls_made: int = 0, error_info: str | None = None

    def __contains__(self, item: str) -> bool:
        """Enable 'in' operator for string contents check.

        Args:
            item: String to check

        Returns:
            True if the string is in content, False otherwise
        """
        return item in self.content

    def endswith(self, suffix: str) -> bool:
        """Enable endswith check, delegating to the content string.

        Args:
            suffix: String to check if content ends with

        Returns:
            True if the content ends with the suffix, False otherwise
        """
        return self.content.endswith(suffix)


@runtime_checkable
class AgentProtocol(Protocol):
    """Protocol defining the interface that agent implementations must satisfy."""

    @abstractmethod
    async def _initialize_resources(self) -> None:
        """Initialize any resources needed by the agent.

        This method is called during agent initialization.
        """
        ...

    @abstractmethod
    async def _cleanup_resources(self) -> None:
        """Clean up any resources used by the agent.

        This method is called during agent shutdown.
        """
        ...

    @abstractmethod
    async def process_prompt(
        self,
        prompt: str,
        chat_history: list[dict[str, Any]] | None = None,
        authorization: str | None = None,
        **kwargs: Any,
    ) -> ProcessPromptResponse:
        """Process a user prompt and return a response.

        Args:
            prompt: The user's input prompt.
            chat_history: Optional list of previous chat messages.
            authorization: Optional authorization header to include in requests.
            **kwargs: Additional arguments for processing.

        Returns:
            The agent's structured response to the prompt.
        """
        ...

    @abstractmethod
    async def determine_coverage(
        self,
        claim_id: str,
        authorization: str | None = None,
        as_of_date: datetime | None = None,
        refined_notes: str | None = None,
    ) -> LegacyCoverageDeterminationResponse:
        """Determine coverage for a claim using the legacy data model.

        Args:
            claim_id: The ID of the claim to analyze.
            authorization: Optional authorization header to include in requests.
            as_of_date: Optional date to perform coverage determination as of
                (defaults to current time)
            refined_notes: Optional previous refined notes for language consistency.

        Returns:
            Coverage determination result as a legacy CoverageDeterminationResponse.
        """
        ...


class AgentManagerBase(ABC):
    """Base class for agent managers.

    This class defines the interface for agent management functionality.
    """

    _instance: ClassVar[AgentProtocol | None] = None

    @classmethod
    @abstractmethod
    def get_agent(cls) -> "AgentProtocol":
        """Get the initialized agent instance.

        Returns:
            The initialized agent instance.

        Raises:
            AgentNotInitializedError: If the agent is not initialized.
        """
        ...

    @classmethod
    @abstractmethod
    async def initialize(cls) -> None:
        """Initialize the agent asynchronously.

        This method creates and initializes an agent instance.
        """
        ...

    @classmethod
    @abstractmethod
    async def shutdown(cls) -> None:
        """Shutdown the agent and clean up resources."""
        ...
