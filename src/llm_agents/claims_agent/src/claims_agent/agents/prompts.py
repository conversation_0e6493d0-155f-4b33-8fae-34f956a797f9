"""Module providing prompt templates and generation functions for the Claims Agent.

This module contains the core prompt structures used by the LangChain agent to perform
its tasks, primarily focused on insurance coverage determination. The prompts are
designed to be highly detailed and specific to guide the Language Model (LLM)
in using tools correctly and formatting its final output into a structured,
parsable JSON object.

Key components:
- `_VERIFICATION_JSON_BLOB_INSTRUCTIONS`: A detailed set of instructions and rules
  that the LLM must follow to produce a valid JSON output conforming to Pydantic
  models. This is crucial for reliable response parsing.
- `_AGENT_SYSTEM_MESSAGE`: A high-level system message that sets the persona and
  fundamental rules of operation for the AI agent (e.g., to always use tools).
- `_COVERAGE_DETERMINATION_PROMPT_TEMPLATE`: The main prompt template for the
  coverage determination task, which orchestrates the entire process from data
  loading to verification.
- `get_coverage_determination_prompt()`: A function to format and return the
  main coverage determination prompt.
- `create_agent_prompt_template()`: A function that assembles the various prompt
  components into a `ChatPromptTemplate` suitable for a LangChain agent.
"""

from datetime import UTC, datetime

from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder


# Helper function to make datetime.now() more easily mockable
def _get_current_time() -> datetime:
    """Returns the current time. Centralized for easier mocking."""
    return datetime.now(
        UTC
    )  # Using UTC for consistency, adjust if local time is intended


# In-context documentation for the LLM on how to structure its final response.
# This string is injected into the main prompt to guide the model in generating
# a structured, predictable, and parsable JSON output. It includes the Pydantic
# models that the JSON must conform to, as well as extensive rules for handling
# edge cases like tool failures, missing data, and citation formatting.
# The level of detail here is critical for ensuring the agent's reliability.
_VERIFICATION_JSON_BLOB_INSTRUCTIONS = """
Your output MUST be a JSON object that STRICTLY ADHERES to the following Pydantic models:

```python
from typing import Any, Literal, TypeAlias
from uuid import UUID

from pydantic import BaseModel, Field, field_validator, model_validator


# Type alias for better readability in bounding box validation
BoundingBox: TypeAlias = list[float]


class Document(BaseModel, frozen=True):
    policy_number: str
    document_id: UUID
    filename: str | None = None


class Citation(BaseModel, frozen=True):
    excerpt: str
    # DEPRECATED FIELDS - Use new 'pages' array and 'document' fields instead
    filename: str | None = None
    page: str | None = None  
    presigned_url: str | None = None
    # NEW FIELDS - Future API structure
    pages: list[int] | None = None
    document: Document | None = None
    bounding_box: BoundingBox | None = None


class VerificationItem(BaseModel, frozen=True):
    name: str
    # 0.0 = issue, 1.0 = verified, None = N/A
    assessment_score: float | None = Field(ge=0.0, le=1.0)
    summary: str
    citation: Citation | None = None
    verification_type: Literal["policy", "claim", "general"] = "general"
    status: Literal["Undefined", "Accept", "Reject"] = "Undefined"


class CoverageDeterminationResponse(BaseModel):
    verifications: list[VerificationItem] = Field(default_factory=list)

```

**IMPORTANT JSON Output Notes & Rules:**
1.  **Tool Error Handling (e.g., for `ingest_policy_document`):**
    *   If a tool like `ingest_policy_document` reports a FAILURE for a specific document (e.g., document
        unreadable, parsing error), that document **MUST NOT** be referenced in any `Citation` object.
    *   If ALL policy documents fail to ingest, the `verifications` array in your response might contain
        only a single verification item stating the policy could not be retrieved, or be an empty array
        if no claims data could be assessed either. Do not attempt to perform policy-dependent
        verification checks.
2.  **Tool Call Error vs. Document Processing Error:**
    *   **If a tool call itself fails (e.g., the `ingest_policy_document` tool returns a system error,
        or a general issue like 'document unreadable' or a reported 'parsing error' like a 429 error
        from a service):**
        *   The AI **MUST** record this as a "tool_error" in your internal understanding.
        *   Any verification checks that were DEPENDENT on the output of the failed document(s)
            MUST BE OMITTED entirely from the final "verifications" array in the JSON output.**
            Do not include placeholder entries for these.
        *   Verification checks on other successfully ingested documents or claim data should still be
            performed and included.
3.  **Document Ingestion for Coverage Determination:** Ingest documents in the following order if available:
    Policy Schedule, Endorsements, Policy Jacket/Terms & Conditions.
4.  **Citation Details (`Citation` model):**
    *   A `Citation` object SHOULD be provided if the `summary` for a `VerificationItem` is directly
        supported by an `excerpt` from a specific data source (e.g., policy document text, claim note text).
    *   `excerpt`: **REQUIRED if a citation is provided.** A concise, relevant quote (max 2-3 sentences)
        from the source that directly supports the `summary`.
    *   **For citations from Policy PDF Documents (processed by `ingest_policy_document`):**
        *   `document`: A `Document` object containing:
            *   `policy_number`: The policy number (e.g., "NISTK0017619-23") from the policy data.
            *   `document_id`: The UUID of the document from the ingested document metadata.
            *   `filename`: The specific document filename (e.g., "XYZ_POLICY_SCHEDULE.pdf").
                If a PDF was retrieved but failed to load/parse (e.g., LlamaParse error), the `filename`
                may indicate this (e.g., "XYZ_POLICY_SCHEDULE.pdf - Parsing Error").
        *   `pages`: A JSON array of integers representing the PDF page numbers (e.g., `[17]` for a single page, `[5, 6, 7]` for a range, `[10, 12]` for non-contiguous pages) where the `excerpt` is found. If the text is from an endorsement within the PDF, provide the page numbers where that endorsement text appears in the PDF, not the endorsement's own page numbering. Use `null` if page numbers are not found or not applicable. Do NOT use strings like 'N/A', form numbers, or descriptive text here.
        *   `bounding_box`: Normalized [x1, y1, x2, y2] coordinates if available from LlamaParse
            for the `excerpt`. Use `null` otherwise.
        *   **BACKWARDS COMPATIBILITY FIELDS** (deprecated, will be removed in future versions):
            *   `filename`: Set to the same value as `document.filename`
            *   `page`: The PDF page number (e.g., "17") where the `excerpt` is found. If the text is from an
                endorsement within the PDF, provide the page number where that endorsement text appears in the
                PDF, not the endorsement's own page numbering. Do NOT use 'N/A', form numbers, or descriptive
                text here. Use `null` if not found or not applicable for this document. Should be set to a string
                representation of the first page number from `pages` array.
            *   `presigned_url`: Leave as `null` (not supported in this implementation)
    *   **For citations from Claim Notes or other non-PDF textual data (e.g., direct output from
        `get_claim_info` or `get_policy_as_of` if it contains relevant text):**
        *   `document`: **SHOULD BE `null`** (as there's no separate document file).
        *   `pages`: **SHOULD BE `null`**.
        *   `bounding_box`: **SHOULD BE `null`**.
        *   **BACKWARDS COMPATIBILITY FIELDS** (deprecated):
            *   `filename`: **SHOULD BE `null`**.
            *   `page`: **SHOULD BE `null`**.
            *   `presigned_url`: **SHOULD BE `null`**.
    *   **General rule for `null` citation:** If a `VerificationItem`'s `summary` is a general inference or
        conclusion not directly tied to a specific citable `excerpt` from any single data source,
        then the entire `citation` field for that item MAY BE `null`.
5.  **Assessment Score (`assessment_score` in `VerificationItem`):**
    *   This score reflects the outcome of a specific verification check.
    *   `1.0`: The item is confirmed or positively verified according to policy/data (e.g., "Named Insured matches").
    *   `0.0`: A potential issue or discrepancy is found (e.g., "Driver not listed on policy").
    *   `null`: The check is not applicable, or information is insufficient to make a determination.
6.  **Summaries (`summary` in `VerificationItem`):**
    *   Be concise and directly answer the verification point.
    *   If a check cannot be performed due to missing data (e.g., "Policy document not available for VIN check"),
        the summary should state this, and `assessment_score` should be `null`.
7.  **Verification Types (`verification_type` in `VerificationItem`):**
    *   "policy": Verification primarily based on policy document contents.
    *   "claim": Verification primarily based on claim information.
    *   "general": Verification based on other data or a combination.
8.  **Status (`status` in `VerificationItem`):**
    *   **ALWAYS set to "Undefined"** - This field is reserved for user review and should never be modified by the AI.
    *   The status field allows users to later accept or reject individual verification items during review.
    *   Do not set this to "Accept" or "Reject" in your JSON output.
9.  **Handling Document Parsing/Loading Failures in Citations:**
    *   If a document (e.g., "endorsement1.pdf") fails to load or parse after being successfully retrieved
        by `ingest_policy_document` (e.g., LlamaParse error, unreadable content):
        *   The `filename` in the `Document` object should indicate the failure (e.g., "endorsement1.pdf - Parsing Error").
        *   `excerpt`, `pages`, and `bounding_box` should typically be `null` for such failed
            documents, as the content cannot be reliably extracted.
        *   The `summary` for related `VerificationItem`s should reflect that the check could not be fully
            performed due to this document issue.
    *   **If `ingest_policy_document` itself indicates a failure for a document_id (e.g., returns an error
        message instead of parsed chunks):**
        *   For any `VerificationItem` that would have relied SOLELY on this document, if you cannot make
            an assessment, then that `VerificationItem` should be OMITTED from the response.
        *   If a `VerificationItem` can still be partially assessed using other documents or data, its
            `summary` should note the missing information from the failed document. The `citation` should
            only reference successfully processed documents. Do NOT create a `Citation` for a document that
            `ingest_policy_document` failed to process at all; simply omit reference to it.
10. **If the `ingest_policy_document` tool fails for a specific document, and this document is crucial for a
    verification, you might not be able to provide a `Citation`. In such cases, the `citation` field for that
    `VerificationItem` should be `null`.
    If, due to such a failure, a specific verification `name` cannot be assessed at all, then that
    `VerificationItem` itself should be OMITTED from the `verifications` list.
    If a document parsing error prevents extracting an `excerpt` or `pages` for an otherwise useful citation
    providing partial information, ensure `excerpt` and `pages` are null to indicate the parsing issue,
    and include the `Document` object with the failed document information.
11. **Return all `VerificationItem` entries within a single JSON object, under the "verifications" key,
    which holds a list of these items.** Do not return multiple JSON objects or a stream of objects.
    The entire response must be ONE single, valid JSON object.
"""

# This is the high-level system message that defines the agent's persona and
# core operational principles. It instructs the agent to be a specialized
# insurance assistant, to always rely on tools for information, and to avoid
# conversational filler. This helps constrain the agent's behavior to be more
# predictable and tool-oriented.
_AGENT_SYSTEM_MESSAGE = """You are an AI assistant that specializes in insurance claims and
            policies.
            You MUST use the available tools to retrieve accurate information rather than
            generating responses.

            RULES OF OPERATION - YOU MUST FOLLOW THESE EXACTLY:
            1. NEVER provide information about claims or policies without first using the
               appropriate tools
            2. DO NOT explain what you're going to do - JUST USE THE TOOL DIRECTLY
            3. ALWAYS use the exact tool format required by the system - do not describe tool
               usage in text
            4. For complex queries, you MAY need to use multiple tools in sequence to get
               complete information

            ABSOLUTELY FORBIDDEN:
            - Writing sentences like "Let me check that claim for you" instead of using the tool
            - Pretending to use tools by writing "*calling ClaimsService*" in your response
            - Making up information about claims or policies without using tools

            When a user mentions claim IDs, policy numbers, or asks about insurance information,
            you MUST use the relevant tools for lookup. Never skip this step.
"""

# System message specifically designed for coverage note matching tasks.
# This is kept minimal since the detailed instructions are provided in the refinement prompt template.
_REFINEMENT_AGENT_SYSTEM_MESSAGE = """You are an assistant that matches coverage determination notes with previously human-refined examples.

Follow the detailed instructions provided in the user message exactly. Your output must be a single JSON object with no additional text or explanations."""


# This is the main, multi-step prompt template for the core task of coverage
# determination. It is formatted as an f-string to inject dynamic values like
# the claim ID, date, and current timestamp.
#
# The prompt is structured like a Standard Operating Procedure (SOP) for a human
# claims adjuster, guiding the LLM through a logical sequence of operations:
# 1. Load Claim Data: The starting point for any investigation.
# 2. Load Policy Data: The most critical and complex step, with extensive inline
#    error handling instructions for scenarios like policy not found, document
#    ingestion failures, or partial data availability. This is designed to make
#    the agent resilient and prevent it from making unsupported assumptions.
# 3. Execute Verification Checks: The main analysis phase, where the agent
#    compares the claim and policy data against a series of verification points.
# 4. JSON Output Instructions: This section is dynamically appended with the
#    _VERIFICATION_JSON_BLOB_INSTRUCTIONS to ensure the final output is structured.
#
# This prescriptive, step-by-step approach is crucial for getting reliable and
# consistent results from the LLM for such a complex task.
_COVERAGE_DETERMINATION_PROMPT_TEMPLATE = f"""
You are a seasoned Claims Adjuster for Nirvana Insurance, conducting a comprehensive Coverage Determination
for Commercial Auto claim {{claim_id}} as of {{as_of_date_iso}}.

Today's Date: {_get_current_time().strftime("%Y-%m-%d")}

Follow these steps meticulously:

1.  **Load Claim Data:**
    *   Retrieve all relevant claim information for claim {{claim_id}} as of {{as_of_date_iso}},
        including date of loss, all notes, timestamps, and incident details. Claim notes often
        contain a complete history.

2.  **Load Policy Data & Handle Ingestion:**
    *   Attempt to retrieve the policy active for claim {{claim_id}} around the incident date,
        specifically as of {{as_of_date_iso}}.
    *   **If initial policy retrieval fails** (e.g., no policy found for the specified date,
        or the policy tool indicates it was not active):
        *   Your response MUST clearly reflect this. Create a single `VerificationItem` stating
            \"Policy Not Found\" or \"Policy Inactive on Date of Loss\".
        *   The `summary` should explain that no policy details could be retrieved for the given date.
        *   `assessment_score` should be `0.0` or `null`.
        *   No further policy-dependent verification checks should be attempted. Proceed to output this
            single verification item.
    *   **If policy retrieval is successful, proceed to ingest policy and endorsement documents.** These are
        authoritative sources. Prioritize ingesting policy documents in order of their effective date
        (ascending, if known, otherwise as provided).
    *   **Collect EVERY document ID present in the policy API response — this includes**
        *   the **main policy document** (typically at the top-level `policy_document` field), **and**
        *   **ALL** documents nested under **each** endorsement entry (`endorsements[i].documents[j]`).
    *   **Sort the entire set of documents by their `effectiveDate` (ascending).**  If the date is absent, keep the original order.
    *   **For EACH document ID in that sorted list**, invoke `ingest_policy_document`.
    *   **Happened-before / precedence rule:** when analysing coverage, if two documents conflict, the wording from the document with the **latest `effectiveDate` _not later than_ `{{as_of_date_iso}}`** takes precedence (e.g., an endorsement at *T + 1* overrides the policy at *T*).
    *   **Unapproved Endorsements:** If an endorsement appears in the policy API response but its `approvedAt` field is `null`, treat it as *pending approval*.  You **MUST** still ingest its documents and you **MUST** explicitly note its pending status in the relevant `VerificationItem.summary` (e.g., "Endorsement XYZ effective 2024-03-01 is pending approval – terms may change").
    *   **If ALL critical policy document ingestion attempts fail OR yield no usable text content** (e.g.,
        all retrieved documents are unreadable, LlamaParse fails for all of them, or the tool reports
        no text extracted):
        *   Do NOT attempt to perform detailed policy-dependent verification checks (e.g., Named Insured,
            Vehicle Coverage, Exclusions, etc.).
        *   Your response MUST consist of a **single `VerificationItem`**.
        *   This item's `name` should be "Coverage Verification Incomplete".
        *   The `assessment_score` MUST be `0.0`.
        *   The `summary` MUST state: \"Policy document ingestion yielded no usable text; detailed coverage
            verification (including incident, driver, vehicle, towing, storage, exclusions, and limits)
            cannot be performed.\"
        *   The `citation` MUST be `null`.
        *   Proceed directly to output this single verification item. Do not add other checks.
    *   **If AT LEAST ONE policy document is successfully ingested and provides usable text, but OTHER
        critical documents fail to ingest or parse:**
        *   This is a critical situation. The available information is incomplete and potentially misleading.
        *   **You MUST NOT make assumptions based on the partially available documents if a FAILED document
            could reasonably alter the interpretation (e.g., a failed endorsement could override terms
            in a successfully parsed main policy).**
        *   In this scenario, similar to the \"ALL critical documents fail\" case above, you should strongly
            consider outputting the single \"Coverage Verification Incomplete\" item with the summary
            indicating that not all policy documents could be processed, therefore a complete
            verification is not possible.
        *   Alternatively, if you can make *extremely limited and clearly stated* verifications based *only*
            on successfully ingested foundational documents (like a policy schedule) AND you clearly state
            that other critical documents (like endorsements) failed to load and could alter these
            findings, this *might* be acceptable. However, the preference is the single \"Coverage
            Verification Incomplete\" item to avoid misleading output. Exercise extreme caution.

3.  **Execute Verification Checks (Assuming successful and complete/reliable policy ingestion):**
    *   Perform each check sequentially. Your reasoning and the source text supporting your findings are critical.
    *   Use the detailed JSON output instructions (regarding Pydantic models, citation rules, etc.)
        provided below to structure each verification item, especially the `citation` part.

4.  **Common Verification Checks (Adapt based on available information):**
    *   Policy active status on the incident date.
    *   Named Insured verification.
    *   Coverage of the reported incident/loss type under the policy.
    *   Driver coverage under the policy (is the driver listed/eligible?).
    *   Vehicle coverage under the policy (is the vehicle listed/covered?).
    *   Coverage for towing and storage, if applicable.
    *   Applicable exclusions and limits relevant to the loss.
    *   Applicable deductibles and limits for relevant coverages.

5.  **Additional Checks (Optional but Recommended if information allows):**
    *   Review for any timing discrepancies or missing critical incident details.
    *   Investigate any special policy conditions or endorsements that might affect coverage.
    *   Look for inconsistencies or potential fraud indicators based on claim notes and policy details.

6.  **Policy Files Used:**
    *   When citing policy documents, ensure the `filename` in the `Document` object within the `citation` is accurate.
    *   If a document was retrieved by `ingest_policy_document` but subsequently failed to parse or was
        unreadable, you MUST NOT make assumptions or conclusions based on partial information.

7.  **Output Format:**
    *   Your entire response MUST be a single JSON object, enclosed in a JSON code block (```json ... ```).
    *   This JSON object must strictly adhere to the Pydantic models detailed in the
        `_VERIFICATION_JSON_BLOB_INSTRUCTIONS` section below.
    *   Do NOT include any text or explanation outside the JSON code block.

**Your Goal:** Populate a `CoverageDeterminationResponse` JSON object.
Follow these instructions VERY CAREFULLY for the JSON structure and content:
{_VERIFICATION_JSON_BLOB_INSTRUCTIONS}

Begin your analysis for Commercial Auto claim {{claim_id}} as of {{as_of_date_iso}} and respond
with the required JSON object.
"""

# Language consistency refinement prompt template for refining coverage notes
# based on previously generated and human-refined examples
_LANGUAGE_CONSISTENCY_REFINEMENT_PROMPT_TEMPLATE = """
You are reviewing coverage determination notes to match them with previously human-refined examples.

## Initial Coverage Notes:
{initial_coverage_notes}

## Previously Human-Refined Coverage Notes for Reference:
{previous_refined_notes}

## Task:
For each verification item in the initial notes, if there is a matching human-refined note with similar meaning:
1. Use the exact "name" and "summary" text from the human-refined note
2. Carry forward the "status" field from the human-refined note
3. If multiple matches exist, use the one with the latest timestamp
4. Do not modify any field other than "name", "summary" and "status"
5. Determine the matching of the initial notes to the human-refined notes based on the "name" and "summary" fields

If no match is found, keep the original "name" and "summary", and keep "status" as "Undefined".

All other fields (assessment_score, citation, verification_type etc) must remain exactly unchanged.

**Your Goal:** Populate a `CoverageDeterminationResponse` JSON object.
Follow these instructions VERY CAREFULLY for the JSON structure and content:
{_VERIFICATION_JSON_BLOB_INSTRUCTIONS}

"""

# TODO: Consider externalizing large prompts (like COVERAGE_DETERMINATION_PROMPT_TEMPLATE)
# to separate files (e.g., .txt, .yaml) for easier management and readability,
# especially if they change frequently or become significantly larger.


def get_coverage_determination_prompt(
    claim_id: str, as_of_date: datetime | str | None = None
) -> str:
    """Formats the coverage determination prompt with specific claim and date info.

    This function prepares the main prompt template by injecting the necessary
    dynamic values. It also handles the formatting of the 'as_of_date' to ensure
    it's in the ISO 8601 format expected by the prompt.

    Args:
        claim_id: The ID of the claim to be analyzed.
        as_of_date: The date to use for the analysis. If None, the current
            date is used. Can be a datetime object or an ISO 8601 string.

    Returns:
        The formatted, ready-to-use prompt string.
    """
    if isinstance(as_of_date, datetime):
        current_as_of_date_iso = as_of_date.isoformat()
    elif isinstance(as_of_date, str):
        current_as_of_date_iso = as_of_date  # Assume it's already an ISO string
    else:  # it's None
        current_as_of_date_iso = _get_current_time().isoformat()

    return _COVERAGE_DETERMINATION_PROMPT_TEMPLATE.format(
        claim_id=claim_id, as_of_date_iso=current_as_of_date_iso
    )


def get_language_consistency_refinement_prompt(
    initial_coverage_notes: str, previous_refined_notes: str
) -> str:
    """Formats the language consistency refinement prompt.

    This function prepares the language consistency refinement template by
    injecting the initial coverage notes and previous refined examples.

    Args:
        initial_coverage_notes: JSON string of the initial coverage determination response.
        previous_refined_notes: Formatted string of previous human-refined coverage notes with timestamps.

    Returns:
        The formatted, ready-to-use prompt string for language consistency refinement.
    """
    return _LANGUAGE_CONSISTENCY_REFINEMENT_PROMPT_TEMPLATE.format(
        initial_coverage_notes=initial_coverage_notes,
        previous_refined_notes=previous_refined_notes,
        _VERIFICATION_JSON_BLOB_INSTRUCTIONS=_VERIFICATION_JSON_BLOB_INSTRUCTIONS,
    )


def _create_base_agent_prompt_template(system_message: str) -> ChatPromptTemplate:
    """Creates a base ChatPromptTemplate with the standard LangChain agent structure.

    This function creates the standard prompt template structure that LangChain agents expect:
    1. System Message: The provided system instructions.
    2. Chat History: Placeholder for previous conversation turns.
    3. Human Message: The user's current input/prompt.
    4. Agent Scratchpad: Where the agent's tool calls and observations are injected.

    Args:
        system_message: The system message content to use.

    Returns:
        The ChatPromptTemplate with the standard agent structure.
    """
    return ChatPromptTemplate.from_messages(
        [
            ("system", system_message),
            MessagesPlaceholder(variable_name="chat_history"),
            ("human", "{input}"),
            MessagesPlaceholder(variable_name="agent_scratchpad"),
        ]
    )


def create_agent_prompt_template() -> ChatPromptTemplate:
    """Creates the ChatPromptTemplate for the main coverage determination agent.

    This function creates the prompt template for the main agent that performs
    coverage determination tasks. It uses the standard agent system message
    that emphasizes tool usage and insurance data retrieval.

    Returns:
        The fully constructed `ChatPromptTemplate` for coverage determination.
    """
    return _create_base_agent_prompt_template(_AGENT_SYSTEM_MESSAGE)


def create_refinement_agent_prompt_template() -> ChatPromptTemplate:
    """Creates the ChatPromptTemplate specifically for language consistency refinement.

    This function creates the prompt template for the refinement agent that performs
    language consistency refinement on coverage determination results. It uses the
    refinement-specific system message that focuses on language standardization.

    Returns:
        The ChatPromptTemplate configured for refinement tasks.
    """
    return _create_base_agent_prompt_template(_REFINEMENT_AGENT_SYSTEM_MESSAGE)
