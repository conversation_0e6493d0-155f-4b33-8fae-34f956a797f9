"""Response parsing and repair utilities for the claims agent LLM outputs.

This module provides a robust, multi-stage pipeline for parsing the JSON output
from a Language Model (LLM). The primary goal is to reliably extract a structured
`CoverageDeterminationResponse` object from a raw, and potentially malformed,
LLM string response.

The process is as follows:
1.  **JSON Extraction**: The `extract_verification_json_from_llm_response`
    function attempts to locate and extract a JSON string from the raw output
    using several strategies in order of reliability:
    - Markdown-fenced blocks (e.g., ```json...```)
    - Raw JSON string (if the whole output is a valid JSON)
    - Content between the first and last curly braces.

2.  **Parsing and Validation**: Once a potential JSON string is extracted, the
    `_attempt_parse_and_validate` function tries to parse it and validate it
    against the `CoverageDeterminationResponse` Pydantic model.

3.  **Item-Level Repair and Parsing**: If the top-level validation fails, the
    `parse_verification_items` function attempts a more granular, "partial success"
    parse. It iterates through the list of verification items, trying to parse
    each one individually.
    - For each item that fails validation, the `_repair_verification_item` function
      is invoked. This "repair" logic attempts to fix common LLM errors, such as:
        - Incorrect or alternative field names (e.g., 'score' instead of 'assessment_score').
        - Type conversions (e.g., string "1.0" to float 1.0).
        - Structural fixes (e.g., wrapping a summary string in the correct format).
    - This resilience is key, as it allows the system to salvage valid data from an
      otherwise malformed response, rather than failing the entire request.

4.  **Fallback Response**: If all parsing and repair attempts fail, the top-level
    `parse_coverage_determination_response` function can return a default error
    response, controlled by the `unsafe_allow_fallback` flag. For production use,
    this flag should be `False` so that parsing failures raise an exception.
"""

import json
import re
import math
from typing import Any, Callable, TypedDict

from loguru import logger
from pydantic import ValidationError

# Assuming CoverageDeterminationResponse and VerificationItem models are Pydantic v2
# Need to import them correctly. Let's assume they are in claims_agent.models
# If VerificationItem is defined within CoverageDeterminationResponse, adjust import.
from claims_agent.interfaces.legacy_models import (
    CoverageDeterminationResponse,
    VerificationItem,
)

# Constants to avoid Cursor LLM confusion with bracket strings
JSON_START_CHARS = "{["

# Parsing and repair configuration constants
MAX_REPAIR_ATTEMPTS = 2
REQUIRED_BOUNDING_BOX_COORDINATES = 4
MAX_INPUT_SIZE_BYTES = 1_000_000  # 1MB limit for safety
BOUNDING_BOX_COORDINATE_MIN = 0.0
BOUNDING_BOX_COORDINATE_MAX = 1.0

# Known alternative field mappings
SCORE_FIELD_VARIANTS = ["score", "assessment_status", "assessmentscore"]
TYPE_FIELD_VARIANTS = ["type", "verificationType", "verificationtype"]
EXCERPT_FIELD_VARIANTS = ["text", "content", "quote", "extract"]

# Define custom error for parsing issues


class ResponseParsingError(ValueError):
    """Custom exception for errors during LLM response parsing."""

    pass


def _is_inside_markdown_block(text: str, position: int) -> bool:
    """Check if the given position is inside a markdown code block.

    Args:
        text: The full text to analyze
        position: Character position to check

    Returns:
        True if position is inside a markdown code block, False otherwise
    """
    # Find all markdown code block boundaries
    code_block_pattern = r"```(?:[a-zA-Z0-9_]*\s*)?"
    matches = list(re.finditer(code_block_pattern, text))

    # Group matches into pairs (start, end)
    code_blocks = []
    for i in range(0, len(matches), 2):
        if i + 1 < len(matches):
            start_match = matches[i]
            end_match = matches[i + 1]
            code_blocks.append((start_match.end(), end_match.start()))

    # Check if position is inside any code block
    for start, end in code_blocks:
        if start <= position <= end:
            return True
    return False


class ExtractionStrategy(TypedDict):
    """Type definition for extraction strategy."""

    name: str
    extractor: Callable[[], str | None]
    enabled: bool


def _try_extract_markdown_json(llm_output_str: str) -> str | None:
    """Attempts to extract JSON from markdown fences.

    Handles various markdown code block formats including triple backticks
    with or without language specifiers, and with different spacing patterns.

    Args:
        llm_output_str: The raw string output from the LLM.

    Returns:
        Extracted JSON string if found in markdown fences, otherwise None.
    """
    # This pattern matches standard markdown code blocks with triple backticks
    # It's more tolerant of whitespace and newline variations
    standard_pattern = re.search(
        r"```(?:[a-zA-Z0-9_]+)?\s*\n?(?P<payload>.*?)\s*\n?```",
        llm_output_str,
        re.DOTALL | re.IGNORECASE,
    )

    if standard_pattern:
        extracted_json = standard_pattern.group("payload").strip()
        common_langs = ["json", "jsonc", "jsonl"]
        for lang in common_langs:
            if extracted_json.lower().startswith(lang + "\n"):
                extracted_json = extracted_json[len(lang) + 1 :].lstrip()
                break
            if extracted_json.lower().startswith(lang + " "):
                extracted_json = extracted_json[len(lang) :].lstrip()
                break
        logger.debug(
            f"Extracted JSON from standard markdown fences: {extracted_json[:200]}"
        )
        return extracted_json

    # Try to match indented code blocks (4 spaces or tab at beginning of lines)
    # This is a fallback for non-standard markdown formats
    indented_pattern = re.search(
        r"(?:^|\n)(?P<block>(\s{4}|\t).+(?:\n(\s{4}|\t).+)*)",
        llm_output_str,
        re.MULTILINE,
    )

    if indented_pattern:
        # Remove the indentation from each line
        indented_block = indented_pattern.group("block")
        lines = indented_block.split("\n")
        dedented_lines = [
            line[4:]
            if line.startswith("    ")
            else line[1:]
            if line.startswith("\t")
            else line
            for line in lines
        ]
        extracted_json = "\n".join(dedented_lines).strip()

        try:
            # Quick validation that this might be JSON
            json.loads(extracted_json)
            logger.debug(
                f"Extracted JSON from indented code block: {extracted_json[:200]}"
            )
            return extracted_json
        except json.JSONDecodeError:
            logger.debug("Indented block did not contain valid JSON")

    logger.debug("Markdown-fenced JSON not found in any format.")
    return None


def _try_extract_raw_json(llm_output_str: str) -> str | None:
    """Attempts to parse the entire string as JSON if it's a dictionary."""
    try:
        data = json.loads(llm_output_str)
        if isinstance(data, dict):
            logger.debug("Fallback: Entire string is valid JSON and a dictionary.")
            return llm_output_str.strip()
        logger.debug("Fallback: Entire string is valid JSON but not a dictionary.")
    except json.JSONDecodeError:
        logger.debug("Fallback: Entire string is not valid JSON.")
    return None


def _try_extract_brace_json(llm_output_str: str) -> str | None:
    """Attempts to extract JSON content between matching braces.

    Uses multiple strategies:
    1. Simple extraction between first '{' and last '}'
    2. Smart extraction accounting for nested structures
    3. Balanced brace matching for complex cases

    Args:
        llm_output_str: The raw string output from the LLM.

    Returns:
        Extracted JSON string if valid JSON object is found, otherwise None.
    """
    # First attempt: Simple extraction between first '{' and last '}'
    try:
        start_brace = llm_output_str.find("{")
        end_brace = llm_output_str.rfind("}")
        if start_brace != -1 and end_brace != -1 and end_brace > start_brace:
            json_candidate = llm_output_str[start_brace : end_brace + 1]
            json.loads(json_candidate)  # Quick check if it's plausible JSON
            logger.debug(
                f"Fallback: Simple brace extraction found valid JSON: {json_candidate[:200]}"
            )
            return json_candidate
    except json.JSONDecodeError:
        logger.debug("Fallback: Simple brace-extracted content is not valid JSON.")

    # Second attempt: Find the most promising JSON object by counting braces
    try:
        # Use a more sophisticated approach to handle nested braces
        brace_count = 0
        start_index = -1
        candidates = []

        for i, char in enumerate(llm_output_str):
            if char == "{":
                if brace_count == 0:
                    start_index = i
                brace_count += 1
            elif char == "}":
                brace_count -= 1
                if brace_count == 0 and start_index != -1:
                    # Found a potential complete JSON object
                    candidate = llm_output_str[start_index : i + 1]
                    try:
                        # Check if it's valid JSON
                        json.loads(candidate)
                        candidates.append((candidate, i - start_index))
                    except json.JSONDecodeError:
                        pass  # Invalid JSON, ignore this candidate

        # Sort candidates by length (prefer longer, more complete objects)
        if candidates:
            # Cache parsed results to avoid redundant JSON parsing
            enhanced_candidates = []
            parsed_cache = {}  # candidate_string -> parsed_dict

            for candidate, length in candidates:
                # Find the position of this candidate in the original string
                candidate_pos = llm_output_str.find(candidate)
                is_in_markdown = _is_inside_markdown_block(
                    llm_output_str, candidate_pos
                )

                # Check if this candidate has root-level 'verifications' key
                has_root_verifications = False
                if candidate not in parsed_cache:
                    try:
                        parsed_cache[candidate] = json.loads(candidate)
                    except json.JSONDecodeError:
                        parsed_cache[candidate] = None

                parsed_candidate = parsed_cache[candidate]
                if (
                    isinstance(parsed_candidate, dict)
                    and "verifications" in parsed_candidate
                ):
                    has_root_verifications = True

                enhanced_candidates.append(
                    (candidate, length, is_in_markdown, has_root_verifications)
                )

            # Sort: prefer root-level verifications first, then non-markdown, then by length
            enhanced_candidates.sort(key=lambda x: (not x[3], x[2], -x[1]))

            logger.debug(
                f"Fallback: Smart brace extraction found valid JSON: {enhanced_candidates[0][0][:200]}"
            )
            return enhanced_candidates[0][0]
    except Exception as e:
        logger.debug(f"Fallback: Smart brace extraction failed: {e}")

    # Third attempt: Use regex to find potentially valid JSON objects
    try:
        # This regex tries to match balanced braces with content between them
        # Note: This is a simplistic approach and won't handle all nested cases correctly
        # (?R) would be recursive in PCRE, but not in Python re
        json_pattern = r"\{(?:[^{}]|(?R))*\}"
        # Since Python re doesn't support recursion, we'll use a simpler pattern
        # that might work for many cases but not deeply nested structures
        json_pattern = r"\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}"
        matches = re.findall(json_pattern, llm_output_str)

        valid_jsons: list[tuple[str, int]] = []
        for match in matches:
            try:
                parsed = json.loads(match)
                if isinstance(parsed, dict):
                    valid_jsons.append((match, len(match)))
            except json.JSONDecodeError:
                continue

        if valid_jsons:
            # Sort by length and return the longest valid JSON
            valid_jsons.sort(key=lambda x: x[1], reverse=True)
            logger.debug(
                f"Fallback: Regex brace extraction found valid JSON: {valid_jsons[0][0][:200]}"
            )
            return valid_jsons[0][0]
    except Exception as e:
        logger.debug(f"Fallback: Regex brace extraction failed: {e}")

    logger.debug("Fallback: All brace extraction methods failed to find valid JSON.")
    return None


def _try_extract_json_schema(llm_output_str: str) -> str | None:
    """Attempts to repair common JSON schema errors in LLM outputs.

    Looks for and fixes common structural issues in LLM-generated JSON:
    - Trailing commas in objects and arrays
    - Unquoted property names
    - Invalid escape sequences / stray backslashes
    - Single quotes instead of double quotes
    - Comments (// or /* */)
    - Special characters (like backticks) instead of quotes

    Args:
        llm_output_str: The raw string output from the LLM.

    Returns:
        Repaired JSON string if fixable, otherwise None.
    """
    if not llm_output_str or not any(c in llm_output_str for c in JSON_START_CHARS):
        return None

    # First, try to extract JSON from the text if it's not pure JSON
    json_candidate = llm_output_str.strip()

    # If the string doesn't start with { or [, try to extract JSON from it
    if not json_candidate.startswith(("{", "[")):
        # Try to find JSON objects in the text
        brace_count = 0
        start_index = -1
        candidates = []

        for i, char in enumerate(llm_output_str):
            if char == "{":
                if brace_count == 0:
                    start_index = i
                brace_count += 1
            elif char == "}":
                brace_count -= 1
                if brace_count == 0 and start_index != -1:
                    candidate = llm_output_str[start_index : i + 1]
                    candidates.append((candidate, i - start_index))

        # Sort candidates by length (prefer longer, more complete objects)
        if candidates:
            candidates.sort(key=lambda x: x[1], reverse=True)
            json_candidate = candidates[0][0]

    try:
        # First try: direct load
        json.loads(json_candidate)
        return json_candidate  # Already valid JSON
    except json.JSONDecodeError as e:
        error_msg = str(e)
        logger.debug(f"JSON repair: Original error: {error_msg}")

        # Start with a copy of the original string
        repaired = json_candidate.strip()

        # Common repair operations based on typical LLM JSON errors

        # 1. Remove JavaScript comments first (before other operations)
        # Single line comments
        repaired = re.sub(r"//.*?(?=\n|$)", "", repaired)
        # Multi-line comments (simplified approach)
        repaired = re.sub(r"/\*.*?\*/", "", repaired, flags=re.DOTALL)

        # 2. Fix common escape sequence issues (especially from Gemini Flash)
        # Fix invalid escape sequences like \n, \t, \r in JSON strings
        # These need to be properly escaped as \\n, \\t, \\r
        repaired = re.sub(r"(?<!\\)\\([ntr])", r"\\\\\\1", repaired)
        # Fix invalid single quote escapes (\') - single quotes don't need escaping in JSON
        repaired = repaired.replace("\\'", "'")
        # Fix other common invalid escape sequences
        repaired = repaired.replace("\\a", "a")  # Alert character
        repaired = repaired.replace("\\v", "v")  # Vertical tab
        # Null character (if not part of unicode)
        repaired = repaired.replace("\\0", "0")
        # Fix unescaped backslashes that aren't part of valid escape sequences
        # Valid JSON escape sequences: \" \\ \/ \b \f \n \r \t \uXXXX
        repaired = re.sub(r'(?<!\\)\\(?!["\\/bfnrtu])', r"\\\\", repaired)

        # 3. Replace backticks with double quotes (before other quote operations)
        repaired = repaired.replace("`", '"')

        # 4. Replace JavaScript-style single quotes with double quotes
        # Be more careful about this - only replace single quotes that are clearly
        # being used as string delimiters, not apostrophes within strings
        # First, handle property values that use single quotes
        repaired = re.sub(r":\s*'([^']*)'", r': "\1"', repaired)
        # Then handle property names that use single quotes
        repaired = re.sub(r"'([^']*)':", r'"\1":', repaired)
        # Handle array elements with single quotes
        repaired = re.sub(r"\[\s*'([^']*)'", r'["\1"', repaired)
        repaired = re.sub(r",\s*'([^']*)'", r', "\1"', repaired)

        # 5. Add quotes to unquoted property names
        # This regex looks for object keys that aren't quoted
        # Match after { or , followed by optional whitespace, then unquoted identifier, then :
        repaired = re.sub(
            r"([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:", r'\1"\2":', repaired
        )

        # 6. Remove trailing commas (common in arrays and objects)
        repaired = re.sub(r",\s*([\]\}])", r"\1", repaired)

        # Try to parse the repaired JSON
        try:
            json.loads(repaired)
            logger.info("Successfully repaired malformed JSON")
            return repaired
        except json.JSONDecodeError as e2:
            logger.debug(f"JSON repair failed: {e2}")
            return None


def extract_verification_json_from_llm_response(
    llm_output_str: str,
    *,  # Make subsequent arguments keyword-only
    use_fallback_raw_json: bool = True,
    use_brace_extraction: bool = True,
    use_markdown_extraction: bool = True,
    use_json_repair: bool = True,
) -> str | None:
    """Extracts a JSON string candidate from LLM output.

    Tries multiple strategies to extract valid JSON:
    1. Markdown code blocks (standard and indented)
    2. Direct parsing of the whole string
    3. Smart brace extraction with multiple sub-strategies
    4. JSON schema repair for common LLM output errors

    Args:
        llm_output_str: The raw string output from the LLM.
        use_fallback_raw_json: If true, attempt to parse the whole string as JSON.
        use_brace_extraction: If true, attempt to extract content with balanced braces.
        use_markdown_extraction: If true, attempt to extract JSON from markdown fences.
        use_json_repair: If true, attempt to repair common JSON schema errors.

    Returns:
        The extracted JSON string if found, otherwise None.

    Raises:
        ResponseParsingError: If input exceeds size limits.
    """
    if not llm_output_str:
        logger.warning("Empty string provided to JSON extractor")
        return None

    # Prevent resource exhaustion attacks
    if len(llm_output_str.encode("utf-8")) > MAX_INPUT_SIZE_BYTES:
        raise ResponseParsingError(
            f"Input size exceeds maximum allowed size of {MAX_INPUT_SIZE_BYTES} bytes"
        )

    # Strategy 1: Extract from markdown code blocks
    if use_markdown_extraction:
        extracted_json = _try_extract_markdown_json(llm_output_str)
        if extracted_json:
            return extracted_json

    # Strategy 2: Try to parse the whole string directly
    if use_fallback_raw_json:
        extracted_json = _try_extract_raw_json(llm_output_str)
        if extracted_json:
            return extracted_json

    # Strategy 3: Smart brace extraction with multiple methods
    if use_brace_extraction:
        extracted_json = _try_extract_brace_json(llm_output_str)
        if extracted_json:
            return extracted_json

    # Strategy 4: Attempt to repair common JSON schema errors
    if use_json_repair:
        repaired_json = _try_extract_json_schema(llm_output_str)
        if repaired_json:
            logger.info("Successfully extracted JSON using schema repair")
            return repaired_json

    logger.debug("No JSON content extracted by any method in helper.")
    return None


def parse_verification_items(
    verification_data: dict[str, Any],
    *,  # Make subsequent arguments keyword-only
    partial_success: bool = True,
    max_repair_attempts: int = MAX_REPAIR_ATTEMPTS,
) -> list[VerificationItem]:
    """Parses the 'verifications' list from extracted JSON data with robust error handling.

    Args:
        verification_data: The dictionary parsed from the JSON string,
                           expected to have a 'verifications' key.
        partial_success: If True, return successfully parsed items even if some fail.
                        If False, raise an error if any item fails validation.
        max_repair_attempts: Maximum number of attempts to repair malformed items.

    Returns:
        A list of VerificationItem objects.

    Raises:
        ResponseParsingError: If validation fails or structure is incorrect.
    """
    # Validate basic structure
    if not isinstance(verification_data, dict):
        logger.error(f"Expected dictionary, got {type(verification_data).__name__}")
        raise ResponseParsingError(
            f"Invalid data structure: expected dictionary, got {type(verification_data).__name__}"
        )
    # Check for verifications key
    if "verifications" not in verification_data:
        # Try to find the verifications key with different casing
        for key in verification_data.keys():
            if key.lower() == "verifications":
                logger.warning(f"Found 'verifications' with different casing: '{key}'")
                verification_data["verifications"] = verification_data[key]
                break
        else:  # No break occurred, key not found
            logger.error("'verifications' key missing from data structure")
            raise ResponseParsingError(
                "Invalid JSON structure: 'verifications' key missing."
            )

    # Ensure verifications is a list
    verifications = verification_data["verifications"]
    if not isinstance(verifications, list):
        # Try to handle the case where it might be a string representation of a list
        if (
            isinstance(verifications, str)
            and verifications.strip().startswith("[")
            and verifications.strip().endswith("]")
        ):
            try:
                verifications = json.loads(verifications)
                logger.warning(
                    "Converted string representation of verifications list to actual list"
                )
            except json.JSONDecodeError:
                pass

        if not isinstance(verifications, list):
            logger.error(
                f"'verifications' must be a list, got {type(verifications).__name__}"
            )
            raise ResponseParsingError(
                f"Invalid JSON structure: 'verifications' must be a list, got {type(verifications).__name__}."
            )

    # Parse verification items
    parsed_items: list[VerificationItem] = []
    # Store (index, item_data, exception)
    parsing_errors: list[tuple[int, dict, Exception]] = []

    for i, item_data in enumerate(verifications):
        if not isinstance(item_data, dict):
            logger.warning(
                f"Skipping item at index {i}: Expected a dictionary, got {type(item_data).__name__}."
            )
            continue

        # Try to parse the item
        for attempt in range(max_repair_attempts + 1):  # +1 for initial attempt
            try:
                # Apply repair if we have field name issues, citation field issues, or if this is a retry after validation failure
                has_field_issues = any(
                    field in item_data
                    for field in SCORE_FIELD_VARIANTS + TYPE_FIELD_VARIANTS
                )
                has_citation_issues = isinstance(
                    item_data.get("citation", {}), dict
                ) and any(
                    field in item_data.get("citation", {})
                    for field in [
                        "file",
                        "pageNumber",
                        "pageNum",
                        "url",
                        "boundingBox",
                        "box",
                        "coords",
                        "coordinates",
                    ]
                )
                should_repair = attempt > 0 or has_field_issues or has_citation_issues

                if should_repair:
                    item_data = _repair_verification_item(item_data, max(attempt, 1))

                # Pydantic v2 performs validation during instantiation
                verification_item = VerificationItem(**item_data)
                parsed_items.append(verification_item)

                if attempt > 0:
                    logger.info(
                        f"Successfully repaired verification item at index {i} on attempt {attempt}"
                    )
                break

            except Exception as e:
                if attempt == max_repair_attempts:
                    logger.error(
                        f"Validation failed for verification item at index {i} after {attempt} repair attempts: {e}"
                    )
                    logger.debug(f"Failed item data: {item_data}")
                    parsing_errors.append((i, item_data, e))
                else:
                    logger.debug(
                        f"Repair attempt {attempt + 1} needed for item at index {i}: {e}"
                    )

    # Handle the case where no items parsed successfully
    if not parsed_items and verifications:
        error_details = "\n".join(
            [f"Item {i}: {str(e)}" for i, _, e in parsing_errors[:5]]
        )
        if len(parsing_errors) > 5:
            error_details += f"\n... and {len(parsing_errors) - 5} more errors"

        error_message = f"Failed to parse any verification items from the list. First errors:\n{error_details}"
        logger.error(error_message)
        raise ResponseParsingError(error_message)

    # Handle partial success case
    if parsing_errors and not partial_success:
        error_message = f"Validation failed for {len(parsing_errors)} out of {len(verifications)} verification items."
        logger.error(error_message)
        raise ResponseParsingError(error_message)

    if not verifications:
        logger.warning("The 'verifications' list in the JSON response was empty.")
        # Returning empty list might be valid if LLM found no verifications needed

    if parsing_errors:
        logger.warning(
            f"Partially parsed verification items: {len(parsed_items)} succeeded, {len(parsing_errors)} failed"
        )
    else:
        logger.info(f"Successfully parsed all {len(parsed_items)} verification items.")

    return parsed_items


def _repair_verification_item(
    item_data: dict[str, Any], attempt: int
) -> dict[str, Any]:
    """Attempts to repair a malformed verification item.

    Applies different repair strategies based on the attempt number.

    Args:
        item_data: The original verification item data.
        attempt: The current repair attempt number (1-based).

    Returns:
        A repaired copy of the verification item data.
    """
    repaired = item_data.copy()  # Don't modify the original

    # First attempt: fix common field naming issues
    if attempt == 1:
        # Map common field name variations to expected names
        field_name_map = {
            # Common name variations for assessment score
            **{variant: "assessment_score" for variant in SCORE_FIELD_VARIANTS},
            # Common name variations for verification type
            **{variant: "verification_type" for variant in TYPE_FIELD_VARIANTS},
        }

        # Replace field names
        for old_name, new_name in field_name_map.items():
            if old_name in repaired and new_name not in repaired:
                repaired[new_name] = repaired.pop(old_name)
            elif old_name in repaired and new_name in repaired:
                # Resolve field name conflicts by preferring canonical field names
                repaired.pop(old_name)

        # Handle assessment_score type issues
        if "assessment_score" in repaired:
            score = repaired["assessment_score"]

            # Convert string representations to float or None
            if isinstance(score, str):
                score_lower = score.lower()
                if score_lower in ("n/a", "na", "null", "none"):
                    repaired["assessment_score"] = None
                elif score_lower in ("verified", "true", "yes", "pass", "1", "1.0"):
                    repaired["assessment_score"] = 1.0
                elif score_lower in ("issue", "false", "no", "fail", "0", "0.0"):
                    repaired["assessment_score"] = 0.0
                else:
                    try:
                        converted_score = float(score)
                        # Ensure assessment scores stay within valid range [0.0, 1.0]
                        repaired["assessment_score"] = max(
                            0.0, min(1.0, converted_score)
                        )
                    except ValueError:
                        # Leave as is, will be caught by validation
                        pass
            elif isinstance(score, (int, float)):
                # Ensure numeric assessment scores stay within valid range [0.0, 1.0]
                # Also handle special float values
                if not (
                    isinstance(score, (int, float))
                    and not (
                        isinstance(score, float)
                        and (math.isnan(score) or math.isinf(score))
                    )
                ):
                    logger.warning(f"Invalid assessment score (NaN/Infinity): {score}")
                    repaired["assessment_score"] = None
                else:
                    repaired["assessment_score"] = max(0.0, min(1.0, float(score)))

        # Handle citation field issues
        if "citation" in repaired and repaired["citation"] is not None:
            citation = repaired["citation"]

            # If citation is a string, assume it's the excerpt
            if isinstance(citation, str):
                repaired["citation"] = {"excerpt": citation}
            elif isinstance(citation, dict):
                # Fix common field naming issues in citation
                citation_field_map = {
                    "file": "filename",
                    "source": "filename",
                    "pageNumber": "page",
                    "pageNum": "page",
                    "url": "presigned_url",
                    "boundingBox": "bounding_box",
                    "box": "bounding_box",
                    "coords": "bounding_box",
                    "coordinates": "bounding_box",
                }

                for old_name, new_name in citation_field_map.items():
                    if old_name in citation and new_name not in citation:
                        citation[new_name] = citation.pop(old_name)

                # Convert integer page numbers to strings (required by schema)
                if "page" in citation and isinstance(citation["page"], int):
                    citation["page"] = str(citation["page"])

                # Handle bounding_box type issues
                if "bounding_box" in citation and citation["bounding_box"] is not None:
                    bbox = citation["bounding_box"]

                    # Convert string representation to list
                    if isinstance(bbox, str):
                        # Try to parse the string as JSON array
                        try:
                            bbox_list = json.loads(bbox)
                            if isinstance(bbox_list, list):
                                citation["bounding_box"] = bbox_list
                        except json.JSONDecodeError:
                            # Try to parse string like "0.1, 0.2, 0.3, 0.4"
                            try:
                                bbox_list = [float(x.strip()) for x in bbox.split(",")]
                                if len(bbox_list) == REQUIRED_BOUNDING_BOX_COORDINATES:
                                    citation["bounding_box"] = bbox_list
                            except ValueError:
                                pass

                    # Validate bounding box format and coordinates
                    if isinstance(citation.get("bounding_box"), list):
                        bbox_coords = citation["bounding_box"]
                        if len(
                            bbox_coords
                        ) == REQUIRED_BOUNDING_BOX_COORDINATES and all(
                            isinstance(coord, (int, float))
                            and BOUNDING_BOX_COORDINATE_MIN
                            <= coord
                            <= BOUNDING_BOX_COORDINATE_MAX
                            for coord in bbox_coords
                        ):
                            # Valid bounding box, keep it
                            pass
                        else:
                            # Invalid bounding box, remove it
                            logger.warning(
                                f"Invalid bounding box format: {bbox_coords}"
                            )
                            citation.pop("bounding_box", None)

                # --- BACKWARDS COMPATIBILITY LOGIC ---
                # Populate old fields based on new fields for backwards compatibility

                # Populate deprecated 'filename' field from document.filename
                if "document" in citation and citation["document"] is not None:
                    if (
                        isinstance(citation["document"], dict)
                        and "filename" in citation["document"]
                    ):
                        citation["filename"] = citation["document"]["filename"]

                # Populate deprecated 'page' string field from 'pages' array
                if "pages" in citation and citation["pages"] is not None:
                    if (
                        isinstance(citation["pages"], list)
                        and len(citation["pages"]) > 0
                    ):
                        # Convert first page number to string for backwards compatibility
                        citation["page"] = str(citation["pages"][0])

                # Ensure deprecated presigned_url field exists for backwards compatibility
                if "presigned_url" not in citation:
                    citation["presigned_url"] = None

                # Ensure excerpt exists by promoting alternative fields
                if "excerpt" not in citation:
                    # Try to find a field that might contain the excerpt
                    for field in EXCERPT_FIELD_VARIANTS:
                        if field in citation:
                            citation["excerpt"] = citation.pop(field)
                            break
                else:
                    # Clean up alternative excerpt fields to avoid data duplication
                    for field in EXCERPT_FIELD_VARIANTS:
                        if field in citation:
                            citation.pop(field)

                repaired["citation"] = citation

        # Handle verification_type issues
        if "verification_type" in repaired:
            vtype = repaired["verification_type"]
            if isinstance(vtype, str):
                vtype_lower = vtype.lower()
                valid_types = {"policy", "claim", "general"}

                # Check for common variations
                type_map = {
                    "pol": "policy",
                    "policy-related": "policy",
                    "clm": "claim",
                    "claims": "claim",
                    "claim-related": "claim",
                    "gen": "general",
                    "general-info": "general",
                    "info": "general",
                    "other": "general",
                }

                if vtype_lower in type_map:
                    repaired["verification_type"] = type_map[vtype_lower]
                elif vtype_lower not in valid_types:
                    # Default to general for invalid types
                    repaired["verification_type"] = "general"

    # Second attempt: more aggressive repairs
    elif attempt == 2:
        # If we're still missing required fields, try to derive them
        required_fields = {"name", "summary"}
        missing_fields = required_fields - set(repaired.keys())

        for field in missing_fields:
            if field == "name":
                # Try to derive name from summary or other fields
                if "summary" in repaired:
                    summary = repaired["summary"]
                    # Use first sentence and add ellipsis if there are more sentences
                    sentences = summary.split(".")
                    first_sentence = sentences[0].strip()
                    # There are more sentences
                    if len(sentences) > 1 and sentences[1].strip():
                        repaired["name"] = first_sentence + "..."
                    else:
                        repaired["name"] = first_sentence
                else:
                    # Create a generic name
                    repaired["name"] = f"Verification Item {attempt}"

            if field == "summary":
                # Try to derive summary from name or other content
                if "name" in repaired:
                    repaired["summary"] = repaired["name"]
                else:
                    # Create a generic summary
                    repaired["summary"] = "No detailed information available."

    # --- Repair pages/page/pageNumber ---
    # Handle both new 'pages' array and legacy 'page' string fields for backwards compatibility
    pages_value = None
    pages_field_found = None

    # Check for 'pages' first (new field), then legacy aliases
    if "pages" in repaired.get("citation", {}):
        pages_value = repaired["citation"]["pages"]
        pages_field_found = "pages"
    elif "page" in repaired.get("citation", {}):
        pages_value = repaired["citation"]["page"]
        pages_field_found = "page"
    else:
        for alias in ["pageNumber", "page_number", "pageNo"]:
            if alias in repaired.get("citation", {}):
                pages_value = repaired["citation"][alias]
                pages_field_found = alias
                break

    if pages_field_found:
        # Convert legacy field names to 'pages' for processing
        if pages_field_found != "pages":
            pages_value = repaired["citation"].pop(pages_field_found)

        # Convert to list[int] for the 'pages' field
        try:
            # Simplified conversion logic for repair path, assuming basic cases
            if pages_value is None:
                repaired["citation"]["pages"] = None
            elif isinstance(pages_value, int):
                repaired["citation"]["pages"] = [pages_value]
            elif isinstance(pages_value, list):
                # Validate all elements are integers
                processed_list = []
                for item in pages_value:
                    if isinstance(item, int):
                        processed_list.append(item)
                    elif isinstance(item, str):
                        processed_list.append(int(item))
                    else:
                        raise ValueError(f"Invalid list item: {item}")
                repaired["citation"]["pages"] = sorted(list(set(processed_list)))
            elif isinstance(pages_value, str):
                val_str = pages_value.strip()
                if not val_str:  # empty string
                    repaired["citation"]["pages"] = None
                elif "," in val_str:
                    parts = [p.strip() for p in val_str.split(",")]
                    repaired["citation"]["pages"] = sorted(
                        list(set([int(p) for p in parts if p]))
                    )
                elif re.fullmatch(r"(\d+)\s*-\s*(\d+)", val_str):
                    match = re.fullmatch(r"(\d+)\s*-\s*(\d+)", val_str)
                    if match:  # Add null check for mypy
                        start = int(match.group(1))
                        end = int(match.group(2))
                        if start > end:
                            raise ValueError("Page range start > end")
                        # Convert range to full list (e.g., "5-7" -> [5, 6, 7])
                        repaired["citation"]["pages"] = list(range(start, end + 1))
                else:
                    repaired["citation"]["pages"] = [int(val_str)]

            # Now populate backwards compatibility 'page' field from 'pages'
            if (
                repaired["citation"]["pages"] is not None
                and len(repaired["citation"]["pages"]) > 0
            ):
                repaired["citation"]["page"] = str(repaired["citation"]["pages"][0])
            else:
                repaired["citation"]["page"] = None

            logger.debug(
                f"Repaired citation.pages from '{pages_value}' to '{repaired['citation']['pages']}' and set page='{repaired['citation']['page']}'"
            )

        except ValueError:
            logger.warning(
                f"Could not convert page value '{pages_value}' to list[int] during repair. Leaving as is for Pydantic to handle."
            )
            # If conversion fails, leave original value and let Pydantic handle it
            if pages_field_found == "pages":
                repaired["citation"]["pages"] = pages_value
            else:
                # Put it back in original field for Pydantic to process
                repaired["citation"][pages_field_found] = pages_value

    return repaired


def _attempt_parse_and_validate(
    json_str: str, strategy_name: str
) -> CoverageDeterminationResponse:
    """Attempts to parse a JSON string and validate it as CoverageDeterminationResponse."""
    try:
        logger.debug(f"Strategy '{strategy_name}': Attempting json.loads().")
        data_dict = json.loads(json_str)
        logger.debug(f"Strategy '{strategy_name}': json.loads() successful.")
    except json.JSONDecodeError as jde:
        logger.warning(
            f"Strategy '{strategy_name}': Failed to decode JSON: {jde}", exc_info=True
        )
        raise ResponseParsingError(
            f"Strategy '{strategy_name}': Failed to decode JSON"
        ) from jde

    try:
        logger.debug(
            f"Strategy '{strategy_name}': Attempting Pydantic validation for CoverageDeterminationResponse."
        )
        response = CoverageDeterminationResponse(**data_dict)
        logger.info(
            f"Strategy '{strategy_name}': Successfully parsed and validated into CoverageDeterminationResponse."
        )
        return response
    except ValidationError as ve:
        logger.warning(
            "Strategy '{}': Pydantic validation failed: {}",
            strategy_name,
            str(ve),
            exc_info=True,
        )
        raise ResponseParsingError(
            f"Strategy '{strategy_name}': Pydantic validation failed"
        ) from ve


def _create_default_response() -> CoverageDeterminationResponse:
    """Create a minimal valid CoverageDeterminationResponse for fallback use.

    This is a last resort when parsing completely fails but we need to return something.

    Returns:
        A basic CoverageDeterminationResponse with an error verification item.
    """

    error_item = VerificationItem(
        name="Response Parsing Error",
        assessment_score=None,  # N/A since we couldn't process the response
        summary="The LLM response could not be parsed into a valid CoverageDeterminationResponse format. "
        "This is a system-generated fallback response.",
        verification_type="general",
        citation=None,
        status="Undefined",
    )

    return CoverageDeterminationResponse(verifications=[error_item])


def parse_coverage_determination_response(
    llm_output_str: str,
    *,  # Make subsequent arguments keyword-only
    partial_success: bool = True,
    # Flag that explicitly marks using the fallback is UNSAFE for production
    unsafe_allow_fallback: bool = False,
) -> CoverageDeterminationResponse:
    """Parses the LLM output string to a CoverageDeterminationResponse object.

    Args:
        llm_output_str: The raw string output from the LLM.
        partial_success: If True, attempts to parse individual VerificationItems
                         even if the overall structure is invalid.
        unsafe_allow_fallback: If True, returns a default error response on complete
                           parsing failure instead of raising an error. This is
                           intended only for non-critical/demo scenarios.  In
                           production flows this MUST remain False so that
                           data issues are surfaced via ResponseParsingError.

    Returns:
        The parsed CoverageDeterminationResponse object.

    Raises:
        ResponseParsingError: If parsing fails and unsafe_allow_fallback is False.
    """
    logger.debug(f"Raw LLM output for parsing:\n{llm_output_str}")  # DEBUG LOGGING
    cleaned_llm_output_str = extract_verification_json_from_llm_response(llm_output_str)
    # DEBUG LOGGING
    logger.debug(f"Cleaned LLM output for JSON parsing:\n{cleaned_llm_output_str}")

    try:
        # Check if cleaned string is None, empty or whitespace
        if not cleaned_llm_output_str or not cleaned_llm_output_str.strip():
            logger.error("Cleaned LLM output is empty. Cannot parse JSON.")
            if unsafe_allow_fallback:
                return CoverageDeterminationResponse(
                    verifications=[
                        VerificationItem(
                            name="Response Parsing Error",
                            assessment_score=0.0,
                            summary="LLM output was empty or did not contain valid JSON content after cleaning.",
                            verification_type="general",
                            citation=None,
                            status="Undefined",
                        )
                    ]
                )
            raise ResponseParsingError(
                "LLM output was empty or did not contain valid JSON content after cleaning."
            )

        # Parse JSON manually to use our repair functionality
        try:
            data_dict = json.loads(cleaned_llm_output_str)
        except json.JSONDecodeError as jde:
            logger.error(f"Failed to decode JSON: {jde}")
            # NEW: attempt to repair the extracted JSON using the same schema repair logic
            data_dict = None  # ensure variable is defined for all control paths
            repaired_json_str = _try_extract_json_schema(cleaned_llm_output_str)
            if repaired_json_str is not None:
                logger.info("Attempting to parse JSON after repair")
                try:
                    data_dict = json.loads(repaired_json_str)
                    cleaned_llm_output_str = (
                        repaired_json_str  # For downstream debugging if needed
                    )
                except json.JSONDecodeError as jde_repair:
                    logger.error(f"JSON repair attempt failed: {jde_repair}")
                    data_dict = None  # explicit for clarity
                    # fall through to existing error handling below
                else:
                    logger.info(
                        "JSON repair successful – continuing with repaired data"
                    )
            if data_dict is None:
                if unsafe_allow_fallback:
                    return CoverageDeterminationResponse(
                        verifications=[
                            VerificationItem(
                                name="Response Parsing Error",
                                assessment_score=0.0,
                                summary=f"Failed to decode JSON: {jde}",
                                verification_type="general",
                                citation=None,
                                status="Undefined",
                            )
                        ]
                    )
                raise ResponseParsingError(f"Failed to decode JSON: {jde}") from jde

        # Use our repair-enabled verification items parser
        verification_items = parse_verification_items(
            data_dict, partial_success=partial_success
        )

        return CoverageDeterminationResponse(verifications=verification_items)

    except ResponseParsingError as e:
        logger.error(f"Error parsing LLM output: {e}")
        if unsafe_allow_fallback:
            return _create_default_response()
        raise e
    except Exception as e:
        logger.error(f"Unexpected error parsing LLM output: {e}")
        if unsafe_allow_fallback:
            return _create_default_response()
        raise ResponseParsingError(f"Unexpected error: {e}") from e


def _create_default_response_with_error(
    error_summary: str,
) -> CoverageDeterminationResponse:
    """Helper to create a default response indicating a parsing error."""
    logger.error(f"Creating default error response: {error_summary}")
    return CoverageDeterminationResponse(
        verifications=[
            VerificationItem(
                name="Response Parsing Error",
                assessment_score=None,
                summary=f"Failed to parse the LLM's response. Error: {error_summary}",
                citation=None,
                verification_type="general",
            )
        ]
    )
