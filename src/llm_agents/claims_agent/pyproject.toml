[project]
name = "claims-agent"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "injector>=0.21.0", # Dependency injection container
    "fastapi-injector>=0.5.0", # FastAPI integration for injector
    "fastapi[standard]>=0.110.0",
    "uvicorn>=0.27.0",
    "pydantic>=2.6.0",
    "pydantic-settings>=2.2.0",
    "loguru>=0.7.2",
    "python-dotenv>=1.0.0",
    "openai>=1.13.0",
    "langchain>=0.3.24",
    "langchain-core>=0.3.56",
    "langchain-openai>=0.3.14",
    "langchain-text-splitters>=0.3.8",
    "google-generativeai>=0.4.0",
    "langchain-mcp-adapters==0.1.9",
    "httpx>=0.26.0",
    "sse-starlette>=1.8.2",
    "langchain-community>=0.0.19",
    "sentry-sdk[fastapi]>=2.28.0",
    "arize>=7.0.0",
    "arize-otel>=0.2.0",
    "openinference-instrumentation-langchain>=0.1",
    "openinference-semantic-conventions>=0.1.12",
    "opentelemetry-api>=1.15.0",
    "opentelemetry-sdk>=1.15.0",
    "pyyaml>=6.0.2",
    "supabase>=2.15.3",
    "temporalio>=1.13.0",
    "sqlalchemy~=2.0.31",
    "alembic~=1.13.1",
    "psycopg[binary,pool]~=3.1.19",
    "wsproto>=1.2.0",
    "python-multipart",
    "asyncpg>=0.29.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
only-include = ["src/claims_agent"]
sources = ["src"]

[dependency-groups]
dev = [
    "colorama>=0.4.6",
    "testcontainers[postgres]>=4.6.0",
    "pytest-asyncio>=0.23.0",
]

# Ruff ignores
[tool.ruff.lint.per-file-ignores]
"tests/**/*.py" = ["S101"]

[tool.poe.tasks]
start = { cmd = "uvicorn claims_agent.main:app --reload --port 8000" }
init-env = { shell = "cp -n .env.example .env" }
generate-openapi = { cmd = "python -m claims_agent.scripts.generate_openapi" }

[tool.poe.tasks.generate-orm]
envfile = ".env"
cmd = "uv run sqlacodegen ${SUPABASE_DB_URL} --outfile src/claims_agent/db/orm/generated.py"

[tool.pytest.ini_options]
asyncio_mode = "auto"
