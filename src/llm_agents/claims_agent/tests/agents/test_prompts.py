"""Unit tests for prompts module functions."""

from claims_agent.agents.prompts import get_language_consistency_refinement_prompt


class TestGetLanguageConsistencyRefinementPrompt:
    """Tests for get_language_consistency_refinement_prompt function."""

    def test_basic_prompt_generation(self) -> None:
        """Test basic prompt generation with sample inputs."""
        initial_coverage_notes = (
            '{"name": "Policy Check", "summary": "Check policy status"}'
        )
        previous_refined_notes = """
**Coverage Note (Updated: 2024-01-15 10:30:45 UTC)**
```json
{"name": "Policy Status Verification", "summary": "Verified policy is active"}
```
"""

        result = get_language_consistency_refinement_prompt(
            initial_coverage_notes=initial_coverage_notes,
            previous_refined_notes=previous_refined_notes,
        )

        # Check that the prompt contains the input data
        assert initial_coverage_notes in result
        assert previous_refined_notes in result

        # Check that key sections are present
        assert "## Initial Coverage Notes:" in result
        assert "## Previously Human-Refined Coverage Notes for Reference:" in result
        assert "## Task:" in result

    def test_empty_initial_notes(self) -> None:
        """Test prompt generation with empty initial notes."""
        initial_coverage_notes = ""
        previous_refined_notes = "Some previous notes"

        result = get_language_consistency_refinement_prompt(
            initial_coverage_notes=initial_coverage_notes,
            previous_refined_notes=previous_refined_notes,
        )

        # Should still contain the empty string
        assert "## Initial Coverage Notes:\n" in result
        assert previous_refined_notes in result

    def test_empty_previous_notes(self) -> None:
        """Test prompt generation with empty previous refined notes."""
        initial_coverage_notes = '{"name": "Test", "summary": "Test summary"}'
        previous_refined_notes = ""

        result = get_language_consistency_refinement_prompt(
            initial_coverage_notes=initial_coverage_notes,
            previous_refined_notes=previous_refined_notes,
        )

        # Should still contain the empty string
        assert initial_coverage_notes in result
        assert "## Previously Human-Refined Coverage Notes for Reference:\n" in result

    def test_complex_json_initial_notes(self) -> None:
        """Test prompt generation with complex JSON in initial notes."""
        initial_coverage_notes = """{
    "name": "Policy Active Status Check",
    "summary": "Verified that the policy was active on the date of incident",
    "assessment_score": 0.95,
    "citation": {
        "excerpt": "This policy is in effect from 2024-01-01 to 2024-12-31",
        "document": {
            "filename": "policy_doc.pdf",
            "page": 2
        }
    },
    "verification_type": "policy"
}"""
        previous_refined_notes = "Previous examples here"

        result = get_language_consistency_refinement_prompt(
            initial_coverage_notes=initial_coverage_notes,
            previous_refined_notes=previous_refined_notes,
        )

        # Should preserve the formatting
        assert "Policy Active Status Check" in result
        assert "assessment_score" in result
        assert "citation" in result

    def test_multiple_previous_notes(self) -> None:
        """Test prompt generation with multiple previous refined notes."""
        initial_coverage_notes = '{"name": "Test Check", "summary": "Test"}'
        previous_refined_notes = """
**Coverage Note (Updated: 2024-01-15 10:30:45 UTC)**
```json
{"name": "Policy Status Verification", "summary": "Verified policy is active"}
```

**Coverage Note (Updated: 2024-01-16 14:22:10 UTC)**
```json
{"name": "Driver Coverage Check", "summary": "Confirmed driver is listed"}
```
"""

        result = get_language_consistency_refinement_prompt(
            initial_coverage_notes=initial_coverage_notes,
            previous_refined_notes=previous_refined_notes,
        )

        # Should contain both previous notes
        assert "Policy Status Verification" in result
        assert "Driver Coverage Check" in result
        assert "2024-01-15 10:30:45 UTC" in result
        assert "2024-01-16 14:22:10 UTC" in result

    def test_special_characters_handling(self) -> None:
        """Test that special characters in input are handled properly."""
        initial_coverage_notes = '{"name": "Test with \\"quotes\\" and \\n newlines", "summary": "Special chars: !@#$%^&*()"}'
        previous_refined_notes = 'Previous notes with "quotes" and special chars: <>&'

        result = get_language_consistency_refinement_prompt(
            initial_coverage_notes=initial_coverage_notes,
            previous_refined_notes=previous_refined_notes,
        )

        # Should contain the special characters
        assert '\\"quotes\\"' in result
        assert "!@#$%^&*()" in result
        assert "<>&" in result

    def test_prompt_structure_consistency(self) -> None:
        """Test that the prompt structure is consistent and contains all required sections."""
        initial_coverage_notes = '{"test": "data"}'
        previous_refined_notes = "Previous data"

        result = get_language_consistency_refinement_prompt(
            initial_coverage_notes=initial_coverage_notes,
            previous_refined_notes=previous_refined_notes,
        )

        # Check for required sections in expected order
        sections = [
            "You are reviewing coverage determination notes",
            "## Initial Coverage Notes:",
            "## Previously Human-Refined Coverage Notes for Reference:",
            "## Task:",
            "**Your Goal:** Populate a `CoverageDeterminationResponse` JSON object",
            "Your output MUST be a JSON object that STRICTLY ADHERES to the following Pydantic models",
        ]

        # Check that all sections exist and are in the right order
        last_index = -1
        for section in sections:
            current_index = result.find(section)
            assert current_index != -1, f"Section '{section}' not found in prompt"
            assert current_index > last_index, (
                f"Section '{section}' appears out of order"
            )
            last_index = current_index

    def test_instruction_content_completeness(self) -> None:
        """Test that all key instructions are present in the prompt."""
        initial_coverage_notes = '{"test": "data"}'
        previous_refined_notes = "Previous data"

        result = get_language_consistency_refinement_prompt(
            initial_coverage_notes=initial_coverage_notes,
            previous_refined_notes=previous_refined_notes,
        )

        # Check for key instruction content that's actually in the template
        key_instructions = [
            'Use the exact "name" and "summary" text from the human-refined note',
            'Carry forward the "status" field from the human-refined note',
            "If multiple matches exist, use the one with the latest timestamp",
            'Do not modify any field other than "name", "summary" and "status"',
            "assessment_score",
            "citation",
            "verification_type",
            "must remain exactly unchanged",
            'keep "status" as "Undefined"',
        ]

        for instruction in key_instructions:
            assert instruction in result, (
                f"Key instruction '{instruction}' missing from prompt"
            )
