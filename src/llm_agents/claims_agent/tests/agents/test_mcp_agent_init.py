"""Unit tests for the MCPAgent class initialization."""

from unittest.mock import <PERSON><PERSON><PERSON>

import pytest
from pydantic import SecretS<PERSON>
from pytest_mock import Mock<PERSON><PERSON><PERSON>

from claims_agent.agents.agent_executor_factory import AgentExecutorFactory
from claims_agent.agents.mcp_agent import MCPAgent
from claims_agent.config import Settings
from tests.agents.conftest import get_test_mcp_agent


class TestMCPAgentInit:
    """Tests for MCPAgent initialization."""

    def test_init_uses_provided_dependencies(
        self,
        mocker: MockFixture,
        mock_mcp_client_manager: MagicMock,
        mock_mcp_tool_service: MagicMock,
        mock_agent_executor_factory: MagicMock,
    ) -> None:
        """Test MCPAgent initialization with provided services."""

        agent = MCPAgent(
            mcp_client_manager=mock_mcp_client_manager,
            mcp_tool_service=mock_mcp_tool_service,
            agent_executor_factory=mock_agent_executor_factory,
        )

        assert agent.mcp_client_manager is mock_mcp_client_manager
        assert agent.mcp_tool_service is mock_mcp_tool_service
        assert agent.agent_executor_factory is mock_agent_executor_factory

    def test_init_with_custom_settings_via_factory(
        self,
        mocker: MockFixture,
        mock_mcp_client_manager: MagicMock,
        mock_mcp_tool_service: MagicMock,
    ) -> None:
        """Test MCPAgent can be configured with custom settings via its factory."""
        # This test now verifies that a custom-configured factory is used correctly,
        # which is the proper DI way to handle custom settings.
        custom_settings = Settings(
            ENVIRONMENT="testing",
            OPENAI_API_KEY=SecretStr("test-key"),
            LEGACY_OPENAI_MODEL_NAME="custom-model",
            MODEL_TEMPERATURE=0.99,
        )
        custom_factory = AgentExecutorFactory(settings_override=custom_settings)

        agent = MCPAgent(
            mcp_client_manager=mock_mcp_client_manager,
            mcp_tool_service=mock_mcp_tool_service,
            agent_executor_factory=custom_factory,
        )

        assert agent.agent_executor_factory is custom_factory
        # We can also check that the factory has the correct settings if needed,
        # for example by inspecting its properties if they are public.
        assert agent.agent_executor_factory.settings is custom_settings

    def test_init_logs_info_messages(
        self,
        caplog: pytest.LogCaptureFixture,
        mock_mcp_client_manager: MagicMock,
        mock_mcp_tool_service: MagicMock,
        mock_agent_executor_factory: MagicMock,
    ) -> None:
        """Test that MCPAgent initialization logs informational messages."""
        with caplog.at_level("INFO"):
            get_test_mcp_agent(
                mock_mcp_client_manager,
                mock_mcp_tool_service,
                mock_agent_executor_factory,
            )
        assert "MCPAgent using provided MCPClientManager instance." in caplog.text
        assert "MCPAgent using provided MCPToolService instance." in caplog.text
        assert "MCPAgent using provided AgentExecutorFactory instance." in caplog.text
