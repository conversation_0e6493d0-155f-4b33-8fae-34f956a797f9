from typing import Any, Dict, List, Type, TypeVar

import attr

from ..models.milliman_accurate_api_version import MillimanAccurateApiVersion
from ..models.milliman_supported_tsp import MillimanSupportedTSP
from ..models.milliman_trip import MillimanTrip
from ..models.milliman_vehicle_class import MillimanVehicleClass

T = TypeVar("T", bound="MillimanAccurateScoreRequest")


@attr.s(auto_attribs=True)
class MillimanAccurateScoreRequest:
    """
    Attributes:
        policy_id (str):
        fleet_id (str):
        vehicle_id (str):
        driver_id (str):
        vin (str):  Example: 1DAKSJALDMA22S090.
        vehicle_class (MillimanVehicleClass):
        tsp (MillimanSupportedTSP):
        accurate_version (MillimanAccurateApiVersion):
        policy_state (str):
        trips (List[MillimanTrip]):
    """

    policy_id: str
    fleet_id: str
    vehicle_id: str
    driver_id: str
    vin: str
    vehicle_class: MillimanVehicleClass
    tsp: MillimanSupportedTSP
    accurate_version: MillimanAccurateApiVersion
    policy_state: str
    trips: List[MillimanTrip]
    additional_properties: Dict[str, Any] = attr.ib(init=False, factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        policy_id = self.policy_id
        fleet_id = self.fleet_id
        vehicle_id = self.vehicle_id
        driver_id = self.driver_id
        vin = self.vin
        vehicle_class = self.vehicle_class.value

        tsp = self.tsp.value

        accurate_version = self.accurate_version.value

        policy_state = self.policy_state
        trips = []
        for trips_item_data in self.trips:
            trips_item = trips_item_data.to_dict()

            trips.append(trips_item)

        field_dict: Dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update(
            {
                "policyId": policy_id,
                "fleetId": fleet_id,
                "vehicleId": vehicle_id,
                "driverId": driver_id,
                "vin": vin,
                "vehicleClass": vehicle_class,
                "tsp": tsp,
                "accurateVersion": accurate_version,
                "policyState": policy_state,
                "trips": trips,
            }
        )

        return field_dict

    @classmethod
    def from_dict(cls: Type[T], src_dict: Dict[str, Any]) -> T:
        d = src_dict.copy()
        policy_id = d.pop("policyId")

        fleet_id = d.pop("fleetId")

        vehicle_id = d.pop("vehicleId")

        driver_id = d.pop("driverId")

        vin = d.pop("vin")

        vehicle_class = MillimanVehicleClass(d.pop("vehicleClass"))

        tsp = MillimanSupportedTSP(d.pop("tsp"))

        accurate_version = MillimanAccurateApiVersion(d.pop("accurateVersion"))

        policy_state = d.pop("policyState")

        trips = []
        _trips = d.pop("trips")
        for trips_item_data in _trips:
            trips_item = MillimanTrip.from_dict(trips_item_data)

            trips.append(trips_item)

        milliman_accurate_score_request = cls(
            policy_id=policy_id,
            fleet_id=fleet_id,
            vehicle_id=vehicle_id,
            driver_id=driver_id,
            vin=vin,
            vehicle_class=vehicle_class,
            tsp=tsp,
            accurate_version=accurate_version,
            policy_state=policy_state,
            trips=trips,
        )

        milliman_accurate_score_request.additional_properties = d
        return milliman_accurate_score_request

    @property
    def additional_keys(self) -> List[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
