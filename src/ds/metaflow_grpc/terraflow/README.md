# Terraflow

Terraform-like infrastructure manager for metaflow pipelines.

Please see [relevant coda RFC/doc](https://coda.io/d/Data-Platform_djr9m0P9rOG/Flow-Deployments-Addendum_suX4F) to understand motivation, requirements and responsibilities of this program.

In this README, the command `terraflow` can be invoked as either `task terraflow` or  from the data science root directory.

For CLI help you can use (note the extra `--` after `terraflow` that is necessary)
```
task terraflow -- --help
```
This will list all available sub-commands and one-liner help of those.

For more detailed help regarding any sub-command use
```
task terraflow <subcommand> -- --help
```

### Remote State Management
Terraflow, like terraform, uses s3 as the backend for storing its state in a mostly non-configurable manner (unlike terraform).

Specifically the plan is persisted by serializing into python pickle file and storing at `terraflow/state.pkl` in the metaflow s3 bucket.
A backup is maintained at `terraflow/state.pkl.backup` should a manual fixup be ever required.

Terraflow has protections to ensure that multiple concurrent state read/writes (including deployments) do not occur, as well as
detect if the last operation resulted in program crash which can mean that the state is in inconsistent state and requires manual fixup.
Each terraflow process picks a random UUID in-memory and persists a "lock" file in s3 using that UUID as the key under `terraflow/.lock` prefix.
Thus, processes do not confuse between their locks. If a processes crashes midway of an operation the lock is left undeleted which can be used
to warn for inconsistent state.

## User Stories
We plan to grow this README with more user stories as we start using it. Feel free to contact @sarthak for any help meanwhile.

### Creating a new flow (or version) / Modifying existing flows
Add your flow source code in `flows/` directory.
Your flow must inherit from `NirvanaMetaflow` class.
Update the `flows.yaml` file in the root with your new
flow's schema. Please ensure no typos (we will soon
start auto-generating flows.yaml as well, meanwhile please
bear with this setup).

If you are just updating an existing flow source code, you
only need to modify `flows.yaml` if you made any change to
schema of your flow.

Not all schema changes to `flows.yaml` are permitted.
Specifically mostly backwards-compatible changes are allowed.
For example, you can not -
1. Resurrect a once-deleted flow
2. Add a new required parameter without bumping version number
3. Change parameter type on whim
4. etc.

Running `task terraflow -- diff` will tell you what changes
will terraflow perform on deployment, and will throw an
error if it detects a breaking change.
(Sometimes you may want to still go ahead with a breaking change,
if flow does not have any critical consumer for example, see the
section on [Forcing breaking changes](#forcing-breaking-changes))

Terraflow can typically detect if the source code of a flow
has changed. Such flows appear in `diff` and will be re-deployed
during deployment. However, terraflow can not detect all possible
source code changes. In particular changes to dependencies are
not tracked. So if you make a change to one of the dependencies
of the flow (whether in-house or external dependency), you will
have to manually tell the terraflow to re-deploy the flow.
This can be done using -
```
task terraflow -- taint <flow_name>/<flow_version>
```
You can omit `flow_version` -- it will taint all versions of that
flow. You can even omit `flow_name` it will taint all flows forcing
a full re-deployment. Once you are happy with the diff
that terraflow shows, you can commit your changes. Once your
changes are merged to main, you can run the Github Action on
the repo which will automatically use terraflow to deploy all your
affected flows (apply the diffs).

If you do end up making any actual schema change, you will also
need to re-generate proto definitions to update the gRPC server.
Note that without doing this your deployment will not succeed anyways.

Generating proto definitions is easy. You can just do
```shell
task terraflow -- protogen
task py-protogen
```
Check that gRPC server is able to boot by doing
```shell
task grpc
```

You are good to go at this point. Just ping @sarthak as the golang gRPC client
would also need to be updated.

### Forcing breaking changes
Sometimes, more so in the early days when we don't want to bother with
multiple flow versions, you want to suppress terraflow and do potentially
breaking schema changes to your flow. This can be done.
First, review diff with `--force`.
```shell
task terraflow -- diff --force
```
This will show you even breaking changes that terraflow, if forced,
would apply.

Then, at your own risk, you can *overwrite* terraflow's state.
```shell
task terraflow -- overwrite
```
This will overwrite the remote state with previous remote state + forced diff.
Note that overwriting does not lead to deployment.
To actually deploy the affected flows you have to now taint them.
```shell
task terraflow -- taint <...>
```
Proceed with committing your changes and running GHA.

Please also refer to the protogen instructions above.

### Inconsistent state
Perhaps due to a bug in program, or a system crash, you may ever get to
see an error message regarding inconsistent remote state / locks.
Please contact @sarthak. Raise a urgent PD if this happens during GHA as
that could indicate a sabotaged deployment.
