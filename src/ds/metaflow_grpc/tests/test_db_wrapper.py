import datetime
import unittest
import uuid

from metaflow_grpc.db_wrapper import *
from telemetry.sdk.trace import init_tracer_provider
from telemetry.api.trace import set_default_tracer
from telemetry.api.logging import get_logger

log = get_logger(__name__)


@unittest.skipUnless("DB_TEST" in os.environ.keys(), "This test requires connection to DB")
class TestDBWrapper(unittest.TestCase):
    @classmethod
    def setUpClass(cls) -> None:
        cls.wrapper = DbWrapper()
        cls.created_at = datetime.datetime(
            year=2022, month=7, day=1, hour=13, minute=43, second=22, microsecond=0, tzinfo=datetime.timezone.utc
        )
        init_tracer_provider("unit-test")
        set_default_tracer("unit-test")

    def _check_exists(self, id: uuid):
        with self.wrapper.pool.connection() as conn:
            with conn.cursor() as cur:
                cur.execute(f"SELECT * FROM {SfnExecution.table()} WHERE run_id = %s", (id,))
                self.assertEqual(1, cur.rowcount, f"inserted run {id} not found")

    def setUp(self) -> None:
        # clear database
        with self.wrapper.pool.connection() as conn:
            with conn.cursor() as cur:
                cur.execute(f"DELETE FROM {SfnExecution.table()}")

    def test_parallel_put(self):
        num_parallel = 10
        uids = [uuid.uuid4() for i in range(num_parallel)]
        exs = [SfnExecution(
            run_id=uid,
            metaflow_id=f"TestFlow/sfn-{uid}",
            sfn_execution_arn=f"sfn-{uid}",
            created_at=self.created_at,
        ) for uid in uids]

        def put_execution(ex: SfnExecution):
            try:
                self.wrapper.put_execution(ex)
            except Exception:
                log.exception("unable to put execution")

        from concurrent.futures import ThreadPoolExecutor
        with ThreadPoolExecutor(max_workers=num_parallel) as pool:
            pool.map(put_execution, exs)
            pool.shutdown(wait=True)
        for ex in exs:
            self._check_exists(ex.run_id)

    def test_put(self):
        id = uuid.uuid4()
        ex = SfnExecution(
            run_id=id,
            metaflow_id=f"TestFlow/sfn-{id}",
            sfn_execution_arn=f"sfn-{id}",
            created_at=self.created_at,
        )
        self.wrapper.put_execution(ex)
        self._check_exists(id)

    def test_get(self):
        executions = list()
        for i in range(100):
            id = uuid.uuid4()
            ex = SfnExecution(
                run_id=id,
                metaflow_id=f"TestFlow/sfn-{id}",
                sfn_execution_arn=f"sfn-{id}",
                created_at=self.created_at,
            )
            executions.append(ex)
            self.wrapper.put_execution(ex)

        from random import shuffle

        shuffle(executions)
        for e in executions:
            get_e = self.wrapper.get_execution(id=e.run_id)
            self.assertEqual(e, get_e, "serde error")
            get_e2 = self.wrapper.get_execution(metaflow_id=e.metaflow_id)
            self.assertEqual(e, get_e2, "serde error")
