import numpy as np
from skopt.space import Real
from skopt.space import Integer
from skopt import BayesSearchCV
from skopt.space import Categorical
from sklearn.linear_model import LinearRegression
from sklearn.linear_model import PoissonRegressor


def linear_regression(
    train_data: np.ndarray,
    train_target: np.ndarray,
    random_state: int,
    cross_validation_folds: int,
) -> tuple[LinearRegression, None]:
    """
    Linear regression model. This is a wrapper around sklearn's LinearRegression.
    """
    reg = LinearRegression(copy_X=True)
    reg.fit(train_data, train_target)
    return reg, None


def fixed_poisson_model(
    train_data: np.ndarray,
    train_target: np.ndarray,
    random_state: int,
    cross_validation_folds: int,
    sample_weight: np.ndarray = np.empty(0),
    alpha: float = 1e-12,
    max_iter: int = 1000,
    solver: str = "newton-cholesky",
) -> tuple[PoissonRegressor, None]:
    """
    Poisson regression model. This is a wrapper around skle<PERSON><PERSON>'s PoissonRegressor.
    """
    poisson = PoissonRegressor(
        alpha=alpha,
        fit_intercept=True,
        max_iter=max_iter,
        solver=solver,
    )

    if sample_weight.shape[0] == 0:
        poisson.fit(train_data, train_target)
    else:
        poisson.fit(train_data, train_target, sample_weight=sample_weight)
    return poisson, None


def poisson_model(
    train_data: np.ndarray,
    train_target: np.ndarray,
    random_state: int,
    cross_validation_folds: int,
    sample_weight: np.ndarray = None,
    scoring: str = 'neg_mean_squared_log_error',
    search_spaces: dict = None,
):
    search_poisson_estimator = PoissonRegressor()

    if search_spaces is None:
        space = {
            'alpha': Real(1e-12, 1e-3, prior='log-uniform'),
            'max_iter': Integer(100, 2000),
            'fit_intercept': Categorical([True]),
            'solver': Categorical(['newton-cholesky', 'lbfgs']),
        }
    else:
        space = search_spaces

    search = BayesSearchCV(
        estimator=search_poisson_estimator,
        cv=cross_validation_folds,
        search_spaces=space,
        random_state=random_state,
        scoring=scoring,
    )
    search.fit(train_data, train_target, sample_weight=sample_weight)
    return search.best_estimator_, None


def poisson_model_tuner(
    train_data: np.ndarray,
    train_target: np.ndarray,
    random_state: int,
    cross_validation_folds: int,
    sample_weight: np.ndarray = np.empty(0),
):
    search_poisson_estimator = PoissonRegressor()
    space = {
        'alpha': Real(1e-5, 1e-1, prior='log-uniform'),
        'max_iter': Integer(100, 3000),
        'fit_intercept': Categorical([True]),
        'solver': Categorical(['newton-cholesky', 'lbfgs']),
    }
    search = BayesSearchCV(
        estimator=search_poisson_estimator,
        cv=cross_validation_folds,
        search_spaces=space,
        random_state=random_state
    )
    if sample_weight.shape[0] == 0:
        sample_weight = None
    search.fit(train_data, train_target, sample_weight=sample_weight)
    return search
