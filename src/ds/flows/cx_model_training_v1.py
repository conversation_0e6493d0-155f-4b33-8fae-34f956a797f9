import copy
import numpy as np
import pandas as pd
import statsmodels.api as sm
import logging

from metaflow import card
from metaflow import retry
from metaflow import timeout
from metaflow import current
from metaflow.cards import Image
from metaflow.cards import Table
from metaflow.cards import Markdown
from metaflow import secrets


# import nirvana's meta flow base class
# this import should be above all other nirvana imports

from base_flow import NirvanaMetaflow
from base_flow import step

from sklearn.preprocessing import OneHotEncoder
from sklearn.metrics import brier_score_loss
from sklearn.preprocessing import QuantileTransformer


from model_training.cancellations_model_v1.config import CancellationDataConfig
from model_training.risk_score_v1.utils.snowflake import fetch_query_result
from utils.snowflake_utils import snowflake_secrets

from model_training.cancellations_model_v1.helpers import snowflake_query

# Import modules for data processing
from model_training.cancellations_model_v1.helpers import util_functions

log = logging.getLogger("metaflow")


class CancellationsModelTrainingFlow(NirvanaMetaflow):
    """
    This class contains the meta flow for training cancellation model v1
    """
    
    @secrets(sources=[snowflake_secrets()])
    @retry(times=3)
    @timeout(minutes=60)
    @card(type="blank")
    @step
    def start(self):
        # Data preprocessing steps
        base_train_data_query = snowflake_query.model_base_train_data

        base_data = fetch_query_result(
            warehouse=CancellationDataConfig.WAREHOUSE,
            database=CancellationDataConfig.DB,
            schema=CancellationDataConfig.SCHEMA,
            query=base_train_data_query,
        )

        base_processed = copy.copy(util_functions.missing_ind_convert_num(base_data))
        print("Base data shape :", base_data.shape)
        print("Base data sample records :", base_data.head())

        base_processed["POLICY_MIN_EFFECTIVE_DATE"] = pd.to_datetime(base_processed["POLICY_MIN_EFFECTIVE_DATE"])

        self.Train = base_processed[base_processed["POLICY_MIN_EFFECTIVE_DATE"] < CancellationDataConfig.TRAIN_END_DATE]

        self.OOT = base_processed[
            (base_processed["POLICY_MIN_EFFECTIVE_DATE"] >= CancellationDataConfig.TRAIN_END_DATE)
            & (base_processed["POLICY_MIN_EFFECTIVE_DATE"] < CancellationDataConfig.OOT_END_DATE)
        ]

        self.Val = base_processed[
            (base_processed["POLICY_MIN_EFFECTIVE_DATE"] >= CancellationDataConfig.OOT_END_DATE)
            & (base_processed["POLICY_MIN_EFFECTIVE_DATE"] <= CancellationDataConfig.VAL_END_DATE)
        ]

        print("Train data shape :", self.Train.shape)
        print("OOT data shape :", self.OOT.shape)
        print("Val data shape :", self.Val.shape)

        num_col, cat_col = util_functions.extract_num_cat_cols(util_functions.num_col, util_functions.cat_col)

        self.model_variables = num_col + cat_col

        X_train1 = self.Train[cat_col + num_col]
        self.y_train = self.Train["IS_CANCELLED"]
        X_test1 = self.OOT[cat_col + num_col]
        self.y_test = self.OOT["IS_CANCELLED"]
        X_val1 = self.Val[cat_col + num_col]
        self.y_val = self.Val["IS_CANCELLED"]

        print("X_train shape :", X_train1.shape)
        print("X_test shape :", X_test1.shape)
        print("X_val shape :", X_val1.shape)
        tab = self.y_train.value_counts()
        print("Trian data - target variable value counts", tab)
        dist_T = self.y_train.value_counts()
        dist_O = self.y_test.value_counts()
        dist_V = self.y_val.value_counts()

        print("self.Train sample:", X_train1.shape)
        print("No of Bads in self.Train:", dist_T[1])
        print("Bad Rate in self.Train:", round((dist_T[1] / X_train1.shape[0]) * 100, 2), "%")
        print("self.OOT sample", X_test1.shape)
        print("No of Bads in self.OOT:", dist_O[1])
        print("Bad Rate in self.OOT:", round((dist_O[1] / X_test1.shape[0]) * 100, 2), "%")
        print("Validation sample:", X_val1.shape)
        print("No of Bads in Validation:", dist_V[1])
        print("Bad Rate in Validation:", round((dist_V[1] / X_val1.shape[0]) * 100, 2), "%")

        # One-hot encoding
        self.encoder = OneHotEncoder(handle_unknown="ignore", sparse=False)
        # cols=cat_col, use_cat_names=True
        # Fit and transform on the training set

        self.X_train_encoded = self.encoder.fit_transform(X_train1[cat_col])
        # Combine transformed one-hot encoded categorical columns with numerical columns
        self.X_train_encoded = pd.concat(
            [
                pd.DataFrame(self.X_train_encoded, columns=self.encoder.get_feature_names_out(cat_col)).reset_index(
                    drop=True
                ),
                X_train1[num_col].reset_index(drop=True),
            ],
            axis=1,
        )

        self.X_test_encoded = self.encoder.transform(X_test1[cat_col])
        self.X_test_encoded = pd.concat(
            [
                pd.DataFrame(self.X_test_encoded, columns=self.encoder.get_feature_names_out(cat_col)).reset_index(
                    drop=True
                ),
                X_test1[num_col].reset_index(drop=True),
            ],
            axis=1,
        )

        self.X_val_encoded = self.encoder.transform(X_val1[cat_col])
        self.X_val_encoded = pd.concat(
            [
                pd.DataFrame(self.X_val_encoded, columns=self.encoder.get_feature_names_out(cat_col)).reset_index(
                    drop=True
                ),
                X_val1[num_col].reset_index(drop=True),
            ],
            axis=1,
        )

        print(f"Train encoded data shape {self.X_train_encoded.shape}")
        print(f"OOT encoded data shape {self.X_test_encoded.shape}")
        print(f"Val encoded data shape {self.X_val_encoded.shape}")

        self.model_encoded_variables = self.X_train_encoded.columns
        print("all encoded columns shape ", len(self.model_encoded_variables.to_list()))

        self.next(self.model_training)

    @retry(times=3)
    @timeout(minutes=60)
    @card(type="blank")
    @step
    def model_training(self):
        imbalance_ratio = sum(self.y_train == 0) / sum(self.y_train == 1)
        xgb_f = util_functions.xgboost_model(imbalance_ratio)
        self.xgboost_model = xgb_f.fit(
            self.X_train_encoded,
            self.y_train,
            eval_set=[
                (self.X_train_encoded, self.y_train),
                (self.X_test_encoded, self.y_test),
                (self.X_val_encoded, self.y_val),
            ],
            eval_metric=util_functions.gini_metrics,
            verbose=True,
        )

        # Make prediction
        self.xgboost_predict_val = self.xgboost_model.predict(self.X_val_encoded)
        self.xgboost_predict_test = self.xgboost_model.predict(self.X_test_encoded)
        self.xgboost_predict_train = self.xgboost_model.predict(self.X_train_encoded)

        # Get predicted probability
        self.xgboost_predict_val_prob = self.xgboost_model.predict_proba(self.X_val_encoded)[:, 1]
        self.xgboost_predict_test_prob = self.xgboost_model.predict_proba(self.X_test_encoded)[:, 1]
        self.xgboost_predict_train_prob = self.xgboost_model.predict_proba(self.X_train_encoded)[:, 1]
        self.xgboost_model.get_params()

        feature_importances_xgb = pd.DataFrame(
            (self.xgboost_model.feature_importances_) * 100, index=self.X_train_encoded.columns, columns=["importance"]
        ).sort_values("importance", ascending=False)
        current.card.append(Markdown("Trained Model feature importances"))
        current.card.append(Table.from_dataframe(feature_importances_xgb, truncate=False))

        self.next(self.interim_evaluation)

    @retry(times=3)
    @timeout(minutes=60)
    @card(type="blank")
    @step
    def interim_evaluation(self):
        # self.Val
        util_functions.evaluate_cohort(self.y_val, self.xgboost_predict_val_prob, self.xgboost_predict_val)

        # self.OOT
        util_functions.evaluate_cohort(self.y_test, self.xgboost_predict_test_prob, self.xgboost_predict_test)

        # self.Train
        util_functions.evaluate_cohort(self.y_train, self.xgboost_predict_train_prob, self.xgboost_predict_train)

        # Risk-rank ordering
        (
            self.Train,
            self.OOT,
            self.Val,
            self.bin_right_endpoints,
        ) = util_functions.calculate_predicted_probability_buckets(
            self.xgboost_predict_train_prob,
            self.y_train,
            self.xgboost_predict_test_prob,
            self.y_test,
            self.xgboost_predict_val_prob,
            self.y_val,
        )

        # Plot the percentage of canceled records for each predicted probability bucket
        fig = util_functions.plot_risk_rank_ordering(self.Train, "PREDICTED_PROBABILITY_BUCKET", "Train")
        current.card.append(Markdown("Train_data_cancellation_score_ordering "))
        current.card.append(Image.from_matplotlib(fig))

        fig = util_functions.plot_risk_rank_ordering(self.OOT, "PREDICTED_PROBABILITY_BUCKET", "OOT")
        current.card.append(Markdown("OOT_data_cancellation_score_ordering "))
        current.card.append(Image.from_matplotlib(fig))

        fig = util_functions.plot_risk_rank_ordering(self.Val, "PREDICTED_PROBABILITY_BUCKET", "Val")
        current.card.append(Markdown("Val_data_cancellation_score_ordering "))
        current.card.append(Image.from_matplotlib(fig))

        self.next(self.model_calibration)

    @retry(times=3)
    @timeout(minutes=60)
    @card(type="blank")
    @step
    def model_calibration(self):
        # Model calibration
        self.Train["LOGODDS_SCORE"] = np.log(
            self.Train["PREDICTED_PROBABILITY"] / (1 - self.Train["PREDICTED_PROBABILITY"])
        )
        self.OOT["LOGODDS_SCORE"] = np.log(self.OOT["PREDICTED_PROBABILITY"] / (1 - self.OOT["PREDICTED_PROBABILITY"]))
        self.Val["LOGODDS_SCORE"] = np.log(self.Val["PREDICTED_PROBABILITY"] / (1 - self.Val["PREDICTED_PROBABILITY"]))

        Train_score = brier_score_loss(self.Train["IS_CANCELLED"], self.Train["PREDICTED_PROBABILITY"], pos_label=1)
        print("Train - brier_score_loss", Train_score)
        OOT_score = brier_score_loss(self.OOT["IS_CANCELLED"], self.OOT["PREDICTED_PROBABILITY"], pos_label=1)
        print("OOT - brier_score_loss", OOT_score)
        Val_score = brier_score_loss(self.Val["IS_CANCELLED"], self.Val["PREDICTED_PROBABILITY"], pos_label=1)
        print("Val - brier_score_loss", Val_score)

        pred_train = sm.add_constant(self.Train["LOGODDS_SCORE"])
        self.glm_calib = sm.Logit(self.Train["IS_CANCELLED"], pred_train)
        self.glm_calib_results = self.glm_calib.fit()

        print("Calibrated model summary", self.glm_calib_results.summary())

        # Calibrated model prediction
        self.Train["CALIB_PD"] = self.glm_calib_results.predict(pred_train)
        self.OOT["CALIB_PD"] = self.glm_calib_results.predict(sm.add_constant(self.OOT["LOGODDS_SCORE"]))
        self.Val["CALIB_PD"] = self.glm_calib_results.predict(sm.add_constant(self.Val["LOGODDS_SCORE"]))

        _, bins = pd.qcut(self.Train["CALIB_PD"], q=10, retbins=True, duplicates="drop")
        bins = np.concatenate(([-np.inf], bins[1:-1], [np.inf]))
        self.Train["PRED_TRAIN_BIN"] = pd.cut(self.Train["CALIB_PD"], bins)
        self.OOT["PRED_OOT_BIN"] = pd.cut(self.OOT["CALIB_PD"], bins)
        self.Val["PRED_OOT_BIN"] = pd.cut(self.Val["CALIB_PD"], bins)

        gini_train = util_functions.calculate_gini(self.Train, "IS_CANCELLED", "CALIB_PD")
        print("GINI_train:", gini_train)

        gini_oot = util_functions.calculate_gini(self.OOT, "IS_CANCELLED", "CALIB_PD")
        print("GINI_OOT:", gini_oot)

        gini_val = util_functions.calculate_gini(self.Val, "IS_CANCELLED", "CALIB_PD")
        print("GINI_Val:", gini_val)

        self.next(self.quantile_transformation)

    @retry(times=3)
    @timeout(minutes=60)
    @card(type="blank")
    @step
    def quantile_transformation(self):
        # Initialize QuantileTransformer
        self.transformer = QuantileTransformer(output_distribution="uniform")

        # Quantile transformation
        calib_pd_values = self.Train["CALIB_PD"].values.reshape(-1, 1)

        # Fit and transform the data
        scaled_values = self.transformer.fit_transform(calib_pd_values)

        # Scale to 0-100 range
        scaled_values *= 100

        # Flatten the scaled values array
        scaled_values = scaled_values.flatten().astype(int)

        self.Train["SCALED_VALUES"] = scaled_values
        _, self.scaled_bins = pd.qcut(self.Train["SCALED_VALUES"], q=10, retbins=True, duplicates="drop")
        self.scaled_bins = np.concatenate(([-np.inf], self.scaled_bins[1:-1], [np.inf]))
        print("scaled bins display :", self.scaled_bins)

        self.Train["SCALED_VALUES_BIN"] = pd.cut(self.Train["SCALED_VALUES"], self.scaled_bins)
        # Quantile Transformation
        self.OOT = util_functions.scale_and_bin(self.OOT, "CALIB_PD", self.transformer, self.scaled_bins)
        self.Val = util_functions.scale_and_bin(self.Val, "CALIB_PD", self.transformer, self.scaled_bins)

        self.next(self.end)

    @retry(times=3)
    @timeout(minutes=60)
    @card(type="blank")
    @step
    def end(self):
        # Plot the percentage of canceled records for each predicted probability bucket
        fig = util_functions.plot_risk_rank_ordering(self.Train, "SCALED_VALUES_BIN", "Train")
        current.card.append(Markdown("Train_data_cancellation_score_ordering - SCALED AND CALIBRATED"))
        current.card.append(Image.from_matplotlib(fig))

        fig = util_functions.plot_risk_rank_ordering(self.OOT, "SCALED_VALUES_BIN", "OOT")
        current.card.append(Markdown("OOT_data_cancellation_score_ordering - SCALED AND CALIBRATED"))
        current.card.append(Image.from_matplotlib(fig))

        fig = util_functions.plot_risk_rank_ordering(self.Val, "SCALED_VALUES_BIN", "Val")
        current.card.append(Markdown("Val_data_cancellation_score_ordering - SCALED AND CALIBRATED"))
        current.card.append(Image.from_matplotlib(fig))

        (
            fig_roc_plot,
            fig_ro_plot,
            fig_pd_vs_actual_plot,
            fig_precision_recall_plot,
            ks_scores,
        ) = util_functions.process_dataframe_and_plot(self.Train)
        current.card.append(Markdown("Train data plots - SCALED AND CALIBRATED"))
        current.card.append(Image.from_matplotlib(fig_roc_plot))
        current.card.append(Image.from_matplotlib(fig_ro_plot))
        current.card.append(Image.from_matplotlib(fig_pd_vs_actual_plot))
        current.card.append(Image.from_matplotlib(fig_precision_recall_plot))
        current.card.append(Table.from_dataframe(ks_scores, truncate=False))

        (
            fig_roc_plot,
            fig_ro_plot,
            fig_pd_vs_actual_plot,
            fig_precision_recall_plot,
            ks_scores,
        ) = util_functions.process_dataframe_and_plot(self.OOT)
        current.card.append(Markdown("OOT data plots - SCALED AND CALIBRATED"))
        current.card.append(Image.from_matplotlib(fig_roc_plot))
        current.card.append(Image.from_matplotlib(fig_ro_plot))
        current.card.append(Image.from_matplotlib(fig_pd_vs_actual_plot))
        current.card.append(Image.from_matplotlib(fig_precision_recall_plot))
        current.card.append(Table.from_dataframe(ks_scores, truncate=False))

        (
            fig_roc_plot,
            fig_ro_plot,
            fig_pd_vs_actual_plot,
            fig_precision_recall_plot,
            ks_scores,
        ) = util_functions.process_dataframe_and_plot(self.Val)
        current.card.append(Markdown("Val data plots - SCALED AND CALIBRATED"))
        current.card.append(Image.from_matplotlib(fig_roc_plot))
        current.card.append(Image.from_matplotlib(fig_ro_plot))
        current.card.append(Image.from_matplotlib(fig_pd_vs_actual_plot))
        current.card.append(Image.from_matplotlib(fig_precision_recall_plot))
        current.card.append(Table.from_dataframe(ks_scores, truncate=False))


if __name__ == "__main__":
    CancellationsModelTrainingFlow()
