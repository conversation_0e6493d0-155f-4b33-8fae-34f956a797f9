import streamlit as st
import pandas as pd
import numpy as np
from gql import gql, Client
from gql.transport.aiohttp import AIOHTTPTransport
from apps import auth
from boards import st_utils

GRAPHQL_SERVER_ENDPOINT = "https://graphqlapi.prod.nirvanatech.com"


def app():
    st.title("User Management")
    st_utils.break_lines(2)

    if not auth.is_logged_in():
        st.error("Unauthorized. Please login and try again.")
        return

    gql_client = get_gql_client()

    bulk_user_creation(gql_client)
    single_user_creation(gql_client)


def bulk_user_creation(gql_client):
    with st.expander("Bulk User Creation"):
        uploaded_file = st.file_uploader("Choose a file", type="xlsx")
        if uploaded_file is not None:
            df = pd.read_excel(uploaded_file)
            cols = df.select_dtypes(include=[np.object]).columns
            df[cols] = df[cols].apply(
                lambda x: x.str.normalize("NFKD").str.strip())
            df = df.fillna(np.nan).replace([np.nan], [None])
            st.write(df)
            bt = st.button("Create Users" if len(df) > 1 else "Create User")
            if bt:
                create_users(gql_client, df.reset_index().to_dict("records"))


def single_user_creation(gql_client):
    with st.expander("Single User Creation"):
        with st.form("single_user_creation_form"):
            user = {}
            col1, col2 = st.columns(2)
            user["First Name"] = col1.text_input("First Name").strip()
            user["Last Name"] = col2.text_input("Last Name").strip()
            user["Email"] = st.text_input("Email").strip()
            user["Phone Number"] = st.text_input("Phone Number").strip()
            user["Title"] = st.text_input("Title").strip()
            user["Password"] = st.text_input(
                "Password", type="password").strip()
            user["Role"] = col1.selectbox(
                "Role", ["AgencyProducerRole", "AgencyAccountManagerRole"])
            user["Agency ID"] = col2.text_input("Agency ID").strip()

            submit = st.form_submit_button("Create User")
            if submit:
                if is_valid_user(user):
                    with st.spinner(""):
                        try:
                            create_user(gql_client, user)
                            st.success("Successfully created user.")
                        except Exception as e:
                            st.error("Failed to create user.")
                            st.exception(e)
                else:
                    st.warning(
                        "Invalid user. Ensure all fields are filled out and correct.")


def get_gql_client():
    headers = {"JSESSIONID": st.session_state.session_id}
    transport = AIOHTTPTransport(
        url=f"{GRAPHQL_SERVER_ENDPOINT}/graphql-http", headers=headers)
    return Client(transport=transport, fetch_schema_from_transport=False)


def create_users(gql_client, users):
    success = []
    failures = []
    invalid = []
    progress_bar = st.progress(0)
    for idx, user in enumerate(users):
        if is_valid_user(user):
            try:
                create_user(gql_client, user)
                success.append(user)
            except Exception as e:
                failures.append(user)
        else:
            invalid.append(user)
        progress_bar.progress((idx + 1) / len(users))
    if len(invalid) > 0:
        invalid_rows = [i["index"] for i in invalid]
        st.warning(
            f"Skipped creating {len(invalid)} invalid user(s) -- Row ID(s): {invalid_rows}")
    if len(failures) > 0:
        failure_emails = [f["Email"] for f in failures]
        st.error(
            f"Failed to create {len(failures)} user(s) -- Email(s): {failure_emails} \n {failures}")
    if len(success) > 0:
        success_emails = [s["Email"] for s in success]
        st.success(
            f"Successfully created {len(success)} user(s) -- Email(s): {success_emails}")


def create_user(client, user):
    query = gql(
        """
        mutation CreateUser(
            $email: string!
            $firstName: string!
            $lastName: string!
            $password: string!
            $phoneNumber: string
            $title: string
            $roles: [createUserRoleArgs_InputObject!]!
        ) {
            createUser(
                email: $email
                firstName: $firstName
                lastName: $lastName
                password: $password
                phoneNumber: $phoneNumber
                title: $title
                roles: $roles
            ) {
                id
                email
                firstName
                lastName
                phoneNumber
                title
                createdAt
                updatedAt
                agencies {
                    id
                    name
                }
                roles {
                    id
                    domain
                    group
                    agency {
                        id
                        name
                    }
                }
            }
        }
        """
    )
    params = {
        "firstName": user["First Name"],
        "lastName": user["Last Name"],
        "email": user["Email"],
        "password": user["Password"],
        "phoneNumber": user["Phone Number"],
        "title": user["Title"],
        "roles": [
            {
                "group": user["Role"],
                "agencyID": user["Agency ID"],
            }
        ],
    }
    return client.execute(query, variable_values=params)


def is_valid_user(user):
    if "First Name" not in user or user["First Name"] is None or user["First Name"] == "":
        return False
    if "Last Name" not in user or user["Last Name"] is None or user["Last Name"] == "":
        return False
    if "Email" not in user or user["Email"] is None or user["Email"] == "":
        return False
    if "Password" not in user or user["Password"] is None or user["Password"] == "":
        return False
    if "Role" not in user or user["Role"] is None or user["Role"] == "":
        return False
    if "Agency ID" not in user or user["Agency ID"] is None or user["Agency ID"] == "":
        return False
    return True
