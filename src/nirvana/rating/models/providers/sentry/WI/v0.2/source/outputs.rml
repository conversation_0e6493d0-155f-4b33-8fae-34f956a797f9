//Non Premium Metrics

output OutputsQuote fleetPowerUnitCount number.decimal {
    number.decimal{quoteData->company->fleetPowerUnitCount}
}

output OutputsQuote tiv number.decimal {
    number_max(sum(quoteData->company->vehicles, [Vehicle.statedValue]),11111.1)
}

//positive values equal savings. eg 0.05 = 5% savings, -0.05 = 5% surcharge
output OutputsQuote safetyDiscountPercentDisplay number.decimal {
    0.00 - quoteData->underwriting->safetyModAllCov
}

property OutputsQuote temp_safetyDiscountPercent number.decimal {
    1.00 / (1.00 + quoteData->underwriting->safetyModAllCov) 
    - 1.00
}

//There are six coverage choices -> (AL or AL&APD) x (Basic or Standard or Complete) -> 2 x 3 = 6

//Auto liability only section + GL added to totals
output OutputsQuote liabALBasicPolicyPremium number.decimal {
    quoteData->coverage->coverageModPremiumLiab +
    quoteData->coverage->coverageModPremiumMedPay +
    quoteData->coverage->coverageModPremiumUm +
    quoteData->coverage->coverageModPremiumUim +
    //quoteData->coverage->coverageModPremiumColl +
    //quoteData->coverage->coverageModPremiumComp +
    quoteData->coverage->temp_alBasicOptionalCoveragePremium
}

output OutputsQuote liabALStandardPolicyPremium number.decimal {
     quoteData->coverage->coverageModPremiumLiab +
    quoteData->coverage->coverageModPremiumMedPay +
    quoteData->coverage->coverageModPremiumUm +
    quoteData->coverage->coverageModPremiumUim +
    //quoteData->coverage->coverageModPremiumColl +
    //quoteData->coverage->coverageModPremiumComp +
    quoteData->coverage->temp_alStandardOptionalCoveragePremium
}

output OutputsQuote liabALCompletePolicyPremium number.decimal {
   quoteData->coverage->coverageModPremiumLiab +
   quoteData->coverage->coverageModPremiumMedPay +
   quoteData->coverage->coverageModPremiumUm +
   quoteData->coverage->coverageModPremiumUim +
   //quoteData->coverage->coverageModPremiumColl +
   //quoteData->coverage->coverageModPremiumComp +
   quoteData->coverage->temp_alCompleteOptionalCoveragePremium
}

output OutputsQuote totalALBasicPolicyPremium number.decimal {
    quoteData->coverage->coverageModPremiumLiab +
    quoteData->coverage->coverageModPremiumMedPay +
    quoteData->coverage->coverageModPremiumUm +
    quoteData->coverage->coverageModPremiumUim +
    //quoteData->coverage->coverageModPremiumColl +
    //quoteData->coverage->coverageModPremiumComp +
    quoteData->coverage->temp_alBasicOptionalCoveragePremium +
    quoteData->outputsQuote->totalGlBasicPolicyPremium
}

output OutputsQuote totalALStandardPolicyPremium number.decimal {
    quoteData->coverage->coverageModPremiumLiab +
    quoteData->coverage->coverageModPremiumMedPay +
    quoteData->coverage->coverageModPremiumUm +
    quoteData->coverage->coverageModPremiumUim +
    //quoteData->coverage->coverageModPremiumColl +
    //quoteData->coverage->coverageModPremiumComp +
    quoteData->coverage->temp_alStandardOptionalCoveragePremium +
    quoteData->outputsQuote->totalGlStandardPolicyPremium
}

output OutputsQuote totalALCompletePolicyPremium number.decimal {
    quoteData->coverage->coverageModPremiumLiab +
    quoteData->coverage->coverageModPremiumMedPay +
    quoteData->coverage->coverageModPremiumUm +
    quoteData->coverage->coverageModPremiumUim +
    //quoteData->coverage->coverageModPremiumColl +
    //quoteData->coverage->coverageModPremiumComp +
    quoteData->coverage->temp_alCompleteOptionalCoveragePremium +
    quoteData->outputsQuote->totalGlCompletePolicyPremium
}

//SUB METRICS 
//
//sub metrics for breaking out other displayed measures

output OutputsQuote liabALBasicPolicyPremiumPpu number.decimal {
    liabALBasicPolicyPremium / fleetPowerUnitCount
}

output OutputsQuote liabALStandardPolicyPremiumPpu number.decimal {
    liabALStandardPolicyPremium / fleetPowerUnitCount
}

output OutputsQuote liabALCompletePolicyPremiumPpu number.decimal {
    liabALCompletePolicyPremium / fleetPowerUnitCount
}

output OutputsQuote alSafetyDiscountBasicPackage number.decimal {
    temp_safetyDiscountPercent * totalALBasicPolicyPremium
}

output OutputsQuote alSafetyDiscountStandardPackage number.decimal {
    temp_safetyDiscountPercent * totalALStandardPolicyPremium
}

output OutputsQuote alSafetyDiscountCompletePackage number.decimal {
    temp_safetyDiscountPercent * totalALCompletePolicyPremium
}



//Auto liability + auto physical damage section + GL added to totals

output OutputsQuote totalALPDBasicPolicyPremium number.decimal {
    quoteData->coverage->coverageModPremiumLiab +
    quoteData->coverage->coverageModPremiumMedPay +
    quoteData->coverage->coverageModPremiumUm + 
    quoteData->coverage->coverageModPremiumUim + 
    quoteData->coverage->coverageModPremiumColl + 
    quoteData->coverage->coverageModPremiumComp +
    quoteData->coverage->temp_alpdBasicOptionalCoveragePremium + 
    quoteData->outputsQuote->totalGlBasicPolicyPremium
}

output OutputsQuote totalALPDStandardPolicyPremium number.decimal {
    quoteData->coverage->coverageModPremiumLiab +
    quoteData->coverage->coverageModPremiumMedPay +
    quoteData->coverage->coverageModPremiumUm + 
    quoteData->coverage->coverageModPremiumUim + 
    quoteData->coverage->coverageModPremiumColl + 
    quoteData->coverage->coverageModPremiumComp +
    quoteData->coverage->temp_alpdStandardOptionalCoveragePremium + 
    quoteData->outputsQuote->totalGlStandardPolicyPremium
}

output OutputsQuote totalALPDCompletePolicyPremium number.decimal {
    quoteData->coverage->coverageModPremiumLiab +
    quoteData->coverage->coverageModPremiumMedPay +
    quoteData->coverage->coverageModPremiumUm + 
    quoteData->coverage->coverageModPremiumUim + 
    quoteData->coverage->coverageModPremiumColl + 
    quoteData->coverage->coverageModPremiumComp +
    quoteData->coverage->temp_alpdCompleteOptionalCoveragePremium + 
    quoteData->outputsQuote->totalGlCompletePolicyPremium
}

output OutputsQuote liabALPDBasicPolicyPremium number.decimal {
    quoteData->coverage->coverageModPremiumLiab +
    quoteData->coverage->coverageModPremiumMedPay +
    quoteData->coverage->coverageModPremiumUm + 
    quoteData->coverage->coverageModPremiumUim + 
    //quoteData->coverage->coverageModPremiumColl + 
    //quoteData->coverage->coverageModPremiumComp +
    quoteData->coverage->temp_alBasicOptionalCoveragePremium
}

output OutputsQuote liabALPDStandardPolicyPremium number.decimal {
    quoteData->coverage->coverageModPremiumLiab +
    quoteData->coverage->coverageModPremiumMedPay +
    quoteData->coverage->coverageModPremiumUm + 
    quoteData->coverage->coverageModPremiumUim + 
    //quoteData->coverage->coverageModPremiumColl + 
    //quoteData->coverage->coverageModPremiumComp +
    quoteData->coverage->temp_alStandardOptionalCoveragePremium
}

output OutputsQuote liabALPDCompletePolicyPremium number.decimal {
    quoteData->coverage->coverageModPremiumLiab +
    quoteData->coverage->coverageModPremiumMedPay +
    quoteData->coverage->coverageModPremiumUm + 
    quoteData->coverage->coverageModPremiumUim + 
    //quoteData->coverage->coverageModPremiumColl + 
    //quoteData->coverage->coverageModPremiumComp +
    quoteData->coverage->temp_alCompleteOptionalCoveragePremium
}

output OutputsQuote physALPDBasicPolicyPremium number.decimal {
    //quoteData->coverage->coverageModPremiumLiab +
    //quoteData->coverage->coverageModPremiumMedPay +
    //quoteData->coverage->coverageModPremiumUm + 
    //quoteData->coverage->coverageModPremiumUim + 
    quoteData->coverage->coverageModPremiumColl + 
    quoteData->coverage->coverageModPremiumComp +
    quoteData->coverage->temp_pdBasicOptionalCoveragePremium
}

output OutputsQuote physALPDStandardPolicyPremium number.decimal {
    //quoteData->coverage->coverageModPremiumLiab +
    //quoteData->coverage->coverageModPremiumMedPay +
    //quoteData->coverage->coverageModPremiumUm + 
    //quoteData->coverage->coverageModPremiumUim + 
    quoteData->coverage->coverageModPremiumColl + 
    quoteData->coverage->coverageModPremiumComp +
    quoteData->coverage->temp_pdStandardOptionalCoveragePremium
}

output OutputsQuote physALPDCompletePolicyPremium number.decimal {
    //quoteData->coverage->coverageModPremiumLiab +
    //quoteData->coverage->coverageModPremiumMedPay +
    //quoteData->coverage->coverageModPremiumUm + 
    //quoteData->coverage->coverageModPremiumUim + 
    quoteData->coverage->coverageModPremiumColl + 
    quoteData->coverage->coverageModPremiumComp +
    quoteData->coverage->temp_pdCompleteOptionalCoveragePremium
}

//GL section
//
//

output OutputsQuote totalGlBasicPolicyPremium number.decimal {
    switch(quoteData->coverage->hasGlCoverage) {
        case False:
            0.0
        case True:
            quoteData->coverage->coverageModPremiumGl +
            quoteData->coverage->basicGlPackageRate
    }
}

output OutputsQuote totalGlStandardPolicyPremium number.decimal {
    switch(quoteData->coverage->hasGlCoverage) {
        case False:
            0.0
        case True:
            quoteData->coverage->coverageModPremiumGl +
            quoteData->coverage->standardGlPackageRate
    }
}

output OutputsQuote totalGlCompletePolicyPremium number.decimal {
    switch(quoteData->coverage->hasGlCoverage) {
        case False:
            0.0
        case True:
            quoteData->coverage->coverageModPremiumGl +
            quoteData->coverage->completeGlPackageRate
    }
}

//SUB METRICS 
//
//sub metrics for breaking out other displayed measures

output OutputsQuote liabALPDBasicPolicyPremiumPpu number.decimal {
    liabALPDBasicPolicyPremium / fleetPowerUnitCount
}

output OutputsQuote liabALPDStandardPolicyPremiumPpu number.decimal {
    liabALPDStandardPolicyPremium / fleetPowerUnitCount
}

output OutputsQuote liabALPDCompletePolicyPremiumPpu number.decimal {
    liabALPDCompletePolicyPremium / fleetPowerUnitCount
}

output OutputsQuote physALPDBasicPolicyPremiumPptiv number.decimal {
    physALPDBasicPolicyPremium / tiv
}

output OutputsQuote physALPDStandardPolicyPremiumPptiv number.decimal {
    physALPDStandardPolicyPremium / tiv
}

output OutputsQuote physALPDCompletePolicyPremiumPptiv number.decimal {
    physALPDCompletePolicyPremium / tiv
}

output OutputsQuote alpdSafetyDiscountBasicPackage number.decimal {
    temp_safetyDiscountPercent * totalALPDBasicPolicyPremium
}

output OutputsQuote alpdSafetyDiscountStandardPackage number.decimal {
    temp_safetyDiscountPercent * totalALPDStandardPolicyPremium
}

output OutputsQuote alpdSafetyDiscountCompletePackage number.decimal {
    temp_safetyDiscountPercent * totalALPDCompletePolicyPremium
}

//The following outputs added for the quote display
//
//Liab only

output OutputsQuote nonFlatALBasicPolicyPremium number.decimal {
    quoteData->coverage->coverageModPremiumLiab +
    quoteData->coverage->coverageModPremiumMedPay +
    quoteData->coverage->coverageModPremiumUm + 
    quoteData->coverage->coverageModPremiumUim 
    //quoteData->coverage->coverageModPremiumColl + 
    //quoteData->coverage->coverageModPremiumComp +
    //quoteData->coverage->temp_alBasicOptionalCoveragePremium
}

output OutputsQuote nonFlatALBasicPolicyPremiumPpu number.decimal {
    nonFlatALBasicPolicyPremium / fleetPowerUnitCount
}

output OutputsQuote flatALBasicPolicyPremium number.decimal {
    quoteData->coverage->temp_alBasicOptionalCoveragePremium
}

output OutputsQuote nonFlatALStandardPolicyPremium number.decimal {
    quoteData->coverage->coverageModPremiumLiab +
    quoteData->coverage->coverageModPremiumMedPay +
    quoteData->coverage->coverageModPremiumUm + 
    quoteData->coverage->coverageModPremiumUim + 
    //quoteData->coverage->coverageModPremiumColl + 
    //quoteData->coverage->coverageModPremiumComp +
    //quoteData->coverage->temp_alStandardOptionalCoveragePremium
    quoteData->coverage->broadenedPollutionPrem
}

output OutputsQuote nonFlatALStandardPolicyPremiumPpu number.decimal {
    nonFlatALStandardPolicyPremium / fleetPowerUnitCount
}

output OutputsQuote flatALStandardPolicyPremium number.decimal {
        //broadenedPollutionPrem +
    //trailerInterchangePrem +
    quoteData->coverage->blanketAdditionalInsuredPrem
}

output OutputsQuote nonFlatALCompletePolicyPremium number.decimal {
    quoteData->coverage->coverageModPremiumLiab +
    quoteData->coverage->coverageModPremiumMedPay +
    quoteData->coverage->coverageModPremiumUm + 
    quoteData->coverage->coverageModPremiumUim + 
    //quoteData->coverage->coverageModPremiumColl + 
    //quoteData->coverage->coverageModPremiumComp +
    //quoteData->coverage->temp_alCompleteOptionalCoveragePremium
    quoteData->coverage->broadenedPollutionPrem
}

output OutputsQuote nonFlatALCompletePolicyPremiumPpu number.decimal {
    nonFlatALCompletePolicyPremium / fleetPowerUnitCount
}

output OutputsQuote flatALCompletePolicyPremium number.decimal {
        //broadenedPollutionPrem +
    //trailerInterchangePrem +
    quoteData->coverage->blanketAdditionalInsuredPrem +
    quoteData->coverage->blanketWaiverOfSubrogationPrem
}

//
//Liab + APD

output OutputsQuote nonFlatLiabAlpdBasicPolicyPremium number.decimal {
    quoteData->coverage->coverageModPremiumLiab +
    quoteData->coverage->coverageModPremiumMedPay +
    quoteData->coverage->coverageModPremiumUm + 
    quoteData->coverage->coverageModPremiumUim 
    //quoteData->coverage->coverageModPremiumColl + 
    //quoteData->coverage->coverageModPremiumComp +
    //quoteData->coverage->temp_alBasicOptionalCoveragePremium
}

output OutputsQuote nonFlatLiabAlpdBasicPolicyPremiumPpu number.decimal {
    nonFlatLiabAlpdBasicPolicyPremium / fleetPowerUnitCount
}

output OutputsQuote nonFlatPhysAlpdBasicPolicyPremium number.decimal {
    physALPDBasicPolicyPremium
}

output OutputsQuote nonFlatPhysAlpdBasicPolicyPremiumPtiv number.decimal {
    nonFlatPhysAlpdBasicPolicyPremium / tiv
}

output OutputsQuote flatAlpdBasicPolicyPremium number.decimal {
    quoteData->coverage->temp_alBasicOptionalCoveragePremium
}

output OutputsQuote nonFlatLiabAlpdStandardPolicyPremium number.decimal {
    quoteData->coverage->coverageModPremiumLiab +
    quoteData->coverage->coverageModPremiumMedPay +
    quoteData->coverage->coverageModPremiumUm + 
    quoteData->coverage->coverageModPremiumUim + 
    //quoteData->coverage->coverageModPremiumColl + 
    //quoteData->coverage->coverageModPremiumComp +
    //quoteData->coverage->temp_alStandardOptionalCoveragePremium
    quoteData->coverage->broadenedPollutionPrem
}

output OutputsQuote nonFlatLiabAlpdStandardPolicyPremiumPpu number.decimal {
    nonFlatLiabAlpdStandardPolicyPremium / fleetPowerUnitCount
}

output OutputsQuote nonFlatPhysAlpdStandardPolicyPremium number.decimal {
    physALPDStandardPolicyPremium
}

output OutputsQuote nonFlatPhysAlpdStandardPolicyPremiumPtiv number.decimal {
    nonFlatPhysAlpdStandardPolicyPremium / tiv
}

output OutputsQuote flatAlpdStandardPolicyPremium number.decimal {
        //broadenedPollutionPrem +
    //trailerInterchangePrem +
    quoteData->coverage->blanketAdditionalInsuredPrem
}

output OutputsQuote nonFlatLiabAlpdCompletePolicyPremium number.decimal {
    quoteData->coverage->coverageModPremiumLiab +
    quoteData->coverage->coverageModPremiumMedPay +
    quoteData->coverage->coverageModPremiumUm + 
    quoteData->coverage->coverageModPremiumUim + 
    //quoteData->coverage->coverageModPremiumColl + 
    //quoteData->coverage->coverageModPremiumComp +
    //quoteData->coverage->temp_alCompleteOptionalCoveragePremium
    quoteData->coverage->broadenedPollutionPrem
}

output OutputsQuote nonFlatLiabAlpdCompletePolicyPremiumPpu number.decimal {
    nonFlatLiabAlpdCompletePolicyPremium / fleetPowerUnitCount
}

output OutputsQuote nonFlatPhysAlpdCompletePolicyPremium number.decimal {
    physALPDCompletePolicyPremium
}

output OutputsQuote nonFlatPhysAlpdCompletePolicyPremiumPtiv number.decimal {
    nonFlatPhysAlpdCompletePolicyPremium / tiv
}

output OutputsQuote flatAlpdCompletePolicyPremium number.decimal {
        //broadenedPollutionPrem +
    //trailerInterchangePrem +
    quoteData->coverage->blanketAdditionalInsuredPrem +
    quoteData->coverage->blanketWaiverOfSubrogationPrem
}