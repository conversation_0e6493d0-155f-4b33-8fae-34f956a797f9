//----- General Liability Insurance (aka GL)

//----- Enum reference section

tag_enum GlOccuranceLimitEnum
	
tag_enum GlAggLimitEnum
	
tag_enum GlDeductibleEnum


//----- Lookup table reference section

lookup_table GlLimitFactor {
    inputs {
        glOccuranceLimit GlOccuranceLimitEnum
        glAggLimit GlAggLimitEnum
    }
    outputs {
        glLimitFactor number.decimal
    }
}

lookup_table GlDeductibleFactor {
    inputs {
        glDeductible GlDeductibleEnum
    }
    outputs {
        glDeductibleFactor number.decimal
    }
}

lookup_table FleetSizeFactorGl {
    inputs {
        fleetPowerUnitCount range[number.integer]
    }
    outputs {
        fleetSizeFactorGl number.decimal
    }
}


//----- Derived Data

property RaterGL glBaseRate number.decimal {
    lookup([OptionalCoverageRates], [OptionalCoverageRates.glRate],
        1
        )
}

property RaterGL glFactorLimit number.decimal {
    lookup([GlLimitFactor], [GlLimitFactor.glLimitFactor],
        quoteData->coverage->glOccuranceLimit,
        quoteData->coverage->glAggLimit
        )
}

property RaterGL glFactorMinusDeductible number.decimal {
    lookup([GlDeductibleFactor], [GlDeductibleFactor.glDeductibleFactor],
        quoteData->coverage->glDeductible
        )
}

property RaterGL glFactorSizeFleet number.decimal {
    lookup([FleetSizeFactorGl], [FleetSizeFactorGl.fleetSizeFactorGl],
        quoteData->company->fleetPowerUnitCount
        )
}

property RaterGL glRateContractual number.decimal {
    lookup([OptionalCoverageRates], [OptionalCoverageRates.glContractualRate],
        quoteData->coverage->hasGlContractual
        )
}

property RaterGL glRateMisdeliveryLiquids number.decimal {
    lookup([OptionalCoverageRates], [OptionalCoverageRates.glMisdeliveryLiquidsRate],
        quoteData->coverage->hasGlMisdeliveryLiquids
        )
}

property RaterGL glRateStopGap number.decimal {
    lookup([OptionalCoverageRates], [OptionalCoverageRates.glStopGapRate],
        quoteData->coverage->hasGlStopGap
        )
}

property RaterGL companyPUFleetCount number.decimal {
    number.decimal{quoteData->company->fleetPowerUnitCount
    }
} 

property RaterGL packageGlBlanketAdditionalInsured number.decimal {
    lookup([OptionalCoverageRates], [OptionalCoverageRates.glBlanketAdditionalInsuredRate],
        1
        )
}

property RaterGL packageGlBlanketWaiverOfSubrogation number.decimal {
    lookup([OptionalCoverageRates], [OptionalCoverageRates.glBlanketWaiverOfSubrogationRate],
        1
        )
}

output RaterGL characteristicCompanyGarageState GeoStateAbbrEnum {
    quoteData->company->companyGarageState
}

output RaterGL characteristicGlDeductible GlDeductibleEnum {
    quoteData->coverage->glDeductible
}

output RaterGL characteristicGlOccuranceLimit GlOccuranceLimitEnum {
    quoteData->coverage->glOccuranceLimit
}

output RaterGL characteristicGlAggLimit GlAggLimitEnum {
    quoteData->coverage->glAggLimit
}

output RaterGL characteristicHasGlContractual number.integer {
    quoteData->coverage->hasGlContractual
}

output RaterGL characteristicHasGlMisdeliveryLiquids number.integer {
    quoteData->coverage->hasGlMisdeliveryLiquids
}

output RaterGL characteristicHasGlStopGap number.integer {
    quoteData->coverage->hasGlStopGap
}


//----- Premium Calculation Section

property Coverage coverageModPremiumGl number.decimal {
    quoteData->raterGL->totalModPremiumGl
}

property RaterGL totalModPremiumGl number.decimal {
      totalManualPremiumGl
      // * TODO add GL schedule mod and GL experience mod if later desired
}

property RaterGL totalManualPremiumGl number.decimal {
    (   (   glBaseRate * 
            ( glFactorLimit - glFactorMinusDeductible) *
            glFactorSizeFleet
        ) +
        glRateContractual +
        glRateMisdeliveryLiquids + 
        glRateStopGap
    ) *
    companyPUFleetCount  
}


//---- GL Specified Additional insured


property RaterGL rateGlSpecifiedAdditionalInsured number.decimal {
    lookup([OptionalCoverageRates], [OptionalCoverageRates.glSpecifiedAdditionalInsuredRate],
        1
        )
}


//---- GL Specified Waiver of Subrogation


property RaterGL rateGlSpecifiedWaiverOfSubrogation number.decimal {
    lookup([OptionalCoverageRates], [OptionalCoverageRates.glSpecifiedWaiverOfSubrogationRate],
        1
        )
}


//----- GL package calculations

property RaterGL totalPackageBasicGl number.decimal {
    0.0
}

property RaterGL totalPackageStandardGl number.decimal {
    packageGlBlanketAdditionalInsured
}

property RaterGL totalPackageCompleteGl number.decimal {
    packageGlBlanketAdditionalInsured +
    packageGlBlanketWaiverOfSubrogation
}

property Coverage basicGlPackageRate number.decimal {
    quoteData->raterGL->totalPackageBasicGl
}

property Coverage standardGlPackageRate number.decimal {
    quoteData->raterGL->totalPackageStandardGl
}

property Coverage completeGlPackageRate number.decimal {
    quoteData->raterGL->totalPackageCompleteGl
}

output RaterGL totalPolicyPremiumBasicGl number.decimal {
    quoteData->outputsQuote->glBasicPolicyPremium
}

output RaterGL totalPolicyPremiumStandardGl number.decimal {
    quoteData->outputsQuote->glStandardPolicyPremium
}

output RaterGL totalPolicyPremiumCompleteGl number.decimal {
    quoteData->outputsQuote->glCompletePolicyPremium
}
