//Non Premium Metrics, shown on quote as well

output OutputsQuote fleetPowerUnitCount number.decimal {
    number.decimal{quoteData->company->fleetPowerUnitCount}
}

property Vehicle statedValueTiv number.decimal {
    switch(vehType) {
        case "non_owned_semi_trailer": 0.0
        case _: statedValue
    }
}

output OutputsQuote tiv number.decimal {
    sum(quoteData->company->vehicles, [Vehicle.statedValueTiv]) 
}

//Positive values equal savings. eg 0.05 = 5% savings, -0.05 = 5% surcharge

output OutputsQuote safetyDiscountPercentDisplay number.decimal {
    0.00 - quoteData->underwriting->safetyModAllCov
}

output OutputsQuote temp_safetyDiscountPercent number.decimal {
    1.00 - 
    1.00 / (1.00 - quoteData->underwriting->safetyModAllCov)
}

output OutputsQuote tierFactorMTCDisplay number.decimal {
    number.decimal{quoteData->company->tierFactorMTC}
}


//Coverage Triggers

output OutputsQuote policyPremiumUm number.decimal {
    switch(quoteData->coverage->hasUMCoverage) {
	case True:
            quoteData->coverage->coverageModPremiumUm
	case False:
            0.0
    }
}

output OutputsQuote policyPremiumUim number.decimal {
    switch(quoteData->coverage->hasUIMCoverage) {
	case True:
            quoteData->coverage->coverageModPremiumUim
	case False:
            0.0
    }
}

output OutputsQuote policyPremiumUmUim number.decimal {
    switch(quoteData->coverage->hasUMUIMCoverage) {
	case True:
            quoteData->coverage->coverageModPremiumUmUim
	case False:
            0.0
    }
}

output OutputsQuote policyPremiumMedPay number.decimal {
    switch(quoteData->coverage->hasMedPayCoverage) {
	case True:
            quoteData->coverage->coverageModPremiumMedPay
	case False:
            0.0
    }
}


//Complete Package

output OutputsQuote safetyDiscountPremiumCompletePackage number.decimal {
    temp_safetyDiscountPercent * totalCompletePolicyPremium
}

output OutputsQuote totalCompletePolicyPremium number.decimal {
    liabCompletePolicyPremium +
    physCompletePolicyPremium +
    glCompletePolicyPremium +
    mtcCompletePolicyPremium
}

output OutputsQuote liabCompletePolicyPremium number.decimal {
    flatCompletePolicyPremium +
    nonFlatLiabCompletePolicyPremium
}

output OutputsQuote liabCompletePolicyPremiumPpu number.decimal {
    liabCompletePolicyPremium / fleetPowerUnitCount
}

output OutputsQuote physCompletePolicyPremium number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
	case True: nonFlatPhysCompletePolicyPremium
	case False: 0.0
    }
}

output OutputsQuote physCompletePolicyPremiumPtiv number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
	case True: physCompletePolicyPremium / tiv
	case False: 0.0
    }
}

output OutputsQuote glCompletePolicyPremium number.decimal {
    switch(quoteData->coverage->hasGlCoverage) {
	case True:
            round(
            quoteData->coverage->coverageModPremiumGl +
            quoteData->coverage->completeGlPackageRate
            )
	case False:
            0.0
    }
}

output OutputsQuote mtcCompletePolicyPremium number.decimal {
    switch(quoteData->coverage->hasMTCCoverage) {
	case True:
            round(
            quoteData->coverage->coverageModPremiumMTC +
            quoteData->coverage->completeMTCPackageRate
            )
	case False:
            0.0
    }
}

output OutputsQuote mtcCompletePolicyPremiumPpu number.decimal {
    mtcCompletePolicyPremium / fleetPowerUnitCount
}

output OutputsQuote flatCompletePolicyPremium number.decimal {
    round(
    quoteData->coverage->blanketAdditionalInsuredPrem +
    quoteData->coverage->blanketWaiverOfSubrogationPrem
    )
}

output OutputsQuote nonFlatLiabCompletePolicyPremium number.decimal {
    round(
    quoteData->coverage->coverageFinalModPremiumLiab +
    policyPremiumMedPay +
    policyPremiumUm +
    policyPremiumUim +
    policyPremiumUmUim +
    quoteData->coverage->broadenedPollutionPrem
    )
}

output OutputsQuote nonFlatLiabCompletePolicyPremiumPpu number.decimal {
    nonFlatLiabCompletePolicyPremium / fleetPowerUnitCount
}

output OutputsQuote nonFlatPhysCompletePolicyPremium number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
	case True:
            round(
            quoteData->coverage->coverageFinalModPremiumPhys +
            quoteData->coverage->trailerInterchangePrem +
            quoteData->coverage->completePdPackagePrem +
            quoteData->coverage->completeAddlTowingLimitPrem
            )
	case False:
            0.0
    }
}

output OutputsQuote coverageFinalModPremiumCollComplete number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
	case True:
            round(
            nonFlatPhysCompletePolicyPremium * 
            quoteData->coverage->collCompMod
            )
	case False:
            0.0
    }
}

output OutputsQuote coverageFinalModPremiumCompComplete number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
	case True:
            nonFlatPhysCompletePolicyPremium - 
            coverageFinalModPremiumCollComplete
	case False:
            0.0
    }
}

output OutputsQuote nonFlatPhysCompletePolicyPremiumPtiv number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
	case True: nonFlatPhysCompletePolicyPremium / tiv
	case False: 0.0
    }
}


//Standard Package

output OutputsQuote safetyDiscountPremiumStandardPackage number.decimal {
    temp_safetyDiscountPercent * totalStandardPolicyPremium
}

output OutputsQuote totalStandardPolicyPremium number.decimal {
    liabStandardPolicyPremium +
    physStandardPolicyPremium +
    glStandardPolicyPremium +
    mtcStandardPolicyPremium
}

output OutputsQuote liabStandardPolicyPremium number.decimal {
    flatStandardPolicyPremium +
    nonFlatLiabStandardPolicyPremium
}

output OutputsQuote liabStandardPolicyPremiumPpu number.decimal {
    liabStandardPolicyPremium / fleetPowerUnitCount
}

output OutputsQuote physStandardPolicyPremium number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
	case True: nonFlatPhysStandardPolicyPremium
	case False: 0.0
    }
}

output OutputsQuote physStandardPolicyPremiumPtiv number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
	case True: physStandardPolicyPremium / tiv
	case False: 0.0
    }
}

output OutputsQuote glStandardPolicyPremium number.decimal {
    switch(quoteData->coverage->hasGlCoverage) {
	case True:
            round(
            quoteData->coverage->coverageModPremiumGl +
            quoteData->coverage->standardGlPackageRate
            )
	case False:
            0.0
    }
}

output OutputsQuote mtcStandardPolicyPremium number.decimal {
    switch(quoteData->coverage->hasMTCCoverage) {
	case True:
            round(
            quoteData->coverage->coverageModPremiumMTC +
            quoteData->coverage->standardMTCPackageRate
            )
	case False:
            0.0
    }
}

output OutputsQuote mtcStandardPolicyPremiumPpu number.decimal {
    mtcStandardPolicyPremium / fleetPowerUnitCount
}

output OutputsQuote flatStandardPolicyPremium number.decimal {
    round(
    quoteData->coverage->blanketAdditionalInsuredPrem
    )
}

output OutputsQuote nonFlatLiabStandardPolicyPremium number.decimal {
    round(
    quoteData->coverage->coverageFinalModPremiumLiab +
    policyPremiumMedPay +
    policyPremiumUm +
    policyPremiumUim +
    policyPremiumUmUim +
    quoteData->coverage->broadenedPollutionPrem
    )
}

output OutputsQuote nonFlatLiabStandardPolicyPremiumPpu number.decimal {
    nonFlatLiabStandardPolicyPremium / fleetPowerUnitCount
}

output OutputsQuote nonFlatPhysStandardPolicyPremium number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
	case True:
            round(
            quoteData->coverage->coverageFinalModPremiumPhys +
            quoteData->coverage->trailerInterchangePrem +
            quoteData->coverage->standardPdPackagePrem
            )
	case False:
            0.0
    }
}

output OutputsQuote coverageFinalModPremiumCollStandard number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
	case True:
            round(
            nonFlatPhysStandardPolicyPremium * 
            quoteData->coverage->collCompMod
            )
	case False:
            0.0
    }
}

output OutputsQuote coverageFinalModPremiumCompStandard number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
	case True:
            nonFlatPhysStandardPolicyPremium - 
            coverageFinalModPremiumCollStandard
	case False:
            0.0
    }
}

output OutputsQuote nonFlatPhysStandardPolicyPremiumPtiv number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
	case True: nonFlatPhysStandardPolicyPremium / tiv
	case False: 0.0
    }
}


//Basic Package

output OutputsQuote safetyDiscountPremiumBasicPackage number.decimal {
    temp_safetyDiscountPercent * totalBasicPolicyPremium
}

output OutputsQuote totalBasicPolicyPremium number.decimal {
    liabBasicPolicyPremium +
    physBasicPolicyPremium +
    glBasicPolicyPremium +
    mtcBasicPolicyPremium
}

output OutputsQuote liabBasicPolicyPremium number.decimal {
    flatBasicPolicyPremium +
    nonFlatLiabBasicPolicyPremium
}

output OutputsQuote liabBasicPolicyPremiumPpu number.decimal {
    liabBasicPolicyPremium / fleetPowerUnitCount
}

output OutputsQuote physBasicPolicyPremium number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
	case True: nonFlatPhysBasicPolicyPremium
	case False: 0.0
    }
}

output OutputsQuote physBasicPolicyPremiumPtiv number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
	case True: physBasicPolicyPremium / tiv
	case False: 0.0
    }
}

output OutputsQuote glBasicPolicyPremium number.decimal {
    switch(quoteData->coverage->hasGlCoverage) {
	case True:
            round(        
            quoteData->coverage->coverageModPremiumGl +
            quoteData->coverage->basicGlPackageRate
            )
	case False:
            0.0
    }
}

output OutputsQuote mtcBasicPolicyPremium number.decimal {
    switch(quoteData->coverage->hasMTCCoverage) {
	case True:
            round(
            quoteData->coverage->coverageModPremiumMTC +
            quoteData->coverage->basicMTCPackageRate
            )
	case False:
            0.0
    }
}

output OutputsQuote mtcBasicPolicyPremiumPpu number.decimal {
    mtcBasicPolicyPremium / fleetPowerUnitCount
}

output OutputsQuote flatBasicPolicyPremium number.decimal {
    0.0
}

output OutputsQuote nonFlatLiabBasicPolicyPremium number.decimal {
    round(
    quoteData->coverage->coverageFinalModPremiumLiab +
    policyPremiumMedPay +
    policyPremiumUm +
    policyPremiumUim +
    policyPremiumUmUim
    )
}

output OutputsQuote nonFlatLiabBasicPolicyPremiumPpu number.decimal {
    nonFlatLiabBasicPolicyPremium / fleetPowerUnitCount
}

output OutputsQuote nonFlatPhysBasicPolicyPremium number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
	case True:
            round(
            quoteData->coverage->coverageFinalModPremiumPhys
            )
	case False:
            0.0
    }
}

output OutputsQuote coverageFinalModPremiumCollBasic number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
	case True:
            round(
            nonFlatPhysBasicPolicyPremium * 
            quoteData->coverage->collCompMod
            )
	case False:
            0.0
    }
}

output OutputsQuote coverageFinalModPremiumCompBasic number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
	case True:
            nonFlatPhysBasicPolicyPremium - 
            coverageFinalModPremiumCollBasic
	case False:
            0.0
    }
}

output OutputsQuote nonFlatPhysBasicPolicyPremiumPtiv number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
	case True: nonFlatPhysBasicPolicyPremium / tiv
	case False: 0.0
    }
}

output OutputsQuote totalSurchargePremium number.decimal {
    0.0
}