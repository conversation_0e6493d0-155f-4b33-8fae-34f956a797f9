//----- Premiums
//----- Auto Liability, Auto Physical Damage (Comp & Coll)
//----- All Package Specific Optionals (Specified/Blanket Waiver of Subro, Specified/Blanket Additional Insured, Broadened Pollution, Trailer Interchange)

//----- Collision premium calculation section

property Coverage coverageManualPremiumColl number.decimal {
    sum(quoteData->company->vehicles, [Vehicle.vehPremiumColl])
}

property Coverage coverageModPremiumColl number.decimal {
    coverageManualPremiumColl
    * quoteData->underwriting->scheduleModColl
    * quoteData->underwriting->expModColl
}

property Vehicle vehPremiumColl number.decimal {
    switch(hasApdCoverage) {
        case False:
            0.0
        case True:
        switch(useZoneRating) {
            case True:
                longHaulBaseRateColl *
                    lcmCollZone *
                    isoPrimaryFactorColl *
                        ( 
                            switch(company->quoteData->underwriting->useStatedValueCollComp) {
                                case True:
                                    number_max( ( longHaulStatedValueFactorColl - longHaulCollDeductibleFactor ), 0.1 )
                                case False:
                                    number_max( ( longHaulOriginalCostNewFactorColl - longHaulCollDeductibleFactor ), 0.1 )
                            }
                        ) *
                    company->tierFactorColl
            case False:
                baseRateColl *
                    lcmColl *
                    isoPrimaryFactorColl *
                    isoSecondaryFactorColl *
                    fleetSizeFactorColl *
                        number_max( 
                            (switch(company->quoteData->underwriting->useStatedValueCollComp) {
                                case True: statedValueFactorColl
                                case False: originalCostNewFactorColl
                            }  -  collDeductibleFactor), 0.1) *
                    naicFactorColl *
                    company->tierFactorColl
        }
    }
}

property Vehicle vehPremiumCollBasicLimitColl number.decimal {
    switch(hasApdCoverage) {
        case False:
            0.0
        case True:
        switch(useZoneRating) {
            case True:
                longHaulBaseRateColl *
                    lcmCollZone *
                    isoPrimaryFactorColl *
                        ( 
                            switch(company->quoteData->underwriting->useStatedValueCollComp) {
                                case True:
                                    number_max( ( longHaulStatedValueFactorColl - longHaulCollDeductibleFactor ), 0.1 )
                                case False:
                                    number_max( ( longHaulOriginalCostNewFactorColl - longHaulCollDeductibleFactor ), 0.1 )
                            }
                        ) *
                    company->tierFactorColl
            case False:
                baseRateColl *
                    lcmColl *
                    isoPrimaryFactorColl *
                    isoSecondaryFactorColl *
                    fleetSizeFactorColl *
                        number_max( 
                            (switch(company->quoteData->underwriting->useStatedValueCollComp) {
                                case True: statedValueFactorColl
                                case False: originalCostNewFactorColl
                            }  -  collDeductibleFactor), 0.1) *
                    naicFactorColl *
                    company->tierFactorColl
        }  * elrColl
            //reduced for target loss ratio
    }
}

property Coverage coveragePremiumCollBasicLimitColl number.decimal {
    sum(quoteData->company->vehicles, [Vehicle.vehPremiumCollBasicLimitColl])
}

property Coverage avgPuPremiumCollBasicLimitColl number.decimal {
    coveragePremiumCollBasicLimitColl / number.decimal{quoteData->company->fleetPowerUnitCount}
}

property Vehicle longHaulBaseRateColl number.decimal {
    lookup([LongHaulIsoBaseRate], [LongHaulIsoBaseRate.longHaulBaseRateColl],
        vehicleStartZone,
        vehicleEndZone
    )
}

property Vehicle baseRateColl number.decimal {
    lookup([IsoBaseRate], [IsoBaseRate.baseRateColl],
        vehicleGarageIsoTerritory
    )
}

property Vehicle lcmColl number.decimal {
    lookup([IsoLCM], [IsoLCM.lcmColl],
        1
    )
}

property Vehicle lcmCollZone number.decimal {
    lookup([IsoLCM], [IsoLCM.lcmCollZone],
        1
    )
}

//expected loss ratio (elr) aka the target loss ratio, or permissible loss ratio
property Vehicle elrColl number.decimal {
    lookup([ExpModElrs], [ExpModElrs.elrColl],
        1
    )
}

property Vehicle isoPrimaryFactorColl number.decimal {
    lookup([IsoPrimaryFactor], [IsoPrimaryFactor.isoPrimaryFactorColl],
        isoWeightGroup,
        vehType,
        vehicleGpsIsoRadiusGroup,
        company->isoOperationType
    )
}

property Vehicle isoSecondaryFactorColl number.decimal {
    lookup ([IsoSecondaryFactor], [IsoSecondaryFactor.isoSecondaryFactorColl],
        vehType,
        company->commodityGroup,
        company->commodityDetail
    )
}

property Vehicle fleetSizeFactorColl number.decimal {
    lookup([FleetSizeFactor], [FleetSizeFactor.fleetSizeFactorColl],
        company->fleetPowerUnitCount,
        vehType,
        isoWeightGroup        
    )
}

property Vehicle longHaulStatedValueFactorColl number.decimal {
    lookup([LongHaulStatedValueFactorCollComp], [LongHaulStatedValueFactorCollComp.longHaulStatedValueFactorColl],
        statedValue,
        vehType
    )
}

property Vehicle longHaulOriginalCostNewFactorColl number.decimal {
    lookup([LongHaulOriginalCostNewFactorCollComp], [LongHaulOriginalCostNewFactorCollComp.longHaulOriginalCostNewFactorColl],
        costNew,
        vehType,
        vehicleAge
    )
}

property Vehicle statedValueFactorColl number.decimal {
    lookup([StatedValueFactorCollComp], [StatedValueFactorCollComp.statedValueFactorColl],
        statedValue,
        vehType
    )
}

property Vehicle originalCostNewFactorColl number.decimal {
    lookup([OriginalCostNewFactorCollComp], [OriginalCostNewFactorCollComp.originalCostNewFactorColl],
        costNew,
        vehType,
        vehicleAge
    )
}

property Vehicle naicFactorColl number.decimal {
    lookup([NaicFactor], [NaicFactor.naicFactorColl],
        company->naic,
        vehType
    )
}

property Vehicle collDeductibleFactor number.decimal {
    lookup([CollDeductibleFactor], [CollDeductibleFactor.collDeductibleFactor],
        company->quoteData->coverage->collDeductible,
        vehType
    )
}

property Vehicle longHaulCollDeductibleFactor number.decimal {
    lookup([CollDeductibleFactor], [CollDeductibleFactor.longHaulCollDeductibleFactor],
        company->quoteData->coverage->collDeductible,
        vehType
    )
}


//----- Comprehensive (aka Comp) premium calculation section

property Coverage coverageManualPremiumComp number.decimal {
    sum(quoteData->company->vehicles, [Vehicle.vehPremiumComp])
}

property Coverage coverageModPremiumComp number.decimal {
    coverageManualPremiumComp
    * quoteData->underwriting->scheduleModComp
    * quoteData->underwriting->expModComp
}

property Vehicle vehPremiumComp number.decimal {
    switch(hasApdCoverage) {
        case False:
            0.0
        case True:
        switch(useZoneRating) {
            case True:
                longHaulBaseRateComp *
                    lcmCompZone *
                    isoPrimaryFactorComp *
                        ( 
                            switch(company->quoteData->underwriting->useStatedValueCollComp) {
                                case True:
                                    number_max( ( longHaulStatedValueFactorComp - longHaulCompDeductibleFactor ), 0.1 )
                                case False:
                                    number_max( (  longHaulOriginalCostNewFactorComp - longHaulCompDeductibleFactor ), 0.1)
                            }
                        ) *
                    company->tierFactorComp
            case False:
                baseRateComp *
                    lcmComp *
                    isoPrimaryFactorComp *
                    isoSecondaryFactorComp *
                    fleetSizeFactorComp *
                        number_max(
                            (switch(company->quoteData->underwriting->useStatedValueCollComp) {
                                case True: statedValueFactorComp
                                case False: originalCostNewFactorComp
                            } -  compDeductibleFactor), 0.1) *
                    naicFactorComp *
                    company->tierFactorComp
        }
    }
}

property Vehicle vehPremiumCompBasicLimitComp number.decimal {
    switch(hasApdCoverage) {
        case False:
            0.0
        case True:
        switch(useZoneRating) {
            case True:
                longHaulBaseRateComp *
                    lcmCompZone *
                    isoPrimaryFactorComp *
                        ( 
                            switch(company->quoteData->underwriting->useStatedValueCollComp) {
                                case True:
                                    number_max( ( longHaulStatedValueFactorComp - longHaulCompDeductibleFactor ), 0.1 )
                                case False:
                                    number_max( (  longHaulOriginalCostNewFactorComp - longHaulCompDeductibleFactor ), 0.1) 
                            }
                        ) *
                    company->tierFactorComp
            case False:
                baseRateComp *
                    lcmComp *
                    isoPrimaryFactorComp *
                    isoSecondaryFactorComp *
                    fleetSizeFactorComp *
                       number_max(
                            (switch(company->quoteData->underwriting->useStatedValueCollComp) {
                                case True: statedValueFactorComp
                                case False: originalCostNewFactorComp
                            } -  compDeductibleFactor), 0.1) *
                    naicFactorComp *
                    company->tierFactorComp
        } * elrComp
                    //reduces for permessible loss ratio aka elr
    }
}

property Coverage coveragePremiumCompBasicLimitComp number.decimal {
    sum(quoteData->company->vehicles, [Vehicle.vehPremiumCompBasicLimitComp])
}

property Coverage avgPuPremiumCompBasicLimitComp number.decimal {
    coveragePremiumCompBasicLimitComp / number.decimal{quoteData->company->fleetPowerUnitCount}
}

property Vehicle longHaulBaseRateComp number.decimal {
    lookup([LongHaulIsoBaseRate], [LongHaulIsoBaseRate.longHaulBaseRateComp],
        vehicleStartZone,
        vehicleEndZone
    )
}

property Vehicle baseRateComp number.decimal {
    lookup([IsoBaseRate], [IsoBaseRate.baseRateComp],
        vehicleGarageIsoTerritory
    )
}

property Vehicle lcmComp number.decimal {
    lookup([IsoLCM], [IsoLCM.lcmComp],
        1
    )
}

property Vehicle lcmCompZone number.decimal {
    lookup([IsoLCM], [IsoLCM.lcmCompZone],
        1
    )
}

//expected loss ratio (elr) aka the target loss ratio, or permissible loss ratio
property Vehicle elrComp number.decimal {
    lookup([ExpModElrs], [ExpModElrs.elrComp],
        1
    )
}

property Vehicle isoPrimaryFactorComp number.decimal {
    lookup([IsoPrimaryFactor], [IsoPrimaryFactor.isoPrimaryFactorComp],
        isoWeightGroup,
        vehType,
        vehicleGpsIsoRadiusGroup,
        company->isoOperationType
    )
}

property Vehicle isoSecondaryFactorComp number.decimal {
    lookup ([IsoSecondaryFactor], [IsoSecondaryFactor.isoSecondaryFactorComp],
        vehType,
        company->commodityGroup,
        company->commodityDetail
    )
}

property Vehicle fleetSizeFactorComp number.decimal {
    lookup([FleetSizeFactor], [FleetSizeFactor.fleetSizeFactorComp],
        company->fleetPowerUnitCount,
        vehType,
        isoWeightGroup        
    )
}

property Vehicle longHaulStatedValueFactorComp number.decimal {
    lookup([LongHaulStatedValueFactorCollComp], [LongHaulStatedValueFactorCollComp.longHaulStatedValueFactorComp],
        statedValue,
        vehType
    )
}

property Vehicle longHaulOriginalCostNewFactorComp number.decimal {
    lookup([LongHaulOriginalCostNewFactorCollComp], [LongHaulOriginalCostNewFactorCollComp.longHaulOriginalCostNewFactorComp],
        costNew,
        vehType,
        vehicleAge
    )
}

property Vehicle statedValueFactorComp number.decimal {
    lookup([StatedValueFactorCollComp], [StatedValueFactorCollComp.statedValueFactorComp],
        statedValue,
        vehType
    )
}

property Vehicle originalCostNewFactorComp number.decimal {
    lookup([OriginalCostNewFactorCollComp], [OriginalCostNewFactorCollComp.originalCostNewFactorComp],
        costNew,
        vehType,
        vehicleAge
    )
}

property Vehicle naicFactorComp number.decimal {
    lookup([NaicFactor], [NaicFactor.naicFactorComp],
        company->naic,
        vehType
    )
}

property Vehicle compDeductibleFactor number.decimal {
    lookup([CompDeductibleFactor], [CompDeductibleFactor.compDeductibleFactor],
        company->quoteData->coverage->compDeductible,
        vehType
    )
}

property Vehicle longHaulCompDeductibleFactor number.decimal {
    lookup([CompDeductibleFactor], [CompDeductibleFactor.longHaulCompDeductibleFactor],
        company->quoteData->coverage->compDeductible,
        vehType
    )
}


//----- Liabilty premium calculation section

property Coverage coverageManualPremiumLiab number.decimal {
    sum(quoteData->company->vehicles, [Vehicle.vehPremiumLiab])
}

property Coverage coverageModPremiumLiab number.decimal {
    coverageManualPremiumLiab
    * quoteData->underwriting->scheduleModLiab
    * quoteData->underwriting->expModLiab
}

property Vehicle vehPremiumLiab number.decimal {
    switch(useZoneRating) {
        case True:
            longHaulBaseRateLiab *
                lcmLiabZone *
                isoPrimaryFactorLiab *
                    //other factors removed as they don't apply
                company->tierFactorLiab *
                ( increasedLimitFactor -
                longHaulLiabDeductibleFactor )
        case False:
            baseRateLiab *
                lcmLiab *
                isoPrimaryFactorLiab *
                isoSecondaryFactorLiab *
                fleetSizeFactorLiab *
                switch(company->quoteData->underwriting->useStatedValueLiab) {
                    case True: statedValueFactorLiab
                    case False: (originalCostNewFactorLiab * vehicleAgeFactorLiab)
                } *
                naicFactorLiab *
                company->tierFactorLiab *
                ( increasedLimitFactor -
                liabDeductibleFactor )
    }
}

property Vehicle vehPremiumLiabBasicLimitLiab number.decimal {
    switch(useZoneRating) {
        case True:
            longHaulBaseRateLiab *
                lcmLiabZone *
                isoPrimaryFactorLiab *
                    //other factors removed as they don't apply
                company->tierFactorLiab
        case False:
            baseRateLiab *
                lcmLiab *
                isoPrimaryFactorLiab *
                isoSecondaryFactorLiab *
                fleetSizeFactorLiab *
                switch(company->quoteData->underwriting->useStatedValueLiab) {
                    case True: statedValueFactorLiab
                    case False: (originalCostNewFactorLiab * vehicleAgeFactorLiab)
                } *
                naicFactorLiab *
                company->tierFactorLiab
    } * elrLiab
    //does not include increasedLimitFactor, liabDeductibleFactor
    //includes loss cost multiplier then reduces for permissible loss ratio aka elr
}

property Coverage coveragePremiumLiabBasicLimitLiab number.decimal {
    sum(quoteData->company->vehicles, [Vehicle.vehPremiumLiabBasicLimitLiab])
}

property Coverage avgPuPremiumLiabBasicLimitLiab number.decimal {
    coveragePremiumLiabBasicLimitLiab / number.decimal{quoteData->company->fleetPowerUnitCount}
}

property Vehicle longHaulBaseRateLiab number.decimal {
    lookup([LongHaulIsoBaseRate], [LongHaulIsoBaseRate.longHaulBaseRateLiab],
        vehicleStartZone,
        vehicleEndZone
    )
}

property Vehicle baseRateLiab number.decimal {
    lookup([IsoBaseRate], [IsoBaseRate.baseRateLiab],
        vehicleGarageIsoTerritory
    )
}

property Vehicle lcmLiab number.decimal {
    lookup([IsoLCM], [IsoLCM.lcmLiab],
        1
    )
}

property Vehicle lcmLiabZone number.decimal {
    lookup([IsoLCM], [IsoLCM.lcmLiabZone],
        1
    )
}

//expected loss ratio (elr) aka the target loss ratio, or permissible loss ratio
property Vehicle elrLiab number.decimal {
    lookup([ExpModElrs], [ExpModElrs.elrLiab],
        1
    )
}

property Vehicle isoPrimaryFactorLiab number.decimal {
    lookup([IsoPrimaryFactor], [IsoPrimaryFactor.isoPrimaryFactorLiab],
        isoWeightGroup,
        vehType,
        //isoRadiusGroup,
        vehicleGpsIsoRadiusGroup,
        company->isoOperationType
    )
}

property Vehicle isoSecondaryFactorLiab number.decimal {
    lookup ([IsoSecondaryFactor], [IsoSecondaryFactor.isoSecondaryFactorLiab],
        vehType,
        company->commodityGroup,
        company->commodityDetail
    )
}

property Vehicle fleetSizeFactorLiab number.decimal {
    lookup([FleetSizeFactor], [FleetSizeFactor.fleetSizeFactorLiab],
        company->fleetPowerUnitCount,
        vehType,
        isoWeightGroup        
    )
}

property Vehicle statedValueFactorLiab number.decimal {
    lookup([StatedValueFactorLiab], [StatedValueFactorLiab.value],
        statedValue,
        vehType,
        isoWeightGroup
    )
}

property Vehicle originalCostNewFactorLiab number.decimal {
    lookup([OriginalCostNewFactorLiab], [OriginalCostNewFactorLiab.value],
        costNew,
        vehType,
        isoWeightGroup
    )
}

property Vehicle vehicleAgeFactorLiab number.decimal {
    lookup([VehAgeFactorLiab], [VehAgeFactorLiab.value],
       vehicleAge
    )
}

property Vehicle naicFactorLiab number.decimal {
    lookup([NaicFactor], [NaicFactor.naicFactorLiab],
        company->naic,
        vehType
    )
}

property Vehicle increasedLimitFactor number.decimal {
    lookup([IncreasedLimitFactor], [IncreasedLimitFactor.value],
        company->quoteData->coverage->liabilityCSL,
        vehType,
        vehicleGpsIsoRadiusGroup,
        isoWeightGroup
    )
}

property Vehicle liabDeductibleFactor number.decimal {
    lookup([LiabDeductibleFactor], [LiabDeductibleFactor.liabDeductibleFactor],
        company->quoteData->coverage->liabilityDeductible
    )
}

property Vehicle longHaulLiabDeductibleFactor number.decimal {
    lookup([LiabDeductibleFactor], [LiabDeductibleFactor.longHaulLiabDeductibleFactor],
        company->quoteData->coverage->liabilityDeductible
    )
}


//----- Optional coverage rating section
//----- Includes: Specified Additional Insured, Specified Waiver of Subrogration, Blanket Additional Insured, Blanket Waiver of Subrogration
//-----           Broadened Pollution, Trailer Interchange

//----- Specified Additional insured


property Coverage specifiedAdditionalInsuredRate number.decimal {
    lookup([OptionalCoverageRates], [OptionalCoverageRates.specifiedAdditionalInsuredRate],
        1
        )
}

//----- Specified Waiver of Subrogation


property Coverage specifiedWaiverOfSubrogationRate number.decimal {
    lookup([OptionalCoverageRates], [OptionalCoverageRates.specifiedWaiverOfSubrogationRate],
        1
        )
}

//----- Broadened Pollution

property Coverage broadenedPollutionPrem number.decimal {
    coverageManualPremiumLiabBroadenedPollution
    * quoteData->underwriting->scheduleModLiab
    * quoteData->underwriting->expModLiab
}

property Coverage coverageManualPremiumLiabBroadenedPollution number.decimal {
    sum(quoteData->company->vehicles, [Vehicle.vehPremiumLiabBroadenedPollution])
}

property Vehicle vehPremiumLiabBroadenedPollution number.decimal {
    switch(useZoneRating) {
        case True:
            longHaulBaseRateLiab *
                lcmLiabZone *
                isoPrimaryFactorLiab *
                    //other factors removed as they don't apply
                company->tierFactorLiab *
                ( 1.0 -
                longHaulLiabDeductibleFactor ) *
                company->quoteData->coverage->broadenedPollutionRate
        case False:
            baseRateLiab *
                lcmLiab *
                isoPrimaryFactorLiab *
                isoSecondaryFactorLiab *
                fleetSizeFactorLiab *
                switch(company->quoteData->underwriting->useStatedValueLiab) {
                    case True: statedValueFactorLiab
                    case False: (originalCostNewFactorLiab * vehicleAgeFactorLiab)
                } *
                naicFactorLiab *
                company->tierFactorLiab *
                ( 1.0 -
                liabDeductibleFactor ) *
                company->quoteData->coverage->broadenedPollutionRate
    }
}
//same as vehPremiumLiab but set increasedLimitFactor = 1.00 and add broadenedPollutionRate

property Coverage broadenedPollutionRate number.decimal {
    lookup([OptionalCoverageRates], [OptionalCoverageRates.broadenedPollutionRate],
        1
        )
}

//----- Trailer Interchange

property Coverage trailerInterchangePrem number.decimal {
    (trailerInterchangeLongHaulRate * number.decimal{quoteData->company->trailerInterchangeTrailerLongHaulCount} ) +
    (trailerInterchangeIntermediateRate * number.decimal{quoteData->company->trailerInterchangeTrailerIntermediateCount} ) +
    (trailerInterchangeLocalRate * number.decimal{quoteData->company->trailerInterchangeTrailerLocalCount} )
}

property Coverage trailerInterchangeLongHaulRate number.decimal {
    lookup([OptionalCoverageRates], [OptionalCoverageRates.trailerInterchangeLongHaulRate],
        1
        )
}

property Coverage trailerInterchangeIntermediateRate number.decimal {
    lookup([OptionalCoverageRates], [OptionalCoverageRates.trailerInterchangeIntermediateRate],
        1
        )
}

property Coverage trailerInterchangeLocalRate number.decimal {
    lookup([OptionalCoverageRates], [OptionalCoverageRates.trailerInterchangeLocalRate],
        1
        )
}

//----- Blanket Additional insured

property Coverage blanketAdditionalInsuredPrem number.decimal {
    blanketAdditionalInsuredRate
}

property Coverage blanketAdditionalInsuredRate number.decimal {
    lookup([OptionalCoverageRates], [OptionalCoverageRates.blanketAdditionalInsuredRate],
        1
        )
}

//----- Blanket Waiver of Subrogation

property Coverage blanketWaiverOfSubrogationPrem number.decimal {
    blanketWaiverOfSubrogationRate
}

property Coverage blanketWaiverOfSubrogationRate number.decimal {
    lookup([OptionalCoverageRates], [OptionalCoverageRates.blanketWaiverOfSubrogationRate],
        1
        )
}


//----- Standard and Complete PD packages

property Coverage standardPdPackagePrem number.decimal {
    standardPdPackageRate *
        number.decimal{quoteData->company->fleetPowerUnitCount}
}

property Coverage standardPdPackageRate number.decimal {
    lookup([OptionalCoverageRates], [OptionalCoverageRates.standardPdPackageRate],
        1
        )
}

property Coverage completePdPackagePrem number.decimal {
    completePdPackageRate *
        number.decimal{quoteData->company->fleetPowerUnitCount}
}

property Coverage completePdPackageRate number.decimal {
    lookup([OptionalCoverageRates], [OptionalCoverageRates.completePdPackageRate],
        1
        )
}
