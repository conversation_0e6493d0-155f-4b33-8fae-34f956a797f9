//----- Premiums
//----- Collision/Comprehensive Premium Modification factor
//----- All Package Specific Optionals (Specified/Blanket Waiver of Subro, Specified/Blanket Additional Insured, Broadened Pollution, Trailer Interchange)



//----- Collision/Comprehensive Premium Modification factor

property Coverage collCompMod number.decimal {
    switch(hasPDCoverage) {
        case False:
            0.0
        case True:
            coverageModPremiumColl /
            (coverageModPremiumColl + coverageModPremiumComp)
    }
}



//----- Optional coverage rating section
//----- Includes: Specified Additional Insured, Specified Waiver of Subrogration, Blanket Additional Insured, Blanket Waiver of Subrogration
//-----           Broadened Pollution, Trailer Interchange

//----- Specified Additional insured


property Coverage specifiedAdditionalInsuredRate number.decimal {
    lookup([OptionalCoverageRates], [OptionalCoverageRates.specifiedAdditionalInsuredRate],
        1
        )
}

//----- Specified Waiver of Subrogation


property Coverage specifiedWaiverOfSubrogationRate number.decimal {
    lookup([OptionalCoverageRates], [OptionalCoverageRates.specifiedWaiverOfSubrogationRate],
        1
        )
}

//----- Broadened Pollution

property Coverage broadenedPollutionPrem number.decimal {
    coverageManualPremiumLiabBroadenedPollution
    * quoteData->underwriting->scheduleModLiab
    * quoteData->underwriting->expModLiab
}

property Coverage coverageManualPremiumLiabBroadenedPollution number.decimal {
    sum(quoteData->company->vehicles, [Vehicle.vehPremiumLiabBroadenedPollution])
}

property Vehicle vehPremiumLiabBroadenedPollution number.decimal {
    switch(useZoneRating) {
        case True:
            longHaulBaseRateLiab *
                lcmLiabZone *
                territoryAdjFactorLongHaulLiab *
                isoPrimaryFactorLiab *
                    //other factors removed as they don't apply
                company->tierFactorLiab *
                ( 1.0 -
                ( 1.0 - longHaulLiabDeductibleFactor ) ) *
                company->quoteData->coverage->broadenedPollutionRate *
                company->pipFactorLiab *
                company->yearsInBusinessFactor
        case False:
            baseRateLiab *
                lcmLiab *
                territoryAdjFactorLiab *
                isoPrimaryFactorLiab *
                isoSecondaryFactorLiab *
                fleetSizeFactorLiab *
                switch(company->quoteData->underwriting->useStatedValueLiab) {
                    case True: statedValueFactorLiab
                    case False: (originalCostNewFactorLiab * vehicleAgeFactorLiab)
                } *
                naicFactorLiab *
                company->tierFactorLiab *
                ( 1.0 -
                ( 1.0 - liabDeductibleFactor ) ) *
                company->quoteData->coverage->broadenedPollutionRate *
                company->yearsInBusinessFactor
    }
}
//same as vehPremiumLiab but set increasedLimitFactor = 1.00 and add broadenedPollutionRate

property Coverage broadenedPollutionRate number.decimal {
    lookup([OptionalCoverageRates], [OptionalCoverageRates.broadenedPollutionRate],
        1
        )
}

//----- Trailer Interchange

property Coverage trailerInterchangePrem number.decimal {
    coverageModPremiumTrailerInterchangeComp +
    coverageModPremiumTrailerInterchangeColl
}

property Coverage coverageModPremiumTrailerInterchangeColl number.decimal {
    coverageManualPremiumTrailerInterchangeColl
    * quoteData->underwriting->scheduleModColl
    * quoteData->underwriting->expModColl
}

property Coverage coverageManualPremiumTrailerInterchangeColl number.decimal {
    sum(quoteData->company->vehicles, [Vehicle.vehPremiumTrailerInterchangeColl])
}

property Vehicle vehPremiumTrailerInterchangeColl number.decimal {
    switch(nonOwnedSemiTrailerFlag) {
        case False:
            0.0
        case True:
        switch(useZoneRating) {
            case True:
                longHaulBaseRateColl *
                    lcmCollZone *
                    territoryAdjFactorLongHaulColl *
                    isoPrimaryFactorColl *
                        ( 
                            switch(company->quoteData->underwriting->useStatedValueCollComp) {
                                case True:
                                    number_max( ( longHaulStatedValueFactorColl - longHaulCollDeductibleFactor ), 0.1 )
                                case False:
                                    number_max( ( longHaulOriginalCostNewFactorColl - longHaulCollDeductibleFactor ), 0.1 ) *
                                    longHaulVehicleAgeFactorColl
                            }
                        ) *
                    company->tierFactorColl *
                    company->yearsInBusinessFactor
            case False:
                baseRateColl *
                    lcmColl *
                    territoryAdjFactorColl *
                    isoPrimaryFactorColl *
                    isoSecondaryFactorColl *
                    fleetSizeFactorColl *
                        number_max( 
                            (switch(company->quoteData->underwriting->useStatedValueCollComp) {
                                case True: statedValueFactorColl
                                case False: originalCostNewFactorColl
                            }  -  collDeductibleFactor), 0.1) *
                    naicFactorColl *
                    company->tierFactorColl *
                    company->yearsInBusinessFactor
        }
    }
}

property Coverage coverageModPremiumTrailerInterchangeComp number.decimal {
    coverageManualPremiumTrailerInterchangeComp
    * quoteData->underwriting->scheduleModComp
    * quoteData->underwriting->expModComp
}

property Coverage coverageManualPremiumTrailerInterchangeComp number.decimal {
    sum(quoteData->company->vehicles, [Vehicle.vehPremiumTrailerInterchangeComp])
}

property Vehicle vehPremiumTrailerInterchangeComp number.decimal {
    switch(nonOwnedSemiTrailerFlag) {
        case False:
            0.0
        case True:
        switch(useZoneRating) {
            case True:
                longHaulBaseRateComp *
                    lcmCompZone *
                    territoryAdjFactorLongHaulComp *
                    isoPrimaryFactorComp *
                        ( 
                            switch(company->quoteData->underwriting->useStatedValueCollComp) {
                                case True:
                                    number_max( ( longHaulStatedValueFactorComp - longHaulCompDeductibleFactor ), 0.1 )
                                case False:
                                    number_max( (  longHaulOriginalCostNewFactorComp - longHaulCompDeductibleFactor ), 0.1) *
                                    longHaulVehicleAgeFactorComp
                            }
                        ) *
                    company->tierFactorComp *
                    company->yearsInBusinessFactor
            case False:
                baseRateComp *
                    lcmComp *
                    territoryAdjFactorComp *
                    isoPrimaryFactorComp *
                    isoSecondaryFactorComp *
                    fleetSizeFactorComp *
                        number_max(
                            (switch(company->quoteData->underwriting->useStatedValueCollComp) {
                                case True: statedValueFactorComp
                                case False: originalCostNewFactorComp
                            } -  compDeductibleFactor), 0.1) *
                    naicFactorComp *
                    company->tierFactorComp *
                    company->yearsInBusinessFactor
        }
    }
}

//----- Blanket Additional insured

property Coverage blanketAdditionalInsuredPrem number.decimal {
    blanketAdditionalInsuredRate
}

property Coverage blanketAdditionalInsuredRate number.decimal {
    lookup([OptionalCoverageRates], [OptionalCoverageRates.blanketAdditionalInsuredRate],
        1
        )
}

//----- Blanket Waiver of Subrogation

property Coverage blanketWaiverOfSubrogationPrem number.decimal {
    blanketWaiverOfSubrogationRate
}

property Coverage blanketWaiverOfSubrogationRate number.decimal {
    lookup([OptionalCoverageRates], [OptionalCoverageRates.blanketWaiverOfSubrogationRate],
        1
        )
}


//----- Standard and Complete PD packages

property Coverage standardPdPackagePrem number.decimal {
    standardPdPackageRate *
        number.decimal{quoteData->company->fleetPowerUnitCount}
}

property Coverage standardPdPackageRate number.decimal {
    lookup([OptionalCoverageRates], [OptionalCoverageRates.standardPdPackageRate],
        1
        )
}

property Coverage completePdPackagePrem number.decimal {
    completePdPackageRate *
        number.decimal{quoteData->company->fleetPowerUnitCount}
}

property Coverage completePdPackageRate number.decimal {
    lookup([OptionalCoverageRates], [OptionalCoverageRates.completePdPackageRate],
        1
        )
}

//----- Additional Towing Limit
//----- This is the option to add an addition limit amount to the COMPLETE package's towing Limit
//----- Complete package towing limit default = 25000. Subtract this from the towingLimit input.

property Coverage completeAddlTowingLimitPrem number.decimal {
    0.005 * 
        number.decimal{quoteData->company->fleetTowingUnitCount} *
        ( number.decimal{towingLimit} - 25000.0 )
}
