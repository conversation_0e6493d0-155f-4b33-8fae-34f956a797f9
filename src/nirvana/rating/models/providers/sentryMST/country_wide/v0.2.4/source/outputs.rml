//Non Premium Metrics, shown on quote as well

output OutputsQuote fleetPowerUnitCount number.decimal {
    number.decimal{quoteData->company->fleetPowerUnitCount}
}

property Vehicle statedValueTiv number.decimal {
    switch(vehType) {
        case "non_owned_semi_trailer": 0.0
        case _: statedValue
    }
}

output OutputsQuote tiv number.decimal {
    sum(quoteData->company->vehicles, [Vehicle.statedValueTiv]) 
}

//Positive values equal savings. eg 0.05 = 5% savings, -0.05 = 5% surcharge

output OutputsQuote safetyDiscountPercentDisplay number.decimal {
    0.00 - quoteData->underwriting->safetyModAllCov
}

output OutputsQuote temp_safetyDiscountPercent number.decimal {
    1.00 - 
    1.00 / (1.00 - quoteData->underwriting->safetyModAllCov)
}

output OutputsQuote tierFactorMTCDisplay number.decimal {
    number.decimal{quoteData->company->tierFactorMTCV2}
}


//Coverage Triggers

output OutputsQuote policyPremiumUm number.decimal {
    switch(quoteData->coverage->hasUMCoverage) {
    case True:
            quoteData->coverage->coverageModPremiumUm
    case False:
            0.0
    }
}

output OutputsQuote policyPremiumUim number.decimal {
    switch(quoteData->coverage->hasUIMCoverage) {
    case True:
            quoteData->coverage->coverageModPremiumUim
    case False:
            0.0
    }
}

output OutputsQuote policyPremiumUmUim number.decimal {
    switch(quoteData->coverage->hasUMUIMCoverage) {
    case True:
            quoteData->coverage->coverageModPremiumUmUim
    case False:
            0.0
    }
}

output OutputsQuote policyPremiumUmpd number.decimal {
    switch(quoteData->coverage->hasUMPDCoverage) {
    case True:
            quoteData->coverage->coverageModPremiumUmpd
    case False:
           0.0
    }
}

output OutputsQuote policyPremiumMedPay number.decimal {
    switch(quoteData->coverage->hasMedPayCoverage) {
    case True:
            quoteData->coverage->coverageModPremiumMedPay
    case False:
            0.0
    }
}

output OutputsQuote policyPremiumPip number.decimal {
    switch(quoteData->coverage->hasPIPCoverage) {
case True:
            quoteData->coverage->coverageModPremiumPip
case False:
            0.0
    }
}
 
output OutputsQuote policyPremiumPpi number.decimal {
    switch(quoteData->coverage->hasPPICoverage) {
case True:
            quoteData->coverage->coverageModPremiumPpi
case False:
            0.0
    }
}
 
output OutputsQuote policyPremiumPipEac number.decimal {
    switch(quoteData->coverage->hasPIPExcessAttendantCareCoverage) {
case True:
            quoteData->coverage->coverageModPremiumPipEac
case False:
            0.0
    }
}
 
output OutputsQuote policyPremiumGuestPip number.decimal {
    switch(quoteData->coverage->hasGuestPIPCoverage) {
case True:
            quoteData->coverage->coverageModPremiumGuestPip
case False:
            0.0
    }
}

output OutputsQuote policyPremiumReeferBreakdown number.decimal {
    quoteData->coverage->reeferBreakdownPremium
}

output OutputsQuote policyPremiumReeferBreakdownWithHumanError number.decimal {
    quoteData->coverage->reeferBreakdownWithHumanErrorPremium
}

output OutputsQuote policyPremiumNamedShipper number.decimal {
    quoteData->coverage->mtcNamedShipperPolicyPremium
}

output OutputsQuote policyPremiumBroadenedPollution number.decimal{
    quoteData->coverage->broadenedPollutionPrem
}

output OutputsQuote policyPremiumGlBase number.decimal {
        (   quoteData->raterGL->glBaseRate * 
            ( quoteData->raterGL->glFactorLimit - quoteData->raterGL->glFactorMinusDeductible) *
            quoteData->raterGL->glFactorSizeFleet
        ) *
        quoteData->raterGL->companyPUFleetCount
}

output OutputsQuote policyPremiumGlContractual number.decimal {
    quoteData->raterGL->glRateContractual * quoteData->raterGL->companyPUFleetCount
}

output OutputsQuote policyPremiumGlMisdeliveryofLiquids number.decimal {
    quoteData->raterGL->glRateMisdeliveryLiquids * quoteData->raterGL->companyPUFleetCount
}

output OutputsQuote policyPremiumGlStopGap number.decimal {
    quoteData->raterGL->glRateStopGap * quoteData->raterGL->companyPUFleetCount
}
// added for Terminal-TI

output Terminal policyPremiumCargoAtTerminal number.decimal {
        switch(company->quoteData->coverage->hasMtcTerminal) {
        case True:
            (terminalLimitNumeric/100.0)*
            terminalBaseRateFactor*
            terminalDeductibleFactor*
            privateTheftProtectionFactor*
            privateFireProtectionFactor
        case False:
        0.0}
}

output Company sumPolicyPremiumCargoAtTerminal number.decimal {
    sum(terminals, [Terminal.policyPremiumCargoAtTerminal]) 
}

output OutputsQuote totalPolicyPremiumCargoAtTerminal number.decimal {
    quoteData->company->sumPolicyPremiumCargoAtTerminal
}

output OutputsQuote policyPremiumMtcTrailerInterchange number.decimal {
    quoteData->coverage->mtcTrailerInterchangePolicyPremium
}

output OutputsQuote policyPremiumTrailerInterchange number.decimal {
    quoteData->coverage->trailerInterchangePrem
}

output OutputsQuote policyPremiumStandardApdPackage number.decimal {
    quoteData->coverage->standardPdPackagePrem
}

output OutputsQuote policyPremiumCompleteApdPackage number.decimal {
    quoteData->coverage->completePdPackagePrem +
    quoteData->coverage->completeAddlTowingLimitPrem
}

output OutputsQuote policyPremiumLiabBase number.decimal {
    round(
        switch(quoteData->coverage->hasNegotiatedRate) {
            case True: quoteData->coverage->negotiatedModRateLiab
            case False: quoteData->coverage->coverageModPremiumLiab
        }
    ,2)
}

output OutputsQuote policyPremiumCollBase number.decimal {
    round(
        switch(quoteData->coverage->hasPDCoverage) {
            case True:
                switch(quoteData->coverage->hasNegotiatedRate) {
                    case True: quoteData->coverage->negotiatedModRateColl
                    case False: quoteData->coverage->coverageModPremiumColl
                }
            case False:
                0.0
        }
    ,2)    
}

output OutputsQuote policyPremiumCompBase number.decimal {
    round(
        switch(quoteData->coverage->hasPDCoverage) {
            case True:
                switch(quoteData->coverage->hasNegotiatedRate) {
                    case True: quoteData->coverage->negotiatedModRateComp
                    case False: quoteData->coverage->coverageModPremiumComp
                }
            case False:
                0.0
        }
    ,2)
}

output OutputsQuote policyPremiumMtcBase number.decimal {
    round(
        switch(quoteData->coverage->hasMTCCoverage) {
            case True: quoteData->coverage->coverageModPremiumMTC
            case False: 0.0
        }
    ,2)
}

// ----- Flat / Fee Charges

output OutputsQuote perEndtPremiumSpecifiedAdditionalInsured number.decimal {
    quoteData->coverage->specifiedAdditionalInsuredRate
}

output OutputsQuote perEndtPremiumSpecifiedWaiverOfSubrogation number.decimal {
    quoteData->coverage->specifiedWaiverOfSubrogationRate
}

output OutputsQuote policyPremiumBlanketAdditionalInsured number.decimal {
    quoteData->coverage->blanketAdditionalInsuredPrem
}

output OutputsQuote policyPremiumBlanketWaiverOfSubrogation number.decimal {
    quoteData->coverage->blanketWaiverOfSubrogationPrem
}

output OutputsQuote perEndtPremiumGlSpecifiedAdditionalInsured number.decimal {
    quoteData->raterGL->rateGlSpecifiedAdditionalInsured
}

output OutputsQuote perEndtPremiumGlSpecifiedWaiverOfSubrogation number.decimal {
    quoteData->raterGL->rateGlSpecifiedWaiverOfSubrogation
}

output OutputsQuote policyPremiumGlBlanketAdditionalInsured number.decimal {
    quoteData->raterGL->packageGlBlanketAdditionalInsured
}

output OutputsQuote policyPremiumGlBlanketWaiverOfSubrogation number.decimal {
    quoteData->raterGL->packageGlBlanketWaiverOfSubrogation
}

output OutputsQuote perEndtPremiumSpecifiedAiPNC number.decimal {
    switch(quoteData->company->companyGarageState) {
        case "pa": 150.0
        case "ok": 150.0
        case "oh": 150.0
        case "mo": 150.0
        case "ks": 150.0
        case _: 100.0
    }
}

output OutputsQuote policyPremiumBlanketAiPNC number.decimal {
    switch(quoteData->company->companyGarageState) {
        case "pa": 450.0
        case "ok": 450.0
        case "oh": 450.0
        case "mo": 450.0
        case "ks": 450.0
        case _: 500.0
    }
}


//Complete Package

output OutputsQuote safetyDiscountPremiumCompletePackage number.decimal {
    temp_safetyDiscountPercent * totalCompletePolicyPremium
}

output OutputsQuote liabCompletePolicyPremium number.decimal {
    flatCompletePolicyPremium +
    nonFlatLiabCompletePolicyPremium
}

output OutputsQuote liabCompletePolicyPremiumPpu number.decimal {
    liabCompletePolicyPremium / fleetPowerUnitCount
}

output OutputsQuote physCompletePolicyPremium number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
    case True: nonFlatPhysCompletePolicyPremium
    case False: 0.0
    }
}

output OutputsQuote physCompletePolicyPremiumPtiv number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
    case True: physCompletePolicyPremium / tiv
    case False: 0.0
    }
}

output OutputsQuote glCompletePolicyPremium number.decimal {
    switch(quoteData->coverage->hasGlCoverage) {
    case True:
            round(
            quoteData->coverage->coverageModPremiumGl +
            quoteData->coverage->completeGlPackageRate
            )
    case False:
            0.0
    }
}

output OutputsQuote policyPremiumMtcDebrisRemoval number.decimal {
   quoteData->coverage->mtcDebrisRemovalPremium
}

output OutputsQuote policyPremiumMtcEarnedFreight number.decimal {
   quoteData->coverage->mtcEarnedFreightPremium
}

output OutputsQuote policyPremiumMtcPollutantClean number.decimal {
   quoteData->coverage->mtcPollutantCleanPremium
}

output OutputsQuote policyPremiumMtcLossMitigation number.decimal {
   quoteData->coverage->mtcLossMitigationPremium
}

output OutputsQuote policyPremiumMtcMiscEquipment number.decimal {
   quoteData->coverage->mtcMiscEquipmentPremium
}

output OutputsQuote mtcCompletePolicyPremium number.decimal {
    switch(quoteData->coverage->hasMTCCoverage) {
        case True:
            round(
                (quoteData->coverage->coverageModPremiumMTC +
                quoteData->coverage->reeferBreakdownPremium +
                quoteData->coverage->reeferBreakdownWithHumanErrorPremium +
                quoteData->company->sumPolicyPremiumCargoAtTerminal+
                quoteData->coverage->mtcTrailerInterchangePolicyPremium+
                quoteData->coverage->mtcNamedShipperPolicyPremium)
            )
        case False:
            0.0
    }
}

output OutputsQuote mtcCompletePolicyPremiumPpu number.decimal {
    mtcCompletePolicyPremium / fleetPowerUnitCount
}

output OutputsQuote flatCompletePolicyPremium number.decimal {
    round(
    quoteData->coverage->blanketAdditionalInsuredPrem +
    quoteData->coverage->blanketWaiverOfSubrogationPrem
    )
}

output OutputsQuote nonFlatLiabCompletePolicyPremium number.decimal {
    round(
    quoteData->coverage->coverageFinalModPremiumLiab +
    quoteData->coverage->coverageSubhaulPremiumLiab +
    policyPremiumMedPay +
    policyPremiumPip +
    policyPremiumPpi +
    policyPremiumPipEac +
    policyPremiumGuestPip +
    policyPremiumUm +
    policyPremiumUim +
    policyPremiumUmUim +
    policyPremiumUmpd +
    quoteData->coverage->broadenedPollutionPrem
    )
}

output OutputsQuote nonFlatLiabCompletePolicyPremiumPpu number.decimal {
    nonFlatLiabCompletePolicyPremium / fleetPowerUnitCount
}

output OutputsQuote nonFlatPhysCompletePolicyPremium number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
    case True:
            round(
            quoteData->coverage->coverageFinalModPremiumPhys +
            quoteData->coverage->trailerInterchangePrem +
            quoteData->coverage->completePdPackagePrem +
            quoteData->coverage->completeAddlTowingLimitPrem
            )
    case False:
            0.0
    }
}

output OutputsQuote coverageFinalModPremiumCollComplete number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
    case True:
            round(
            nonFlatPhysCompletePolicyPremium *
            quoteData->coverage->collCompMod
            )
    case False:
            0.0
    }
}

output OutputsQuote coverageFinalModPremiumCompComplete number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
    case True:
            nonFlatPhysCompletePolicyPremium -
            coverageFinalModPremiumCollComplete
    case False:
            0.0
    }
}

output OutputsQuote nonFlatPhysCompletePolicyPremiumPtiv number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
    case True: nonFlatPhysCompletePolicyPremium / tiv
    case False: 0.0
    }
}


//Standard Package

output OutputsQuote safetyDiscountPremiumStandardPackage number.decimal {
    temp_safetyDiscountPercent * totalStandardPolicyPremium
}

output OutputsQuote liabStandardPolicyPremium number.decimal {
    flatStandardPolicyPremium +
    nonFlatLiabStandardPolicyPremium
}

output OutputsQuote liabStandardPolicyPremiumPpu number.decimal {
    liabStandardPolicyPremium / fleetPowerUnitCount
}

output OutputsQuote physStandardPolicyPremium number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
    case True: nonFlatPhysStandardPolicyPremium
    case False: 0.0
    }
}

output OutputsQuote physStandardPolicyPremiumPtiv number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
    case True: physStandardPolicyPremium / tiv
    case False: 0.0
    }
}

output OutputsQuote glStandardPolicyPremium number.decimal {
    switch(quoteData->coverage->hasGlCoverage) {
    case True:
            round(
            quoteData->coverage->coverageModPremiumGl +
            quoteData->coverage->standardGlPackageRate
            )
    case False:
            0.0
    }
}

output OutputsQuote mtcStandardPolicyPremium number.decimal {
    switch(quoteData->coverage->hasMTCCoverage) {
    case True:
            round(
            quoteData->coverage->coverageModPremiumMTC +
            quoteData->coverage->reeferBreakdownPremium +
            quoteData->coverage->reeferBreakdownWithHumanErrorPremium +
            quoteData->company->sumPolicyPremiumCargoAtTerminal+
            quoteData->coverage->mtcTrailerInterchangePolicyPremium+
            quoteData->coverage->mtcNamedShipperPolicyPremium
            )
    case False:
            0.0
    }
}

output OutputsQuote mtcStandardPolicyPremiumPpu number.decimal {
    mtcStandardPolicyPremium / fleetPowerUnitCount
}

output OutputsQuote flatStandardPolicyPremium number.decimal {
    round(
    quoteData->coverage->blanketAdditionalInsuredPrem
    )
}

output OutputsQuote nonFlatLiabStandardPolicyPremium number.decimal {
    round(
    quoteData->coverage->coverageFinalModPremiumLiab +
    quoteData->coverage->coverageSubhaulPremiumLiab +
    policyPremiumMedPay +
    policyPremiumPip +
    policyPremiumPpi +
    policyPremiumPipEac +
    policyPremiumGuestPip +
    policyPremiumUm +
    policyPremiumUim +
    policyPremiumUmUim +
    policyPremiumUmpd +
    quoteData->coverage->broadenedPollutionPrem
    )
}

output OutputsQuote nonFlatLiabStandardPolicyPremiumPpu number.decimal {
    nonFlatLiabStandardPolicyPremium / fleetPowerUnitCount
}

output OutputsQuote nonFlatPhysStandardPolicyPremium number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
    case True:
            round(
            quoteData->coverage->coverageFinalModPremiumPhys +
            quoteData->coverage->trailerInterchangePrem +
            quoteData->coverage->standardPdPackagePrem
            )
    case False:
            0.0
    }
}

output OutputsQuote coverageFinalModPremiumCollStandard number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
    case True:
            round(
            nonFlatPhysStandardPolicyPremium *
            quoteData->coverage->collCompMod
            )
    case False:
            0.0
    }
}

output OutputsQuote coverageFinalModPremiumCompStandard number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
    case True:
            nonFlatPhysStandardPolicyPremium -
            coverageFinalModPremiumCollStandard
    case False:
            0.0
    }
}

output OutputsQuote nonFlatPhysStandardPolicyPremiumPtiv number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
    case True: nonFlatPhysStandardPolicyPremium / tiv
    case False: 0.0
    }
}


//Basic Package

output OutputsQuote safetyDiscountPremiumBasicPackage number.decimal {
    temp_safetyDiscountPercent * totalBasicPolicyPremium
}

output OutputsQuote liabBasicPolicyPremium number.decimal {
    flatBasicPolicyPremium +
    nonFlatLiabBasicPolicyPremium
}

output OutputsQuote liabBasicPolicyPremiumPpu number.decimal {
    liabBasicPolicyPremium / fleetPowerUnitCount
}

output OutputsQuote physBasicPolicyPremium number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
    case True: nonFlatPhysBasicPolicyPremium
    case False: 0.0
    }
}

output OutputsQuote physBasicPolicyPremiumPtiv number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
    case True: physBasicPolicyPremium / tiv
    case False: 0.0
    }
}

output OutputsQuote glBasicPolicyPremium number.decimal {
    switch(quoteData->coverage->hasGlCoverage) {
    case True:
            round(
            quoteData->coverage->coverageModPremiumGl +
            quoteData->coverage->basicGlPackageRate
            )
    case False:
            0.0
    }
}

output OutputsQuote mtcBasicPolicyPremium number.decimal {
    switch(quoteData->coverage->hasMTCCoverage) {
    case True:
            round(
            quoteData->coverage->coverageModPremiumMTC +
            quoteData->coverage->reeferBreakdownPremium +
            quoteData->coverage->reeferBreakdownWithHumanErrorPremium +
            quoteData->company->sumPolicyPremiumCargoAtTerminal+
            quoteData->coverage->mtcTrailerInterchangePolicyPremium+
            quoteData->coverage->mtcNamedShipperPolicyPremium
            )
    case False:
            0.0
    }
}

output OutputsQuote mtcBasicPolicyPremiumPpu number.decimal {
    mtcBasicPolicyPremium / fleetPowerUnitCount
}

output OutputsQuote flatBasicPolicyPremium number.decimal {
    0.0
}

output OutputsQuote nonFlatLiabBasicPolicyPremium number.decimal {
    round(
    quoteData->coverage->coverageFinalModPremiumLiab +
    quoteData->coverage->coverageSubhaulPremiumLiab +
    policyPremiumMedPay +
    policyPremiumPip +
    policyPremiumPpi +
    policyPremiumPipEac +
    policyPremiumGuestPip +
    policyPremiumUm +
    policyPremiumUim +
    policyPremiumUmUim +
    policyPremiumUmpd
    )
}

output OutputsQuote nonFlatLiabBasicPolicyPremiumPpu number.decimal {
    nonFlatLiabBasicPolicyPremium / fleetPowerUnitCount
}

output OutputsQuote nonFlatPhysBasicPolicyPremium number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
    case True:
            round(
            quoteData->coverage->coverageFinalModPremiumPhys
            )
    case False:
            0.0
    }
}

output OutputsQuote coverageFinalModPremiumCollBasic number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
    case True:
            round(
            nonFlatPhysBasicPolicyPremium *
            quoteData->coverage->collCompMod
            )
    case False:
            0.0
    }
}

output OutputsQuote coverageFinalModPremiumCompBasic number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
    case True:
            nonFlatPhysBasicPolicyPremium -
            coverageFinalModPremiumCollBasic
    case False:
            0.0
    }
}

output OutputsQuote nonFlatPhysBasicPolicyPremiumPtiv number.decimal {
    switch(quoteData->coverage->hasPDCoverage) {
    case True: nonFlatPhysBasicPolicyPremium / tiv
    case False: 0.0
    }
}


//----Unmodified Premium Outputs (premiums with sch mod = 1.0 & excluding surcharges)

// Complete package

output OutputsQuote totalCompletePolicyPremiumUnmodified number.decimal {
    liabCompletePolicyPremiumUnmodified +
    physCompletePolicyPremiumUnmodified +
    glCompletePolicyPremiumUnmodified +
    mtcCompletePolicyPremiumUnmodified
}

// Standard package

output OutputsQuote totalStandardPolicyPremiumUnmodified number.decimal {
    liabStandardPolicyPremiumUnmodified +
    physStandardPolicyPremiumUnmodified +
    glStandardPolicyPremiumUnmodified +
    mtcStandardPolicyPremiumUnmodified
}

// Basic package

output OutputsQuote totalBasicPolicyPremiumUnmodified number.decimal {
    liabBasicPolicyPremiumUnmodified +
    physBasicPolicyPremiumUnmodified +
    glBasicPolicyPremiumUnmodified +
    mtcBasicPolicyPremiumUnmodified
}

// Primary Coverage Level

output OutputsQuote liabCompletePolicyPremiumUnmodified number.decimal {
    round(
    switch(quoteData->coverage->hasNegotiatedRate) {
        case True:
            quoteData->coverage->negotiatedModRateLiab
        case False:
            quoteData->coverage->coverageModPremiumLiab / quoteData->underwriting->scheduleModLiab
        } +
    quoteData->coverage->broadenedPollutionPrem / quoteData->underwriting->scheduleModLiab +
        // ^no switch here because policy will always have Liab & Complete package always gets Broadened Poll
    switch(quoteData->coverage->hasMedPayCoverage) {
        case True:
            quoteData->coverage->coverageModPremiumMedPay / quoteData->underwriting->scheduleModMedPay
        case False:
            0.0
        } +
    switch(quoteData->coverage->hasUMCoverage) {
        case True:
            quoteData->coverage->coverageModPremiumUm / quoteData->underwriting->scheduleModUms
        case False:
            0.0
        } +
    switch(quoteData->coverage->hasUIMCoverage) {
        case True:
            quoteData->coverage->coverageModPremiumUim / quoteData->underwriting->scheduleModUms
        case False:
            0.0
        } +
    switch(quoteData->coverage->hasUMUIMCoverage) {
        case True:
            quoteData->coverage->coverageModPremiumUmUim / quoteData->underwriting->scheduleModUms
        case False:
            0.0
        } +
    switch(quoteData->coverage->hasUMPDCoverage) {
        case True:
            quoteData->coverage->coverageModPremiumUmpd / quoteData->underwriting->scheduleModUms
        case False:
            0.0
        } +
    switch(quoteData->coverage->hasPIPCoverage) {
        case True:
            quoteData->coverage->coverageModPremiumPip / quoteData->underwriting->scheduleModMedPay
        case False:
            0.0
        } +
    switch(quoteData->coverage->hasGuestPIPCoverage) {
        case True:
            quoteData->coverage->coverageModPremiumGuestPip / quoteData->underwriting->scheduleModMedPay
        case False:
            0.0
        } +
    switch(quoteData->coverage->hasPPICoverage) {
        case True:
            quoteData->coverage->coverageModPremiumPpi
        case False:
            0.0
        } +
    switch(quoteData->coverage->hasPIPExcessAttendantCareCoverage) {
        case True:
            quoteData->coverage->coverageModPremiumPipEac
        case False:
            0.0
    } +
    switch(quoteData->coverage->hasNegotiatedRate) {
        case True:
            quoteData->coverage->coverageSubhaulPremiumLiab
        case False:
            quoteData->coverage->coverageSubhaulPremiumLiab / quoteData->underwriting->scheduleModLiab
        } +
    flatCompletePolicyPremium
    )
}

output OutputsQuote physCompletePolicyPremiumUnmodified number.decimal {
    round(
    switch(quoteData->coverage->hasPDCoverage) {
        case True:
            switch(quoteData->coverage->hasNegotiatedRate) {
                case True:
                    quoteData->coverage->negotiatedModRateColl
                case False:
                    quoteData->coverage->coverageModPremiumColl / quoteData->underwriting->scheduleModColl
            }
        case False:
            0.0
    } +
    switch(quoteData->coverage->hasPDCoverage) {
        case True:
            switch(quoteData->coverage->hasNegotiatedRate) {
                case True:
                    quoteData->coverage->negotiatedModRateComp
                case False:
                    quoteData->coverage->coverageModPremiumComp / quoteData->underwriting->scheduleModComp
            }
        case False:
            0.0
    } +
    switch(quoteData->coverage->hasPDCoverage) {
        case True:
            quoteData->coverage->coverageModPremiumTrailerInterchangeColl / quoteData->underwriting->scheduleModColl
        case False:
            0.0
    } +
    switch(quoteData->coverage->hasPDCoverage) {
        case True:
            quoteData->coverage->coverageModPremiumTrailerInterchangeComp / quoteData->underwriting->scheduleModComp
        case False:
            0.0
    } +
    switch(quoteData->coverage->hasPDCoverage) {
        case True:
            quoteData->coverage->completePdPackagePrem
        case False:
            0.0
    } +
    switch(quoteData->coverage->hasPDCoverage) {
        case True:
            quoteData->coverage->completeAddlTowingLimitPrem
        case False:
            0.0
    }
    )
}

output OutputsQuote glCompletePolicyPremiumUnmodified number.decimal {
    round(
    switch(quoteData->coverage->hasGlCoverage) {
        case True:
            quoteData->coverage->coverageModPremiumGl +
            quoteData->coverage->completeGlPackageRate
        case False:
            0.0
    }
    )
}

output OutputsQuote mtcCompletePolicyPremiumUnmodified number.decimal {
    round(
    switch(quoteData->coverage->hasMTCCoverage) {
        case True:
            quoteData->coverage->coverageModPremiumMTC / quoteData->underwriting->scheduleModMTC +
            quoteData->coverage->reeferBreakdownPremium / quoteData->underwriting->scheduleModMTC +
            quoteData->coverage->reeferBreakdownWithHumanErrorPremium / quoteData->underwriting->scheduleModMTC +
            quoteData->coverage->mtcNamedShipperPolicyPremium / quoteData->underwriting->scheduleModMTC +
            quoteData->company->sumPolicyPremiumCargoAtTerminal +
            quoteData->coverage->mtcTrailerInterchangePolicyPremium
        case False:
            0.0
    }
    )
}

output OutputsQuote liabStandardPolicyPremiumUnmodified number.decimal {
    round(
    switch(quoteData->coverage->hasNegotiatedRate) {
        case True:
            quoteData->coverage->negotiatedModRateLiab
        case False:
            quoteData->coverage->coverageModPremiumLiab / quoteData->underwriting->scheduleModLiab
        } +
    quoteData->coverage->broadenedPollutionPrem / quoteData->underwriting->scheduleModLiab +
        // ^no switch here because policy will always have Liab & Standard package always gets Broadened Poll
    switch(quoteData->coverage->hasMedPayCoverage) {
        case True:
            quoteData->coverage->coverageModPremiumMedPay / quoteData->underwriting->scheduleModMedPay
        case False:
            0.0
        } +
    switch(quoteData->coverage->hasUMCoverage) {
        case True:
            quoteData->coverage->coverageModPremiumUm / quoteData->underwriting->scheduleModUms
        case False:
            0.0
        } +
    switch(quoteData->coverage->hasUIMCoverage) {
        case True:
            quoteData->coverage->coverageModPremiumUim / quoteData->underwriting->scheduleModUms
        case False:
            0.0
        } +
    switch(quoteData->coverage->hasUMUIMCoverage) {
        case True:
            quoteData->coverage->coverageModPremiumUmUim / quoteData->underwriting->scheduleModUms
        case False:
            0.0
        } +
    switch(quoteData->coverage->hasUMPDCoverage) {
        case True:
            quoteData->coverage->coverageModPremiumUmpd / quoteData->underwriting->scheduleModUms
        case False:
            0.0
        } +
    switch(quoteData->coverage->hasPIPCoverage) {
        case True:
            quoteData->coverage->coverageModPremiumPip / quoteData->underwriting->scheduleModMedPay
        case False:
            0.0
        } +
    switch(quoteData->coverage->hasGuestPIPCoverage) {
        case True:
            quoteData->coverage->coverageModPremiumGuestPip / quoteData->underwriting->scheduleModMedPay
        case False:
            0.0
        } +
    switch(quoteData->coverage->hasPPICoverage) {
        case True:
            quoteData->coverage->coverageModPremiumPpi
        case False:
            0.0
        } +
    switch(quoteData->coverage->hasPIPExcessAttendantCareCoverage) {
        case True:
            quoteData->coverage->coverageModPremiumPipEac
        case False:
            0.0
    } +
    switch(quoteData->coverage->hasNegotiatedRate) {
        case True:
            quoteData->coverage->coverageSubhaulPremiumLiab
        case False:
            quoteData->coverage->coverageSubhaulPremiumLiab / quoteData->underwriting->scheduleModLiab
        } +
    flatStandardPolicyPremium
    )
}

output OutputsQuote physStandardPolicyPremiumUnmodified number.decimal {
    round(
    switch(quoteData->coverage->hasPDCoverage) {
        case True:
            switch(quoteData->coverage->hasNegotiatedRate) {
                case True:
                    quoteData->coverage->negotiatedModRateColl
                case False:
                    quoteData->coverage->coverageModPremiumColl / quoteData->underwriting->scheduleModColl
            }
        case False:
            0.0
    } +
    switch(quoteData->coverage->hasPDCoverage) {
        case True:
            switch(quoteData->coverage->hasNegotiatedRate) {
                case True:
                    quoteData->coverage->negotiatedModRateComp
                case False:
                    quoteData->coverage->coverageModPremiumComp / quoteData->underwriting->scheduleModComp
            }
        case False:
            0.0
    } +
    switch(quoteData->coverage->hasPDCoverage) {
        case True:
            quoteData->coverage->coverageModPremiumTrailerInterchangeColl / quoteData->underwriting->scheduleModColl
        case False:
            0.0
    } +
    switch(quoteData->coverage->hasPDCoverage) {
        case True:
            quoteData->coverage->coverageModPremiumTrailerInterchangeComp / quoteData->underwriting->scheduleModComp
        case False:
            0.0
    } +
    switch(quoteData->coverage->hasPDCoverage) {
        case True:
            quoteData->coverage->standardPdPackagePrem
        case False:
            0.0
    }
    )
}

output OutputsQuote glStandardPolicyPremiumUnmodified number.decimal {
    round(
    switch(quoteData->coverage->hasGlCoverage) {
        case True:
            quoteData->coverage->coverageModPremiumGl +
            quoteData->coverage->standardGlPackageRate
        case False:
            0.0
    }
    )
}

output OutputsQuote mtcStandardPolicyPremiumUnmodified number.decimal {
    round(
    switch(quoteData->coverage->hasMTCCoverage) {
        case True:
            quoteData->coverage->coverageModPremiumMTC / quoteData->underwriting->scheduleModMTC +
            quoteData->coverage->reeferBreakdownPremium / quoteData->underwriting->scheduleModMTC +
            quoteData->coverage->reeferBreakdownWithHumanErrorPremium / quoteData->underwriting->scheduleModMTC +
            quoteData->coverage->mtcNamedShipperPolicyPremium / quoteData->underwriting->scheduleModMTC + 
            quoteData->company->sumPolicyPremiumCargoAtTerminal +
            quoteData->coverage->mtcTrailerInterchangePolicyPremium
        case False:
            0.0
    }
    )
}

output OutputsQuote liabBasicPolicyPremiumUnmodified number.decimal {
    round(
    switch(quoteData->coverage->hasNegotiatedRate) {
        case True:
            quoteData->coverage->negotiatedModRateLiab
        case False:
            quoteData->coverage->coverageModPremiumLiab / quoteData->underwriting->scheduleModLiab
        } +
    switch(quoteData->coverage->hasMedPayCoverage) {
        case True:
            quoteData->coverage->coverageModPremiumMedPay / quoteData->underwriting->scheduleModMedPay
        case False:
            0.0
        } +
    switch(quoteData->coverage->hasUMCoverage) {
        case True:
            quoteData->coverage->coverageModPremiumUm / quoteData->underwriting->scheduleModUms
        case False:
            0.0
        } +
    switch(quoteData->coverage->hasUIMCoverage) {
        case True:
            quoteData->coverage->coverageModPremiumUim / quoteData->underwriting->scheduleModUms
        case False:
            0.0
        } +
    switch(quoteData->coverage->hasUMUIMCoverage) {
        case True:
            quoteData->coverage->coverageModPremiumUmUim / quoteData->underwriting->scheduleModUms
        case False:
            0.0
        } +
    switch(quoteData->coverage->hasUMPDCoverage) {
        case True:
            quoteData->coverage->coverageModPremiumUmpd / quoteData->underwriting->scheduleModUms
        case False:
            0.0
        } +
    switch(quoteData->coverage->hasPIPCoverage) {
        case True:
            quoteData->coverage->coverageModPremiumPip / quoteData->underwriting->scheduleModMedPay
        case False:
            0.0
        } +
    switch(quoteData->coverage->hasGuestPIPCoverage) {
        case True:
            quoteData->coverage->coverageModPremiumGuestPip / quoteData->underwriting->scheduleModMedPay
        case False:
            0.0
        } +
    switch(quoteData->coverage->hasPPICoverage) {
        case True:
            quoteData->coverage->coverageModPremiumPpi
        case False:
            0.0
        } +
    switch(quoteData->coverage->hasPIPExcessAttendantCareCoverage) {
        case True:
            quoteData->coverage->coverageModPremiumPipEac
        case False:
            0.0
    } +
    switch(quoteData->coverage->hasNegotiatedRate) {
        case True:
            quoteData->coverage->coverageSubhaulPremiumLiab
        case False:
            quoteData->coverage->coverageSubhaulPremiumLiab / quoteData->underwriting->scheduleModLiab
        } +
    flatBasicPolicyPremium
    )
}

output OutputsQuote physBasicPolicyPremiumUnmodified number.decimal {
    round(
    switch(quoteData->coverage->hasPDCoverage) {
        case True:
            switch(quoteData->coverage->hasNegotiatedRate) {
                case True:
                    quoteData->coverage->negotiatedModRateColl
                case False:
                    quoteData->coverage->coverageModPremiumColl / quoteData->underwriting->scheduleModColl
            }
        case False:
            0.0
    } +
    switch(quoteData->coverage->hasPDCoverage) {
        case True:
            switch(quoteData->coverage->hasNegotiatedRate) {
                case True:
                    quoteData->coverage->negotiatedModRateComp
                case False:
                    quoteData->coverage->coverageModPremiumComp / quoteData->underwriting->scheduleModComp
            }
        case False:
            0.0
    }
    )
}

output OutputsQuote glBasicPolicyPremiumUnmodified number.decimal {
    round(
        switch(quoteData->coverage->hasGlCoverage) {
        case True:
            quoteData->coverage->coverageModPremiumGl +
            quoteData->coverage->basicGlPackageRate
        case False:
            0.0
    }
    )
}

output OutputsQuote mtcBasicPolicyPremiumUnmodified number.decimal {
    round(
        switch(quoteData->coverage->hasMTCCoverage) {
        case True:
            quoteData->coverage->coverageModPremiumMTC / quoteData->underwriting->scheduleModMTC +
            quoteData->coverage->reeferBreakdownPremium / quoteData->underwriting->scheduleModMTC +
            quoteData->coverage->reeferBreakdownWithHumanErrorPremium / quoteData->underwriting->scheduleModMTC +
            quoteData->coverage->mtcNamedShipperPolicyPremium / quoteData->underwriting->scheduleModMTC +
            quoteData->company->sumPolicyPremiumCargoAtTerminal +
            quoteData->coverage->mtcTrailerInterchangePolicyPremium
        case False:
            0.0
    }
    )
}