//model config to be company state = 'IN' and date = launch as this is the initial model

entity QuoteData {
    coverage Coverage
    company Company
    underwriting Underwriting
    lossHistory LossHistory
    outputsQuote OutputsQuote
    raterGL RaterGL
    raterMTC RaterMTC
}

entity OutputsQuote{
    //this section points to other objects
    quoteData  QuoteData
}

entity Underwriting {
    //this section points to other objects
    quoteData  QuoteData

    scheduleModLiab number.decimal
    //expModLiab fully calculated
    scheduleModColl number.decimal
    //expMod<PERSON>oll fully calculated
    scheduleModMedPay number.decimal
    expModMedPay number.decimal
    scheduleModUms number.decimal
    expModUms number.decimal
    scheduleModComp number.decimal
    //expModComp fully calculated
    scheduleModMTC number.decimal
    safetyModAllCov number.decimal

    //switches for Actual Value (aka state value) rating vs Original Cost New rating (aka OCN)
    useStatedValueLiab boolean
    useStatedValueCollComp boolean

    //Policy Effective Date as YYYYMMDD
    policyEffectiveDate number.integer
}

entity Coverage {
    //this section points to other objects
    quoteData  QuoteData

    effectiveDateYear number.integer
    liabilityCSL LiabCslEnum //CSL stands for Combined Single Limit. looks like a number, but only certain options available so enum
    medPayLimit MedPayLimitEnum //looks like a number, but only certain options available so enum
    umLimit UmLimitEnum //looks like a number, but only certain options available so enum
    uimLimit UimLimitEnum //looks like a number, but only certain options available so enum
    umUimLimit UmUimLimitEnum //looks like a number, but only certain options available so enum
    liabilityDeductible LiabDeductibleEnum //looks like a number, but only certain options available so enum
    collDeductible CollDeductibleEnum //looks like a number, but only certain options available so enum
    compDeductible CompDeductibleEnum //looks like a number, but only certain options available so enum
    glOccuranceLimit GlOccuranceLimitEnum //looks like a number, but only certain options available so enum
    glAggLimit GlAggLimitEnum //looks like a number, but only certain options available so enum
    glDeductible GlDeductibleEnum //looks like a number, but only certain options available so enum
    hasGlMisdeliveryLiquids number.integer //1 if has it, 0 if not
    hasGlContractual number.integer //1 if has it, 0 if not
    hasGlStopGap number.integer //1 if has it, 0 if not
    mtcLimit number.integer
    mtcDeductible MtcDeductibleEnum //looks like a number, but only certain options available so enum
    combineDeductiblePhysMTC boolean
    trailerInterchangeLimit number.integer
    towingLimit number.integer

    //switch for PD, GL, MTC Coverage
    hasPDCoverage boolean
    hasGlCoverage boolean
    hasMTCCoverage boolean
    hasUMCoverage boolean
    hasUIMCoverage boolean
    hasUMUIMCoverage boolean
    hasMedPayCoverage boolean
    hasReeferBreakdown boolean
    hasReeferBreakdownWithHumanError boolean
    pipLimit PipLimitEnum
    hasPIPCoverage boolean
    hasGuestPIPCoverage boolean
    hasPPICoverage boolean
    hasPIPExcessAttendantCareCoverage boolean

    //Negotiated Rates
    hasNegotiatedRate boolean
    negotiatedModRateLiab number.decimal
    negotiatedModRatePhys number.decimal

    //Liab type determination for KY
    ownerRejectedTortLim boolean
    pctDriversAccepted number.decimal

    //Subhaul percent for California coverage
    percentSubhaul number.decimal
}

entity LossHistory {
    //this section points to other objects
    quoteData  QuoteData
    yearlyLossSummaries list[YearlyLossSummary]
}

property LossHistory histClaimCountLiab number.integer {
    sum(yearlyLossSummaries, [YearlyLossSummary.claimCountLiab])
}

property LossHistory histClaimCountCollComp number.integer {
    sum(yearlyLossSummaries, [YearlyLossSummary.claimCountCollComp])
}

property LossHistory histPowerUnitCount number.integer {
    sum(yearlyLossSummaries, [YearlyLossSummary.powerUnitCount])
}

property LossHistory priorClaimFreqLiab number.decimal {
    number.decimal{histClaimCountLiab} / number.decimal{histPowerUnitCount}
}

property LossHistory priorClaimFreqCollComp number.decimal {
    number.decimal{histClaimCountCollComp} / number.decimal{histPowerUnitCount}
}

entity YearlyLossSummary {
    // Only includes full historical years - one per year

    //this section points to other objects
    lossHistory LossHistory
    largeLiabilityLosses list[LargeLiabilityLoss]
    largeCollCompLosses list[LargeCollCompLoss]

    // date loss run was generated - policy effective start date
    reportAgeInMonths number.integer
    priorYearLabel PriorYearLabelEnum
    includePriorYearExpMod boolean
    powerUnitCount number.integer
    claimCountLiab number.integer
    lossesLiab number.decimal //$$
    dccLiab number.decimal //$$, dcc stands for defence and cost containment aka alae
    claimCountCollComp number.integer
    lossesCollComp number.decimal //$$
    claimCountMTC number.integer
}

entity LargeLiabilityLoss {
    //point to parent
    yearlyLossSummary YearlyLossSummary

    indemnityAmount number.decimal //$$
    dccAmount number.decimal //$$
}

entity LargeCollCompLoss {
    //point to parent
    yearlyLossSummary YearlyLossSummary

    indemnityAmount number.decimal //$$
}

entity Company {
    //this section points to other objects
    quoteData  QuoteData
    drivers list [Driver]
    vehicles list [Vehicle]

    // This section adds fmcsa derived information not defined internally in rateML
    fmcsaAvgMiles number.decimal
    fmcsaAvgGrossWeight number.decimal
    fmcsaPriorFrequency number.decimal
    fmcsaVehInspectionRatio number.decimal
    fmcsaMaintenanceVioRatio number.decimal
    fmcsaUnsafeVioRatio number.decimal
    fmcsaYears number.integer
    fmcsaInspectionIndicator FmcsaInspectionIndicatorEnum
    fmcsaLargeMachineCargoInd boolean
    fmcsaPowerUnitCount number.integer
    companyGarageState GeoStateAbbrEnum
    truckingNonTrucking TruckingNonTruckingEnum
    nBasicGrade NBasicGradeEnum
    
    //this section is sourced from user entries
    isoOperationType IsoOperationTypeEnum
    commodityGroup CommodityGroupEnum
    commodityDetail CommodityDetailEnum
    naic NaicEnum //looks like a number, but its a label
    driverCountTotalLastYear number.integer
    driverCountNewHiredLastYear number.integer
    percentRadiusLocal number.decimal
    percentRadiusIntermediate number.decimal
    percentRadiusLong number.decimal
    percentCommodityClassA number.decimal
    percentCommodityClassB number.decimal
    percentCommodityClassC number.decimal
    percentCommodityClassD number.decimal
    percentCommodityClassE number.decimal
    percentCommodityClassF number.decimal

    //this section is sourced from Verisk API
    lgptCitySurchargeAuto number.decimal
    lgptCountySurchargeAuto number.decimal
    lgptCitySurchargeGL number.decimal
    lgptCountySurchargeGL number.decimal
    lgptCitySurchargeMTC number.decimal
    lgptCountySurchargeMTC number.decimal
    lgptCityJurisdictionName LgptCityJurisdictionNameEnum
    lgptCountyJurisdictionName LgptCountyJurisdictionNameEnum
    lgptCityJurisdictionCode LgptCityJurisdictionCodeEnum
    lgptCountyJurisdictionCode LgptCountyJurisdictionCodeEnum

    //for optional coverages
    trailerInterchangeTrailerLongHaulCount number.integer
    trailerInterchangeTrailerIntermediateCount number.integer
    trailerInterchangeTrailerLocalCount number.integer
}


property Company driverTurnover number.decimal {
    number.decimal{driverCountNewHiredLastYear} / number.decimal{driverCountTotalLastYear}
}

entity Driver {
    company Company
    //TODO(NIR-184) hacked movingViolationCount and attractScore to number.decimal instead of integer because averaging was failing
    movingViolationCount number.decimal //this needs to be calculated from MVRs. not currently in rateML
    attractScore number.decimal //sourced from LexisNexis
}

property Company avgDriverVios number.decimal {
    average(drivers, [Driver.movingViolationCount])
} 

entity Vehicle {
    company Company
    raterCOLL RaterCOLL
    raterCOMP RaterCOMP
    raterAL RaterAL

    //this section could be derived via VIN decoding
    vehType VehTypeEnum
    isoWeightGroup IsoWeightGroupEnum
    yearMade number.integer
    costNew number.decimal

    //this section is sourced from user entries
    statedValue number.decimal
    garageZip GeoZipEnum
    gpsRadius number.decimal
    
    //TODO figure out how to map these from geo data
    vehicleStartZone IsoZoneEnum
    vehicleEndZone IsoZoneEnum

    //TODO useZone rating should be internal calc, but stubbed until switches can work
    useZoneRating boolean

    hasApdCoverage boolean
    nonOwnedSemiTrailerFlag boolean
}

property Vehicle vehicleAge number.integer {
    company->quoteData->coverage->effectiveDateYear - yearMade
}

property Vehicle vehicleGpsIsoRadiusGroup IsoRadiusGroupEnum {
    lookup([IsoRadiusGroup], [IsoRadiusGroup.isoRadiusGroup],
        gpsRadius
        )
}

property Vehicle vehicleGarageIsoTerritory IsoTerritoryEnum {
    lookup([IsoTerritory], [IsoTerritory.value],
        garageZip
        )
}

// TODO: define IsoEnvironment table
//property Vehicle vehicleGarageIsoEnvironment number.integer {
//    lookup(IsoEnvironment, value,
//        vehicle.garageZip
//        )
//}

property Vehicle isPowerUnit number.integer {
    lookup([PowerUnitFlag], [PowerUnitFlag.isPowerUnit],
        vehType
        )
}

property Company fleetPowerUnitCount number.integer {
    sum(vehicles, [Vehicle.isPowerUnit])
}

property Vehicle isTowingUnit number.integer {
    lookup([PowerUnitFlag], [PowerUnitFlag.isTowingUnit],
        vehType
        )
}

property Company fleetTowingUnitCount number.integer {
    sum(vehicles, [Vehicle.isTowingUnit])
}

entity RaterGL {
    //this section points to other objects
     quoteData  QuoteData
}

entity RaterMTC {
    //this section points to other objects
     quoteData  QuoteData
}

entity RaterCOLL {
    //this section points to other objects
    vehicle Vehicle
}

entity RaterCOMP {
    //this section points to other objects
    vehicle Vehicle
}

entity RaterAL {
    //this section points to other objects
    vehicle Vehicle
}
