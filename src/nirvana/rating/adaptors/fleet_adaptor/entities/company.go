package entities

import (
	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/rating/adaptors/common"
)

type Company struct {
	Id string

	// this section points to other objects
	QuoteData   string   `rml:"reference,QuoteData"`
	Drivers     []string `rml:"reference,Driver"`
	Vehicles    []string `rml:"reference,Vehicle"`
	Commodities []string `rml:"reference,Commodity"`
	Terminals   []string `rml:"reference,Terminal"`

	FmcsaAvgMiles             float64 `rml:"float,number.decimal"`
	FmcsaAvgGrossWeight       float64 `rml:"float,number.decimal"`
	FmcsaPriorFrequency       float64 `rml:"float,number.decimal"`
	FmcsaVehInspectionRatio   float64 `rml:"float,number.decimal"`
	FmcsaMaintenanceVioRatio  float64 `rml:"float,number.decimal"`
	FmcsaUnsafeVioRatio       float64 `rml:"float,number.decimal"`
	FmcsaYears                int64   `rml:"integer,number.integer"`
	FmcsaInspectionIndicator  string  `rml:"enum,FmcsaInspectionIndicatorEnum"`
	FmcsaLargeMachineCargoInd bool    `rml:"bool,boolean"`
	FmcsaPowerUnitCount       int64   `rml:"integer,number.integer"`
	FmcsaUnsafeDrivingScore   float64 `rml:"float,number.decimal"`

	IsoOperationType            string  `rml:"enum,IsoOperationTypeEnum"`
	CommodityGroup              string  `rml:"enum,CommodityGroupEnum"`
	CommodityDetail             string  `rml:"enum,CommodityDetailEnum"`
	Naic                        string  `rml:"enum,NaicEnum"`
	DriverCountTotalLastYear    int64   `rml:"integer,number.integer"`
	DriverCountNewHiredLastYear int64   `rml:"integer,number.integer"`
	PercentRadiusLocal          float64 `rml:"float,number.decimal"`
	PercentRadiusIntermediate   float64 `rml:"float,number.decimal"`
	PercentRadiusLong           float64 `rml:"float,number.decimal"`
	CompanyGarageState          string  `rml:"enum,GeoStateAbbrEnum"`
	TruckingNonTrucking         string  `rml:"enum,TruckingNonTruckingEnum"`

	TrailerInterchangeTrailerLongHaulCount     int64 `rml:"integer,number.integer"`
	TrailerInterchangeTrailerIntermediateCount int64 `rml:"integer,number.integer"`
	TrailerInterchangeTrailerLocalCount        int64 `rml:"integer,number.integer"`

	PercentCommodityClassA float64 `rml:"float,number.decimal"`
	PercentCommodityClassB float64 `rml:"float,number.decimal"`
	PercentCommodityClassC float64 `rml:"float,number.decimal"`
	PercentCommodityClassD float64 `rml:"float,number.decimal"`
	PercentCommodityClassE float64 `rml:"float,number.decimal"`
	PercentCommodityClassF float64 `rml:"float,number.decimal"`

	NBasicGrade string `rml:"enum,NBasicGradeEnum"`

	LgptCitySurchargeAuto      float64 `rml:"float,number.decimal"`
	LgptCitySurchargeGL        float64 `rml:"float,number.decimal"`
	LgptCitySurchargeMTC       float64 `rml:"float,number.decimal"`
	LgptCountySurchargeAuto    float64 `rml:"float,number.decimal"`
	LgptCountySurchargeGL      float64 `rml:"float,number.decimal"`
	LgptCountySurchargeMTC     float64 `rml:"float,number.decimal"`
	LgptCityJurisdictionCode   string  `rml:"enum,LgptCityJurisdictionCodeEnum"`
	LgptCityJurisdictionName   string  `rml:"enum,LgptCityJurisdictionNameEnum"`
	LgptCountyJurisdictionCode string  `rml:"enum,LgptCountyJurisdictionCodeEnum"`
	LgptCountyJurisdictionName string  `rml:"enum,LgptCountyJurisdictionNameEnum"`

	PercentCommodityClassANamedShipper float64 `rml:"float,number.decimal"`
	PercentCommodityClassBNamedShipper float64 `rml:"float,number.decimal"`
	PercentCommodityClassCNamedShipper float64 `rml:"float,number.decimal"`
	PercentCommodityClassDNamedShipper float64 `rml:"float,number.decimal"`
	PercentCommodityClassENamedShipper float64 `rml:"float,number.decimal"`
	PercentCommodityClassFNamedShipper float64 `rml:"float,number.decimal"`

	YearsRetained             int64  `rml:"integer,number.integer"`
	PriorCarrierYearsRetained int64  `rml:"integer,number.integer"`
	PrimaryVehicleType        string `rml:"enum,PrimaryVehicleTypeEnum"`
}

var _ common.Entity = (*Company)(nil)

func (c *Company) RatemlId() string {
	return c.Id
}

func (c *Company) RatemlTypeName() common.EntityType {
	return common.EntityTypeCompany
}

func (c *Company) HandleConnectedEntity(v common.Entity) error {
	//nolint:exhaustive
	switch v.RatemlTypeName() {
	case common.EntityTypeQuoteData:
		if c.QuoteData != "" {
			return errors.Newf("quotedata entity with id %s already connected", c.QuoteData)
		}
		c.QuoteData = v.RatemlId()
	case common.EntityTypeVehicle:
		if slice_utils.Contains(c.Vehicles, v.RatemlId()) {
			return errors.Newf("vehicle entity with id %s already connected", v.RatemlId())
		}
		c.Vehicles = append(c.Vehicles, v.RatemlId())
	case common.EntityTypeDriver:
		if slice_utils.Contains(c.Drivers, v.RatemlId()) {
			return errors.Newf("driver entity with id %s already connected", v.RatemlId())
		}
		c.Drivers = append(c.Drivers, v.RatemlId())
	case common.EntityTypeCommodity:
		if slice_utils.Contains(c.Commodities, v.RatemlId()) {
			return errors.Newf("commodity entity with id %s already connected", v.RatemlId())
		}
		c.Commodities = append(c.Commodities, v.RatemlId())
	case common.EntityTypeTerminal:
		if slice_utils.Contains(c.Terminals, v.RatemlId()) {
			return errors.Newf("terminal entity with id %s already connected", v.RatemlId())
		}
		c.Terminals = append(c.Terminals, v.RatemlId())
	default:
		return errors.Newf(
			"no linkage possible for %v(id %s) with %v",
			c.RatemlTypeName(), c.RatemlId(), c.RatemlTypeName(),
		)
	}
	return nil
}

func (*Company) ConnectedEntityTypes() []common.EntityType {
	return []common.EntityType{
		common.EntityTypeQuoteData,
		common.EntityTypeDriver,
		common.EntityTypeVehicle,
		common.EntityTypeCommodity,
		common.EntityTypeTerminal,
	}
}
