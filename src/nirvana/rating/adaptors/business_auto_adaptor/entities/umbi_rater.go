package entities

import (
	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/rating/adaptors/business_auto_adaptor/entities/common"
	adaptors "nirvanatech.com/nirvana/rating/adaptors/common"
)

type UMBIRater struct {
	Id string

	Vehicle  string `rml:"reference,Vehicle"`
	Company  string `rml:"reference,Company"`
	Coverage string `rml:"reference,Coverage"`

	LiabRaters []string `rml:"reference,LiabRater"`

	VehicleUMBIPremium float64 `rml:"float,number.decimal,output"`
}

var _ adaptors.Entity = (*UMBIRater)(nil)

func (r *UMBIRater) RatemlId() string {
	return r.Id
}

func (r *UMBIRater) RatemlTypeName() adaptors.EntityType {
	return adaptors.EntityTypeUMBIRater
}

func (r *UMBIRater) HandleConnectedEntity(e adaptors.Entity) error {
	switch e.RatemlTypeName() {
	case adaptors.EntityTypeVehicle:
		if common.GetVehicleIdFromRaterId(r.Id) != e.RatemlId() {
			return nil
		}
		if r.Vehicle != "" {
			return errors.Newf(
				"entity type %v already connected for entity %v(id=%s)",
				e.RatemlTypeName(),
				r.RatemlTypeName(),
				r.RatemlId(),
			)
		}
		r.Vehicle = e.RatemlId()
	case adaptors.EntityTypeCompany:
		if r.Company != "" {
			return errors.Newf(
				"entity type %v already connected for entity %v(id=%s)",
				e.RatemlTypeName(),
				r.RatemlTypeName(),
				r.RatemlId(),
			)
		}
		r.Company = e.RatemlId()
	case adaptors.EntityTypeCoverage:
		if r.Coverage != "" {
			return errors.Newf(
				"entity type %v already connected for entity %v(id=%s)",
				e.RatemlTypeName(),
				r.RatemlTypeName(),
				r.RatemlId(),
			)
		}
		r.Coverage = e.RatemlId()
	case adaptors.EntityTypeLiabRater:
		if slice_utils.Contains(r.LiabRaters, e.RatemlId()) {
			return errors.Newf(
				"entity %v(id=%s) already connected for entity %v(id=%s)",
				e.RatemlTypeName(),
				e.RatemlId(),
				r.RatemlTypeName(),
				r.RatemlId(),
			)
		}
		r.LiabRaters = append(r.LiabRaters, e.RatemlId())
	default:
		return errors.Newf(
			"unhandled entity %v(id=%s) for entity %v(id=%s)",
			e.RatemlTypeName(),
			e.RatemlId(),
			r.RatemlTypeName(),
			r.RatemlId(),
		)
	}
	return nil
}

func (r *UMBIRater) ConnectedEntityTypes() []adaptors.EntityType {
	return []adaptors.EntityType{
		adaptors.EntityTypeVehicle,
		adaptors.EntityTypeCompany,
		adaptors.EntityTypeCoverage,
		adaptors.EntityTypeLiabRater,
	}
}
