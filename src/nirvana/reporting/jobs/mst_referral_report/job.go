package mst_referral_report

import (
	"bytes"
	"context"
	"encoding/base64"
	"fmt"
	"io"
	"os"
	"strconv"

	"github.com/cactus/go-statsd-client/v5/statsd"
	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/xuri/excelize/v2"

	fileUploadEnums "nirvanatech.com/nirvana/common-go/file_upload_lib/enums"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/emailer/models"
	"nirvanatech.com/nirvana/jobber/job_utils"
	"nirvanatech.com/nirvana/jobber/jtypes"
	reportEnums "nirvanatech.com/nirvana/reporting/enums"
	"nirvanatech.com/nirvana/reporting/jobs/common"
	"nirvanatech.com/nirvana/reporting/jobs/deps"
)

const (
	successTagKey         = "success"
	mstReferralReportID   = "MSTReferralReport"
	tempZipFileNamePrefix = "output"
	attachmentFileName    = "output.zip"
	attachmentType        = "application/zip"
	excelFileName         = "mst_referral.xlsx"
)

type mstReferralReport struct {
	deps *deps.Deps
	job_utils.NonRetryableTask[*MSTReferralReportArgs]
	job_utils.NoopUndoTask[*MSTReferralReportArgs]
}

func NewMSTReferralReport(deps *deps.Deps) (*jtypes.Job[*MSTReferralReportArgs], error) {
	return jtypes.NewJob(
		MSTReferralReport,
		[]jtypes.TaskCreator[*MSTReferralReportArgs]{
			func() jtypes.Task[*MSTReferralReportArgs] {
				return &mstReferralReport{deps: deps}
			},
		},
		MSTReferralReportUnmarshalFn,
	)
}

func (m *mstReferralReport) ID() string {
	return mstReferralReportID
}

func (m *mstReferralReport) Run(ctx jtypes.Context, message *MSTReferralReportArgs) (err error) {
	defer func() {
		if r := recover(); r != nil {
			err = errors.Newf("panic: %v", r)
		}
		if err != nil {
			uploadErr := m.deps.ReportWrapper.UpdateStatus(ctx, message.Id, reportEnums.Failed)
			if uploadErr != nil {
				log.Error(ctx, "failed to update status", log.Err(uploadErr))
			}
			log.Error(ctx, "MSTReferralReport job failed", log.Err(err))
		} else {
			uploadErr := m.deps.ReportWrapper.UpdateStatus(ctx, message.Id, reportEnums.Completed)
			if uploadErr != nil {
				log.Error(ctx, "failed to update status", log.Err(uploadErr))
			}
			log.Info(ctx, "MSTReferralReport job completed successfully")
		}
		m.emitJobCompletionMetric(ctx, err)
	}()

	ctx = ctx.WithUpdatedBaseCtx(func(ctx context.Context) context.Context {
		return log.ContextWithFields(ctx, log.String("applicationReviewID", message.ApplicationReviewId))
	})
	log.Info(ctx, "Running MSTReferralReport Job")

	if err := m.reportManager(ctx, message.ApplicationReviewId, message.Id); err != nil {
		return errors.Wrap(err, "failed to run report manager")
	}

	return nil
}

func (m *mstReferralReport) reportManager(ctx jtypes.Context, appReviewId, reportId string) error {
	file, err := m.deps.MstReferralFileManager.InitialiseFile(ctx)
	if err != nil {
		return errors.Wrap(err, "failed to initialise file")
	}

	if err = m.deps.MstReferralFileManager.GenerateAccountOverviewSheet(ctx, appReviewId, file); err != nil {
		return errors.Wrap(err, "failed to get account overview sheet")
	}

	if err = m.deps.MstReferralFileManager.GenerateUnitScheduleSheet(ctx, appReviewId, file); err != nil {
		return errors.Wrap(err, "failed to get unit schedule sheet")
	}

	if err = m.deps.MstReferralFileManager.GenerateLossHistorySummarySheet(ctx, appReviewId, file); err != nil {
		return errors.Wrap(err, "failed to get loss history summary sheet")
	}

	if err = m.deps.MstReferralFileManager.GenerateCabFmcsaReportSheet(ctx, appReviewId, file); err != nil {
		return errors.Wrap(err, "failed to get cab fmcsa report sheet")
	}

	if err = m.deps.MstReferralFileManager.GenerateDriverScheduleSheet(ctx, appReviewId, file); err != nil {
		return errors.Wrap(err, "failed to get driver schedule sheet")
	}

	if err = m.deps.MstReferralFileManager.GenerateTelematicsSheet(ctx, appReviewId, file); err != nil {
		return errors.Wrap(err, "failed to get telematics sheet")
	}

	if err = m.emailOutput(ctx, appReviewId, reportId, file); err != nil {
		return errors.Wrap(err, "failed to email output")
	}

	return nil
}

func (m *mstReferralReport) emitJobCompletionMetric(ctx jtypes.Context, err error) {
	statName := "mst_referral_report_job.count"
	jobSucceeded := err == nil
	err = m.deps.MetricClient.Inc(
		statName, 1, 1, statsd.Tag{successTagKey, strconv.FormatBool(jobSucceeded)},
	)
	if err != nil {
		log.Error(ctx, "failed to emit metric", log.String("metricName", statName))
	}
}

func (m *mstReferralReport) emailOutput(
	ctx jtypes.Context,
	appReviewId, reportId string,
	excelFile *excelize.File,
) error {
	appReview, err := m.deps.AppReviewWrapper.GetReview(ctx, appReviewId)
	if err != nil {
		return errors.Wrap(err, "failed to get application review")
	}

	report, err := m.deps.ReportWrapper.GetReportByID(ctx, reportId)
	if err != nil {
		return errors.Wrap(err, "failed to get report")
	}

	// Prepare files for zipping
	files, err := m.prepareFiles(ctx, excelFile, appReview)
	if err != nil {
		return err
	}

	zipBuffer, err := m.createZipBuffer(ctx, files)
	if err != nil {
		return err
	}

	// Upload the report to S3 FIRST - this ensures the report is preserved even if email fails
	seekReader := bytes.NewReader(zipBuffer.Bytes())
	err = common.UploadReportToS3(ctx,
		m.deps,
		seekReader,
		reportId,
		fileUploadEnums.FileTypeReport,
		fileUploadEnums.FileDestinationGroupReport,
		attachmentFileName,
	)
	if err != nil {
		return errors.Wrap(err, "failed to upload report to S3")
	}

	log.Info(ctx, "Report successfully uploaded to S3", log.String("reportId", reportId))

	// Email the output (if this fails, at least the report is saved in S3)
	user, err := m.deps.AuthWrapper.FetchAuthzUser(ctx, report.CreatedBy)
	if err != nil {
		return errors.Wrap(err, "failed to fetch underwriter user")
	}

	attachment := &models.Attachment{
		Content:     base64.StdEncoding.EncodeToString(zipBuffer.Bytes()),
		Filename:    attachmentFileName,
		Type:        attachmentType,
		Disposition: models.AttachmentDispositionAttachment,
	}

	err = common.SendEmail(ctx, m.deps, user, reportEnums.MSTReferralReport, attachment)
	if err != nil {
		return errors.Wrap(err, "failed to send email with attachment")
	}

	return nil
}

func (m *mstReferralReport) prepareFiles(
	ctx jtypes.Context,
	excelFile *excelize.File,
	appReview *uw.ApplicationReview,
) (map[string]io.Reader, error) {
	files := make(map[string]io.Reader)

	// Add Excel file
	excelBuffer := new(bytes.Buffer)
	if err := excelFile.Write(excelBuffer); err != nil {
		return nil, errors.Wrap(err, "failed to write excel file to buffer")
	}
	files[excelFileName] = excelBuffer

	// Add all other documents
	documents, err := m.deps.AppReviewManager.Global.Documents.Get(ctx, appReview.Id)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get documents")
	}

	metadataList := documents.Files
	for _, metadata := range metadataList {
		if metadata.Handle == nil {
			continue
		}

		fileHandle, err := uuid.Parse(*metadata.Handle)
		if err != nil {
			return nil, errors.Wrapf(err, "unable to parse file handle %s", *metadata.Handle)
		}

		if fileHandle == uuid.Nil {
			continue
		}

		fileName := metadata.Name
		file, err := m.deps.FileUploadManager.DownloadFile(ctx, fileHandle)
		if err != nil {
			return nil, errors.Wrapf(err, "unable to get file %s for review %s", fileName, appReview.Id)
		}

		if _, exists := files[fileName]; exists {
			fileName = fmt.Sprintf("%s_%s", fileName, common.GenerateRandomTag())
		}
		files[fileName] = file
	}

	return files, nil
}

func (m *mstReferralReport) createZipBuffer(
	ctx jtypes.Context,
	files map[string]io.Reader,
) (*bytes.Buffer, error) {
	tempZipFileName := fmt.Sprintf("%s_%s.zip", tempZipFileNamePrefix, uuid.New().String())
	// Create temporary zip file
	outputFile, err := os.Create(tempZipFileName)
	if err != nil {
		return nil, errors.Wrap(err, "failed to create zip file")
	}

	defer func() {
		err := outputFile.Close()
		if err != nil {
			log.Error(ctx, "failed to close zip file", log.Err(err))
		}
		if err := os.Remove(tempZipFileName); err != nil {
			log.Error(ctx, "failed to remove temp zip file", log.Err(err))
		}
	}()

	// Zip files
	if err := common.ZipFiles(files, outputFile); err != nil {
		return nil, errors.Wrap(err, "failed to zip files")
	}

	// Read zip file into buffer
	zipFile, err := os.Open(tempZipFileName)
	if err != nil {
		return nil, errors.Wrap(err, "failed to open zip file")
	}
	defer func(zipFile *os.File) {
		err := zipFile.Close()
		if err != nil {
			log.Error(ctx, "failed to close zip file", log.Err(err))
		}
	}(zipFile)

	zipBuffer := new(bytes.Buffer)
	if _, err := io.Copy(zipBuffer, zipFile); err != nil {
		return nil, errors.Wrap(err, "failed to read zip file into buffer")
	}

	return zipBuffer, nil
}
