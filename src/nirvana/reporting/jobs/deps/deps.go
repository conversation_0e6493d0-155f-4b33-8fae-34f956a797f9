package deps

import (
	"github.com/cactus/go-statsd-client/v5/statsd"
	"go.uber.org/fx"

	endorsementrequest "nirvanatech.com/nirvana/application/endorsementapp/endorsement-request"
	endorsementreview "nirvanatech.com/nirvana/application/endorsementapp/endorsement-review"
	"nirvanatech.com/nirvana/common-go/file_upload_lib"
	"nirvanatech.com/nirvana/db-api/db_wrappers/agency"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	nf_app_review "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application_review"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	"nirvanatech.com/nirvana/db-api/db_wrappers/reporting_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/reports"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/emailer"
	legacyendorsementrequest "nirvanatech.com/nirvana/endorsement/endorsement-request"
	legacyendorsementreview "nirvanatech.com/nirvana/endorsement/endorsement-review"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
	"nirvanatech.com/nirvana/insurance-bundle/service"
	policyclient "nirvanatech.com/nirvana/policy"
	registry "nirvanatech.com/nirvana/reporting"
	"nirvanatech.com/nirvana/reporting/mst_excel_manager"
	uwAppReview "nirvanatech.com/nirvana/underwriting/app_review"
)

// Deps needs to be in its own package to avoid circular dependencies
type Deps struct {
	fx.In

	PolicyWrapper                  policy.DataWrapper
	FileUploadManager              file_upload_lib.FileUploadManager[file_upload_lib.DefaultS3Keygen]
	Emailer                        emailer.Emailer
	ReportWrapper                  *reports.ReportWrapper
	AuthWrapper                    auth.DataWrapper
	ReportingUtilWrapper           *reporting_utils.ReportingUtilWrapper
	AppWrapper                     application.DataWrapper
	AdmittedAppWrapper             nf_app.Wrapper[*admitted_app.AdmittedApp]
	FetcherClientFactory           data_fetching.FetcherClientFactory
	ProcessorClientFactory         data_processing.ProcessorClientFactory
	LegacyEndorsementReviewClient  legacyendorsementreview.Client
	LegacyEndorsementRequestClient legacyendorsementrequest.Client
	PolicyClient                   policyclient.Client
	AgencyWrapper                  agency.DataWrapper
	ReportInputRegistry            registry.Registry
	MetricClient                   statsd.Statter
	AppReviewManager               uwAppReview.ReviewManager
	AppReviewWrapper               uw.ApplicationReviewWrapper
	MstReferralFileManager         *mst_excel_manager.MSTReferralExcelFileManager
	IBClient                       service.InsuranceBundleManagerClient
	EndorsementRequestManager      endorsementrequest.Manager
	EndorsementReviewManager       endorsementreview.Manager
	NFApplicationReviewWrapper     nf_app_review.Wrapper
	NFApplicationWrapper           nf_app.Wrapper[*admitted_app.AdmittedApp]
}
