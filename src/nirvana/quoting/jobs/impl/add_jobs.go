package impl

import (
	"github.com/benb<PERSON><PERSON><PERSON>/clock"
	"github.com/cactus/go-statsd-client/v5/statsd"
	"github.com/cockroachdb/errors"
	"go.uber.org/fx"
	"go.uber.org/multierr"
	non_fleet_experiment "nirvanatech.com/nirvana/application/experiments/non_fleet"
	"nirvanatech.com/nirvana/db-api/db_wrappers/ds"
	"nirvanatech.com/nirvana/distsem"
	"nirvanatech.com/nirvana/ds/model"
	risk_factors_impl "nirvanatech.com/nirvana/underwriting/risk_factors"

	"nirvanatech.com/nirvana/api-server/quoting_jobber"
	ruleengine "nirvanatech.com/nirvana/api-server/rule-engine"
	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/common-go/file_upload_lib"
	"nirvanatech.com/nirvana/common-go/insurance_carriers_utils"
	"nirvanatech.com/nirvana/common-go/pibit_ai"
	db_api "nirvanatech.com/nirvana/db-api"
	"nirvanatech.com/nirvana/db-api/db_wrappers/agency"
	"nirvanatech.com/nirvana/db-api/db_wrappers/agency_bd_mapping"
	"nirvanatech.com/nirvana/db-api/db_wrappers/appetite_lite"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	business_auto_app "nirvanatech.com/nirvana/db-api/db_wrappers/business_auto/application"
	application_email "nirvanatech.com/nirvana/db-api/db_wrappers/emails/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/external/pibit"
	"nirvanatech.com/nirvana/db-api/db_wrappers/fleet"
	"nirvanatech.com/nirvana/db-api/db_wrappers/fleet_telematics"
	"nirvanatech.com/nirvana/db-api/db_wrappers/fmcsa"
	"nirvanatech.com/nirvana/db-api/db_wrappers/forms"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	admitted "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy_set"
	"nirvanatech.com/nirvana/db-api/db_wrappers/sharing"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/emailer"
	"nirvanatech.com/nirvana/events"
	salesforce "nirvanatech.com/nirvana/external_client/salesforce/wrapper"
	"nirvanatech.com/nirvana/external_client/verisk"
	"nirvanatech.com/nirvana/external_data_management/clients_management"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/read_from_store_interceptor"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/write_to_store_interceptor"
	"nirvanatech.com/nirvana/external_data_management/nhtsa"
	"nirvanatech.com/nirvana/feature_store"
	"nirvanatech.com/nirvana/infra/config"
	insurance_eng "nirvanatech.com/nirvana/insurance-core/monitoring"
	"nirvanatech.com/nirvana/jobber/event"
	"nirvanatech.com/nirvana/jobber/registry"
	"nirvanatech.com/nirvana/metaflow"
	statemachine "nirvanatech.com/nirvana/nonfleet/state-machine"
	"nirvanatech.com/nirvana/pdfgen"
	policyclient "nirvanatech.com/nirvana/policy"
	"nirvanatech.com/nirvana/policy_common/forms_generator"
	"nirvanatech.com/nirvana/quoting/app_state_machine"
	"nirvanatech.com/nirvana/quoting/clearance"
	"nirvanatech.com/nirvana/quoting/clearance/emails"
	quoting_emailer "nirvanatech.com/nirvana/quoting/emailer"
	"nirvanatech.com/nirvana/quoting/experiments"
	"nirvanatech.com/nirvana/quoting/tags"
	"nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor"
	"nirvanatech.com/nirvana/rating/artifacts"
	"nirvanatech.com/nirvana/servers/telematicsv2"
	tsp_connections "nirvanatech.com/nirvana/telematics/connections"
	"nirvanatech.com/nirvana/telematics/data_platform/api"
	"nirvanatech.com/nirvana/textmessaging"
	uw_app_review "nirvanatech.com/nirvana/underwriting/app_review"
	"nirvanatech.com/nirvana/underwriting/app_review_widget_manager"
	"nirvanatech.com/nirvana/underwriting/common"
	"nirvanatech.com/nirvana/underwriting/state_machine"
)

type Deps struct {
	fx.In

	ASMWrapper                      app_state_machine.AppStateMachineWrapper
	AgencyWrapper                   agency.DataWrapper
	AppReviewStateMachine           state_machine.AppReviewWrapper
	AppReviewWidgetManager          app_review_widget_manager.AppReviewWidgetManager
	AppReviewWrapper                uw.ApplicationReviewWrapper
	AppWrapper                      application.DataWrapper
	AuthWrapper                     auth.DataWrapper
	ArtifactWriter                  artifacts.ArtifactWriter
	BusinessAutoAppWrapper          business_auto_app.Wrapper
	Config                          *config.Config
	DPClient                        *api.Client
	Emailer                         emailer.Emailer
	TextMessager                    textmessaging.Service
	EventHandler                    events.EventsHandler
	FMCSAWrapper                    fmcsa.DataWrapper
	FeatureFlagClient               feature_flag_lib.Client
	FeatureStore                    feature_store.FeatureStore
	FileUploadManager               file_upload_lib.FileUploadManager[file_upload_lib.DefaultS3Keygen]
	FleetWrapper                    fleet.DataWrapper
	FormsGenerator                  forms_generator.FormsGenerator
	FormsWrapper                    forms.FormWrapper
	InsuranceCarrierTree            insurance_carriers_utils.InsuranceCarriersTree
	InsuranceEngPDClient            insurance_eng.PagerDutyClient
	Jobber                          quoting_jobber.Client
	MetaflowClient                  metaflow.PipelineManager
	MetricsClient                   statsd.Statter
	NFAppStateMachineWrapper        *statemachine.NFAppStateMachineImpl
	NHTSAClient                     nhtsa.Client
	PDFGenClient                    pdfgen.PDFGenClient
	ParsedLossRunWrapper            pibit.DataWrapper
	PibitAiClient                   *pibit_ai.PibitAiClient
	PolicySetWrapper                policy_set.DataWrapper
	PolicyWrapper                   policy.DataWrapper
	RuleEngine                      *ruleengine.RuleEngine
	TSPConnManager                  *tsp_connections.TSPConnManager
	Telematicsv2                    telematicsv2.TelematicsPipelineManager
	FleetTelematicsWrapper          fleet_telematics.Wrapper
	AppReviewManager                uw_app_review.ReviewManager
	UWSafetyFetcher                 *common.UWSafetyFetcher
	VeriskClient                    verisk.Client
	PolicyClient                    policyclient.Client
	AdmittedAppWrapper              nf_app.Wrapper[*admitted.AdmittedApp]
	AppEmailLogWrapper              *application_email.DataWrapper
	ClearanceStateManager           clearance.StateManager
	ClearanceEmailer                emails.EmailWrapper
	FetcherClientFactory            data_fetching.FetcherClientFactory
	ProcessorClientFactory          data_processing.ProcessorClientFactory
	TspConnectionManager            *tsp_connections.TSPConnManager
	TelematicsDataPlatformClient    telematicsv2.TelematicsPipelineManager
	QuotingEmailerClient            *quoting_emailer.Client
	EventClient                     event.Client
	SnowflakeDsDB                   db_api.SnowflakeDsRW
	NirvanaDB                       db_api.NirvanaRW
	SFDCWrapper                     salesforce.SalesforceWrapper
	Clock                           clock.Clock
	AppetiteLiteWrapper             appetite_lite.Wrapper
	SharingWrapper                  sharing.DataWrapper
	TagsManager                     *tags.Manager
	AgencyBdWrapper                 agency_bd_mapping.Wrapper
	ClientsManager                  clients_management.ClientsManager
	WriteToStoreInterceptorFactory  write_to_store_interceptor.Factory
	ReadFromStoreInterceptorFactory read_from_store_interceptor.Factory
	ModelRunner                     *fleet_adaptor.ModelRunner
	QuotingExperimentsManager       experiments.Manager
	RiskFactorsClient               risk_factors_impl.RiskFactorsServiceClient
	M1Client                        model.M1ModelClient
	DSWrapper                       ds.DataWrapper
	NFApplicationExperimentManager  *non_fleet_experiment.Manager
	DistSemManager                  distsem.Manager
}

func AddJobsToRegistry(
	r registry.RegistryInterface,
	deps Deps,
) (multiErr error) {
	basicApplicationSubmittedJob, err := NewGenerateIndicationJob(&deps)
	multiErr = multierr.Append(multiErr, err)

	finalApplicationSubmittedJob, err := NewGenerateSubmissionForUWJob(&deps)
	multiErr = multierr.Append(multiErr, err)

	regenerateQuoteJob, err := NewUpdateSubmissionForUWJob(&deps)
	multiErr = multierr.Append(multiErr, err)

	generateBindableSubQuoteJob, err := NewGenerateQuoteJob(&deps)
	multiErr = multierr.Append(multiErr, err)

	generateQuotePDFJob, err := NewGenerateQuotePdfJob(&deps)
	multiErr = multierr.Append(multiErr, err)

	generateAppPDFJob, err := NewGenerateAppPdfJob(&deps)
	multiErr = multierr.Append(multiErr, err)

	sendTelematicsConsentReminderEmailJob, err := NewSendTelematicsConsentReminderEmailJob(&deps)
	multiErr = multierr.Append(multiErr, err)

	scheduleTelematicsConsentReminderEmailsJob, err := NewScheduleTelematicsConsentReminderEmailsJob(&deps)
	multiErr = multierr.Append(multiErr, err)

	sendSubmissionAcknowledgementEmailJob, err := NewSendSubmissionAcknowledgmentEmailJob(&deps)
	multiErr = multierr.Append(multiErr, err)

	transitionLostAppAndAppReviewsJob, err := NewTransitionAppsToClosedStateJob(&deps)
	multiErr = multierr.Append(multiErr, err)

	sendTelematicsConnectionSuccessfulEmailJob, err := NewSendTelematicsConnectionSuccessfulEmailJob(&deps)
	multiErr = multierr.Append(multiErr, err)

	sendPostBindOnboardingEmailJob, err := NewSendPostBindOnboardingEmailJob(&deps)
	multiErr = multierr.Append(multiErr, err)

	dataPipelineStateChangedjob, err := NewDataPipelineStateChangedJob(&deps)
	multiErr = multierr.Append(multiErr, err)

	markPendingAppReviewsToStale, err := NewMarkPendingReviewsToStale(&deps)
	multiErr = multierr.Append(multiErr, err)

	jobTriggerCarrierLoyaltyMetaflow, err := NewTriggerCarrierLoyaltyMetaflow(&deps)
	multiErr = multierr.Append(multiErr, err)

	populatePolicySetJob, err := NewPopulatePolicySetJob(&deps)
	multiErr = multierr.Append(multiErr, err)

	transitionPolicyToExpiredStateJob, err := NewTransitionPoliciesToExpiredStateJob(&deps)
	multiErr = multierr.Append(multiErr, err)

	transitionPolicyToActiveStateJob, err := NewTransitionPoliciesToActiveStateJob(&deps)
	multiErr = multierr.Append(multiErr, err)

	jobTriggerParseLossRuns, err := NewTriggerParseLossRuns(&deps)
	multiErr = multierr.Append(multiErr, err)

	sendTelematicsConnectionOnAgentBehalfEmailJob, err := NewSendTelematicsConnectionEmailOnAgentBehalfJob(&deps)
	multiErr = multierr.Append(multiErr, err)

	sendTelematicsConnectionSMSToInsuredJob, err := NewSendTelematicsConnectionSMSToInsuredJob(&deps)
	multiErr = multierr.Append(multiErr, err)

	checkClearanceJob, err := NewCheckClearanceJob(&deps)
	multiErr = multierr.Append(multiErr, err)

	updateSfdcWinningCarrierJob, err := NewUpdateSfdcWinningCarrierJob(&deps)
	multiErr = multierr.Append(multiErr, err)

	appetiteLiteSnowflakePostgresSyncJob, err := NewAppetiteLiteSnowflakePostgresSyncJob(&deps)
	multiErr = multierr.Append(multiErr, err)

	reportRiskMetricsJob, err := NewReportRiskMetricsJob(&deps)
	multiErr = multierr.Append(multiErr, err)

	sendUserInviteEmailJob, err := NewSendUserInviteEmailJob(&deps)
	multiErr = multierr.Append(multiErr, err)

	verifyTSPConnection, err := NewVerifyTSPConnection(&deps)
	multiErr = multierr.Append(multiErr, err)

	indicationWorkflowJob, err := NewIndicationWorkflowJob(&deps)
	multiErr = multierr.Append(multiErr, err)

	syncApplicationBDJob, err := NewSyncApplicationBD(&deps)
	multiErr = multierr.Append(multiErr, err)

	submissionForUWWorkflowJob, err := NewSubmissionForUWWorkflowJob(&deps)
	multiErr = multierr.Append(multiErr, err)

	updateSubmissionForUWWorkflowJob, err := NewUpdateSubmissionForUWWorkflowJob(&deps)
	multiErr = multierr.Append(multiErr, err)

	updateSubmissionForUWOrchestratorJob, err := NewUpdateSubmissionForUWOrchestratorJob(&deps)
	multiErr = multierr.Append(multiErr, err)

	quoteWorkflowJob, err := NewQuoteWorkflowJob(&deps)
	multiErr = multierr.Append(multiErr, err)

	if multiErr != nil {
		return errors.Wrap(err, "failed to initialise one more more jobs")
	}

	return multierr.Combine(multiErr,
		registry.AddJob(r, basicApplicationSubmittedJob),
		registry.AddJob(r, finalApplicationSubmittedJob),
		registry.AddJob(r, regenerateQuoteJob),
		registry.AddJob(r, generateBindableSubQuoteJob),
		registry.AddJob(r, generateQuotePDFJob),
		registry.AddJob(r, generateAppPDFJob),
		registry.AddJob(r, sendTelematicsConsentReminderEmailJob),
		registry.AddJob(r, scheduleTelematicsConsentReminderEmailsJob),
		registry.AddJob(r, sendSubmissionAcknowledgementEmailJob),
		registry.AddJob(r, transitionLostAppAndAppReviewsJob),
		registry.AddJob(r, sendTelematicsConnectionSuccessfulEmailJob),
		registry.AddJob(r, sendPostBindOnboardingEmailJob),
		registry.AddJob(r, dataPipelineStateChangedjob),
		registry.AddJob(r, markPendingAppReviewsToStale),
		registry.AddJob(r, jobTriggerCarrierLoyaltyMetaflow),
		registry.AddJob(r, populatePolicySetJob),
		registry.AddJob(r, transitionPolicyToExpiredStateJob),
		registry.AddJob(r, transitionPolicyToActiveStateJob),
		registry.AddJob(r, jobTriggerParseLossRuns),
		registry.AddJob(r, sendTelematicsConnectionOnAgentBehalfEmailJob),
		registry.AddJob(r, sendTelematicsConnectionSMSToInsuredJob),
		registry.AddJob(r, checkClearanceJob),
		registry.AddJob(r, updateSfdcWinningCarrierJob),
		registry.AddJob(r, appetiteLiteSnowflakePostgresSyncJob),
		registry.AddJob(r, reportRiskMetricsJob),
		registry.AddJob(r, sendUserInviteEmailJob),
		registry.AddJob(r, verifyTSPConnection),
		registry.AddJob(r, indicationWorkflowJob),
		registry.AddJob(r, syncApplicationBDJob),
		registry.AddJob(r, submissionForUWWorkflowJob),
		registry.AddJob(r, updateSubmissionForUWWorkflowJob),
		registry.AddJob(r, updateSubmissionForUWOrchestratorJob),
		registry.AddJob(r, quoteWorkflowJob),
	)
}
