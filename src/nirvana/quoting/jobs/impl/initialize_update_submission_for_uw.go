package impl

import (
	"context"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/jobber/job_utils"
	"nirvanatech.com/nirvana/jobber/jtypes"
	"nirvanatech.com/nirvana/quoting/jobs"
	uw_utils "nirvanatech.com/nirvana/underwriting/app_review/utils"
	underwriting_utils "nirvanatech.com/nirvana/underwriting/utils"
)

type initializeUpdateSubmissionForUWTask struct {
	deps *Deps
	job_utils.NoopUndoTask[*jobs.UpdateSubmissionForUWWorkflowMessage]
	job_utils.DefaultRetryable[*jobs.UpdateSubmissionForUWWorkflowMessage]
}

func (s *initializeUpdateSubmissionForUWTask) ID() string {
	return initializeUpdateSubmissionForUWTaskId
}

func (s *initializeUpdateSubmissionForUWTask) Run(
	ctx jtypes.Context,
	message *jobs.UpdateSubmissionForUWWorkflowMessage,
) (err error) {
	// Transition app review to UpdateSubmissionForUW state
	appReviewId, err := uuid.Parse(message.ReviewID)
	if err != nil {
		return errors.Wrapf(err, "failed to parse app review id")
	}
	err = s.deps.AppReviewStateMachine.TransitionToUpdateSubmissionForUw(ctx, appReviewId)
	if err != nil {
		return errors.Wrapf(err, "failed to transition app review to refreshing-premiums")
	}
	// transition to pending state even if there is an error
	defer func(ctx context.Context, appReviewId uuid.UUID) {
		err := s.deps.AppReviewStateMachine.TransitionToPending(ctx, appReviewId)
		if err != nil {
			log.Error(ctx, "Failed to transition app review to pending", log.Err(err))
		}
	}(ctx, appReviewId)

	// Fetch updated appReview after applying experiments
	appReview, err := s.deps.AppReviewWrapper.GetReview(ctx, appReviewId.String())
	if err != nil || appReview == nil {
		log.Error(
			ctx,
			"Failed to fetch application review",
			log.Err(err),
		)
		return errors.Wrap(err, "unable to get review by id")
	}

	// Create and insert submission from app review submission
	submission := appReview.Submission
	submission.ID = message.SubmissionID.String()
	submission.IndicationOptionsIDs = nil
	err = s.deps.AppWrapper.InsertSubmission(ctx, submission)
	if err != nil {
		return errors.Wrap(err, "failed to insert new submission to db")
	}

	// Step 3: Apply pricing experiments (requires submission to exist in DB)
	appId, err := uuid.Parse(appReview.ApplicationID)
	if err != nil {
		return errors.Wrapf(err, "failed to parse app id")
	}

	err = s.deps.QuotingExperimentsManager.ApplyUWExperiments(
		ctx,
		appId,
		message.SubmissionID,
		appReviewId,
	)
	if err != nil {
		log.Error(
			ctx,
			"failed to get and apply pricing experiments",
			log.Err(err),
		)
		return errors.Wrap(err, "unable to get and apply pricing experiments")
	}

	// Fetch updated appReview after experiments are applied
	// This is necessary because for legacy app reviews (without risk worksheet),
	// the experiments apply directly to the app review.
	// TODO: Remove this step once all app reviews have risk worksheet
	appReview, err = s.deps.AppReviewWrapper.GetReview(ctx, message.ReviewID)
	if err != nil || appReview == nil {
		log.Error(
			ctx,
			"Failed to fetch application review after experiments",
			log.Err(err),
		)
		return errors.Wrap(err, "unable to get review by id after experiments")
	}

	// Now compile the submission with experiments applied
	compiledSubmission := underwriting_utils.CompileSubmissionAndUnderwriterData(ctx, submission, *appReview,
		s.deps.RiskFactorsClient)

	// Generate vehicle zones for the submission
	vehicleZones, err := uw_utils.GenerateVehicleZones(ctx, s.deps.FeatureStore, appReview)
	if err != nil {
		return errors.Wrap(err, "unable to generate vehicle zones")
	}
	if vehicleZones != nil {
		compiledSubmission.UnderwriterInput.VehicleZoneDistribution = &vehicleZones
	}

	// Update the submission with compiled data
	err = s.deps.AppWrapper.UpdateSub(ctx, submission.ID, func(sub application.SubmissionObject) (application.SubmissionObject, error) {
		return compiledSubmission, nil
	})
	if err != nil {
		return errors.Wrap(err, "failed to update submission with compiled data")
	}

	// Update existing app review's LatestSubmissionID
	err = s.deps.AppReviewWrapper.UpdateAppReview(
		ctx,
		appReview.Id,
		func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
			if message.Reason == jobs.QuoteRefresh {
				review.LatestUwSubmission = compiledSubmission
			} else if message.Reason == jobs.ReadyForReview {
				review.ReviewReadinessSubID = &compiledSubmission.ID
			} else {
				return review, errors.Newf("unknown reason %s for running %s", message.Reason,
					jobs.UpdateSubmissionForUWOrchestrator)
			}
			// Remove the existing indicationOptions
			review.QuoteInfo.IndicationOptionsIDs = nil
			return review, nil
		},
	)
	if err != nil {
		return errors.Wrap(err, "failed to update app review's latest submission ID")
	}

	return nil
}

func (s *initializeUpdateSubmissionForUWTask) Retry(
	ctx jtypes.Context,
	message *jobs.UpdateSubmissionForUWWorkflowMessage,
) (err error) {
	return s.Run(ctx, message)
}

func newInitializeUpdateSubmissionForUWTask(deps *Deps) jtypes.TaskCreator[*jobs.UpdateSubmissionForUWWorkflowMessage] {
	return func() jtypes.Task[*jobs.UpdateSubmissionForUWWorkflowMessage] {
		return &initializeUpdateSubmissionForUWTask{deps: deps}
	}
}

var _ jtypes.Task[*jobs.UpdateSubmissionForUWWorkflowMessage] = (*initializeUpdateSubmissionForUWTask)(nil)
