load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "impl",
    srcs = [
        "actiontype_enumer.go",
        "add_jobs.go",
        "appetite_lite_snowflake_postgres_sync.go",
        "check_clearance.go",
        "datapipeline_state_change.go",
        "fx.go",
        "generate_app_pdf.go",
        "generate_indication_job.go",
        "generate_quote_job.go",
        "generate_quote_pdf.go",
        "generate_submission_for_uw.go",
        "indication_task_runner.go",
        "indication_workflow_job.go",
        "initialize_quote_job.go",
        "initialize_update_submission_for_uw.go",
        "log_event.go",
        "populate_policy_set.go",
        "pricing_trail_event.go",
        "pricingtraileventtype_enumer.go",
        "queries.go",
        "quote_workflow_job.go",
        "report_risk_metrics.go",
        "schedule_telematics_reminder_emails.go",
        "send_post_bind_onboarding_email.go",
        "send_submission_acknowledgement_email.go",
        "send_telematics_connection_email_on_agent_behalf.go",
        "send_telematics_connection_sms_to_insured.go",
        "send_telematics_connection_successful_email.go",
        "send_telematics_reminder_email.go",
        "send_user_invite_email.go",
        "smart_indication.go",
        "statustype_enumer.go",
        "submission_for_uw_workflow_job.go",
        "sync_application_bds.go",
        "transition_app_reviews_to_stale.go",
        "transition_apps_to_closed_state_job.go",
        "transition_policies_to_active_state.go",
        "transition_policies_to_expired_state.go",
        "trigger_carrier_loyalty_metaflow.go",
        "trigger_parse_loss_runs.go",
        "update_sfdc_winning_carrier.go",
        "update_submission_for_uw.go",
        "update_submission_for_uw_orchestrator.go",
        "update_submission_for_uw_workflow.go",
        "utils.go",
        "verify_tsp_connection.go",
    ],
    importpath = "nirvanatech.com/nirvana/quoting/jobs/impl",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/api-server/quoting_jobber",
        "//nirvana/api-server/rule-engine",
        "//nirvana/application/experiments/non_fleet",
        "//nirvana/application/experiments/non_fleet/pre_telematics_quote",
        "//nirvana/common-go/application-util",
        "//nirvana/common-go/auth-util",
        "//nirvana/common-go/feature_flag_lib",
        "//nirvana/common-go/file_upload_lib",
        "//nirvana/common-go/file_upload_lib/enums",
        "//nirvana/common-go/insurance_carriers_utils",
        "//nirvana/common-go/log",
        "//nirvana/common-go/metrics",
        "//nirvana/common-go/pibit_ai",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/postgres_utils",
        "//nirvana/common-go/problem",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/common-go/url_util",
        "//nirvana/common-go/us_states",
        "//nirvana/common-go/zip_code_utils",
        "//nirvana/db-api",
        "//nirvana/db-api/db_models",
        "//nirvana/db-api/db_wrappers/agency",
        "//nirvana/db-api/db_wrappers/agency_bd_mapping",
        "//nirvana/db-api/db_wrappers/appetite_lite",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/application/model-output",
        "//nirvana/db-api/db_wrappers/auth",
        "//nirvana/db-api/db_wrappers/business_auto/application",
        "//nirvana/db-api/db_wrappers/ds",
        "//nirvana/db-api/db_wrappers/emails/application",
        "//nirvana/db-api/db_wrappers/emails/application/enums",
        "//nirvana/db-api/db_wrappers/external/pibit",
        "//nirvana/db-api/db_wrappers/fleet",
        "//nirvana/db-api/db_wrappers/fleet_telematics",
        "//nirvana/db-api/db_wrappers/fmcsa",
        "//nirvana/db-api/db_wrappers/forms",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/nonfleet/enums",
        "//nirvana/db-api/db_wrappers/policy",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/db-api/db_wrappers/policy_set",
        "//nirvana/db-api/db_wrappers/policy_set/enums",
        "//nirvana/db-api/db_wrappers/risk_metrics",
        "//nirvana/db-api/db_wrappers/sharing",
        "//nirvana/db-api/db_wrappers/uw",
        "//nirvana/distsem",
        "//nirvana/ds/model",
        "//nirvana/emailer",
        "//nirvana/emailer/models",
        "//nirvana/events",
        "//nirvana/events/quoting_events",
        "//nirvana/external_client/salesforce/jobs",
        "//nirvana/external_client/salesforce/jobs/enums",
        "//nirvana/external_client/salesforce/wrapper",
        "//nirvana/external_client/verisk",
        "//nirvana/external_data_management/clients_management",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/external_data_management/data_processing",
        "//nirvana/external_data_management/interceptors_management/read_from_store_interceptor",
        "//nirvana/external_data_management/interceptors_management/write_to_store_interceptor",
        "//nirvana/external_data_management/nhtsa",
        "//nirvana/feature_store",
        "//nirvana/fmcsa/models",
        "//nirvana/infra/authz",
        "//nirvana/infra/config",
        "//nirvana/infra/constants",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/insurance-core/monitoring",
        "//nirvana/jobber",
        "//nirvana/jobber/event",
        "//nirvana/jobber/job_utils",
        "//nirvana/jobber/jtypes",
        "//nirvana/jobber/registry",
        "//nirvana/metaflow",
        "//nirvana/metaflow/grpc",
        "//nirvana/nonfleet/quoting-jobs",
        "//nirvana/nonfleet/state-machine",
        "//nirvana/oauth",
        "//nirvana/pdfgen",
        "//nirvana/policy",
        "//nirvana/policy/enums",
        "//nirvana/policy_common/forms_generator",
        "//nirvana/pricing/explainability/jobs/process_entities",
        "//nirvana/pricing/explainability/jobs/process_entities/messages",
        "//nirvana/quoting/ancillary_coverages",
        "//nirvana/quoting/app_state_machine",
        "//nirvana/quoting/app_state_machine/enums",
        "//nirvana/quoting/clearance",
        "//nirvana/quoting/clearance/emails",
        "//nirvana/quoting/clearance/enums",
        "//nirvana/quoting/emailer",
        "//nirvana/quoting/experiments",
        "//nirvana/quoting/jobs",
        "//nirvana/quoting/jobs/impl/checker",
        "//nirvana/quoting/jobs/messages",
        "//nirvana/quoting/pricing_client_wrapper",
        "//nirvana/quoting/quote_generator",
        "//nirvana/quoting/quoting_logic",
        "//nirvana/quoting/quoting_metrics",
        "//nirvana/quoting/reporting",
        "//nirvana/quoting/tags",
        "//nirvana/quoting/utils",
        "//nirvana/rating/adaptors/fleet_adaptor",
        "//nirvana/rating/artifacts",
        "//nirvana/rating/data_fetching/lni_fetching",
        "//nirvana/rating/data_fetching/mvr_fetching",
        "//nirvana/rating/data_processing/lni_processing",
        "//nirvana/rating/rateml/program",
        "//nirvana/rating/utils",
        "//nirvana/risk_metrics/jobs/messages",
        "//nirvana/servers/telematicsv2",
        "//nirvana/sharing",
        "//nirvana/telematics",
        "//nirvana/telematics/connections",
        "//nirvana/telematics/data_platform/api",
        "//nirvana/telematics/data_platform/workflows/common",
        "//nirvana/telematics/data_platform/workflows/jobs",
        "//nirvana/textmessaging",
        "//nirvana/textmessaging/models",
        "//nirvana/underwriting/app_review",
        "//nirvana/underwriting/app_review/actions",
        "//nirvana/underwriting/app_review/actions/reasons",
        "//nirvana/underwriting/app_review/utils",
        "//nirvana/underwriting/app_review_widget_manager",
        "//nirvana/underwriting/common",
        "//nirvana/underwriting/jobs",
        "//nirvana/underwriting/risk_factors",
        "//nirvana/underwriting/rule-engine/authorities",
        "//nirvana/underwriting/state_machine",
        "//nirvana/underwriting/utils",
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_cactus_go_statsd_client_v5//statsd",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_looplab_fsm//:fsm",
        "@com_github_pagerduty_go_pagerduty//:go-pagerduty",
        "@com_github_volatiletech_null_v8//:null",
        "@in_gopkg_segmentio_analytics_go_v3//:analytics-go_v3",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_multierr//:multierr",
    ],
)

go_test(
    name = "impl_test",
    srcs = [
        "rateml_output_test.go",
        "send_telematics_reminder_email_test.go",
    ],
    embed = [":impl"],
    deps = [
        "//nirvana/api-server/interceptors/application/deps",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/problem",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/auth",
        "//nirvana/db-api/db_wrappers/fleet",
        "//nirvana/db-api/db_wrappers/fleet_telematics",
        "//nirvana/external_data_management/clients_management",
        "//nirvana/external_data_management/data_processing/vin/testing/testfixtures",
        "//nirvana/external_data_management/interceptors_management/read_from_store_interceptor",
        "//nirvana/external_data_management/interceptors_management/write_to_store_interceptor",
        "//nirvana/golden_dataset",
        "//nirvana/infra/authz",
        "//nirvana/infra/fx/testfixtures/fmcsa_fixture",
        "//nirvana/infra/fx/testloader",
        "//nirvana/jobber/test_utils",
        "//nirvana/oauth",
        "//nirvana/policy_common/constants",
        "//nirvana/quoting/app_state_machine/app_logic",
        "//nirvana/quoting/pricing_client_wrapper",
        "//nirvana/rating/adaptors/fleet_adaptor",
        "//nirvana/rating/rtypes",
        "//nirvana/rating/utils",
        "//nirvana/telematics",
        "//nirvana/telematics/connections",
        "//nirvana/telematics/connections/oauth",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//require",
        "@com_github_volatiletech_null_v8//:null",
        "@org_uber_go_fx//:fx",
    ],
)
