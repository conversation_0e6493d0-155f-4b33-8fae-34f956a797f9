package app_state_machine

import (
	"context"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/db-api/db_wrappers/agency"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	"nirvanatech.com/nirvana/events"
	"nirvanatech.com/nirvana/events/quoting_events"
	policy_constants "nirvanatech.com/nirvana/policy_common/constants"
	"nirvanatech.com/nirvana/quoting/appetite_checker"
	app_chkr "nirvanatech.com/nirvana/quoting/appetite_checker"
)

//go:generate go run github.com/dmarkham/enumer -type=StatusType
type StatusType int

const (
	Created StatusType = iota
	Declined
	Flagged
)

func logApplicationEvent(
	ctx context.Context,
	appWrapper application.DataWrapper,
	authWrapper auth.DataWrapper,
	eventHandler events.EventsHandler,
	agencyWrapper agency.DataWrapper,
	eventType StatusType,
	appId string,
	declinedRules appetite_checker.DeclinedRules,
	yearsInBusiness *float64,
	insurance *quoting_events.CurrentInsurance,
	isRenewal bool,
	renewalOriginalApplicationId string,
	insuranceCarrier policy_constants.InsuranceCarrier,
	isNonAdmitted bool,
) {
	appObj, err := appWrapper.GetAppById(ctx, appId)
	if err != nil {
		err = errors.Wrap(err, "unable to fetch application")
		log.Warn(ctx, "unable to create event", log.String("event type", eventType.String()), log.Err(err))
		return
	}
	// Mandatory Agent, and Agency fetches.
	underwriter, err := authWrapper.FetchAuthzUser(ctx, appObj.UnderwriterID)
	if err != nil {
		err = errors.Wrap(err, "unable to fetch underwriter")
		log.Warn(ctx, "unable to create event", log.String("event type", eventType.String()), log.Err(err))
		return
	}
	agentId, err := uuid.Parse(appObj.CreatedBy)
	if err != nil {
		err = errors.Wrapf(err, "unable to parse created by id %s to uuid", appObj.CreatedBy)
		log.Warn(ctx, "unable to create event", log.String("event type", eventType.String()), log.Err(err))
		return
	}
	agent, err := authWrapper.FetchAuthzUser(ctx, agentId)
	if err != nil {
		err = errors.Wrapf(err, "unable to fetch agent %s", agentId.String())
		log.Warn(ctx, "unable to create event", log.String("event type", eventType.String()), log.Err(err))
		return
	}
	appAgency, err := agencyWrapper.FetchAgency(ctx, appObj.AgencyID)
	if err != nil {
		err = errors.Wrapf(err, "unable to fetch agency %s", agentId.String())
		log.Warn(ctx, "unable to create event", log.String("event type", eventType.String()), log.Err(err))
		return
	}

	base := quoting_events.BaseEventInputs{
		Agency:      appAgency,
		Underwriter: *underwriter,
		Agent:       *agent,
	}

	var producerInfo quoting_events.ProducerInfo
	if appObj.ProducerID != nil {

		producerId, err := uuid.Parse(*appObj.ProducerID)
		if err != nil {
			err = errors.Wrapf(err, "unable to parse producer id %s to uuid", *appObj.ProducerID)
			log.Warn(ctx, "unable to create event", log.String("event type", eventType.String()), log.Err(err))
			return
		}
		producer, err := authWrapper.FetchAuthzUser(ctx, producerId)
		if err != nil {
			err = errors.Wrapf(err, "unable to fetch producer %s", appObj.ProducerID)
			log.Warn(ctx, "unable to create event", log.String("event type", eventType.String()), log.Err(err))
			return
		}

		producerInfo = quoting_events.ProducerInfo{
			Id:    producer.ID.String(),
			Name:  producer.FullName(),
			Email: producer.Email,
		}
	}
	event, err := createApplicationEvent(
		ctx,
		eventType,
		base,
		appObj,
		declinedRules,
		yearsInBusiness,
		insurance,
		isRenewal,
		renewalOriginalApplicationId,
		producerInfo,
		insuranceCarrier,
		isNonAdmitted,
	)
	if err != nil {
		log.Warn(ctx, "unable to create event", log.String("event type", eventType.String()), log.Err(err))
	} else if err := eventHandler.UploadEvent(ctx, event); err != nil {
		log.Warn(ctx, "unable to upload event", log.String("event",
			event.UserVisibleName()), log.Err(err))
	}
}

func createApplicationEvent(
	ctx context.Context,
	statusType StatusType,
	base quoting_events.BaseEventInputs,
	appObj *application.Application,
	declinedRules app_chkr.DeclinedRules,
	yearsInBusiness *float64,
	insurance *quoting_events.CurrentInsurance,
	isRenewal bool,
	renewalOriginalApplicationId string,
	producerInfo quoting_events.ProducerInfo,
	insuranceCarrier policy_constants.InsuranceCarrier,
	isNonAdmitted bool,
) (events.Event, error) {
	switch {
	case statusType == Created:
		return quoting_events.NewQuotingApplicationCreated(
			ctx,
			base,
			*appObj,
			yearsInBusiness,
			insurance,
			isRenewal,
			renewalOriginalApplicationId,
			producerInfo,
			insuranceCarrier,
			isNonAdmitted,
		)
	case statusType == Declined:
		return quoting_events.NewQuotingApplicationDeclined(ctx, base, *appObj, declinedRules)
	case statusType == Flagged:
		return quoting_events.NewQuotingApplicationFlagged(ctx, base, *appObj)
	default:
		return nil, errors.Newf("unable to find event of type %s", statusType.String())
	}
}

// logProducerAddedEvent sends an event to Segment with the information of the producer.
func logProducerAddedEvent(
	ctx context.Context,
	app application.Application,
	authWrapper auth.DataWrapper,
	agencyWrapper agency.DataWrapper,
	eventHandler events.EventsHandler,
) {
	producerID, err := uuid.Parse(*app.ProducerID)
	if err != nil {
		err = errors.Wrapf(err, "Couldn't parse producer ID %s", app.ProducerID)
		log.Warn(ctx, "unable to upload added producer event", log.Err(err))
		return
	}

	p, err := authWrapper.FetchAuthzUser(ctx, producerID)
	if err != nil {
		log.Warn(ctx, "couldn't find producer")
		return
	}

	event, err := quoting_events.NewProducerAddedEvent(ctx, p, app, authWrapper, agencyWrapper)
	if err != nil {
		log.Warn(ctx, "failed to construct producer added event")
		return
	}

	err = eventHandler.UploadEvent(ctx, event)
	if err != nil {
		log.Warn(ctx, "failed to upload event for added producer")
	}
}
