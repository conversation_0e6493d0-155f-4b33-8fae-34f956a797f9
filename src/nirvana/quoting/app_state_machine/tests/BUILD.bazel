load("@io_bazel_rules_go//go:def.bzl", "go_test")

go_test(
    name = "tests_test",
    srcs = [
        "declined_state_test.go",
        "indication_generated_test.go",
        "panic_state_test.go",
        "policy_created_test.go",
        "quote_generated_test.go",
        "state_transitions_test.go",
        "under_uw_review_test.go",
        "unsubmitted_state_test.go",
    ],
    deps = [
        "//nirvana/billing/enums",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/test_utils",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/policy",
        "//nirvana/db-api/db_wrappers/uw",
        "//nirvana/experiments",
        "//nirvana/experiments/client",
        "//nirvana/experiments/enums",
        "//nirvana/external_data_management/data_processing/vin/testing/testfixtures",
        "//nirvana/infra/fx/testfixtures/emailer_fixture",
        "//nirvana/infra/fx/testfixtures/feature_store_fixture",
        "//nirvana/infra/fx/testfixtures/fmcsa_fixture",
        "//nirvana/infra/fx/testfixtures/lni_fixture",
        "//nirvana/infra/fx/testloader",
        "//nirvana/openapi-specs/components/application",
        "//nirvana/openapi-specs/components/common",
        "//nirvana/quoting/app_state_machine/cerrors",
        "//nirvana/quoting/app_state_machine/enums",
        "//nirvana/quoting/app_state_machine/test_utils",
        "//nirvana/quoting/experiments",
        "//nirvana/quoting/experiments/impl/common",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_go_cmp//cmp",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//suite",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
        "@org_uber_go_mock//gomock",
    ],
)
