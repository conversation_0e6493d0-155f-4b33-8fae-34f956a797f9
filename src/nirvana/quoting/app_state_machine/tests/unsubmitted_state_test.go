package tests

import (
	"context"
	"testing"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"go.uber.org/mock/gomock"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/experiments"
	"nirvanatech.com/nirvana/experiments/client"
	experiments_enums "nirvanatech.com/nirvana/experiments/enums"
	"nirvanatech.com/nirvana/external_data_management/data_processing/vin/testing/testfixtures"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/feature_store_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/fmcsa_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/lni_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	oapi_app "nirvanatech.com/nirvana/openapi-specs/components/application"
	"nirvanatech.com/nirvana/openapi-specs/components/common"
	"nirvanatech.com/nirvana/quoting/app_state_machine/cerrors"
	state_enums "nirvanatech.com/nirvana/quoting/app_state_machine/enums"
	"nirvanatech.com/nirvana/quoting/app_state_machine/test_utils"
	quoting_experiments "nirvanatech.com/nirvana/quoting/experiments"
	quoting_experiments_common "nirvanatech.com/nirvana/quoting/experiments/impl/common"
)

func TestUnsubmittedStateTestSuite(t *testing.T) {
	suite.Run(t, new(unsubmittedStateTestSuite))
}

type unsubmittedStateTestSuite struct {
	suite.Suite
	h     *test_utils.AppStateMachineHarness
	fxApp *fxtest.App

	ctx  context.Context
	ctrl *gomock.Controller

	experimentId          uuid.UUID
	mockExperimentsClient *client.MockExperimentsClient
	mockExperiment        *quoting_experiments.MockExperiment
}

func (s *unsubmittedStateTestSuite) TearDownTest() {
	s.fxApp.RequireStop()
}

func (s *unsubmittedStateTestSuite) SetupTest() {
	var env struct {
		fx.In

		ExperimentsRegistry *quoting_experiments.Registry
		Harness             *test_utils.AppStateMachineHarness
		*testfixtures.NhtsaToIsoMappingV1Fixture
		*feature_store_fixture.FeatureStoreFixture
		*fmcsa_fixture.FmcsaFixture
		*lni_fixture.ActiveOrPendingInsuranceFixture
	}

	s.ctrl, s.ctx = gomock.WithContext(context.Background(), s.T())
	s.experimentId = uuid.New()
	s.mockExperiment = quoting_experiments.NewMockExperiment(s.ctrl)

	s.mockExperimentsClient = client.NewMockExperimentsClient(s.ctrl)

	newMockExperimentsClient := func(realClient experiments.Client) experiments.Client {
		return s.mockExperimentsClient
	}

	s.fxApp = testloader.RequireStart(
		s.T(),
		&env,
		testloader.Use(fx.Decorate(newMockExperimentsClient)),
	)
	s.h = env.Harness

	s.mockExperiment.EXPECT().
		Id().
		Return(s.experimentId).
		AnyTimes()

	env.ExperimentsRegistry.Reset_TestOnly()
	env.ExperimentsRegistry.Register(s.experimentId, s.mockExperiment)
}

// TestGetAppDetailsFromBasicInfo validates that the basic data of an
// application gets stored/can be accessed correctly.
func (s *unsubmittedStateTestSuite) TestGetAppDetailsFromBasicInfo() {
	basicAppData := s.h.MockBasicAppData()
	appId, err := s.h.CreateApplication(s.h.Ctx, basicAppData)
	s.Require().NoError(err)
	s.Require().NotNil(appId)
	machine := s.h.Deps.AppStateMachineWrapper.NewAppStateMachine(appId)
	s.Require().NotNil(machine)
	state, err := s.h.GetAppState(appId)
	s.Require().NoError(err)
	s.Require().Equal(*state, state_enums.AppStateUnsubmitted)
	details, err := machine.GetApplicationDetails(s.h.Ctx)
	s.Require().NoError(err)
	s.Require().NotNil(details)
	s.Require().Equal(details.Summary.State, oapi_app.ApplicationStateInProgress)
	test_utils.ValidateSummaryInfo(&s.Suite, basicAppData, details.Summary)
}

// TestGetAppDetailsAfterIndicationFormUpdated validates that the indication
// form data can be updated/accessed correctly. Additionally, we check that
// the app maintains Unsubmitted BE state and InProgress FE state.
func (s *unsubmittedStateTestSuite) TestGetAppDetailsAfterIndicationFormUpdated() {
	basicAppData := s.h.MockBasicAppData()
	appId, err := s.h.CreateApplication(s.h.Ctx, basicAppData)
	s.Require().NoError(err)
	s.Require().NotNil(appId)
	machine := s.h.Deps.AppStateMachineWrapper.NewAppStateMachine(appId)
	s.Require().NotNil(machine)
	// Update Indication Form
	form := s.h.AppStateMachineHelper.MockIndicationFormData(
		s.h.MockUsers.DefaultUserId,
		common.CoverageAutoLiability,
		common.CoverageAutoPhysicalDamage,
	)
	err = machine.UpdateIndicationForm(s.h.Ctx, form)
	s.Require().NoError(err)
	state, err := s.h.GetAppState(appId)
	s.Require().NoError(err)
	s.Require().Equal(*state, state_enums.AppStateUnsubmitted)
	details, err := machine.GetApplicationDetails(s.h.Ctx)
	s.Require().NoError(err)
	s.Require().NotNil(details)
	s.Require().Equal(details.Summary.State, oapi_app.ApplicationStateInProgress)
	test_utils.ValidateIndicationFormInfo(&s.Suite, form, *details)
}

// TestGetAppDetailsAfterIndicationFormUpdatedForMTC validates that the indication
// form data can be updated/accessed correctly when MTC Coverage is selected. Additionally,
// we check that the app maintains Unsubmitted BE state and InProgress FE state.
func (s *unsubmittedStateTestSuite) TestGetAppDetailsAfterIndicationFormUpdatedForMTC() {
	basicAppData := s.h.MockBasicAppDataC(2081158)
	appId, err := s.h.CreateApplication(s.h.Ctx, basicAppData)
	s.Require().NoError(err)
	s.Require().NotNil(appId)
	machine := s.h.Deps.AppStateMachineWrapper.NewAppStateMachine(appId)
	s.Require().NotNil(machine)
	// Update Indication Form
	form := s.h.AppStateMachineHelper.MockIndicationFormData(
		s.h.MockUsers.DefaultUserId,
		common.CoverageAutoLiability,
		common.CoverageAutoPhysicalDamage,
		common.CoverageMotorTruckCargo,
	)
	err = machine.UpdateIndicationForm(s.h.Ctx, form)
	s.Require().NoError(err)
	state, err := s.h.GetAppState(appId)
	s.Require().NoError(err)
	s.Require().Equal(*state, state_enums.AppStateUnsubmitted)
	details, err := machine.GetApplicationDetails(s.h.Ctx)
	s.Require().NoError(err)
	s.Require().NotNil(details)
	s.Require().Equal(details.Summary.State, oapi_app.ApplicationStateInProgress)
	test_utils.ValidateIndicationFormInfo(&s.Suite, form, *details)
}

// TestBeginAndSetIndication checks that Begin/Set Indication process
// effectively manages the application states.
func (s *unsubmittedStateTestSuite) TestBeginAndSetIndication() {
	basicAppData := s.h.MockBasicAppData()
	appId, err := s.h.CreateApplication(s.h.Ctx, basicAppData)
	s.Require().NoError(err)
	s.Require().NotNil(appId)

	s.mockExperimentsClient.EXPECT().
		GetLatestExperimentsAssignations(gomock.Any(), experiments_enums.DomainPricing, appId).
		Return([]*experiments.Assignment{}, nil).
		Times(1)

	form := s.h.AppStateMachineHelper.MockIndicationFormData(
		s.h.MockUsers.DefaultUserId,
		common.CoverageAutoLiability,
		common.CoverageAutoPhysicalDamage,
	)
	machine := s.h.Deps.AppStateMachineWrapper.NewAppStateMachine(appId)
	err = machine.UpdateIndicationForm(s.h.Ctx, form)
	s.Require().NoError(err)

	subId, err := machine.BeginIndicationGeneration(s.h.Ctx)
	s.Require().NoError(err)
	s.Require().NotNil(subId)
	state, err := s.h.GetAppState(appId)
	s.Require().NoError(err)
	s.Require().Equal(*state, state_enums.AppStateUnsubmitted)
	details, err := machine.GetApplicationDetails(s.h.Ctx)
	s.Require().NoError(err)
	s.Require().NotNil(details)
	s.Require().Equal(details.Summary.State, oapi_app.ApplicationStateUnderReviewForIndication)
	// Finish task
	subObj, err := s.h.Deps.AppWrapper.GetSubmissionById(s.h.Ctx, subId)
	err = s.h.Deps.Jobber.WaitForJobRunCompletion(s.h.Ctx, *subObj.JobRunId)
	s.Require().NoError(err)
	state, err = s.h.GetAppState(appId)
	s.Require().NoError(err)
	s.Require().Equal(*state, state_enums.AppStateIndicationGenerated)

	details, err = machine.GetApplicationDetails(s.h.Ctx)
	s.Require().NoError(err)
	s.Require().NotNil(details)
	s.Require().Equal(details.Summary.State, oapi_app.ApplicationStateIndicationGenerated)

	// Check passing of DataContextID from app to sub
	appObj, err := s.h.Deps.AppWrapper.GetAppById(s.h.Ctx, appId)
	s.Require().NoError(err)
	s.Require().Equal(subObj.DataContextID, appObj.DataContextID)
}

// TestParallelSub tests the parallel processing of submissions. The flow
// is as follows:
// 1. Begin an indication generation
// 2. Update the indication form. This should fail because 1 is still in progress
// 3. Try to manually set 1. and expect it to pass
func (s *unsubmittedStateTestSuite) TestParallelSub() {
	s.T().Skip()
	// Step 1
	basicAppData := s.h.MockBasicAppData()
	appId, err := s.h.CreateApplication(s.h.Ctx, basicAppData)
	s.Require().NoError(err)
	machine := s.h.Deps.AppStateMachineWrapper.NewAppStateMachine(appId)
	// Update indication form
	form := s.h.AppStateMachineHelper.MockIndicationFormData(
		s.h.MockUsers.DefaultUserId,
		common.CoverageAutoLiability,
		common.CoverageAutoPhysicalDamage,
	)
	err = machine.UpdateIndicationForm(s.h.Ctx, form)
	s.Require().NoError(err)
	// Begin Indication Generation
	firstSubId, err := machine.BeginIndicationGeneration(s.h.Ctx)
	s.Require().NoError(err)
	latestSubId, err := s.h.GetAppIndicationSubmissionId(appId)
	s.Require().Equal(firstSubId, *latestSubId)
	// Step 2
	// Update indication form. This will fail
	err = machine.UpdateIndicationForm(s.h.Ctx, form)
	s.Require().Error(err)
	s.Require().True(errors.Is(err, cerrors.ErrActionNotValid))
	// Step 3
	// Try to manually set 1. and pass
	s.Require().NoError(machine.SetIndication(s.h.Ctx, firstSubId))
}

// TestNotValidSetIndication checks that SetIndication should return an
// error when manually called before BeginIndicationGeneration.
func (s *unsubmittedStateTestSuite) TestNotValidSetIndication() {
	basicAppData := s.h.MockBasicAppData()
	appId, err := s.h.CreateApplication(s.h.Ctx, basicAppData)
	s.Require().NoError(err)
	s.Require().NotNil(appId)
	machine := s.h.Deps.AppStateMachineWrapper.NewAppStateMachine(appId)
	s.Require().NotNil(machine)
	// Update indication form
	form := s.h.AppStateMachineHelper.MockIndicationFormData(
		s.h.MockUsers.DefaultUserId,
		common.CoverageAutoLiability,
		common.CoverageAutoPhysicalDamage,
	)
	err = machine.UpdateIndicationForm(s.h.Ctx, form)
	s.Require().NoError(err)
	// Try to manually set indication
	err = machine.SetIndication(s.h.Ctx, "")
	s.Require().NotNil(err)
	s.Require().True(errors.Is(err, cerrors.ErrActionNotValid))
}

// TestUnsupportedActions test all the rest of the available actions returning
// the ErrActionNotSupported error.
func (s *unsubmittedStateTestSuite) TestUnsupportedActions() {
	basicAppData := s.h.MockBasicAppData()
	appId, err := s.h.CreateApplication(s.h.Ctx, basicAppData)
	s.Require().NoError(err)
	s.Require().NotNil(appId)
	machine := s.h.Deps.AppStateMachineWrapper.NewAppStateMachine(appId)
	s.Require().NotNil(machine)

	// Test begin quote generation
	_, err = machine.BeginQuoteGeneration(s.h.Ctx)
	s.Require().NotNil(err)
	s.Require().True(errors.Is(err, cerrors.ErrActionNotSupported))
	// Test set quote
	err = machine.SetQuote(s.h.Ctx, "", nil)
	s.Require().NotNil(err)
	s.Require().True(errors.Is(err, cerrors.ErrActionNotSupported))
	// Test finalize quote
	_, err = machine.FinalizeQuote(s.h.Ctx)
	s.Require().NotNil(err)
	s.Require().True(errors.Is(err, cerrors.ErrActionNotSupported))
}

func (s *unsubmittedStateTestSuite) TestPatchAppBasicInfoFields() {
	basicAppData := s.h.MockBasicAppData()
	appId, err := s.h.CreateApplication(s.h.Ctx, basicAppData)
	s.Require().NoError(err)
	machine := s.h.Deps.AppStateMachineWrapper.NewAppStateMachine(appId)
	s.Require().NotNil(machine)
	form := s.h.MockBasicInfoFieldsData()
	err = machine.UpdateBasicInfoFields(s.h.Ctx, &form)
	s.Require().NoError(err)
}

// TestBeginIndicationGenerationWithExperimentCredits validates that when experiments
// are applied during indication generation, their credits/debits are correctly
// added to the submission's AggregateCreditByCoverage field.
func (s *unsubmittedStateTestSuite) TestBeginIndicationGenerationWithExperimentCredits() {
	// Create application
	basicAppData := s.h.MockBasicAppData()
	appId, err := s.h.CreateApplication(s.h.Ctx, basicAppData)
	s.Require().NoError(err)
	s.Require().NotNil(appId)

	// Update indication form
	form := s.h.AppStateMachineHelper.MockIndicationFormData(
		s.h.MockUsers.DefaultUserId,
		common.CoverageAutoLiability,
		common.CoverageAutoPhysicalDamage,
		common.CoverageGeneralLiability,
		common.CoverageMotorTruckCargo,
	)
	machine := s.h.Deps.AppStateMachineWrapper.NewAppStateMachine(appId)
	err = machine.UpdateIndicationForm(s.h.Ctx, form)
	s.Require().NoError(err)

	s.mockExperimentsClient.EXPECT().
		GetLatestExperimentsAssignations(gomock.Any(), experiments_enums.DomainPricing, appId).
		Return([]*experiments.Assignment{
			{
				Id:           uuid.New(),
				ExperimentId: s.experimentId,
				Enabled:      true,
			},
		}, nil).
		Times(1)

	s.mockExperimentsClient.EXPECT().
		RecordExperimentApplicability(gomock.Any(), gomock.Any()).
		Return(nil).
		Times(1)

	applicability := experiments.NewApplicabilityBuilder().
		WithMockData_TestOnly().
		WithExperimentId(s.experimentId).
		WithSubjectId(appId).
		WithMetadata(&experiments.ApplicabilityMetadata{
			SubmissionId: pointer_utils.ToPointer(uuid.New()),
			CreditsByCoverage: map[string]int{
				app_enums.CoverageAutoLiability.String(): 5,
			},
		}).
		Build()

	metadata := &quoting_experiments_common.ExperimentMetadata{
		CreditApplied: int32(5),
		ValueUsed:     "A",
		CreditsByCoverage: map[app_enums.Coverage]int32{
			app_enums.CoverageAutoLiability: 5,
		},
	}

	s.mockExperiment.EXPECT().
		Apply(gomock.Any(), uuid.MustParse(appId), gomock.Any(), gomock.Any()).
		Return(&applicability, metadata, nil).
		Times(1)

	// Begin indication generation
	subId, err := machine.BeginIndicationGeneration(s.h.Ctx)
	s.Require().NoError(err)
	s.Require().NotNil(subId)

	// Verify the submission has the expected credits
	sub, err := s.h.Deps.AppWrapper.GetSubmissionById(s.h.Ctx, subId)
	s.Require().NoError(err)
	s.Require().NotNil(sub)
	s.Require().NotNil(sub.UnderwriterInput)
	s.Require().NotNil(sub.UnderwriterInput.AggregateCreditByCoverage)

	// Check that credits were correctly aggregated
	aggregateCredits := sub.UnderwriterInput.AggregateCreditByCoverage
	s.Require().Equal(float32(5), *aggregateCredits.AutoLiability)
	s.Require().Equal(float32(0), *aggregateCredits.AutoPhysicalDamage)
	s.Require().Equal(float32(0), *aggregateCredits.GeneralLiability)
	s.Require().Equal(float32(0), *aggregateCredits.MotorTruckCargo)
}
