package app_state_machine

import (
	"context"
	"fmt"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	auth_utils "nirvanatech.com/nirvana/common-go/auth-util"
	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/jobber"
	"nirvanatech.com/nirvana/jobber/jtypes"
	oapi_app "nirvanatech.com/nirvana/openapi-specs/components/application"
	"nirvanatech.com/nirvana/quoting/app_state_machine/cerrors"
	state_enums "nirvanatech.com/nirvana/quoting/app_state_machine/enums"
	app_chkr "nirvanatech.com/nirvana/quoting/appetite_checker"
	"nirvanatech.com/nirvana/quoting/jobs"
	"nirvanatech.com/nirvana/quoting/utils"
	uw_jobs "nirvanatech.com/nirvana/underwriting/jobs"
	risk_factors_client "nirvanatech.com/nirvana/underwriting/risk_factors"
	tsm "nirvanatech.com/nirvana/underwriting/state_machine/telematics_connection_state_machine"
)

type AppStateUnsubmitted struct {
	appStateMachineHelper
	deps ASMDeps
}

func newAppStateUnsubmitted(deps ASMDeps, appId string) ApplicationState {
	return &AppStateUnsubmitted{
		appStateMachineHelper: appStateMachineHelper{
			deps: deps, appId: appId, appState: state_enums.AppStateUnsubmitted,
		},
		deps: deps,
	}
}

func (s *AppStateUnsubmitted) CreateRenewalApplication(ctx context.Context, appId string) (*string, *app_chkr.AppetiteCheckerResult, error) {
	return nil, nil, errors.Wrapf(cerrors.ErrActionNotSupported,
		"Cannot set create renewal application from Unsubmitted state for app %s", appId)
}

func (s *AppStateUnsubmitted) CreateRenewalApplicationV2(ctx context.Context, appId string) (*string, *ApplicationEvaluation, error) {
	return nil, nil, errors.Wrapf(cerrors.ErrActionNotSupported,
		"Cannot set create renewal application from Unsubmitted state for app %s", appId)
}

func (s *AppStateUnsubmitted) UpdateRenewalFields(ctx context.Context, form *oapi_app.PatchRenewalApplicationForm) error {
	// Allow and move to unsubmitted state
	expectedState := state_enums.AppStateUnsubmitted

	return s.updateRenewalsFieldsAndMoveToUnsubmitted(ctx, expectedState, form, func(app application.Application) error {
		// Block update if there's an ongoing submission and task
		if app.UwSubmissionID != nil {
			jobRun, err := s.getJobStatus(ctx, app.UwSubmissionID)
			if err != nil {
				return errors.Wrapf(err, "unable to get job status %s", *app.UwSubmissionID)
			}
			if !jobRun.HasTerminalStatus() {
				return errors.Wrap(cerrors.ErrActionNotValid,
					"Cannot update application since another submission is currently being processed")
			}
		}

		containsAncillaryCoverage := func(coverageType app_enums.Coverage) bool {
			if form.RenewalCoverageForm == nil || form.RenewalCoverageForm.AncillaryCoveragesRequired == nil {
				return false
			}
			for _, cov := range *form.RenewalCoverageForm.AncillaryCoveragesRequired {
				if string(cov.CoverageType) == coverageType.String() {
					return true
				}
			}
			return false
		}

		containsCoverage := func(coverageType app_enums.Coverage) bool {
			if form.RenewalCoverageForm == nil || form.RenewalCoverageForm.CoveragesRequired == nil {
				return false
			}
			for _, cov := range form.RenewalCoverageForm.CoveragesRequired {
				if string(cov.CoverageType) == coverageType.String() {
					return true
				}
			}
			return false
		}

		// Validate terminal schedules for Cargo at Terminals coverage
		if containsAncillaryCoverage(app_enums.CoverageCargoAtScheduledTerminals) &&
			form.RenewalOperationsForm != nil && form.RenewalOperationsForm.TerminalLocations != nil {
			for _, terminalLocation := range *form.RenewalOperationsForm.TerminalLocations {
				if terminalLocation.CargoTerminalSchedule == nil {
					return errors.New(
						"Please complete the missing terminal info needed for Cargo at Terminals")
				}
			}
		}

		if containsAncillaryCoverage(app_enums.CoverageCargoTrailerInterchange) &&
			containsCoverage(app_enums.CoverageAutoPhysicalDamage) {
			return errors.New("Cargo at Trailer Interchange cannot be selected with Auto Physical Damage")
		}
		return nil
	},
	)
}

func (s *AppStateUnsubmitted) Rollback(ctx context.Context, metadata application.StateMetadata) (*application.StateMetadata, error) {
	return nil, errors.Wrap(cerrors.ErrActionNotSupported, "Cannot rollback from Unsubmitted state")
}

func (s *AppStateUnsubmitted) SetPanic(ctx context.Context, metadata application.StateMetadata) error {
	metadata.PreviousState = state_enums.AppStateUnsubmitted
	if err := s.setPanic(ctx, metadata); err != nil {
		return errors.Wrap(err, "unable to set panic")
	}
	return nil
}

func (s *AppStateUnsubmitted) SetDeclined(ctx context.Context, metadata application.StateMetadata) error {
	metadata.PreviousState = state_enums.AppStateUnsubmitted
	return s.setDeclined(ctx, metadata)
}

func (s *AppStateUnsubmitted) GetApplicationDetails(ctx context.Context) (*oapi_app.ApplicationDetail, error) {
	appObj, err := s.loadApp(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "unable to load app")
	}
	appDetails, err := s.bindAppDetailsFromDbToRest(ctx, appObj)
	if err != nil {
		return nil, errors.Wrap(err, "unable to bind app details from db to REST")
	}

	appSummaryState, err := s.getApplicationSummaryState(ctx, *appObj)
	if err != nil {
		return nil, errors.Wrap(err, "unable to get application summary state")
	}
	appDetails.Summary.State = *appSummaryState
	return appDetails, nil
}

func (s *AppStateUnsubmitted) getApplicationSummaryState(
	ctx context.Context,
	appObj application.Application,
) (*oapi_app.ApplicationState, error) {
	if appObj.IndicationSubmissionID == nil {
		return pointer_utils.ToPointer(oapi_app.ApplicationStateInProgress), nil
	}
	jobRun, err := s.getJobStatus(ctx, appObj.IndicationSubmissionID)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to get job status for sub %s", *appObj.IndicationSubmissionID)
	}
	switch {
	case jobRun.HasTerminalStatus() && jobRun.Status != jtypes.JobRunStatusSucceeded:
		metadata := application.StateMetadata{
			Description: fmt.Sprintf("Unexpected task state associated with %s app", s.appId),
			Time:        time.Now(),
		}
		err = s.SetPanic(ctx, metadata)
		if err != nil {
			return nil, errors.Wrap(err, "unable to set panic")
		}
		return nil, errors.Newf("unexpected status %s with error %s", jobRun.Status, jobRun.Error)
	default:
		return pointer_utils.ToPointer(oapi_app.ApplicationStateUnderReviewForIndication), nil
	}
}

func (s *AppStateUnsubmitted) GetApplicationSummary(
	ctx context.Context,
	appObj application.Application,
) (*oapi_app.ApplicationSummary, error) {
	appSummary, err := s.bindApplicationSummaryFromDbToRest(ctx, &appObj)
	if err != nil {
		return nil, errors.Wrap(err, "unable to bind app summary from db to REST")
	}

	appSummaryState, err := s.getApplicationSummaryState(ctx, appObj)
	if err != nil {
		return nil, errors.Wrap(err, "unable to get application summary state")
	}
	appSummary.State = *appSummaryState

	return appSummary, nil
}

func (s *AppStateUnsubmitted) GetStateMetadata(ctx context.Context) (
	*application.StateMetadata, error,
) {
	return nil, errors.Wrapf(cerrors.ErrActionNotSupported, "Cannot get state metadata from Unsubmitted state")
}

func (s *AppStateUnsubmitted) GetIndicationOptions(ctx context.Context) (
	*oapi_app.IndicationOptions, error,
) {
	appObj, err := s.loadApp(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "unable to load app")
	}
	if appObj.IsRenewalV2() {
		return s.getRenewalIndicationOptions(ctx)
	}
	return nil, errors.Wrapf(cerrors.ErrActionNotSupported,
		"Cannot get indication options from Unsubmitted state")
}

func (s *AppStateUnsubmitted) UpdateIndicationForm(ctx context.Context, form *oapi_app.IndicationForm) error {
	expectedState := state_enums.AppStateUnsubmitted

	return s.updateIndicationFormAndMoveToUnsubmitted(ctx, expectedState, form, func(app application.Application) error {
		// Block update if there's an ongoing submission and task
		if app.IndicationSubmissionID != nil {
			jobRun, err := s.getJobStatus(ctx, app.IndicationSubmissionID)
			if err != nil {
				return errors.Wrapf(err, "unable to get job status %s", *app.IndicationSubmissionID)
			}
			if !jobRun.HasTerminalStatus() {
				return errors.Wrap(cerrors.ErrActionNotValid,
					"Cannot update application since another submission is currently being processed")
			}
		}

		containsAncillaryCoverage := func(coverageType app_enums.Coverage) bool {
			if form.OperationsForm.AncillaryCoveragesRequired == nil {
				return false
			}
			for _, cov := range *form.OperationsForm.AncillaryCoveragesRequired {
				if string(cov.CoverageType) == coverageType.String() {
					return true
				}
			}
			return false
		}

		containsCoverage := func(coverageType app_enums.Coverage) bool {
			if form.OperationsForm.CoveragesRequired == nil {
				return false
			}
			for _, cov := range *form.OperationsForm.CoveragesRequired {
				if string(cov.CoverageType) == coverageType.String() {
					return true
				}
			}
			return false
		}

		// Validate terminal schedules for Cargo at Terminals coverage
		if containsAncillaryCoverage(app_enums.CoverageCargoAtScheduledTerminals) &&
			form.OperationsForm.TerminalLocations != nil {
			for _, terminalLocation := range *form.OperationsForm.TerminalLocations {
				if terminalLocation.CargoTerminalSchedule == nil {
					return errors.New(
						"Please complete the missing terminal info needed for Cargo at Terminals")
				}
			}
		}

		if containsAncillaryCoverage(app_enums.CoverageCargoTrailerInterchange) &&
			containsCoverage(app_enums.CoverageAutoPhysicalDamage) {
			return errors.New("Cargo at Trailer Interchange cannot be selected with Auto Physical Damage")
		}

		return nil
	},
	)
}

func (s *AppStateUnsubmitted) BeginIndicationGeneration(ctx context.Context) (string, error) {
	subId := uuid.New()

	err := s.deps.AppWrapper.InsertSubmissionFromApplicationSnapshot(
		ctx,
		s.appId,
		subId.String(),
		notBindable,
		func(
			submission application.SubmissionObject,
			app application.Application,
		) (application.SubmissionObject, application.Application, error) {
			err := s.validateAppForIndicationSubmission(app)
			if err != nil {
				return submission, app, errors.Wrap(err, "unable to validate app for submission")
			}

			if app.State != state_enums.AppStateUnsubmitted {
				return submission, app, errors.Wrapf(cerrors.ErrActionNotValid, "unexpected state: %s. Expected %s",
					app.State.String(), state_enums.AppStateUnsubmitted.String())
			}

			app.IndicationSubmissionID = pointer_utils.ToPointer(subId.String())

			return submission, app, nil
		},
	)
	if err != nil {
		return "", errors.Wrap(err, "unable to insert submission from app snapshot")
	}

	// Apply experiments and retrieve all assignments. We are calling this here because
	// experiments require the submission to be created.
	err = s.deps.AppWrapper.UpdateSub(ctx, subId.String(), func(sub application.SubmissionObject) (application.SubmissionObject, error) {
		err = s.deps.QuotingExperimentsManager.ApplyIndicationExperiments(ctx, &sub)
		if err != nil {
			return sub, errors.Wrapf(err, "unable to apply experiments to submission %s", subId)
		}
		return sub, nil
	})

	jobRunId, err := s.deps.Jobber.AddJobRun(ctx, jobber.NewAddJobRunParams(
		jobs.GenerateIndicationWorkflow,
		&jobs.IndicationWorkflowMessage{
			SubmissionID: subId,
			IndicationOptionSpecs: []jobs.IndicationOptionSpec{
				{
					ID:          uuid.New(),
					PackageType: app_enums.IndicationOptionTagBasic,
				},
				{
					ID:          uuid.New(),
					PackageType: app_enums.IndicationOptionTagStandard,
				},
				{
					ID:          uuid.New(),
					PackageType: app_enums.IndicationOptionTagComplete,
				},
			},
		},
		jtypes.NewMetadata(jtypes.Immediate),
	))
	if err != nil {
		return "", errors.Wrap(err, "unable to add job run")
	}
	// Updated sub in DB
	err = s.deps.AppWrapper.UpdateSub(ctx, subId.String(),
		func(sub application.SubmissionObject) (application.SubmissionObject, error) {
			sub.JobRunId = &jobRunId
			return sub, nil
		})
	// TODO: Wrap DB errors after moving DB wrapper errors to crdb/errors
	if err != nil {
		return "", errors.Wrap(err, "unable to update sub")
	}
	return subId.String(), nil
}

func (s *AppStateUnsubmitted) SetIndication(ctx context.Context, subId string) error {
	expectedState, nextState := state_enums.AppStateUnsubmitted, state_enums.AppStateIndicationGenerated
	// Updated app in DB
	err := s.deps.AppWrapper.UpdateApp(ctx,
		s.appId,
		func(app application.Application) (application.Application, error) { //nolint:exhaustive
			// Check if submission is still valid/the latest submission for
			// application
			switch {
			case app.State != expectedState:
				return app, errors.Wrapf(cerrors.ErrActionNotValid, "unexpected state %s. Expected %s",
					app.State, expectedState)
			case app.IndicationSubmissionID == nil:
				return app, errors.Wrapf(cerrors.ErrActionNotValid, "App not ready for submission")
			case *app.IndicationSubmissionID != subId:
				return app, errors.Newf(cerrors.SubNoLongerValid + ": Submission is no longer valid")
			}
			app.State = nextState
			return app, nil
		})
	// TODO: Wrap DB errors after moving DB wrapper errors to crdb/errors
	if err != nil {
		return errors.Wrap(err, "unable to update app")
	}
	return nil
}

func (s *AppStateUnsubmitted) FinalizeIndicationOption(ctx context.Context, indicationId string) error {
	return errors.Wrap(cerrors.ErrActionNotSupported, "Cannot finalize indication option from Unsubmitted state")
}

func (s *AppStateUnsubmitted) UpdateAdditionalInfoForm(
	ctx context.Context, form *oapi_app.AdditionalInformationForm,
) error {
	user := authz.UserFromContext(ctx)
	fleetTypeRaw, err := s.deps.FeatureFlag.StringVariation(
		feature_flag_lib.BuildLookupAttributes(user),
		feature_flag_lib.FeatureFleetType,
		utils.FleetTypeFleet.String())
	if err != nil {
		return errors.Wrap(err, "couldn't fetch fleet variation")
	}
	fleetType, err := utils.FleetTypeString(fleetTypeRaw)
	if err != nil {
		return errors.Wrapf(err, "unable to parse the fleet type from variation %s", fleetTypeRaw)
	}

	// Only valid for non-fleets
	if slice_utils.Contains(utils.NonFleetTypes, fleetType) {
		expectedState := state_enums.AppStateUnsubmitted
		err := s.updateAddlInfoFormInUnsubmittedState(ctx,
			expectedState,
			form,
			nil,
		)
		if err != nil {
			return errors.Wrapf(err, "failed to update additional info form for app %s from state %s",
				s.appId, s.appState)
		}
		return nil
	}

	return errors.Wrap(cerrors.ErrActionNotSupported, "Cannot update additional information from Unsubmitted state")
}

func (s *AppStateUnsubmitted) UpdateBasicInfoFields(ctx context.Context, basicInfo *oapi_app.ApplicationBasicInfoForm) error {
	return s.updateApplicationBasicInfoFields(ctx, state_enums.AppStateUnsubmitted, basicInfo, nil)
}

func (s *AppStateUnsubmitted) GetQuote(ctx context.Context) (*oapi_app.QuoteDetails, error) {
	return nil, errors.Wrap(cerrors.ErrActionNotSupported, "Cannot get quote from Unsubmitted state")
}

func (s *AppStateUnsubmitted) BeginQuoteGeneration(ctx context.Context) (string, error) {
	return "", errors.Wrap(cerrors.ErrActionNotSupported, "Cannot begin quote generation from Unsubmitted state")
}

func (s *AppStateUnsubmitted) BeginRenewalQuoteGeneration(ctx context.Context) (string, error) {
	expectedState := state_enums.AppStateUnsubmitted
	subId, err := s.beginRenewalQuoteGeneration(ctx, expectedState, func(app application.Application) error {
		switch {
		case app.PackageType == nil:
			return errors.Newf("missing package type for app %s", s.appId)
		}
		return nil
	}, notBindable, nil)
	if err != nil {
		return "", errors.Wrapf(err, "failed to begin renewal quote generation for app %s from state %s",
			s.appId, s.appState)
	}
	return subId, nil
}

func (s *AppStateUnsubmitted) SetQuote(ctx context.Context, subId string, negotiatedRates *application.NegotiatedRates) error {
	app, err := s.loadApp(ctx)
	if err != nil {
		return errors.Wrapf(err, "unable to load app %s", s.appId)
	}
	if !app.IsRenewalV2() {
		return errors.Wrap(cerrors.ErrActionNotSupported, "Cannot set quote from Unsubmitted state")
	}
	latestPendingAppReview, err := s.deps.AppReviewWrapper.GetLatestPendingReview(ctx, app.ID)
	if err != nil && !errors.Is(err, uw.ErrAppReviewNotFound) {
		return errors.Wrapf(err, "unable to get latest pending app review by app id %s", app.ID)
	}
	err = s.MarkPendingAppReviewsToStale(ctx, app.ID)
	if err != nil {
		return errors.Wrapf(err, "unable to mark pending app reviews for app id %s", app.ID)
	}
	sub, err := s.deps.AppWrapper.GetSubmissionById(ctx, subId)
	if err != nil {
		return errors.Wrapf(err, "unable to get submission by id %s", subId)
	}

	var underwriterID uuid.UUID
	if sub.UnderwriterID == uuid.Nil {
		underwriterID = app.UnderwriterID
	} else {
		underwriterID = sub.UnderwriterID
	}
	if negotiatedRates == nil {
		return errors.New("negotiated rates were found nil")
	}

	if sub.CoverageInfo == nil {
		return errors.New("coverage info was found nil")
	}

	var devUser, underwriterUser *authz.User
	devUser, err = auth_utils.GetUserFromUserID(ctx, s.deps.AuthWrapper, app.CreatedBy)
	if err != nil {
		return errors.Wrapf(err, "failed to get dev user for app Id: %s", s.appId)
	}
	if underwriterID != uuid.Nil {
		underwriterUser, err = auth_utils.GetUserFromUserID(ctx, s.deps.AuthWrapper, underwriterID.String())
		if err != nil {
			return errors.Wrapf(err, "failed to get underwriter user for app Id: %s", s.appId)
		}
	}
	shouldCreateOverview, err := shouldCreateOverviewPanel(ctx, s.deps.FeatureFlag, devUser, underwriterUser)
	if err != nil {
		return errors.Wrapf(err, "unable to determine if overview panel should be created for app Id: %s", s.appId)
	}

	appetiteFactorsLogicResolver, err := s.deps.AppetiteFactorsLogicResolverFactory.GetLogicResolver(enums.ProgramTypeFleet)
	if err != nil {
		return errors.Wrapf(err, "unable to get logic resolver for appetite factors logic resolver")
	}
	appetiteScoreVersion, err := getAppetiteScoreVersion(ctx, s.deps.FeatureFlag, appetiteFactorsLogicResolver, app.CompanyInfo.DOTNumber, s.deps.UWSafetyFetcher, devUser)
	if err != nil {
		return errors.Wrapf(err, "unable to get appetite score version for app Id: %s", s.appId)
	}

	telematicsConnectionState, err := tsm.CalculateInitialTelematicsConnectionState(ctx, s.deps.AppWrapper, *s.deps.TspConnManager, s.deps.TelematicsDataPlatformClient, s.deps.Jobber, s.deps.AppReviewWrapper, app.ID)
	if err != nil {
		return errors.Wrapf(err, "unable to calculate initial telematics connection state for app id %s", app.ID)
	}
	review := uw.NewAppReviewFromSubmission(
		*sub, negotiatedRates, underwriterID, uuid.New().String(), time.Now(), shouldCreateOverview, *appetiteScoreVersion,
		uw.AppReviewReadinessStateNotReady, *telematicsConnectionState,
	)
	err = s.deps.AppReviewWrapper.CreateReview(ctx, review)
	if err != nil {
		return errors.Wrapf(err, "unable to create review for app id %s", app.ID)
	}

	// Trigger worksheet creation call
	worksheetID, err := s.deps.RiskFactorsClient.CreateWorksheet(ctx,
		&risk_factors_client.CreateWorksheetRequest{ReviewId: review.Id})
	if err != nil {
		log.Error(ctx, "failed to create worksheet", log.Err(err))
		return errors.Wrapf(err, "failed to create worksheet for app %s", s.appId)
	}
	log.Info(ctx, "Created worksheet",
		log.String("worksheetID", worksheetID.WorksheetId),
		log.String("reviewID", review.Id),
	)

	// Assign pricing experiments on review creation
	appId, err := uuid.Parse(app.ID)
	if err != nil {
		return errors.Wrapf(err, "unable to parse app id %s", app.ID)
	}

	err = s.deps.QuotingExperimentsManager.AssignExperiments(
		ctx,
		appId,
	)
	if err != nil {
		return errors.Wrapf(err, "unable to assign pricing experiments for app id %s", app.ID)
	}

	if latestPendingAppReview != nil {
		err = s.deps.AppReviewWrapper.UpdateAppReview(ctx, review.Id,
			func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
				err = s.copyReviewDataFromLatestPendingAppReview(ctx, &review, latestPendingAppReview)
				if err != nil {
					return review, errors.Wrapf(err, "error while copying review data from latest pending app review, latestPendingAppReview ID: %s", latestPendingAppReview.Id)
				}
				return review, nil
			})
		if err != nil {
			return errors.Wrapf(err, "unable to update app review for app id %s and review id %s",
				app.ID, review.Id)
		}
	}

	expectedStates := map[state_enums.AppState]struct{}{
		state_enums.AppStateUnsubmitted:   {},
		state_enums.AppStateUnderUWReview: {},
	}
	nextState := state_enums.AppStateUnderUWReview
	err = s.deps.AppWrapper.UpdateApp(ctx, s.appId, func(app application.Application) (application.Application, error) {
		// Check if submission is still valid/the latest submission for
		// application
		_, ok := expectedStates[app.State]
		switch {
		case !ok:
			return app, errors.Wrapf(cerrors.ErrActionNotValid, "Unexpected state %s. Expected some of %s",
				app.State, expectedStates)
		case *app.UwSubmissionID != subId:
			return app, errors.Newf(
				cerrors.SubNoLongerValid+
					": Submission %s is no longer valid,since it is not the latest submission %s for application %s",
				subId,
				*app.UwSubmissionID,
				app.ID)
		}
		app.State = nextState
		return app, nil
	})
	if err != nil {
		return errors.Wrapf(err, cerrors.DbPersistingErr+": Unable to update app %s to state %s", s.appId, nextState)
	}

	// Trigger CheckClearance job
	addJobRunParams := jobber.NewAddJobRunParams(
		jobs.CheckClearance,
		&jobs.CheckClearanceArgs{ApplicationID: s.appId},
		jtypes.NewMetadata(jtypes.Immediate),
	)
	jobRunId, err := s.deps.Jobber.AddJobRun(ctx, addJobRunParams)
	if err != nil {
		log.Error(ctx, "failed to add CheckClearance job", log.Err(err),
			log.Any("addJobRunParams", addJobRunParams),
		)
		return errors.Wrap(err, "failed to add CheckClearance job")
	}
	log.Info(ctx, "Added CheckClearance job", log.Stringer("JobRunId", jobRunId))

	// Trigger underwriting app-review widget generation job
	jobRunId, err = s.deps.Jobber.AddJobRun(ctx, jobber.NewAddJobRunParams(uw_jobs.GenerateWidgetsForAppReview,
		&uw_jobs.GenerateAppReviewWidgetsArgs{AppReviewId: review.Id}, jtypes.NewMetadata(jtypes.Immediate)))
	if err != nil {
		log.Error(ctx, "unable to add generate widgets job", log.Err(err))
		return nil
	}
	log.Info(ctx, "generate widgets job added", log.String("job_run_id", jobRunId.String()))
	// Persist job run id in panels info
	err = s.deps.AppReviewWrapper.UpdateAppReview(ctx, review.Id,
		func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
			panelsInfo := review.PanelsInfo
			if panelsInfo == nil {
				panelsInfo = new(uw.PanelsInfo)
			}
			panelsInfo.JobRunId = &jobRunId
			review.PanelsInfo = panelsInfo
			return review, nil
		})
	if err != nil {
		return errors.Wrapf(err, "unable to update app review for app id %s and review id %s",
			app.ID, review.Id)
	}

	err = s.deps.Jobber.WaitForJobRunCompletion(ctx, jobRunId)
	if err != nil {
		return errors.Wrapf(err, "failed to wait for job run completion for job run id %s", jobRunId)
	}

	err = s.createTasksAndUpdateReviewReadinessState(ctx, &review, latestPendingAppReview)
	if err != nil {
		return errors.Wrapf(err, "unable to create tasks and update review readiness state for app id %s", app.ID)
	}
	return nil
}

func (s *AppStateUnsubmitted) FinalizeQuote(ctx context.Context) (*[]oapi_app.PolicyDetails, error) {
	return nil, errors.Wrap(cerrors.ErrActionNotSupported, "Cannot finalize quote from Unsubmitted state")
}

func (s *AppStateUnsubmitted) ReleaseQuote(ctx context.Context, compilationID uuid.UUID) error {
	return errors.Wrap(cerrors.ErrActionNotValid, "Cannot release quote from Unsubmitted state")
}

func (s *AppStateUnsubmitted) State() state_enums.AppState {
	return state_enums.AppStateUnsubmitted
}

func (s *AppStateUnsubmitted) GetStateInOAPIFormat(
	ctx context.Context,
	appObj application.Application,
) (*oapi_app.ApplicationState, error) {
	return s.getApplicationSummaryState(ctx, appObj)
}

var _ ApplicationState = &AppStateUnsubmitted{}
