package app_state_machine

import (
	"context"
	"time"

	"github.com/benb<PERSON><PERSON><PERSON>/clock"
	"github.com/cactus/go-statsd-client/v5/statsd"
	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/api-server/quoting_jobber"
	"nirvanatech.com/nirvana/billing/exposure/tracked_vehicle"
	"nirvanatech.com/nirvana/billing/fleet_pipeline/static_param"
	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/db-api/db_wrappers/agency"
	"nirvanatech.com/nirvana/db-api/db_wrappers/agency_bd_mapping"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	app "nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	"nirvanatech.com/nirvana/db-api/db_wrappers/billing"
	"nirvanatech.com/nirvana/db-api/db_wrappers/ds"
	entitylicense "nirvanatech.com/nirvana/db-api/db_wrappers/entity_license"
	"nirvanatech.com/nirvana/db-api/db_wrappers/fmcsa"
	"nirvanatech.com/nirvana/db-api/db_wrappers/forms"
	db_policy "nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy_set"
	"nirvanatech.com/nirvana/db-api/db_wrappers/sharing"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/ds/model"
	"nirvanatech.com/nirvana/events"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
	"nirvanatech.com/nirvana/feature_store"
	"nirvanatech.com/nirvana/insurance-bundle/service"
	oapi_app "nirvanatech.com/nirvana/openapi-specs/components/application"
	"nirvanatech.com/nirvana/policy"
	"nirvanatech.com/nirvana/policy_common/forms_generator"
	agentlicense "nirvanatech.com/nirvana/quoting/agent_license"
	"nirvanatech.com/nirvana/quoting/app_state_machine/cerrors"
	state_enums "nirvanatech.com/nirvana/quoting/app_state_machine/enums"
	app_chkr "nirvanatech.com/nirvana/quoting/appetite_checker"
	"nirvanatech.com/nirvana/quoting/clearance/emails"
	"nirvanatech.com/nirvana/quoting/experiments"
	"nirvanatech.com/nirvana/quoting/tags"
	"nirvanatech.com/nirvana/servers/telematicsv2"
	tsp_connections "nirvanatech.com/nirvana/telematics/connections"
	"nirvanatech.com/nirvana/underwriting/appetite_factors/appetite_score_rubric"
	"nirvanatech.com/nirvana/underwriting/common"
	risk_factors_client "nirvanatech.com/nirvana/underwriting/risk_factors"
	"nirvanatech.com/nirvana/underwriting/scheduler"
	"nirvanatech.com/nirvana/underwriting/task/taskmanager"
)

type ASMDeps struct {
	fx.In

	AppWrapper                          app.DataWrapper
	AuthWrapper                         auth.DataWrapper
	AppReviewWrapper                    uw.ApplicationReviewWrapper
	PolicyWrapper                       db_policy.DataWrapper
	PolicyClient                        policy.Client
	Jobber                              quoting_jobber.Client
	TspConnManager                      *tsp_connections.TSPConnManager
	FormsGenerator                      forms_generator.FormsGenerator
	EventHandler                        events.EventsHandler
	FeatureStore                        feature_store.FeatureStore
	FormsWrapper                        forms.FormWrapper
	UwScheduler                         scheduler.UwScheduler
	FeatureFlag                         feature_flag_lib.Client
	AgencyWrapper                       agency.DataWrapper
	AppetiteChecker                     *app_chkr.AppetiteChecker
	FmcsaWrapper                        fmcsa.DataWrapper
	PolicySetWrapper                    policy_set.DataWrapper
	BillingWrapper                      billing.DataWrapper
	MetricsClient                       statsd.Statter
	AppetiteFactorsLogicResolverFactory appetite_score_rubric.LogicResolverFactory
	BillingStaticParamClient            static_param.Client
	ClearanceEmailer                    emails.EmailWrapper
	TelematicsDataPlatformClient        telematicsv2.TelematicsPipelineManager
	AgencyBDMapping                     agency_bd_mapping.Wrapper
	BillingTrackedVehicleClient         tracked_vehicle.Client
	SharingWrapper                      sharing.DataWrapper
	ReviewReadinessTaskManager          taskmanager.TaskManager
	FetcherClientFactory                data_fetching.FetcherClientFactory
	ProcessorClientFactory              data_processing.ProcessorClientFactory
	Clock                               clock.Clock
	AgentLicenseManager                 agentlicense.AgentLicenseManager
	TagsManager                         *tags.Manager
	RiskFactorsClient                   risk_factors_client.RiskFactorsServiceClient
	QuotingExperimentsManager           experiments.Manager
	M1Client                            model.M1ModelClient
	DSWrapper                           ds.DataWrapper
	UWSafetyFetcher                     *common.UWSafetyFetcher
	CreateApplicationHandler            *CreateApplicationHandler
	IBService                           service.InsuranceBundleManagerClient
	EntityLicenseWrapper                entitylicense.Wrapper
}

// AppStateMachineWrapper generates new instances of the AppStateMachine.
type AppStateMachineWrapper struct {
	fx.In

	Deps ASMDeps
}

// NewAppStateMachine creates a new instance of AppStateMachine. We load the
// application and create a new AppStateMachine with the loaded state as the
// current state.
func (w *AppStateMachineWrapper) NewAppStateMachine(appId string) *AppStateMachine {
	machine := &AppStateMachine{
		appId:  appId,
		states: states,
		deps:   w.Deps,
	}
	return machine
}

// AppStateMachine is the main struct for this module, holding all the
// necessary data to handle the machine.
//
// NOTE: we store appId instead of the db object, to avoid holding the
// object in memory and potentially concurrency issues.
//
// Refer to README.md for errors returned from the actions.
// TODO: Fix all error paths so that guidelines in ^ are met.
type AppStateMachine struct {
	appStateMachineHelper
	deps ASMDeps

	appId  string
	states map[state_enums.AppState]loadStateFn
}

func (m *AppStateMachine) SetPanic(ctx context.Context, metadata *app.StateMetadata) error {
	currentState, err := m.loadStateFromApp(ctx, m.deps, m.appId)
	if err != nil {
		log.Error(ctx, "unable to load state from app while setting panic",
			log.String("app id", m.appId), log.Err(err))
		return errors.Wrap(err, "unable to load state from app")
	}
	if metadata == nil {
		metadata = &app.StateMetadata{
			Time: time.Now(),
		}
		log.Warn(ctx, "defaulting metadata while setting panic", log.String("app id", m.appId),
			log.String("state", (*currentState).State().String()), log.Any("metadata", metadata))
	} else if metadata.Time.IsZero() {
		metadata.Time = time.Now()
	}
	err = (*currentState).SetPanic(ctx, *metadata)
	if err != nil {
		log.Error(ctx, "unable to set panic", log.String("app id", m.appId),
			log.String("state", (*currentState).State().String()),
			log.Any("metadata", metadata), log.Err(err))
		return cerrors.WrapAsmError(m.appId, (*currentState).State().Ptr(), errors.Wrap(err, "unable to set panic"))
	}
	log.Error(ctx, "app sent to panic", log.String("app id", m.appId),
		log.String("state", (*currentState).State().String()),
		log.Any("metadata", metadata))

	return nil
}

// TODO: revisit declined actions from all states once application clearance
// has been implemented.
func (m *AppStateMachine) SetDeclined(ctx context.Context, metadata *app.StateMetadata) error {
	currentState, err := m.loadStateFromApp(ctx, m.deps, m.appId)
	if err != nil {
		log.Error(ctx, "unable to load state from app while setting declined",
			log.String("app id", m.appId), log.Err(err))
		return errors.Wrap(err, "unable to load state from app")
	}
	if metadata == nil {
		metadata = &app.StateMetadata{
			Time: time.Now(),
		}
		log.Warn(ctx, "defaulting metadata while setting declined", log.String("app id", m.appId),
			log.String("state", (*currentState).State().String()), log.Any("metadata", metadata))
	} else if metadata.Time.IsZero() {
		metadata.Time = time.Now()
	}
	err = (*currentState).SetDeclined(ctx, *metadata)
	if err != nil {

		log.Error(ctx, "unable to set declined", log.String("app id", m.appId),
			log.String("state", (*currentState).State().String()),
			log.Any("metadata", metadata), log.Err(err))
		return cerrors.WrapAsmError(m.appId, (*currentState).State().Ptr(),
			errors.Wrap(err, "unable to set declined"))
	}
	return nil
}

func (m *AppStateMachine) Rollback(ctx context.Context, metadata *app.StateMetadata) (*app.StateMetadata, error) {
	currentState, err := m.loadStateFromApp(ctx, m.deps, m.appId)
	if err != nil {
		log.Error(ctx, "unable to load state from app while rolling back",
			log.String("app id", m.appId), log.Err(err))
		return nil,
			errors.Wrap(err, "unable to load state from app while rolling back")
	}
	if metadata == nil {
		metadata = &app.StateMetadata{
			Time: time.Now(),
		}
		log.Warn(ctx, "defaulting metadata while rolling back", log.String("app id", m.appId),
			log.String("state", (*currentState).State().String()), log.Any("metadata", metadata))
	} else if metadata.Time.IsZero() {
		metadata.Time = time.Now()
	}
	metadata, err = (*currentState).Rollback(ctx, *metadata)
	if err != nil {
		log.Error(ctx, "unable to rollback", log.String("app id", m.appId),
			log.String("state", (*currentState).State().String()),
			log.Any("metadata", metadata), log.Err(err))
		return nil, cerrors.WrapAsmError(m.appId, (*currentState).State().Ptr(),
			errors.Wrap(err, "unable to rollback"))
	}

	return metadata, nil
}

func (m *AppStateMachine) GetApplicationDetails(ctx context.Context) (*oapi_app.ApplicationDetail, error) {
	currentState, err := m.loadStateFromApp(ctx, m.deps, m.appId)
	if err != nil {
		log.Error(ctx, "unable to load state from app while getting app details",
			log.String("app id", m.appId), log.Err(err))
		return nil, errors.Wrap(err, "unable to load state from app")
	}
	details, err := (*currentState).GetApplicationDetails(ctx)
	if err != nil {
		log.Error(
			ctx, "unable to get app details", log.String("app id", m.appId),
			log.String("state", (*currentState).State().String()), log.Err(err),
		)
		if errors.IsAny(
			err,
			app.ErrApplicationArchived,
			app.ErrApplicationNotFound,
			ErrInvalidStateToGetAppDetails,
		) {
			return nil, err
		}
		asmError := cerrors.WrapAsmError(m.appId, (*currentState).State().Ptr(),
			errors.Wrap(err, "unable to get app details"))
		return nil, asmError
	}
	return details, nil
}

func (m *AppStateMachine) GetApplicationSummary(
	ctx context.Context,
	appObj app.Application,
) (*oapi_app.ApplicationSummary, error) {
	currentState, err := m.loadStateFromAppObj(m.deps, appObj)
	if err != nil {
		log.Error(ctx, "invalid app state",
			log.String("app id", appObj.ID),
			log.String("app_state", appObj.State.String()),
			log.Err(err))
		return nil, errors.Wrap(err, cerrors.StateNotSupported)
	}

	details, err := (*currentState).GetApplicationSummary(ctx, appObj)
	if err != nil {
		log.Error(ctx, "unable to get app details", log.String("app id", m.appId),
			log.String("state", (*currentState).State().String()), log.Err(err))
		return nil, cerrors.WrapAsmError(m.appId, (*currentState).State().Ptr(),
			errors.Wrap(err, "unable to get app details"))
	}
	return details, nil
}

func (m *AppStateMachine) GetStateMetadata(ctx context.Context) (
	*app.StateMetadata, error,
) {
	currentState, err := m.loadStateFromApp(ctx, m.deps, m.appId)
	if err != nil {
		log.Error(ctx, "unable to load state from app while getting state metadata",
			log.String("app id", m.appId), log.Err(err))
		return nil, errors.Wrap(err, "unable to load state from app")
	}
	state, err := (*currentState).GetStateMetadata(ctx)
	if err != nil {
		log.Error(ctx, "unable to get state matadata", log.String("app id", m.appId),
			log.String("state", (*currentState).State().String()), log.Err(err))
		return nil, cerrors.WrapAsmError(m.appId, (*currentState).State().Ptr(),
			errors.Wrap(err, "unable to get state metadata"))
	}
	return state, nil
}

func (m *AppStateMachine) GetIndicationOptions(ctx context.Context) (
	*oapi_app.IndicationOptions, error,
) {
	currentState, err := m.loadStateFromApp(ctx, m.deps, m.appId)
	if err != nil {
		log.Error(ctx, "unable to load state from app while getting indication options",
			log.String("app id", m.appId), log.Err(err))
		return nil, errors.Wrap(err, "unable to load state from app")
	}
	options, err := (*currentState).GetIndicationOptions(ctx)
	if err != nil {
		log.Error(ctx, "unable to get indication options", log.String("app id", m.appId),
			log.String("state", (*currentState).State().String()), log.Err(err))
		return nil, cerrors.WrapAsmError(m.appId, (*currentState).State().Ptr(),
			errors.Wrap(err, "unable to get indication options"))
	}
	return options, nil
}

func (m *AppStateMachine) UpdateIndicationForm(ctx context.Context, form *oapi_app.IndicationForm) error {
	currentState, err := m.loadStateFromApp(ctx, m.deps, m.appId)
	if err != nil {
		log.Error(ctx, "unable to load state from app while updating indication form",
			log.String("app id", m.appId), log.Err(err))
		return errors.Wrap(err, "unable to load state from app")
	}
	err = (*currentState).UpdateIndicationForm(ctx, form)
	if err != nil {

		log.Error(ctx, "unable to update indication form", log.String("app id", m.appId),
			log.String("state", (*currentState).State().String()), log.Err(err))
		return cerrors.WrapAsmError(m.appId, (*currentState).State().Ptr(),
			errors.Wrap(err, "unable to update indication form"))
	}
	return nil
}

func (m *AppStateMachine) GetQuote(ctx context.Context) (*oapi_app.QuoteDetails, error) {
	currentState, err := m.loadStateFromApp(ctx, m.deps, m.appId)
	if err != nil {
		log.Error(ctx, "unable to load state from app while getting quote",
			log.String("app id", m.appId), log.Err(err))
		return nil, cerrors.WrapAsmError(m.appId, (*currentState).State().Ptr(),
			errors.Wrap(err, "unable to load state from app"))
	}
	quote, err := (*currentState).GetQuote(ctx)
	if err != nil {
		log.Error(ctx, "unable to get quote", log.String("app id", m.appId),
			log.String("state", (*currentState).State().String()), log.Err(err))
		return nil, cerrors.WrapAsmError(m.appId, (*currentState).State().Ptr(),
			errors.Wrap(err, "unable to get quote"))
	}
	return quote, nil
}

func (m *AppStateMachine) BeginIndicationGeneration(ctx context.Context) (string, error) {
	currentState, err := m.loadStateFromApp(ctx, m.deps, m.appId)
	if err != nil {
		log.Error(ctx, "unable to load state from app while beginning indication generation",
			log.String("app id", m.appId), log.Err(err))
		return "", cerrors.WrapAsmError(m.appId, (*currentState).State().Ptr(),
			errors.Wrap(err, "unable to load state from app"))
	}
	subId, err := (*currentState).BeginIndicationGeneration(ctx)
	if err != nil {
		log.Error(ctx, "unable to begin indication generation", log.String("app id", m.appId),
			log.String("state", (*currentState).State().String()), log.Err(err))
		return "", cerrors.WrapAsmError(m.appId, (*currentState).State().Ptr(),
			errors.Wrap(err, "unable to begin indication generation"))
	}
	return subId, nil
}

func (m *AppStateMachine) SetIndication(ctx context.Context, subId string) error {
	currentState, err := m.loadStateFromApp(ctx, m.deps, m.appId)
	if err != nil {
		log.Error(ctx, "unable to load state from app while setting indication",
			log.String("app id", m.appId), log.Err(err))
		return errors.Wrap(err, "unable to load state from app")
	}
	err = (*currentState).SetIndication(ctx, subId)
	if err != nil {

		log.Error(ctx, "unable to set indication", log.String("app id", m.appId),
			log.String("state", (*currentState).State().String()), log.Err(err))
		return cerrors.WrapAsmError(m.appId, (*currentState).State().Ptr(),
			errors.Wrap(err, "unable to set indication"))
	}
	return nil
}

func (m *AppStateMachine) FinalizeIndicationOption(ctx context.Context, indicationOptionId string) error {
	currentState, err := m.loadStateFromApp(ctx, m.deps, m.appId)
	if err != nil {
		log.Error(ctx, "unable to load state from app while finalizing indication option",
			log.String("app id", m.appId), log.Err(err))
		return errors.Wrap(err, "unable to load state from app")
	}
	err = (*currentState).FinalizeIndicationOption(ctx, indicationOptionId)
	if err != nil {
		log.Error(ctx, "unable to finalize indication option", log.String("app id", m.appId),
			log.String("state", (*currentState).State().String()), log.Err(err))
		return cerrors.WrapAsmError(m.appId, (*currentState).State().Ptr(),
			errors.Wrap(err, "unable to finalize indication option"))
	}
	return nil
}

func (m *AppStateMachine) UpdateAdditionalInfoForm(
	ctx context.Context, form *oapi_app.AdditionalInformationForm,
) error {
	currentState, err := m.loadStateFromApp(ctx, m.deps, m.appId)
	if err != nil {
		log.Error(ctx, "unable to load state from app while updating addl info form",
			log.String("app id", m.appId), log.Err(err))
		return errors.Wrap(err, "unable to load state from app")
	}
	err = (*currentState).UpdateAdditionalInfoForm(ctx, form)
	if err != nil {
		log.Error(ctx, "unable to update addl info form", log.String("app id", m.appId),
			log.String("state", (*currentState).State().String()), log.Err(err))
		return cerrors.WrapAsmError(m.appId, (*currentState).State().Ptr(),
			errors.Wrap(err, "unable to update addl info form"))
	}
	return nil
}

func (m *AppStateMachine) UpdateBasicInfoFields(
	ctx context.Context, form *oapi_app.ApplicationBasicInfoForm,
) error {
	currentState, err := m.loadStateFromApp(ctx, m.deps, m.appId)
	if err != nil {
		log.Error(ctx, "unable to load state from app while updating basic info fields",
			log.String("app id", m.appId), log.Err(err))
		return errors.Wrap(err, "unable to load state from app")
	}
	err = (*currentState).UpdateBasicInfoFields(ctx, form)
	if err != nil {
		log.Error(ctx, "unable to update basic info fields", log.String("app id", m.appId),
			log.String("state", (*currentState).State().String()), log.Err(err))
		return cerrors.WrapAsmError(m.appId, (*currentState).State().Ptr(),
			errors.Wrap(err, "unable to update basic info fields"))
	}
	return nil
}

func (m *AppStateMachine) BeginQuoteGeneration(ctx context.Context) (string, error) {
	currentState, err := m.loadStateFromApp(ctx, m.deps, m.appId)
	if err != nil {
		log.Error(ctx, "unable to load state from app while beginning quote generation",
			log.String("app id", m.appId), log.Err(err))
		return "", errors.Wrap(err, "unable to load state from app")
	}
	subId, err := (*currentState).BeginQuoteGeneration(ctx)
	if err != nil {
		log.Error(ctx, "unable to begin quote generation", log.String("app id", m.appId),
			log.String("state", (*currentState).State().String()), log.Err(err))
		return "", cerrors.WrapAsmError(m.appId, (*currentState).State().Ptr(),
			errors.Wrap(err, "unable to begin quote generation"))
	}
	return subId, nil
}

func (m *AppStateMachine) BeginRenewalQuoteGeneration(ctx context.Context) (string, error) {
	currentState, err := m.loadStateFromApp(ctx, m.deps, m.appId)
	if err != nil {
		log.Error(ctx, "unable to load state from app while beginning renewal quote generation",
			log.String("app id", m.appId), log.Err(err))
		return "", errors.Wrap(err, "unable to load state from app")
	}
	appObj, _ := m.deps.AppWrapper.GetAppById(ctx, m.appId)
	if !appObj.IsRenewalV2() {
		log.Error(ctx, "app be renewal v2 to generate renewal quote")
		return "", errors.New("unable to generate renewal quote for non-renewal app")
	}
	subId, err := (*currentState).BeginRenewalQuoteGeneration(ctx)
	if err != nil {
		log.Error(ctx, "unable to begin renewal quote generation", log.String("app id", m.appId),
			log.String("state", (*currentState).State().String()), log.Err(err))
		return "", cerrors.WrapAsmError(m.appId, (*currentState).State().Ptr(),
			errors.Wrap(err, "unable to begin renewal quote generation"))
	}
	return subId, nil
}

func (m *AppStateMachine) SetQuote(ctx context.Context, subId string, negotiatedRates *application.NegotiatedRates) error {
	currentState, err := m.loadStateFromApp(ctx, m.deps, m.appId)
	if err != nil {
		log.Error(ctx, "unable to load state from app while setting quote",
			log.String("app id", m.appId), log.Err(err))
		return errors.Wrap(err, "unable to load state from app")
	}
	err = (*currentState).SetQuote(ctx, subId, negotiatedRates)
	if err != nil {

		log.Error(ctx, "unable to set quote", log.String("app id", m.appId),
			log.String("state", (*currentState).State().String()), log.Err(err))
		return cerrors.WrapAsmError(m.appId, (*currentState).State().Ptr(),
			errors.Wrap(err, "unable to set quote"))
	}
	return nil
}

func (m *AppStateMachine) FinalizeQuote(ctx context.Context) (*[]oapi_app.PolicyDetails, error) {
	currentState, err := m.loadStateFromApp(ctx, m.deps, m.appId)
	if err != nil {
		log.Error(ctx, "unable to load state from app while finalizing quote",
			log.String("app id", m.appId), log.Err(err))
		return nil, errors.Wrap(err, "unable to load state from app")
	}
	appObj, _ := m.deps.AppWrapper.GetAppById(ctx, m.appId)

	// Generating Billing info requires indication to be present
	// some tests are not creating indication info
	// Inserting indication info if it is not present
	// Fixing the tests is difficult as creating the indication has a dependency on other
	// steps required to generate it like rateml
	if appObj.BindableSubmissionID != nil {
		submission, err := m.deps.AppWrapper.GetSubmissionById(ctx, *appObj.BindableSubmissionID)
		if err != nil {
			return nil, err
		}
		_, err = m.deps.AppWrapper.GetIndOptionById(ctx, submission.SelectedIndicationID)
		if err != nil {
			mockIndicationId := uuid.New().String()
			err = m.deps.AppWrapper.InsertIndOption(ctx, app.IndicationOption{
				ID:           mockIndicationId,
				SubmissionID: submission.ID,
			})
			if err != nil {
				return nil, err
			}
			err = m.deps.AppWrapper.UpdateSub(ctx, submission.ID,
				func(subObj application.SubmissionObject) (application.SubmissionObject, error) {
					subObj.SelectedIndicationID = mockIndicationId
					return subObj, nil
				})
			if err != nil {
				return nil, err
			}
		}
	}
	policyDetails, err := (*currentState).FinalizeQuote(ctx)
	if err != nil {

		log.Error(ctx, "unable to finalize quote", log.String("app id", m.appId),
			log.String("state", (*currentState).State().String()), log.Err(err))
		return nil, cerrors.WrapAsmError(m.appId, (*currentState).State().Ptr(),
			errors.Wrap(err, "unable to finalize quote"))
	}
	return policyDetails, nil
}

func (m *AppStateMachine) ReleaseQuote(ctx context.Context, sigPacketFormCompId uuid.UUID) error {
	currentState, err := m.loadStateFromApp(ctx, m.deps, m.appId)
	if err != nil {
		log.Error(ctx, "unable to load state from app while releasing quote",
			log.String("app id", m.appId), log.Err(err))
		return errors.Wrap(err, "unable to load state from app")
	}
	err = (*currentState).ReleaseQuote(ctx, sigPacketFormCompId)
	if err != nil {
		log.Error(ctx, "unable to release quote", log.String("app id", m.appId),
			log.String("state", (*currentState).State().String()), log.Err(err))
		return cerrors.WrapAsmError(m.appId, (*currentState).State().Ptr(),
			errors.Wrap(err, "unable to release quote"))
	}
	return nil
}

func (m *AppStateMachine) CreateRenewalApplication(ctx context.Context, appId string) (*string, *app_chkr.AppetiteCheckerResult, error) {
	currentState, err := m.loadStateFromApp(ctx, m.deps, m.appId)
	if err != nil {
		log.Error(ctx, "unable to load state from app while releasing quote",
			log.String("app id", m.appId), log.Err(err))
		return nil, nil, errors.Wrap(err, "unable to load state from app")
	}
	id, appetiteCheckerResult, err := (*currentState).CreateRenewalApplication(ctx, appId)
	if err != nil {
		log.Error(ctx, "unable to create renewal application", log.String("app id", m.appId),
			log.String("state", (*currentState).State().String()), log.Err(err))
		return nil, nil, cerrors.WrapAsmError(m.appId, (*currentState).State().Ptr(),
			errors.Wrap(err, "unable to create renewal application"))
	}
	if appetiteCheckerResult != nil {
		log.Error(ctx, "unable to create renewal application , app declined", log.String("app id", m.appId),
			log.String("state", (*currentState).State().String()),
			log.Any("appetiteCheckerResult", appetiteCheckerResult))
		return nil, appetiteCheckerResult, nil
	}
	return id, nil, nil
}

func (m *AppStateMachine) CreateRenewalApplicationV2(ctx context.Context, appId string) (*string, *ApplicationEvaluation, error) {
	currentState, err := m.loadStateFromApp(ctx, m.deps, m.appId)
	if err != nil {
		log.Error(ctx, "unable to load state from app while releasing quote",
			log.String("app id", m.appId), log.Err(err))
		return nil, nil, errors.Wrap(err, "unable to load state from app")
	}
	id, applicationEvaluation, err := (*currentState).CreateRenewalApplicationV2(ctx, appId)
	if err != nil {
		log.Error(ctx, "unable to create renewal application", log.String("app id", m.appId),
			log.String("state", (*currentState).State().String()), log.Err(err))
		return nil, nil, cerrors.WrapAsmError(m.appId, (*currentState).State().Ptr(),
			errors.Wrap(err, "unable to create renewal application"))
	}
	if applicationEvaluation != nil && applicationEvaluation.ExistingApplicationMetadata != nil {
		log.Error(ctx, "unable to create renewal application , renewal app exist", log.String("app id", m.appId),
			log.String("state", (*currentState).State().String()),
			log.Any("existingApplicationMetadata", applicationEvaluation.ExistingApplicationMetadata))
		return nil, applicationEvaluation, nil
	}
	if applicationEvaluation != nil && applicationEvaluation.AppetiteCheckResult != nil {
		log.Error(ctx, "unable to create renewal application , app declined", log.String("app id", m.appId),
			log.String("state", (*currentState).State().String()),
			log.Any("appetiteCheckerResult", applicationEvaluation.AppetiteCheckResult))
		return nil, applicationEvaluation, nil
	}
	return id, nil, nil
}

func (m *AppStateMachine) UpdateRenewalFields(
	ctx context.Context, form *oapi_app.PatchRenewalApplicationForm,
) error {
	currentState, err := m.loadStateFromApp(ctx, m.deps, m.appId)
	if err != nil {
		log.Error(ctx, "unable to load state from app while patching renewal fields",
			log.Err(err))
		return errors.Wrap(err, "unable to load state from app")
	}
	appObj, _ := m.deps.AppWrapper.GetAppById(ctx, m.appId)
	if !appObj.IsRenewalV2() {
		log.Error(ctx, "app must be renewal v2 to patch renewal fields")
		return errors.New("unable to patch renewal fields for non-renewal app")
	}
	err = (*currentState).UpdateRenewalFields(ctx, form)
	if err != nil {
		log.Error(ctx, "unable to update renewal fields", log.String("app id", m.appId),
			log.String("state", (*currentState).State().String()), log.Err(err))
		return cerrors.WrapAsmError(m.appId, (*currentState).State().Ptr(),
			errors.Wrap(err, "unable to update renewal fields"))
	}
	return nil
}

// loadStateFromApp loads the application state, and maps it to one of the
// defined states of the machine.
func (m *AppStateMachine) loadStateFromApp(ctx context.Context, deps ASMDeps, appId string) (*ApplicationState, error) {
	appObj, err := deps.AppWrapper.GetAppById(ctx, appId)
	if err != nil {
		return nil, err
	}
	stateEnum, err := state_enums.AppStateString(appObj.State.String())
	if err != nil {
		return nil, errors.Wrap(err, cerrors.StateNotSupported)
	}
	loadStateFn, ok := states[stateEnum]
	if !ok {
		return nil, errors.New(cerrors.StateNotSupported)
	}
	state := loadStateFn(deps, appObj.ID)
	return &state, nil
}

// loadStateFromAppObj loads the application state from the supplied appObj,
// and maps it to one of the defined states of the machine.
func (m *AppStateMachine) loadStateFromAppObj(
	deps ASMDeps,
	appObj app.Application,
) (*ApplicationState, error) {
	stateEnum, err := state_enums.AppStateString(appObj.State.String())
	if err != nil {
		return nil, errors.Wrap(err, cerrors.StateNotSupported)
	}
	loadStateFn, ok := states[stateEnum]
	if !ok {
		return nil, errors.New(cerrors.StateNotSupported)
	}
	state := loadStateFn(deps, appObj.ID)
	return &state, nil
}

func (m *AppStateMachine) GetStateInOAPIFormat(
	ctx context.Context,
	appObj app.Application,
) (*oapi_app.ApplicationState, error) {
	currentState, err := m.loadStateFromAppObj(m.deps, appObj)
	if err != nil {
		log.Error(ctx, "invalid app state",
			log.String("app id", appObj.ID),
			log.String("app_state", appObj.State.String()),
			log.Err(err))
		return nil, errors.Wrap(err, cerrors.StateNotSupported)
	}

	state, err := (*currentState).GetStateInOAPIFormat(ctx, appObj)
	if err != nil {
		log.Error(ctx, "unable to get OAPI state",
			log.String("app id", m.appId),
			log.Stringer("state", (*currentState).State()),
			log.Err(err))
		return nil, cerrors.WrapAsmError(m.appId, (*currentState).State().Ptr(),
			errors.Wrap(err, "unable to get app details"))
	}
	return state, nil
}
