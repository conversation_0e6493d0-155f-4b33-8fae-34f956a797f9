load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "app_state_machine",
    srcs = [
        "addl_info_helper.go",
        "app_state_machine.go",
        "application_creation.go",
        "application_helper.go",
        "approved_state.go",
        "closed_state.go",
        "common.go",
        "create_policy.go",
        "declined_state.go",
        "errors.go",
        "fx.go",
        "indication_generated_state.go",
        "interfaces.go",
        "log_application_event.go",
        "panic_state.go",
        "policy_created_state.go",
        "quote_generated_state.go",
        "serde_utils.go",
        "statustype_enumer.go",
        "under_uw_review_state.go",
        "unsubmitted_state.go",
        "upsert_ib.go",
        "utils.go",
    ],
    importpath = "nirvanatech.com/nirvana/quoting/app_state_machine",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/api-server/helpers",
        "//nirvana/api-server/quoting_jobber",
        "//nirvana/billing/bpid",
        "//nirvana/billing/enums",
        "//nirvana/billing/exposure/tracked_vehicle",
        "//nirvana/billing/fleet_pipeline/static_param",
        "//nirvana/billing/legacy/billinginfo",
        "//nirvana/common-go/application-util",
        "//nirvana/common-go/auth-util",
        "//nirvana/common-go/feature_flag_lib",
        "//nirvana/common-go/log",
        "//nirvana/common-go/math_utils",
        "//nirvana/common-go/metrics",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/problem",
        "//nirvana/common-go/proto",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/str_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/common-go/tracing",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/agency",
        "//nirvana/db-api/db_wrappers/agency_bd_mapping",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/application/enums/old_enums",
        "//nirvana/db-api/db_wrappers/auth",
        "//nirvana/db-api/db_wrappers/billing",
        "//nirvana/db-api/db_wrappers/ds",
        "//nirvana/db-api/db_wrappers/entity_license",
        "//nirvana/db-api/db_wrappers/entity_license/enums",
        "//nirvana/db-api/db_wrappers/fmcsa",
        "//nirvana/db-api/db_wrappers/forms",
        "//nirvana/db-api/db_wrappers/forms/enums",
        "//nirvana/db-api/db_wrappers/policy",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/db-api/db_wrappers/policy/program_data",
        "//nirvana/db-api/db_wrappers/policy/program_data/fleet",
        "//nirvana/db-api/db_wrappers/policy_set",
        "//nirvana/db-api/db_wrappers/policy_set/enums",
        "//nirvana/db-api/db_wrappers/sharing",
        "//nirvana/db-api/db_wrappers/uw",
        "//nirvana/db-api/db_wrappers/uw/appetite_factors",
        "//nirvana/ds/model",
        "//nirvana/events",
        "//nirvana/events/quoting_events",
        "//nirvana/events/underwriter_events/assignment_events",
        "//nirvana/external_client/salesforce/jobs/enums",
        "//nirvana/external_client/salesforce/wrapper",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/external_data_management/data_processing",
        "//nirvana/feature_store",
        "//nirvana/fleet/model",
        "//nirvana/fmcsa/models",
        "//nirvana/infra/authz",
        "//nirvana/infra/constants",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/insurance-bundle/model",
        "//nirvana/insurance-bundle/model/charges",
        "//nirvana/insurance-bundle/service",
        "//nirvana/insurance-core/coverage",
        "//nirvana/insurance-core/proto",
        "//nirvana/insured/model",
        "//nirvana/jobber",
        "//nirvana/jobber/jtypes",
        "//nirvana/openapi-specs/components/application",
        "//nirvana/openapi-specs/components/common",
        "//nirvana/openapi-specs/components/forms",
        "//nirvana/policy",
        "//nirvana/policy/constants",
        "//nirvana/policy/enums",
        "//nirvana/policy/fleet",
        "//nirvana/policy_common/constants",
        "//nirvana/policy_common/forms_generator",
        "//nirvana/policy_common/forms_generator/compilation",
        "//nirvana/policy_common/forms_generator/forms",
        "//nirvana/policy_common/policy/shared",
        "//nirvana/quoting/agent_license",
        "//nirvana/quoting/app_state_machine/app_logic",
        "//nirvana/quoting/app_state_machine/cerrors",
        "//nirvana/quoting/app_state_machine/enums",
        "//nirvana/quoting/appetite_checker",
        "//nirvana/quoting/appetite_checker/enums",
        "//nirvana/quoting/clearance",
        "//nirvana/quoting/clearance/emails",
        "//nirvana/quoting/clearance/enums",
        "//nirvana/quoting/experiments",
        "//nirvana/quoting/jobs",
        "//nirvana/quoting/quote_generator",
        "//nirvana/quoting/tags",
        "//nirvana/quoting/utils",
        "//nirvana/rating/data_processing/lni_processing",
        "//nirvana/rating/data_processing/rating_tier",
        "//nirvana/rating/models/models_release",
        "//nirvana/rating/rtypes",
        "//nirvana/servers/telematicsv2",
        "//nirvana/sharing",
        "//nirvana/telematics",
        "//nirvana/telematics/connections",
        "//nirvana/underwriting/app_review/utils",
        "//nirvana/underwriting/appetite_factors/appetite_score_rubric",
        "//nirvana/underwriting/appetite_factors/input",
        "//nirvana/underwriting/common",
        "//nirvana/underwriting/common-utils",
        "//nirvana/underwriting/jobs",
        "//nirvana/underwriting/risk_factors",
        "//nirvana/underwriting/scheduler",
        "//nirvana/underwriting/state_machine/review_readiness_state_machine",
        "//nirvana/underwriting/state_machine/telematics_connection_state_machine",
        "//nirvana/underwriting/task/taskmanager",
        "//nirvana/underwriting/utils",
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_cactus_go_statsd_client_v5//statsd",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_jinzhu_copier//:copier",
        "@com_github_oapi_codegen_runtime//types",
        "@com_github_volatiletech_null_v8//:null",
        "@io_opentelemetry_go_otel//attribute",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "app_state_machine_test",
    srcs = [
        "addl_info_helper_test.go",
        "create_policy_test.go",
        "upsert_ib_test.go",
        "utils_test.go",
    ],
    embed = [":app_state_machine"],
    deps = [
        "//nirvana/billing/enums",
        "//nirvana/billing/legacy/billinginfo",
        "//nirvana/common-go/feature_flag_lib",
        "//nirvana/common-go/feature_flag_lib/mock",
        "//nirvana/common-go/grpc",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/proto",
        "//nirvana/common-go/short_id",
        "//nirvana/common-go/time_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/agency",
        "//nirvana/db-api/db_wrappers/agency/mock",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/application/enums/old_enums",
        "//nirvana/db-api/db_wrappers/auth/mock",
        "//nirvana/db-api/db_wrappers/entity_license",
        "//nirvana/db-api/db_wrappers/entity_license/mock",
        "//nirvana/db-api/db_wrappers/fmcsa",
        "//nirvana/db-api/db_wrappers/forms",
        "//nirvana/db-api/db_wrappers/forms/mock",
        "//nirvana/db-api/db_wrappers/uw",
        "//nirvana/db-api/db_wrappers/uw/mock",
        "//nirvana/fleet/model",
        "//nirvana/fmcsa/models",
        "//nirvana/infra/authz",
        "//nirvana/insurance-bundle/model",
        "//nirvana/insurance-bundle/service",
        "//nirvana/insurance-core/proto",
        "//nirvana/insured/model",
        "//nirvana/openapi-specs/components/application",
        "//nirvana/openapi-specs/components/common",
        "//nirvana/openapi-specs/components/forms",
        "//nirvana/policy_common/constants",
        "//nirvana/policy_common/forms_generator/compilation",
        "//nirvana/quoting/app_state_machine/enums",
        "//nirvana/rating/data_processing/vin_processing/iso_utils",
        "//nirvana/rating/pricing/api/ptypes",
        "//nirvana/telematics",
        "@com_github_google_uuid//:uuid",
        "@com_github_oapi_codegen_runtime//types",
        "@com_github_stretchr_testify//suite",
        "@com_github_volatiletech_null_v8//:null",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_mock//gomock",
    ],
)
