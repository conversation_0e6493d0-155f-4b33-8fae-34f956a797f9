package vehicles_test

import (
	"context"
	"testing"
	"time"

	"github.com/samsarahq/go/snapshotter"
	"github.com/stretchr/testify/require"
	"go.uber.org/fx"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/infra/fx/testfixtures/vehicles_graph_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/servers/vehicles"
)

func TestVehiclesServiceTelematics(t *testing.T) {
	var env struct {
		fx.In
		Client vehicles.VehiclesServiceClient
		Data   vehicles_graph_fixture.VehiclesGraphData
		Snap   *snapshotter.Snapshotter
	}
	defer testloader.RequireStart(t, &env).RequireStop()

	var snapshots []*vehicles.TelematicsConnectionSnapshotNode
	for _, snapshot := range env.Data.TelematicsData.SnapshotUpserts {
		snapshots = append(snapshots, snapshot.GetConnection())
	}
	require.True(t, len(snapshots) > 4)
	snapshot := snapshots[2]
	startTime := snapshots[0].GetKey().GetTime()
	endTime := snapshots[len(snapshots)-1].GetKey().GetTime()

	ctx := context.Background()
	fleetID := env.Data.FleetData.Fleet.Key.FleetID
	handleID := snapshot.GetKey().GetHandleID()
	listConnectionsResp, err := env.Client.ListTelematicsConnections(ctx, &vehicles.ListTelematicsConnectionsRequest{
		FleetID: fleetID,
	})
	require.NoError(t, err)
	env.Snap.Snapshot("ListTelematicsConnections response", listConnectionsResp)

	listSnapshotsResp, err := env.Client.ListTelematicsSnapshots(ctx, &vehicles.ListTelematicsSnapshotsRequest{
		HandleID:  handleID,
		StartTime: startTime,
		EndTime:   endTime,
	})
	require.NoError(t, err)
	env.Snap.Snapshot("ListTelematicsSnapshots response", listSnapshotsResp)

	getSnapshotResp, err := env.Client.GetTelematicsSnapshot(ctx, &vehicles.GetTelematicsSnapshotRequest{
		Snapshot: snapshot.GetKey(),
	})
	require.NoError(t, err)
	env.Snap.Snapshot("GetTelematicsSnapshot response", getSnapshotResp)

	testcases := []struct {
		Name      string
		StartTime time.Time
		EndTime   time.Time
		Expected  []*vehicles.TelematicsConnectionSnapshotNode
	}{
		{
			Name:      "Exact time match should return the snapshot",
			StartTime: snapshots[1].GetKey().GetTime().AsTime(),
			EndTime:   snapshots[1].GetKey().GetTime().AsTime(),
			Expected:  []*vehicles.TelematicsConnectionSnapshotNode{snapshots[1]},
		},
		{
			Name:      "Start time and end time overlapping a single snapshot time",
			StartTime: snapshots[1].GetKey().GetTime().AsTime().Add(-time.Minute),
			EndTime:   snapshots[1].GetKey().GetTime().AsTime().Add(time.Minute),
			Expected:  []*vehicles.TelematicsConnectionSnapshotNode{snapshots[1]},
		},
		{
			Name:      "Time range with no snapshots should return an empty response",
			StartTime: snapshots[0].GetKey().GetTime().AsTime().Add(-2 * time.Minute),
			EndTime:   snapshots[0].GetKey().GetTime().AsTime().Add(-time.Minute),
			Expected:  nil,
		},
		{
			Name:      "Time range enclosing multiple snapshots should return both snapshots",
			StartTime: snapshots[1].GetKey().GetTime().AsTime().Add(-time.Minute),
			EndTime:   snapshots[3].GetKey().GetTime().AsTime().Add(-time.Minute), // EndTime is before snapshots[3].
			Expected: []*vehicles.TelematicsConnectionSnapshotNode{
				snapshots[1], snapshots[2],
			},
		},
		{
			Name:      "Zero to now should return all snapshots",
			StartTime: time.Time{},
			EndTime:   time.Now(),
			Expected:  snapshots,
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.Name, func(t *testing.T) {
			resp, err := env.Client.ListTelematicsSnapshots(ctx, &vehicles.ListTelematicsSnapshotsRequest{
				HandleID:  handleID,
				StartTime: timestamppb.New(testcase.StartTime),
				EndTime:   timestamppb.New(testcase.EndTime),
			})
			require.NoError(t, err)
			require.Len(t, resp.Snapshots, len(testcase.Expected))
			for i := 0; i < len(testcase.Expected); i++ {
				expected := testcase.Expected[i]
				actual := resp.Snapshots[i]
				require.True(t, proto.Equal(expected, actual))
			}
		})
	}
}
