package source_data

import (
	"text/template"
	"time"
)

// SMSMonth returns YYYYMon used by existing sms templates.
func SMSMonth(t time.Time) string {
	return FormatSMSDate(t, false)
}

// SMSDay returns YYYYMMDD used by upcoming day-level templates.
func SMSDay(t time.Time) string {
	return FormatSMSDate(t, true)
}

// TemplateFuncMap contains functions that generators can inject into their
// text/template instances so that templates can call {{ SMSMonth . }} or
// {{ SMSDay . }}.
var TemplateFuncMap = template.FuncMap{
	"SMSMonth": SMSMonth,
	"SMSDay":   SMSDay,
}
