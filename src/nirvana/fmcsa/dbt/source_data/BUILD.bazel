load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "source_data",
    srcs = [
        "sms_date.go",
        "template_funcs.go",
    ],
    importpath = "nirvanatech.com/nirvana/fmcsa/dbt/source_data",
    visibility = ["//visibility:public"],
)

go_test(
    name = "source_data_test",
    srcs = [
        "sms_date_test.go",
        "template_funcs_test.go",
    ],
    embed = [":source_data"],
    deps = ["@com_github_stretchr_testify//assert"],
)
