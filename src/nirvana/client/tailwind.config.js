const colors = require('tailwindcss/colors');
const defaultTheme = require('tailwindcss/defaultTheme');

module.exports = {
  content: ['./packages/**/*.{html,tsx,ts}', './apps/**/*.{html,tsx,ts}'],
  theme: {
    extend: {
      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 300ms ease-out',
        'accordion-up': 'accordion-up 300ms ease-out',
      },
      aria: {
        invalid: 'invalid="true"',
      },
      fontFamily: {
        sans: ['"Inter"', ...defaultTheme.fontFamily.sans],
      },
      fontSize: {
        xxs: '0.6rem',
        '28px': '1.75rem',
        '13px': '0.8125rem',
      },
      letterSpacing: {
        tight: '0.15px',
      },
      maxWidth: {
        '8xl': '1400px',
        10: '2.5rem', // ~40px
        44: '11rem', // ~175px
        48: '12rem', // ~192px
        52: '13rem', // ~208px,
        72: '18rem', // ~288px,
        76: '19rem', // ~304px,
      },
      maxHeight: {
        10: '2.5rem', // ~40px
      },
      minWidth: {
        12: '3rem', // ~16px
        40: '10em',
      },
      height: {
        '2x': '200%',
        10: '2.5rem', // ~40px
      },
      flexGrow: {
        2: '2',
      },
      boxShadow: {
        'tw-sm':
          '0px 0px 1px 0px rgba(0, 0, 0, 0.32), 0px 0px 2px 0px rgba(0, 0, 0, 0.08), 0px 1px 3px 0px rgba(0, 0, 0, 0.08)',
        'tw-md':
          '0px 0px 1px 0px rgba(0, 0, 0, 0.24), 0px 0px 2px 0px rgba(0, 0, 0, 0.08), 0px 2px 4px 0px rgba(0, 0, 0, 0.08), 0px 2px 16px 0px rgba(0, 0, 0, 0.06)',
        'tw-lg':
          '0px 0px 1px rgba(0, 0, 0, 0.24), 0px 0px 2px rgba(0, 0, 0, 0.16), 0px 3px 4px rgba(0, 0, 0, 0.06), 0px 6px 8px rgba(0, 0, 0, 0.06), 0px 12px 16px rgba(0, 0, 0, 0.08), 0px 18px 32px rgba(0, 0, 0, 0.1)',
      },
      width: {
        xs: '20rem',
        lg: '32rem',
      },
      colors: {
        orange: colors.orange,
        primary: {
          main: '#3350A1',
          dark: '#00248A',
          light: '#8091C4',
          extraLight: '#F3F4FA',
          tint1: '#FAFBFF',
          tint2: '#D7DCFE',
          tint3: '#E6E7EF',
        },
        secondary: {
          main: '#040C21',
          dark: '#4A505F',
          light: '#8C8F99',
          charcoal: '#0C0B0B',
          tint1: '#D2E7FD',
          tint2: '#F3F3F3',
          tint3: '#A3CCFB',
          tint4: '#D0DCFF',
        },
        text: {
          primary: '#363D4D',
          secondary: '#4A505F',
          disabled: '#D7D8DB',
          hint: '#878B95',
          blue: '#1F64CD',
          lightestGreen: '#F6FFF9',
          darkGrey: '#F5F5F6',
          lightGrey: '#F9FAFC',
        },
        info: {
          main: '#00A0FF',
          dark: '#00A0FF',
          light: '#FAFDFF',
          extraLight: '#EEF8FF',
        },
        success: {
          main: '#1ED05A',
          dark: '#25B255',
          tint1: '#25B34A',
          light: '#FAFEFC',
          extraLight: '#E5FFEE',
        },
        error: {
          main: '#FA3252',
          dark: '#FA3252',
          light: '#FFFBFC',
          extraLight: '#FFF1F4',
          tint1: '#FFE3E7',
          tint2: '#FFF5F7',
          tint3: '#FDAAB7',
        },
        warning: {
          main: '#FF823C',
          dark: '#FF823C',
          light: '#FFFCFB',
          extraLight: '#FFF7F4',
          tint1: '#FFF4EE',
          tint2: '#FFC4A0',
          tint3: '#FEC6CF',
        },
        gold: {
          main: '#FFB900',
          light: '#FFDC80',
          dark: '#B67D28',
          tint: '#FFFAEB',
          tint1: '#FFE399',
          'tint1-opacity-35': 'rgba(255, 227, 153, 0.35)',
        },
        green: {
          main: '#00A839',
          light: '#B5EBBA',
          tint: '#D9F8DD',
          tint2: '#EEFFF2',
        },
        gradient: {
          primary: 'rgba(244,246,255,0)',
          secondary: 'rgba(250,250,254,1)',
        },
        background: {
          default: '#FBFBFB', // Based on palette.background.default from ui-kit/themes
          secondary: '#F2F3F4',
        },
        mark: {
          default: '#ffdcc8',
        },
        icon: {
          default: '#4A505F',
          active: '#00248A',
        },
        trs: {
          inconclusive: '#D7D8DB',
          excellent: '#25B24A',
          good: '#82D01E',
          fair: '#FF9900',
          poor: '#FA3252',
        },
        red: {
          500: '#FA3252',
        },
        navy: {
          50: '#F9F9F9',
        },
        'tw-secondary': '#686D7A',
        'tw-primary': '#040C21',
        'tw-border': {
          secondary: '#CDCED3',
        },
        'tw-actions': {
          'fill-primary': '#265FD9',
        },
        'tw-gray': {
          100: 'var(--gray-100)',
          200: 'var(--gray-200)',
          300: 'var(--gray-300)',
          400: 'var(--gray-400)',
          600: 'var(--gray-600)',
          700: 'var(--gray-700)',
          1000: 'var(--gray-1000)',
        },
        'tw-navy': {
          50: 'var(--navy-50)',
          100: 'var(--navy-100)',
          200: 'var(--navy-200)',
          600: 'var(--navy-600)',
          700: 'var(--navy-700)',
          900: 'var(--navy-900)',
        },
        'tw-blue': {
          100: 'var(--blue-100)',
          200: 'var(--blue-200)',
          400: 'var(--blue-400)',
          500: 'var(--blue-500)',
          600: 'var(--blue-600)',
          700: 'var(--blue-700)',
          1000: 'var(--blue-1000)',
        },
        'tw-teal': {
          100: 'var(--teal-100)',
          200: 'var(--teal-200)',
          500: 'var(--teal-500)',
          600: 'var(--teal-600)',
          700: 'var(--teal-700)',
        },
        'tw-green': {
          100: 'var(--green-100)',
          200: 'var(--green-200)',
          600: 'var(--green-600)',
          700: 'var(--green-700)',
        },
        'tw-gold': {
          100: 'var(--gold-100)',
          200: 'var(--gold-200)',
          400: 'var(--gold-400)',
          500: 'var(--gold-500)',
          600: 'var(--gold-600)',
          700: 'var(--gold-700)',
        },
        'tw-orange': {
          100: 'var(--orange-100)',
          200: 'var(--orange-200)',
          600: 'var(--orange-600)',
          700: 'var(--orange-700)',
        },
        'tw-red': {
          100: 'var(--red-100)',
          200: 'var(--red-200)',
          600: 'var(--red-600)',
          700: 'var(--red-700)',
        },
        teal: {
          200: '#DAF2F1',
          600: '#48BFB7',
        },
      },
    },
  },
  plugins: [require('@tailwindcss/typography'), require('tailwindcss-animate')],
};
