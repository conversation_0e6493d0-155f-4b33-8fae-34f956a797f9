/* tslint:disable */
/* eslint-disable */
/**
 * Nirvana Billing API
 * Nirvana Billing APIs
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { Configuration } from './configuration';
import globalAxios, {
  AxiosPromise,
  AxiosInstance,
  AxiosRequestConfig,
} from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import {
  DUMMY_BASE_URL,
  assertParamExists,
  setApiKeyToObject,
  setBasicAuthToObject,
  setBearerAuthToObject,
  setOAuthToObject,
  setSearchParams,
  serializeDataIfNeeded,
  toPathString,
  createRequestFunction,
} from './common';
// @ts-ignore
import {
  BASE_PATH,
  COLLECTION_FORMATS,
  RequestArgs,
  BaseAPI,
  RequiredError,
} from './base';

/**
 *
 * @export
 * @enum {string}
 */

export enum BillingFrequency {
  PaidInFull = 'PaidInFull',
  Monthly = 'Monthly',
}

/**
 *
 * @export
 * @interface BillingInsuredSummary
 */
export interface BillingInsuredSummary {
  /**
   *
   * @type {string}
   * @memberof BillingInsuredSummary
   */
  billingPolicyId: string;
  /**
   *
   * @type {number}
   * @memberof BillingInsuredSummary
   */
  dotNumber: number;
  /**
   *
   * @type {string}
   * @memberof BillingInsuredSummary
   */
  name: string;
  /**
   *
   * @type {Array<MileageSourceSummary>}
   * @memberof BillingInsuredSummary
   */
  mileageSources: Array<MileageSourceSummary>;
}
/**
 *
 * @export
 * @interface BillingRates
 */
export interface BillingRates {
  /**
   *
   * @type {number}
   * @memberof BillingRates
   */
  ALRate: number;
  /**
   *
   * @type {number}
   * @memberof BillingRates
   */
  APDPremium: number;
  /**
   *
   * @type {number}
   * @memberof BillingRates
   */
  GLPremium: number;
  /**
   *
   * @type {number}
   * @memberof BillingRates
   */
  MTCRate: number;
  /**
   *
   * @type {number}
   * @memberof BillingRates
   */
  FlatCharge: number;
  /**
   *
   * @type {number}
   * @memberof BillingRates
   */
  CommissionRate: number;
  /**
   *
   * @type {number}
   * @memberof BillingRates
   */
  DepositAmount: number;
  /**
   *
   * @type {number}
   * @memberof BillingRates
   */
  StateSurcharge: number;
  /**
   *
   * @type {number}
   * @memberof BillingRates
   */
  YearlyEstimatedMiles: number;
  /**
   *
   * @type {number}
   * @memberof BillingRates
   */
  CollateralAmount?: number;
}
/**
 *
 * @export
 * @interface CalculateDiffTrackedVehicleScheduleRequest
 */
export interface CalculateDiffTrackedVehicleScheduleRequest {
  /**
   * id consisting of policy identifier and issuance year
   * @type {string}
   * @memberof CalculateDiffTrackedVehicleScheduleRequest
   */
  billingPolicyId: string;
  /**
   * is an array of the only vehicles that will be tracked for the referenced policy
   * @type {Array<TrackedVehicleToDiff>}
   * @memberof CalculateDiffTrackedVehicleScheduleRequest
   */
  trackedVehicles: Array<TrackedVehicleToDiff>;
}
/**
 *
 * @export
 * @interface CalculateDiffTrackedVehicleScheduleResponse
 */
export interface CalculateDiffTrackedVehicleScheduleResponse {
  /**
   * the number of currently insured vehicles that would be marked as uncovered for APD
   * @type {number}
   * @memberof CalculateDiffTrackedVehicleScheduleResponse
   */
  vehiclesRemoved: number;
  /**
   * the number of additional vehicles that would be marked as covered for APD
   * @type {number}
   * @memberof CalculateDiffTrackedVehicleScheduleResponse
   */
  vehiclesAdded: number;
}
/**
 *
 * @export
 * @interface ContactSummary
 */
export interface ContactSummary {
  /**
   *
   * @type {string}
   * @memberof ContactSummary
   */
  firstName: string;
  /**
   *
   * @type {string}
   * @memberof ContactSummary
   */
  lastName: string;
  /**
   *
   * @type {string}
   * @memberof ContactSummary
   */
  email: string;
}
/**
 *
 * @export
 * @interface CreateTrackedVehicleRequest
 */
export interface CreateTrackedVehicleRequest {
  /**
   * id consisting of policy identifier and issuance year
   * @type {string}
   * @memberof CreateTrackedVehicleRequest
   */
  billingPolicyId: string;
  /**
   *
   * @type {string}
   * @memberof CreateTrackedVehicleRequest
   */
  vin: string;
  /**
   *
   * @type {TrackedVehicleType}
   * @memberof CreateTrackedVehicleRequest
   */
  type?: TrackedVehicleType;
  /**
   *
   * @type {number}
   * @memberof CreateTrackedVehicleRequest
   */
  insuredValue?: number;
  /**
   *
   * @type {boolean}
   * @memberof CreateTrackedVehicleRequest
   */
  isTrackedForAPD: boolean;
  /**
   *
   * @type {string}
   * @memberof CreateTrackedVehicleRequest
   */
  dateSeen: string;
  /**
   *
   * @type {TrackedVehicleEffectiveFrom}
   * @memberof CreateTrackedVehicleRequest
   */
  effectiveFrom: TrackedVehicleEffectiveFrom;
}
/**
 *
 * @export
 * @interface DisputeConsumptionFormItem
 */
export interface DisputeConsumptionFormItem {
  /**
   *
   * @type {string}
   * @memberof DisputeConsumptionFormItem
   */
  billingPolicyId: string;
  /**
   *
   * @type {string}
   * @memberof DisputeConsumptionFormItem
   */
  policyNumber: string;
  /**
   * Time when the aggregate mileage dispute was recorded
   * @type {string}
   * @memberof DisputeConsumptionFormItem
   */
  aggregateMileageSubmittedAt?: string;
  /**
   *
   * @type {string}
   * @memberof DisputeConsumptionFormItem
   */
  aggregateMileageCreatedBy?: string;
  /**
   * Time when the mileage dispute was recorded
   * @type {string}
   * @memberof DisputeConsumptionFormItem
   */
  mileageSubmittedAt?: string;
  /**
   * Time when the TIV dispute was recorded
   * @type {string}
   * @memberof DisputeConsumptionFormItem
   */
  tivSubmittedAt?: string;
  /**
   *
   * @type {Array<string>}
   * @memberof DisputeConsumptionFormItem
   */
  billableVINsForMileage: Array<string>;
  /**
   *
   * @type {Array<string>}
   * @memberof DisputeConsumptionFormItem
   */
  billableVINsForTIV: Array<string>;
  /**
   * The resulting amount of total mileage for the fleet after disputes have been applied (if any).
   * @type {number}
   * @memberof DisputeConsumptionFormItem
   */
  disputedAggregateMileage?: number;
  /**
   *
   * @type {Array<GetDisputedMileageResponse>}
   * @memberof DisputeConsumptionFormItem
   */
  disputedMileagePerVIN: Array<GetDisputedMileageResponse>;
  /**
   *
   * @type {Array<DisputedInsuredValueForForm>}
   * @memberof DisputeConsumptionFormItem
   */
  disputedInsuredValuesPerVIN: Array<DisputedInsuredValueForForm>;
  /**
   *
   * @type {string}
   * @memberof DisputeConsumptionFormItem
   */
  sourceType: DisputeConsumptionFormItemSourceTypeEnum;
}

/**
 * @export
 * @enum {string}
 */
export enum DisputeConsumptionFormItemSourceTypeEnum {
  SelfReporter = 'SelfReporter',
  PartialSelfReporter = 'PartialSelfReporter',
  MinimumMileageGuarantee = 'MinimumMileageGuarantee',
  Telematics = 'Telematics',
}

/**
 *
 * @export
 * @interface DisputeConsumptionRequest
 */
export interface DisputeConsumptionRequest {
  /**
   *
   * @type {string}
   * @memberof DisputeConsumptionRequest
   */
  policyNumber: string;
  /**
   *
   * @type {string}
   * @memberof DisputeConsumptionRequest
   */
  billingPeriodStartDate: string;
  /**
   *
   * @type {string}
   * @memberof DisputeConsumptionRequest
   */
  billingPeriodEndDate: string;
  /**
   * The total amount of miles traveled during the month. Ignored if mileage per VIN is given.
   * @type {number}
   * @memberof DisputeConsumptionRequest
   */
  disputedAggregateMileage?: number;
  /**
   *
   * @type {Array<DisputedMileage>}
   * @memberof DisputeConsumptionRequest
   */
  disputedMileagePerVIN: Array<DisputedMileage>;
  /**
   *
   * @type {Array<DisputedInsuredValue>}
   * @memberof DisputeConsumptionRequest
   */
  disputedInsuredValuesPerVIN: Array<DisputedInsuredValue>;
}
/**
 *
 * @export
 * @interface DisputedInsuredValue
 */
export interface DisputedInsuredValue {
  /**
   *
   * @type {string}
   * @memberof DisputedInsuredValue
   */
  VIN: string;
  /**
   *
   * @type {number}
   * @memberof DisputedInsuredValue
   */
  insuredValue: number;
  /**
   *
   * @type {TrackedVehicleEffectiveFrom}
   * @memberof DisputedInsuredValue
   */
  effectiveFrom: TrackedVehicleEffectiveFrom;
}
/**
 *
 * @export
 * @interface DisputedInsuredValueForForm
 */
export interface DisputedInsuredValueForForm {
  /**
   *
   * @type {string}
   * @memberof DisputedInsuredValueForForm
   */
  VIN: string;
  /**
   *
   * @type {number}
   * @memberof DisputedInsuredValueForForm
   */
  insuredValue: number;
  /**
   *
   * @type {TrackedVehicleEffectiveFrom}
   * @memberof DisputedInsuredValueForForm
   */
  effectiveFrom: TrackedVehicleEffectiveFrom;
  /**
   *
   * @type {string}
   * @memberof DisputedInsuredValueForForm
   */
  createdBy?: string;
}
/**
 *
 * @export
 * @interface DisputedMileage
 */
export interface DisputedMileage {
  /**
   *
   * @type {string}
   * @memberof DisputedMileage
   */
  VIN: string;
  /**
   *
   * @type {number}
   * @memberof DisputedMileage
   */
  mileage: number;
  /**
   *
   * @type {string}
   * @memberof DisputedMileage
   */
  reason?: string;
}
/**
 *
 * @export
 * @interface DownloadFileLinkResponse
 */
export interface DownloadFileLinkResponse {
  /**
   *
   * @type {string}
   * @memberof DownloadFileLinkResponse
   */
  link: string;
}
/**
 *
 * @export
 * @interface ErrorMessage
 */
export interface ErrorMessage {
  /**
   *
   * @type {string}
   * @memberof ErrorMessage
   */
  message: string;
  /**
   *
   * @type {number}
   * @memberof ErrorMessage
   */
  code: number;
}
/**
 *
 * @export
 * @interface GetBillingInfoForPolicyIdentifierResponse
 */
export interface GetBillingInfoForPolicyIdentifierResponse {
  /**
   *
   * @type {string}
   * @memberof GetBillingInfoForPolicyIdentifierResponse
   */
  applicationId: string;
  /**
   *
   * @type {string}
   * @memberof GetBillingInfoForPolicyIdentifierResponse
   */
  agencyName: string;
  /**
   *
   * @type {string}
   * @memberof GetBillingInfoForPolicyIdentifierResponse
   */
  agentEmail: string;
  /**
   *
   * @type {string}
   * @memberof GetBillingInfoForPolicyIdentifierResponse
   */
  agentName: string;
  /**
   *
   * @type {BillingFrequency}
   * @memberof GetBillingInfoForPolicyIdentifierResponse
   */
  billingFrequency: BillingFrequency;
  /**
   *
   * @type {string}
   * @memberof GetBillingInfoForPolicyIdentifierResponse
   */
  carrier: string;
  /**
   *
   * @type {ContactSummary}
   * @memberof GetBillingInfoForPolicyIdentifierResponse
   */
  primaryBillingContact?: ContactSummary;
  /**
   *
   * @type {string}
   * @memberof GetBillingInfoForPolicyIdentifierResponse
   */
  effectiveDate: string;
  /**
   *
   * @type {string}
   * @memberof GetBillingInfoForPolicyIdentifierResponse
   */
  effectiveDateTo: string;
  /**
   *
   * @type {string}
   * @memberof GetBillingInfoForPolicyIdentifierResponse
   */
  insuredName: string;
  /**
   *
   * @type {string}
   * @memberof GetBillingInfoForPolicyIdentifierResponse
   */
  policyIdentifier: string;
  /**
   *
   * @type {Array<PolicySummary>}
   * @memberof GetBillingInfoForPolicyIdentifierResponse
   */
  policySummaries: Array<PolicySummary>;
  /**
   *
   * @type {BillingRates}
   * @memberof GetBillingInfoForPolicyIdentifierResponse
   */
  rates: BillingRates;
  /**
   *
   * @type {PolicyStatus}
   * @memberof GetBillingInfoForPolicyIdentifierResponse
   */
  status: PolicyStatus;
  /**
   *
   * @type {boolean}
   * @memberof GetBillingInfoForPolicyIdentifierResponse
   */
  canCancel: boolean;
  /**
   *
   * @type {boolean}
   * @memberof GetBillingInfoForPolicyIdentifierResponse
   */
  canReinstate: boolean;
  /**
   *
   * @type {string}
   * @memberof GetBillingInfoForPolicyIdentifierResponse
   */
  policySetId: string;
  /**
   *
   * @type {number}
   * @memberof GetBillingInfoForPolicyIdentifierResponse
   */
  countOfDepositInstallments?: number;
}
/**
 *
 * @export
 * @interface GetBillingInfoResponse
 */
export interface GetBillingInfoResponse {
  /**
   *
   * @type {number}
   * @memberof GetBillingInfoResponse
   */
  dotNumber: number;
  /**
   *
   * @type {string}
   * @memberof GetBillingInfoResponse
   */
  companyName: string;
  /**
   *
   * @type {string}
   * @memberof GetBillingInfoResponse
   */
  carrierName: string;
  /**
   *
   * @type {PolicyActivityStatus}
   * @memberof GetBillingInfoResponse
   */
  activityStatus: PolicyActivityStatus;
  /**
   *
   * @type {BillingFrequency}
   * @memberof GetBillingInfoResponse
   */
  frequency: BillingFrequency;
  /**
   *
   * @type {Array<MileageSourceDetails>}
   * @memberof GetBillingInfoResponse
   */
  mileageSources: Array<MileageSourceDetails>;
}
/**
 *
 * @export
 * @interface GetDisputedMileageResponse
 */
export interface GetDisputedMileageResponse {
  /**
   *
   * @type {string}
   * @memberof GetDisputedMileageResponse
   */
  VIN: string;
  /**
   *
   * @type {number}
   * @memberof GetDisputedMileageResponse
   */
  mileage: number;
  /**
   *
   * @type {string}
   * @memberof GetDisputedMileageResponse
   */
  reason?: string;
  /**
   *
   * @type {string}
   * @memberof GetDisputedMileageResponse
   */
  createdBy?: string;
}
/**
 *
 * @export
 * @interface GetMonthlyBillingItem
 */
export interface GetMonthlyBillingItem {
  /**
   *
   * @type {string}
   * @memberof GetMonthlyBillingItem
   */
  billingStartDate: string;
  /**
   *
   * @type {string}
   * @memberof GetMonthlyBillingItem
   */
  billingEndDate: string;
  /**
   *
   * @type {number}
   * @memberof GetMonthlyBillingItem
   */
  projectedMileage: number;
  /**
   *
   * @type {string}
   * @memberof GetMonthlyBillingItem
   */
  mileageReportHandleId?: string;
}
/**
 *
 * @export
 * @interface GetMonthlyBillingResponse
 */
export interface GetMonthlyBillingResponse {
  /**
   *
   * @type {Array<GetMonthlyBillingItem>}
   * @memberof GetMonthlyBillingResponse
   */
  monthly: Array<GetMonthlyBillingItem>;
}
/**
 *
 * @export
 * @interface HealthResponse
 */
export interface HealthResponse {
  /**
   *
   * @type {string}
   * @memberof HealthResponse
   */
  health?: string;
  /**
   *
   * @type {string}
   * @memberof HealthResponse
   */
  message?: string;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum JobRunStatus {
  Waiting = 'Waiting',
  Running = 'Running',
  Failed = 'Failed',
  Terminated = 'Terminated',
  Succeeded = 'Succeeded',
  Unknown = 'Unknown',
}

/**
 *
 * @export
 * @interface MileagePerVIN
 */
export interface MileagePerVIN {
  /**
   *
   * @type {string}
   * @memberof MileagePerVIN
   */
  VIN: string;
  /**
   *
   * @type {number}
   * @memberof MileagePerVIN
   */
  mileage: number;
  /**
   *
   * @type {MileageType}
   * @memberof MileagePerVIN
   */
  type: MileageType;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum MileageSource {
  MileageSpecialCase = 'MileageSpecialCase',
  Telematics = 'Telematics',
  SelfReporting = 'Self-Reporting',
}

/**
 *
 * @export
 * @interface MileageSourceDetails
 */
export interface MileageSourceDetails {
  /**
   *
   * @type {string}
   * @memberof MileageSourceDetails
   */
  mileageSourceOverrideId?: string;
  /**
   *
   * @type {string}
   * @memberof MileageSourceDetails
   */
  source: MileageSourceDetailsSourceEnum;
  /**
   *
   * @type {MileageSourceType}
   * @memberof MileageSourceDetails
   */
  type?: MileageSourceType;
  /**
   *
   * @type {string}
   * @memberof MileageSourceDetails
   */
  policyIdentifier: string;
  /**
   *
   * @type {number}
   * @memberof MileageSourceDetails
   */
  policyIssuanceYear: number;
  /**
   *
   * @type {string}
   * @memberof MileageSourceDetails
   */
  TSP?: string;
  /**
   *
   * @type {string}
   * @memberof MileageSourceDetails
   */
  connectionHandleId?: string;
  /**
   * VehicleTags allow us to specify if we need to consider only a subset of the VINs from a telematics connection for Shared TSPs. Tags are not VINs.
   * @type {Array<string>}
   * @memberof MileageSourceDetails
   */
  vehicleTags?: Array<string>;
  /**
   * ExcludedVINs allow us to specify if we need to exclude a subset of VINs from a telematics connection.
   * @type {Array<string>}
   * @memberof MileageSourceDetails
   */
  excludedVINs?: Array<string>;
}

/**
 * @export
 * @enum {string}
 */
export enum MileageSourceDetailsSourceEnum {
  SingleTsp = 'SingleTSP',
  MultiTsp = 'MultiTSP',
  SharedTsp = 'SharedTSP',
  SelfReporter = 'SelfReporter',
}

/**
 *
 * @export
 * @enum {string}
 */

export enum MileageSourceOverrideInputSource {
  Tsp = 'TSP',
  SelfReporter = 'SelfReporter',
}

/**
 *
 * @export
 * @enum {string}
 */

export enum MileageSourceOverrideInputType {
  IftaDisabled = 'IFTADisabled',
  EldExempted = 'ELDExempted',
  NonCompliantTsp = 'NonCompliantTSP',
  OwnerOperators = 'OwnerOperators',
  MinimumMileageGuarantee = 'MinimumMileageGuarantee',
}

/**
 *
 * @export
 * @enum {string}
 */

export enum MileageSourceOverrideStatus {
  Active = 'Active',
  Deleted = 'Deleted',
  Disabled = 'Disabled',
}

/**
 *
 * @export
 * @enum {string}
 */

export enum MileageSourceOverrideStatusReason {
  TspSwitching = 'TSP Switching',
  UnreliableData = 'Unreliable Data',
  Cancelled = 'Cancelled',
  NoLongerInUse = 'NoLongerInUse',
  Other = 'Other',
}

/**
 *
 * @export
 * @interface MileageSourceSummary
 */
export interface MileageSourceSummary {
  /**
   *
   * @type {string}
   * @memberof MileageSourceSummary
   */
  billingPolicyId: string;
  /**
   *
   * @type {string}
   * @memberof MileageSourceSummary
   */
  type: MileageSourceSummaryTypeEnum;
  /**
   *
   * @type {string}
   * @memberof MileageSourceSummary
   */
  tsp?: string;
  /**
   *
   * @type {string}
   * @memberof MileageSourceSummary
   */
  connectionHandleId?: string;
}

/**
 * @export
 * @enum {string}
 */
export enum MileageSourceSummaryTypeEnum {
  Telematics = 'Telematics',
  SelfReporter = 'SelfReporter',
}

/**
 *
 * @export
 * @enum {string}
 */

export enum MileageSourceType {
  Ifta = 'IFTA',
  IftaDisabled = 'IFTA Disabled',
  EldExempted = 'ELD Exempted',
  NonCompliantTsp = 'Non Compliant TSP',
  OwnerOperators = 'Owner Operators',
  MinimumMileageGuarantee = 'Minimum Mileage Guarantee',
}

/**
 *
 * @export
 * @enum {string}
 */

export enum MileageType {
  MileageTypeProjected = 'MileageTypeProjected',
  MileageTypeSelfReported = 'MileageTypeSelfReported',
  MileageTypeDs = 'MileageTypeDS',
  MileageTypeIfta = 'MileageTypeIFTA',
  MileageTypeBillable = 'MileageTypeBillable',
  MileageTypeBilled = 'MileageTypeBilled',
}

/**
 *
 * @export
 * @interface MonthlyArtifactItem
 */
export interface MonthlyArtifactItem {
  /**
   *
   * @type {number}
   * @memberof MonthlyArtifactItem
   */
  year: number;
  /**
   *
   * @type {number}
   * @memberof MonthlyArtifactItem
   */
  month: number;
  /**
   *
   * @type {string}
   * @memberof MonthlyArtifactItem
   */
  pipelineRunId: string;
  /**
   * A unique identifier for a job run. This is used to track the status of a job run. The format is the Job\'s ID followed by a double colon (::) and a sequence number.
   * @type {string}
   * @memberof MonthlyArtifactItem
   */
  jobRunId: string;
  /**
   *
   * @type {MonthlyArtifactItemReportsRefs}
   * @memberof MonthlyArtifactItem
   */
  reports: MonthlyArtifactItemReportsRefs;
  /**
   *
   * @type {MonthlyArtifactStats}
   * @memberof MonthlyArtifactItem
   */
  stats: MonthlyArtifactStats;
}
/**
 *
 * @export
 * @interface MonthlyArtifactItemReportsRefs
 */
export interface MonthlyArtifactItemReportsRefs {
  /**
   *
   * @type {MonthlyReportRef}
   * @memberof MonthlyArtifactItemReportsRefs
   */
  originalPolicyBillsRef?: MonthlyReportRef;
  /**
   *
   * @type {MonthlyReportRef}
   * @memberof MonthlyArtifactItemReportsRefs
   */
  overriddenPolicyBillsRef?: MonthlyReportRef;
  /**
   *
   * @type {MonthlyReportRef}
   * @memberof MonthlyArtifactItemReportsRefs
   */
  originalFleetActivityZipRef?: MonthlyReportRef;
  /**
   *
   * @type {MonthlyReportRef}
   * @memberof MonthlyArtifactItemReportsRefs
   */
  overriddenFleetActivityZipRef?: MonthlyReportRef;
}
/**
 *
 * @export
 * @interface MonthlyArtifactStats
 */
export interface MonthlyArtifactStats {
  /**
   *
   * @type {number}
   * @memberof MonthlyArtifactStats
   */
  totalPolicies: number;
  /**
   *
   * @type {number}
   * @memberof MonthlyArtifactStats
   */
  mileageRecordsPulled: number;
  /**
   *
   * @type {number}
   * @memberof MonthlyArtifactStats
   */
  fleetActivityReportsCreated: number;
}
/**
 *
 * @export
 * @interface MonthlyReportRef
 */
export interface MonthlyReportRef {
  /**
   *
   * @type {string}
   * @memberof MonthlyReportRef
   */
  reportId: string;
  /**
   * the signed link to download the file.
   * @type {string}
   * @memberof MonthlyReportRef
   */
  url: string;
}
/**
 *
 * @export
 * @interface PatchBillingInfoRequest
 */
export interface PatchBillingInfoRequest {
  /**
   *
   * @type {PaymentMethod}
   * @memberof PatchBillingInfoRequest
   */
  paymentMethod?: PaymentMethod;
  /**
   * indicates if the deferred deposit option should be enabled
   * @type {boolean}
   * @memberof PatchBillingInfoRequest
   */
  deferredDeposit?: boolean;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum PaymentMethod {
  PaidInFull = 'PaidInFull',
  MonthlyReporter = 'MonthlyReporter',
}

/**
 *
 * @export
 * @interface PipelineRun
 */
export interface PipelineRun {
  /**
   * A unique identifier for a job run. This is used to track the status of a job run. The format is the Job\'s ID followed by a double colon (::) and a sequence number.
   * @type {string}
   * @memberof PipelineRun
   */
  jobRunId: string;
  /**
   * The user\'s email who triggered the pipeline run.
   * @type {string}
   * @memberof PipelineRun
   */
  triggeredBy: string;
  /**
   * The billing start date range for the pipeline run.
   * @type {string}
   * @memberof PipelineRun
   */
  startDate: string;
  /**
   * The billing end date range for the pipeline run.
   * @type {string}
   * @memberof PipelineRun
   */
  endDate: string;
  /**
   * The date-time when the pipeline run was executed.
   * @type {string}
   * @memberof PipelineRun
   */
  executionTime: string;
  /**
   *
   * @type {Array<string>}
   * @memberof PipelineRun
   */
  billingPolicyIds: Array<string>;
  /**
   *
   * @type {Array<PipelineRunReport>}
   * @memberof PipelineRun
   */
  reports: Array<PipelineRunReport>;
}
/**
 *
 * @export
 * @interface PipelineRunJobRunIdStatus
 */
export interface PipelineRunJobRunIdStatus {
  /**
   * A unique identifier for a job run. This is used to track the status of a job run. The format is the Job\'s ID followed by a double colon (::) and a sequence number.
   * @type {string}
   * @memberof PipelineRunJobRunIdStatus
   */
  jobRunId: string;
  /**
   *
   * @type {JobRunStatus}
   * @memberof PipelineRunJobRunIdStatus
   */
  status: JobRunStatus;
}
/**
 *
 * @export
 * @interface PipelineRunReport
 */
export interface PipelineRunReport {
  /**
   *
   * @type {string}
   * @memberof PipelineRunReport
   */
  id: string;
  /**
   * the signed link to download the file.
   * @type {string}
   * @memberof PipelineRunReport
   */
  url: string;
  /**
   *
   * @type {string}
   * @memberof PipelineRunReport
   */
  type: PipelineRunReportTypeEnum;
}

/**
 * @export
 * @enum {string}
 */
export enum PipelineRunReportTypeEnum {
  MonthlyMileagesZip = 'MonthlyMileagesZip',
  MonthlyPolicyBills = 'MonthlyPolicyBills',
}

/**
 *
 * @export
 * @enum {string}
 */

export enum PolicyActivityStatus {
  Active = 'Active',
  Inactive = 'Inactive',
}

/**
 *
 * @export
 * @enum {string}
 */

export enum PolicyStatus {
  Active = 'Active',
  ExpiringSoon = 'Expiring Soon',
  Expired = 'Expired',
  Cancelled = 'Cancelled',
  Upcoming = 'Upcoming',
  CancellationPending = 'Cancellation Pending',
}

/**
 *
 * @export
 * @interface PolicySummary
 */
export interface PolicySummary {
  /**
   *
   * @type {string}
   * @memberof PolicySummary
   */
  ID: string;
  /**
   *
   * @type {string}
   * @memberof PolicySummary
   */
  coverageName: string;
  /**
   *
   * @type {string}
   * @memberof PolicySummary
   */
  effectiveDate: string;
  /**
   *
   * @type {string}
   * @memberof PolicySummary
   */
  effectiveDateTo: string;
  /**
   *
   * @type {string}
   * @memberof PolicySummary
   */
  policyNumber: string;
  /**
   *
   * @type {PolicyStatus}
   * @memberof PolicySummary
   */
  status: PolicyStatus;
}
/**
 *
 * @export
 * @interface Report
 */
export interface Report {
  /**
   *
   * @type {string}
   * @memberof Report
   */
  id: string;
  /**
   *
   * @type {ReportType}
   * @memberof Report
   */
  type: ReportType;
  /**
   *
   * @type {string}
   * @memberof Report
   */
  handleId: string;
  /**
   *
   * @type {string}
   * @memberof Report
   */
  startDate: string;
  /**
   *
   * @type {string}
   * @memberof Report
   */
  endDate: string;
  /**
   *
   * @type {string}
   * @memberof Report
   */
  jobRunId?: string;
  /**
   *
   * @type {string}
   * @memberof Report
   */
  policySetId?: string;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum ReportType {
  ReportTypeMonthlyInvoicingOutput = 'ReportTypeMonthlyInvoicingOutput',
  ReportTypeAccurateMonthlyInvoicingOutput = 'ReportTypeAccurateMonthlyInvoicingOutput',
  ReportTypeMonthlyMileage = 'ReportTypeMonthlyMileage',
  ReportTypeMonthlyMileagesZip = 'ReportTypeMonthlyMileagesZip',
  ReportTypeActualBillingInsuredValueDetail = 'ReportTypeActualBillingInsuredValueDetail',
  ReportTypeExpectedBillingInsuredValueDetail = 'ReportTypeExpectedBillingInsuredValueDetail',
}

/**
 *
 * @export
 * @interface SelfReportMileageRequest
 */
export interface SelfReportMileageRequest {
  /**
   *
   * @type {string}
   * @memberof SelfReportMileageRequest
   */
  policyNumber: string;
  /**
   *
   * @type {string}
   * @memberof SelfReportMileageRequest
   */
  billingPeriodStartDate: string;
  /**
   *
   * @type {string}
   * @memberof SelfReportMileageRequest
   */
  billingPeriodEndDate: string;
  /**
   * The total amount of miles traveled during the month. Ignored if mileage per VIN is given.
   * @type {number}
   * @memberof SelfReportMileageRequest
   */
  totalMileage?: number;
  /**
   * The miles traveled by each separate VIN. Takes precedence over totalMileage.
   * @type {Array<MileagePerVIN>}
   * @memberof SelfReportMileageRequest
   */
  mileagePerVIN?: Array<MileagePerVIN>;
  /**
   * If true, mileage is considered projected. Else, it\'s considered self reported
   * @type {boolean}
   * @memberof SelfReportMileageRequest
   */
  isProjected: boolean;
}
/**
 *
 * @export
 * @interface SelfReporterMileageIngestionFormItem
 */
export interface SelfReporterMileageIngestionFormItem {
  /**
   *
   * @type {string}
   * @memberof SelfReporterMileageIngestionFormItem
   */
  policyNumber: string;
  /**
   *
   * @type {string}
   * @memberof SelfReporterMileageIngestionFormItem
   */
  insuredName: string;
  /**
   *
   * @type {number}
   * @memberof SelfReporterMileageIngestionFormItem
   */
  dotNumber: number;
  /**
   *
   * @type {string}
   * @memberof SelfReporterMileageIngestionFormItem
   */
  sourceType: SelfReporterMileageIngestionFormItemSourceTypeEnum;
  /**
   *
   * @type {number}
   * @memberof SelfReporterMileageIngestionFormItem
   */
  projectedMileageForMonth: number;
  /**
   * Time when the mileage record was ingested
   * @type {string}
   * @memberof SelfReporterMileageIngestionFormItem
   */
  submittedAt?: string;
  /**
   *
   * @type {number}
   * @memberof SelfReporterMileageIngestionFormItem
   */
  initialPowerUnits: number;
  /**
   *
   * @type {number}
   * @memberof SelfReporterMileageIngestionFormItem
   */
  totalMileage: number;
  /**
   *
   * @type {MileageType}
   * @memberof SelfReporterMileageIngestionFormItem
   */
  totalMileageType?: MileageType;
  /**
   *
   * @type {Array<MileagePerVIN>}
   * @memberof SelfReporterMileageIngestionFormItem
   */
  mileagePerVIN: Array<MileagePerVIN>;
}

/**
 * @export
 * @enum {string}
 */
export enum SelfReporterMileageIngestionFormItemSourceTypeEnum {
  SelfReporter = 'SelfReporter',
  PartialSelfReporter = 'PartialSelfReporter',
  MinimumMileageGuarantee = 'MinimumMileageGuarantee',
}

/**
 *
 * @export
 * @interface SetStaticParamRequest
 */
export interface SetStaticParamRequest {
  /**
   *
   * @type {string}
   * @memberof SetStaticParamRequest
   */
  policyNumber: string;
  /**
   *
   * @type {StaticParamType}
   * @memberof SetStaticParamRequest
   */
  type: StaticParamType;
  /**
   *
   * @type {number}
   * @memberof SetStaticParamRequest
   */
  value: number;
  /**
   *
   * @type {string}
   * @memberof SetStaticParamRequest
   */
  validFrom: string;
}
/**
 *
 * @export
 * @interface SetTrackedVehicleScheduleRequest
 */
export interface SetTrackedVehicleScheduleRequest {
  /**
   * id consisting of policy identifier and issuance year
   * @type {string}
   * @memberof SetTrackedVehicleScheduleRequest
   */
  billingPolicyId: string;
  /**
   * is an array of the only vehicles that will be tracked for the referenced policy
   * @type {Array<TrackedVehicleToSet>}
   * @memberof SetTrackedVehicleScheduleRequest
   */
  trackedVehicles: Array<TrackedVehicleToSet>;
  /**
   *
   * @type {number}
   * @memberof SetTrackedVehicleScheduleRequest
   */
  changeEffectiveYear: number;
  /**
   *
   * @type {number}
   * @memberof SetTrackedVehicleScheduleRequest
   */
  changeEffectiveMonth: number;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum StaticParamType {
  AlRate = 'ALRate',
  ApdRate = 'APDRate',
  MtcRate = 'MTCRate',
  GlPremium = 'GLPremium',
  AgencyCommissionRate = 'AgencyCommissionRate',
  Tiv = 'TIV',
}

/**
 *
 * @export
 * @interface TrackedVehicle
 */
export interface TrackedVehicle {
  /**
   *
   * @type {string}
   * @memberof TrackedVehicle
   */
  id: string;
  /**
   *
   * @type {string}
   * @memberof TrackedVehicle
   */
  billingPolicyId: string;
  /**
   *
   * @type {string}
   * @memberof TrackedVehicle
   */
  vin: string;
  /**
   *
   * @type {TrackedVehicleType}
   * @memberof TrackedVehicle
   */
  type?: TrackedVehicleType;
  /**
   *
   * @type {number}
   * @memberof TrackedVehicle
   */
  insuredValue?: number;
  /**
   * is an array of sources where we\'ve seen the vehicle (e.g. equipment-list)
   * @type {Array<TrackedVehicleSource>}
   * @memberof TrackedVehicle
   */
  sources: Array<TrackedVehicleSource>;
  /**
   *
   * @type {boolean}
   * @memberof TrackedVehicle
   */
  isTrackedForAPD: boolean;
  /**
   *
   * @type {string}
   * @memberof TrackedVehicle
   */
  activeSince: string;
  /**
   *
   * @type {string}
   * @memberof TrackedVehicle
   */
  lastSeen: string;
  /**
   *
   * @type {TrackedVehicleEffectiveFrom}
   * @memberof TrackedVehicle
   */
  effectiveFrom: TrackedVehicleEffectiveFrom;
  /**
   *
   * @type {TrackedVehicleVinProblem}
   * @memberof TrackedVehicle
   */
  vinProblem?: TrackedVehicleVinProblem;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum TrackedVehicleEffectiveFrom {
  FirstSeen = 'FirstSeen',
  Save = 'Save',
}

/**
 *
 * @export
 * @enum {string}
 */

export enum TrackedVehicleSource {
  EquipmentList = 'EquipmentList',
  Ifta = 'IFTA',
  Manual = 'Manual',
  Disputed = 'Disputed',
}

/**
 *
 * @export
 * @interface TrackedVehicleToDiff
 */
export interface TrackedVehicleToDiff {
  /**
   *
   * @type {string}
   * @memberof TrackedVehicleToDiff
   */
  vin: string;
  /**
   *
   * @type {number}
   * @memberof TrackedVehicleToDiff
   */
  insuredValue: number;
  /**
   *
   * @type {boolean}
   * @memberof TrackedVehicleToDiff
   */
  covered: boolean;
}
/**
 *
 * @export
 * @interface TrackedVehicleToSet
 */
export interface TrackedVehicleToSet {
  /**
   *
   * @type {string}
   * @memberof TrackedVehicleToSet
   */
  vin: string;
  /**
   *
   * @type {number}
   * @memberof TrackedVehicleToSet
   */
  insuredValue: number;
  /**
   *
   * @type {boolean}
   * @memberof TrackedVehicleToSet
   */
  covered: boolean;
  /**
   *
   * @type {string}
   * @memberof TrackedVehicleToSet
   */
  dateModified: string;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum TrackedVehicleType {
  Incomplete = 'Incomplete',
  Motorcycle = 'Motorcycle',
  Trailer = 'Trailer',
  Truck = 'Truck',
  Nil = 'Nil',
  PassengerCar = 'PassengerCar',
  MultipurposePassengerVehicle = 'MultipurposePassengerVehicle',
  Bus = 'Bus',
}

/**
 *
 * @export
 * @interface TrackedVehicleVinProblem
 */
export interface TrackedVehicleVinProblem {
  /**
   *
   * @type {boolean}
   * @memberof TrackedVehicleVinProblem
   */
  isResolved: boolean;
  /**
   *
   * @type {boolean}
   * @memberof TrackedVehicleVinProblem
   */
  isReviewed: boolean;
  /**
   *
   * @type {string}
   * @memberof TrackedVehicleVinProblem
   */
  error?: string;
}
/**
 *
 * @export
 * @interface TransitionMileageSourceOverrideStatusRequest
 */
export interface TransitionMileageSourceOverrideStatusRequest {
  /**
   *
   * @type {MileageSourceOverrideStatus}
   * @memberof TransitionMileageSourceOverrideStatusRequest
   */
  status: MileageSourceOverrideStatus;
  /**
   *
   * @type {MileageSourceOverrideStatusReason}
   * @memberof TransitionMileageSourceOverrideStatusRequest
   */
  statusReason?: MileageSourceOverrideStatusReason;
}
/**
 *
 * @export
 * @interface TransitionMileageSourceOverrideStatusResponse
 */
export interface TransitionMileageSourceOverrideStatusResponse {
  /**
   *
   * @type {string}
   * @memberof TransitionMileageSourceOverrideStatusResponse
   */
  id: string;
}
/**
 *
 * @export
 * @interface TriggerBillingPipelineRunRequest
 */
export interface TriggerBillingPipelineRunRequest {
  /**
   *
   * @type {string}
   * @memberof TriggerBillingPipelineRunRequest
   */
  startDate: string;
  /**
   *
   * @type {string}
   * @memberof TriggerBillingPipelineRunRequest
   */
  endDate: string;
  /**
   *
   * @type {boolean}
   * @memberof TriggerBillingPipelineRunRequest
   */
  isDryRun: boolean;
}
/**
 *
 * @export
 * @interface TriggerBillingPipelineRunResponse
 */
export interface TriggerBillingPipelineRunResponse {
  /**
   * A unique identifier for a job run. This is used to track the status of a job run. The format is the Job\'s ID followed by a double colon (::) and a sequence number.
   * @type {string}
   * @memberof TriggerBillingPipelineRunResponse
   */
  jobRunId?: string;
}
/**
 * Triggers the billing disputes pipeline job to amend the consumption of the specified pipeline run.
 * @export
 * @interface TriggerDisputesPipelineRequest
 */
export interface TriggerDisputesPipelineRequest {
  /**
   * The pipeline run ID whose consumption is being disputed and needs to be amended.
   * @type {string}
   * @memberof TriggerDisputesPipelineRequest
   */
  disputedPipelineRunId: string;
}
/**
 *
 * @export
 * @interface TriggerDisputesPipelineResponse
 */
export interface TriggerDisputesPipelineResponse {
  /**
   * The pipeline run ID whose consumption is being disputed and needs to be amended.
   * @type {string}
   * @memberof TriggerDisputesPipelineResponse
   */
  jobRunId: string;
}
/**
 *
 * @export
 * @interface UpsertMileageSourcesOverridesItem
 */
export interface UpsertMileageSourcesOverridesItem {
  /**
   *
   * @type {string}
   * @memberof UpsertMileageSourcesOverridesItem
   */
  id?: string;
  /**
   *
   * @type {MileageSourceOverrideInputSource}
   * @memberof UpsertMileageSourcesOverridesItem
   */
  source: MileageSourceOverrideInputSource;
  /**
   *
   * @type {MileageSourceOverrideInputType}
   * @memberof UpsertMileageSourcesOverridesItem
   */
  type?: MileageSourceOverrideInputType;
  /**
   *
   * @type {string}
   * @memberof UpsertMileageSourcesOverridesItem
   */
  policyIdentifier: string;
  /**
   *
   * @type {number}
   * @memberof UpsertMileageSourcesOverridesItem
   */
  policyIssuanceYear: number;
  /**
   *
   * @type {string}
   * @memberof UpsertMileageSourcesOverridesItem
   */
  TSP?: string;
  /**
   *
   * @type {string}
   * @memberof UpsertMileageSourcesOverridesItem
   */
  connectionHandleId?: string;
  /**
   * VehicleTags allow us to specify if we need to consider only a subset of the VINs from a telematics connection for Shared TSPs. Tags are not VINs.
   * @type {Array<string>}
   * @memberof UpsertMileageSourcesOverridesItem
   */
  vehicleTags?: Array<string>;
  /**
   * ExcludedVINs allow us to specify if we need to exclude a subset of VINs from a telematics connection.
   * @type {Array<string>}
   * @memberof UpsertMileageSourcesOverridesItem
   */
  excludedVINs?: Array<string>;
  /**
   *
   * @type {MileageSourceOverrideStatus}
   * @memberof UpsertMileageSourcesOverridesItem
   */
  initialStatus?: MileageSourceOverrideStatus;
}
/**
 *
 * @export
 * @interface UpsertMileageSourcesOverridesRequest
 */
export interface UpsertMileageSourcesOverridesRequest {
  /**
   *
   * @type {Array<UpsertMileageSourcesOverridesItem>}
   * @memberof UpsertMileageSourcesOverridesRequest
   */
  mileageSourcesOverrides: Array<UpsertMileageSourcesOverridesItem>;
}
/**
 *
 * @export
 * @interface UpsertMileageSourcesOverridesResponse
 */
export interface UpsertMileageSourcesOverridesResponse {
  /**
   *
   * @type {Array<string>}
   * @memberof UpsertMileageSourcesOverridesResponse
   */
  ids: Array<string>;
}

/**
 * BillingApi - axios parameter creator
 * @export
 */
export const BillingApiAxiosParamCreator = function (
  configuration?: Configuration,
) {
  return {
    /**
     * Calculates the number of vehicles that would be removed from coverage, as well as how many would be added to coverage, given the provided schedule. It doesn\'t persist anything in the DB.
     * @param {CalculateDiffTrackedVehicleScheduleRequest} calculateDiffTrackedVehicleScheduleRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    calculateDiffTrackedVehicleSchedule: async (
      calculateDiffTrackedVehicleScheduleRequest: CalculateDiffTrackedVehicleScheduleRequest,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'calculateDiffTrackedVehicleScheduleRequest' is not null or undefined
      assertParamExists(
        'calculateDiffTrackedVehicleSchedule',
        'calculateDiffTrackedVehicleScheduleRequest',
        calculateDiffTrackedVehicleScheduleRequest,
      );
      const localVarPath = `/billing/tracked-vehicles/diff-full-schedule`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        calculateDiffTrackedVehicleScheduleRequest,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Create a tracked vehicle
     * @param {CreateTrackedVehicleRequest} createTrackedVehicleRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    createTrackedVehicle: async (
      createTrackedVehicleRequest: CreateTrackedVehicleRequest,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'createTrackedVehicleRequest' is not null or undefined
      assertParamExists(
        'createTrackedVehicle',
        'createTrackedVehicleRequest',
        createTrackedVehicleRequest,
      );
      const localVarPath = `/billing/tracked-vehicles`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createTrackedVehicleRequest,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Dispute the policy\'s consumption for the month. For now, only mileage will be supported.
     * @param {DisputeConsumptionRequest} disputeConsumptionRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    disputeConsumption: async (
      disputeConsumptionRequest: DisputeConsumptionRequest,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'disputeConsumptionRequest' is not null or undefined
      assertParamExists(
        'disputeConsumption',
        'disputeConsumptionRequest',
        disputeConsumptionRequest,
      );
      const localVarPath = `/billing/disputes`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        disputeConsumptionRequest,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Return information about the Billing.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getBillingHealth: async (
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      const localVarPath = `/billing/health`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Return billing info of all DOTs
     * @param {MileageSource} [mileageSource]
     * @param {PolicyActivityStatus} [activityStatus]
     * @param {BillingFrequency} [frequency]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getBillingInfoForAllDOTs: async (
      mileageSource?: MileageSource,
      activityStatus?: PolicyActivityStatus,
      frequency?: BillingFrequency,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      const localVarPath = `/billing/dot_info`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (mileageSource !== undefined) {
        localVarQueryParameter['mileageSource'] = mileageSource;
      }

      if (activityStatus !== undefined) {
        localVarQueryParameter['activityStatus'] = activityStatus;
      }

      if (frequency !== undefined) {
        localVarQueryParameter['frequency'] = frequency;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Returns billing info for a policy identifier + issuance year combination
     * @param {string} policyIdentifier
     * @param {number} issuanceYear
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getBillingInfoForPolicyIdentifier: async (
      policyIdentifier: string,
      issuanceYear: number,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'policyIdentifier' is not null or undefined
      assertParamExists(
        'getBillingInfoForPolicyIdentifier',
        'policyIdentifier',
        policyIdentifier,
      );
      // verify required parameter 'issuanceYear' is not null or undefined
      assertParamExists(
        'getBillingInfoForPolicyIdentifier',
        'issuanceYear',
        issuanceYear,
      );
      const localVarPath = `/billing/policies/{policyIdentifier}`.replace(
        `{${'policyIdentifier'}}`,
        encodeURIComponent(String(policyIdentifier)),
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (issuanceYear !== undefined) {
        localVarQueryParameter['issuanceYear'] = issuanceYear;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Retrieves a list of the last 25 billing pipeline runs ordered from recently created.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getBillingPipelineRuns: async (
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      const localVarPath = `/billing/pipeline-runs`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Create a temporary public URL to download a billing report file.
     * @param {string} fileHandle
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getBillingReportFileLink: async (
      fileHandle: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'fileHandle' is not null or undefined
      assertParamExists('getBillingReportFileLink', 'fileHandle', fileHandle);
      const localVarPath = `/billing/report/file-link`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (fileHandle !== undefined) {
        localVarQueryParameter['fileHandle'] = fileHandle;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Returns the inputs required to dispute the policy\'s consumption for the specified month of year.
     * @param {string} billingPeriodStartDate
     * @param {string} billingPeriodEndDate
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getDisputeConsumptionForm: async (
      billingPeriodStartDate: string,
      billingPeriodEndDate: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'billingPeriodStartDate' is not null or undefined
      assertParamExists(
        'getDisputeConsumptionForm',
        'billingPeriodStartDate',
        billingPeriodStartDate,
      );
      // verify required parameter 'billingPeriodEndDate' is not null or undefined
      assertParamExists(
        'getDisputeConsumptionForm',
        'billingPeriodEndDate',
        billingPeriodEndDate,
      );
      const localVarPath = `/billing/disputes`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (billingPeriodStartDate !== undefined) {
        localVarQueryParameter['billingPeriodStartDate'] =
          (billingPeriodStartDate as any) instanceof Date
            ? (billingPeriodStartDate as any).toISOString().substr(0, 10)
            : billingPeriodStartDate;
      }

      if (billingPeriodEndDate !== undefined) {
        localVarQueryParameter['billingPeriodEndDate'] =
          (billingPeriodEndDate as any) instanceof Date
            ? (billingPeriodEndDate as any).toISOString().substr(0, 10)
            : billingPeriodEndDate;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get the list of insureds considered for the pipelines falling inside the given date range.
     * @param {string} billingPeriodStartDate
     * @param {string} billingPeriodEndDate
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getInsuredsForPipelineRun: async (
      billingPeriodStartDate: string,
      billingPeriodEndDate: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'billingPeriodStartDate' is not null or undefined
      assertParamExists(
        'getInsuredsForPipelineRun',
        'billingPeriodStartDate',
        billingPeriodStartDate,
      );
      // verify required parameter 'billingPeriodEndDate' is not null or undefined
      assertParamExists(
        'getInsuredsForPipelineRun',
        'billingPeriodEndDate',
        billingPeriodEndDate,
      );
      const localVarPath = `/billing/pipeline-runs/insureds`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (billingPeriodStartDate !== undefined) {
        localVarQueryParameter['billingPeriodStartDate'] =
          (billingPeriodStartDate as any) instanceof Date
            ? (billingPeriodStartDate as any).toISOString().substr(0, 10)
            : billingPeriodStartDate;
      }

      if (billingPeriodEndDate !== undefined) {
        localVarQueryParameter['billingPeriodEndDate'] =
          (billingPeriodEndDate as any) instanceof Date
            ? (billingPeriodEndDate as any).toISOString().substr(0, 10)
            : billingPeriodEndDate;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get the status of the jobs for the given job run ids
     * @param {Array<string>} ids
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getJobRunIdStatuses: async (
      ids: Array<string>,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'ids' is not null or undefined
      assertParamExists('getJobRunIdStatuses', 'ids', ids);
      const localVarPath = `/billing/pipeline-runs/job-runs`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (ids) {
        localVarQueryParameter['ids'] = ids.join(COLLECTION_FORMATS.csv);
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Returns mileage sources for a policy identifier + issuance year combination
     * @param {string} policyIdentifier
     * @param {number} issuanceYear
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getMileageSourcesForPolicyIdentifier: async (
      policyIdentifier: string,
      issuanceYear: number,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'policyIdentifier' is not null or undefined
      assertParamExists(
        'getMileageSourcesForPolicyIdentifier',
        'policyIdentifier',
        policyIdentifier,
      );
      // verify required parameter 'issuanceYear' is not null or undefined
      assertParamExists(
        'getMileageSourcesForPolicyIdentifier',
        'issuanceYear',
        issuanceYear,
      );
      const localVarPath =
        `/billing/policies/{policyIdentifier}/mileage_sources`.replace(
          `{${'policyIdentifier'}}`,
          encodeURIComponent(String(policyIdentifier)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (issuanceYear !== undefined) {
        localVarQueryParameter['issuanceYear'] = issuanceYear;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Returns, for each month, the final artifacts produced by billing product (Fleet Activity Reports zip and Bills CSV, together with whether or not there is any ongoing pipeline executing).
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getMonthlyArtifacts: async (
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      const localVarPath = `/billing/monthly-artifacts`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Fetch monthly billing info for policy identifier and issuance year
     * @param {string} policyIdentifier
     * @param {number} issuanceYear
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getMonthlyBillingInfo: async (
      policyIdentifier: string,
      issuanceYear: number,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'policyIdentifier' is not null or undefined
      assertParamExists(
        'getMonthlyBillingInfo',
        'policyIdentifier',
        policyIdentifier,
      );
      // verify required parameter 'issuanceYear' is not null or undefined
      assertParamExists('getMonthlyBillingInfo', 'issuanceYear', issuanceYear);
      const localVarPath = `/billing/monthly-info`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (policyIdentifier !== undefined) {
        localVarQueryParameter['policyIdentifier'] = policyIdentifier;
      }

      if (issuanceYear !== undefined) {
        localVarQueryParameter['issuanceYear'] = issuanceYear;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get billing report entities according to the input filters.
     * @param {ReportType} type
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getReports: async (
      type: ReportType,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'type' is not null or undefined
      assertParamExists('getReports', 'type', type);
      const localVarPath = `/billing/reports`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (type !== undefined) {
        localVarQueryParameter['type'] = type;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Returns the inputs required to manually ingest mileage
     * @param {string} billingPeriodStartDate
     * @param {string} billingPeriodEndDate
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getSelfReportersMileageIngestionForm: async (
      billingPeriodStartDate: string,
      billingPeriodEndDate: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'billingPeriodStartDate' is not null or undefined
      assertParamExists(
        'getSelfReportersMileageIngestionForm',
        'billingPeriodStartDate',
        billingPeriodStartDate,
      );
      // verify required parameter 'billingPeriodEndDate' is not null or undefined
      assertParamExists(
        'getSelfReportersMileageIngestionForm',
        'billingPeriodEndDate',
        billingPeriodEndDate,
      );
      const localVarPath = `/billing/mileage/self-reporters-ingestion-form`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (billingPeriodStartDate !== undefined) {
        localVarQueryParameter['billingPeriodStartDate'] =
          (billingPeriodStartDate as any) instanceof Date
            ? (billingPeriodStartDate as any).toISOString().substr(0, 10)
            : billingPeriodStartDate;
      }

      if (billingPeriodEndDate !== undefined) {
        localVarQueryParameter['billingPeriodEndDate'] =
          (billingPeriodEndDate as any) instanceof Date
            ? (billingPeriodEndDate as any).toISOString().substr(0, 10)
            : billingPeriodEndDate;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get the list of tracked vehicles that satisfy the given filters, and that have been launched for IVM.
     * @param {string} [effectiveFromUpperLimit]
     * @param {Array<string>} [billingPolicyIds]
     * @param {boolean} [onlyInIVMExperiment] If true, only vehicles that have been launched for IVM will be returned.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getTrackedVehicles: async (
      effectiveFromUpperLimit?: string,
      billingPolicyIds?: Array<string>,
      onlyInIVMExperiment?: boolean,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      const localVarPath = `/billing/tracked-vehicles`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (effectiveFromUpperLimit !== undefined) {
        localVarQueryParameter['effectiveFromUpperLimit'] =
          (effectiveFromUpperLimit as any) instanceof Date
            ? (effectiveFromUpperLimit as any).toISOString().substr(0, 10)
            : effectiveFromUpperLimit;
      }

      if (billingPolicyIds) {
        localVarQueryParameter['billingPolicyIds'] = billingPolicyIds;
      }

      if (onlyInIVMExperiment !== undefined) {
        localVarQueryParameter['onlyInIVMExperiment'] = onlyInIVMExperiment;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get the list of vehicles with their insured value, and whether they\'re tracked for APD coverage
     * @param {string} billingPolicyId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getTrackedVehiclesForPolicyId: async (
      billingPolicyId: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'billingPolicyId' is not null or undefined
      assertParamExists(
        'getTrackedVehiclesForPolicyId',
        'billingPolicyId',
        billingPolicyId,
      );
      const localVarPath =
        `/billing/tracked-vehicles/{billingPolicyId}`.replace(
          `{${'billingPolicyId'}}`,
          encodeURIComponent(String(billingPolicyId)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Update billing info for a policy identifier + issuance year combination or application ID.
     * @param {PatchBillingInfoRequest} patchBillingInfoRequest
     * @param {string} [policyIdentifier]
     * @param {number} [issuanceYear]
     * @param {string} [applicationID]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    patchBillingInfo: async (
      patchBillingInfoRequest: PatchBillingInfoRequest,
      policyIdentifier?: string,
      issuanceYear?: number,
      applicationID?: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'patchBillingInfoRequest' is not null or undefined
      assertParamExists(
        'patchBillingInfo',
        'patchBillingInfoRequest',
        patchBillingInfoRequest,
      );
      const localVarPath = `/billing/policies/billing-info`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PATCH',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (policyIdentifier !== undefined) {
        localVarQueryParameter['policyIdentifier'] = policyIdentifier;
      }

      if (issuanceYear !== undefined) {
        localVarQueryParameter['issuanceYear'] = issuanceYear;
      }

      if (applicationID !== undefined) {
        localVarQueryParameter['applicationID'] = applicationID;
      }

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        patchBillingInfoRequest,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {SelfReportMileageRequest} selfReportMileageRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    selfReportMileage: async (
      selfReportMileageRequest: SelfReportMileageRequest,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'selfReportMileageRequest' is not null or undefined
      assertParamExists(
        'selfReportMileage',
        'selfReportMileageRequest',
        selfReportMileageRequest,
      );
      const localVarPath = `/billing/mileage/self-reported`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        selfReportMileageRequest,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {SetStaticParamRequest} setStaticParamRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    setStaticParam: async (
      setStaticParamRequest: SetStaticParamRequest,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'setStaticParamRequest' is not null or undefined
      assertParamExists(
        'setStaticParam',
        'setStaticParamRequest',
        setStaticParamRequest,
      );
      const localVarPath = `/billing/static-params`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        setStaticParamRequest,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Set the full tracked vehicles schedule for an account. Any vehicle not defined here will be marked as not covered. Useful for inbound schemas received from the insureds.
     * @param {SetTrackedVehicleScheduleRequest} setTrackedVehicleScheduleRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    setTrackedVehicleSchedule: async (
      setTrackedVehicleScheduleRequest: SetTrackedVehicleScheduleRequest,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'setTrackedVehicleScheduleRequest' is not null or undefined
      assertParamExists(
        'setTrackedVehicleSchedule',
        'setTrackedVehicleScheduleRequest',
        setTrackedVehicleScheduleRequest,
      );
      const localVarPath = `/billing/tracked-vehicles/set-full-schedule`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        setTrackedVehicleScheduleRequest,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Transition a mileage source override\'s status
     * @param {string} mileageSourceOverrideId
     * @param {TransitionMileageSourceOverrideStatusRequest} transitionMileageSourceOverrideStatusRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    transitionMileageSourceOverrideStatus: async (
      mileageSourceOverrideId: string,
      transitionMileageSourceOverrideStatusRequest: TransitionMileageSourceOverrideStatusRequest,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'mileageSourceOverrideId' is not null or undefined
      assertParamExists(
        'transitionMileageSourceOverrideStatus',
        'mileageSourceOverrideId',
        mileageSourceOverrideId,
      );
      // verify required parameter 'transitionMileageSourceOverrideStatusRequest' is not null or undefined
      assertParamExists(
        'transitionMileageSourceOverrideStatus',
        'transitionMileageSourceOverrideStatusRequest',
        transitionMileageSourceOverrideStatusRequest,
      );
      const localVarPath =
        `/billing/mileage_sources_overrides/{mileageSourceOverrideId}/status`.replace(
          `{${'mileageSourceOverrideId'}}`,
          encodeURIComponent(String(mileageSourceOverrideId)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PATCH',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        transitionMileageSourceOverrideStatusRequest,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Create a new billing pipeline run
     * @param {TriggerBillingPipelineRunRequest} triggerBillingPipelineRunRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    triggerBillingPipelineRun: async (
      triggerBillingPipelineRunRequest: TriggerBillingPipelineRunRequest,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'triggerBillingPipelineRunRequest' is not null or undefined
      assertParamExists(
        'triggerBillingPipelineRun',
        'triggerBillingPipelineRunRequest',
        triggerBillingPipelineRunRequest,
      );
      const localVarPath = `/billing/pipeline-runs`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        triggerBillingPipelineRunRequest,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Triggers the disputes pipeline job for the specified pipeline run ID.
     * @param {TriggerDisputesPipelineRequest} triggerDisputesPipelineRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    triggerDisputesPipeline: async (
      triggerDisputesPipelineRequest: TriggerDisputesPipelineRequest,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'triggerDisputesPipelineRequest' is not null or undefined
      assertParamExists(
        'triggerDisputesPipeline',
        'triggerDisputesPipelineRequest',
        triggerDisputesPipelineRequest,
      );
      const localVarPath = `/billing/disputes/trigger-pipeline`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        triggerDisputesPipelineRequest,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Creates or creates multiple mileage sources overrides
     * @param {UpsertMileageSourcesOverridesRequest} upsertMileageSourcesOverridesRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    upsertMileageSourcesOverrides: async (
      upsertMileageSourcesOverridesRequest: UpsertMileageSourcesOverridesRequest,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'upsertMileageSourcesOverridesRequest' is not null or undefined
      assertParamExists(
        'upsertMileageSourcesOverrides',
        'upsertMileageSourcesOverridesRequest',
        upsertMileageSourcesOverridesRequest,
      );
      const localVarPath = `/billing/mileage_sources_overrides`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        upsertMileageSourcesOverridesRequest,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * BillingApi - functional programming interface
 * @export
 */
export const BillingApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = BillingApiAxiosParamCreator(configuration);
  return {
    /**
     * Calculates the number of vehicles that would be removed from coverage, as well as how many would be added to coverage, given the provided schedule. It doesn\'t persist anything in the DB.
     * @param {CalculateDiffTrackedVehicleScheduleRequest} calculateDiffTrackedVehicleScheduleRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async calculateDiffTrackedVehicleSchedule(
      calculateDiffTrackedVehicleScheduleRequest: CalculateDiffTrackedVehicleScheduleRequest,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<CalculateDiffTrackedVehicleScheduleResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.calculateDiffTrackedVehicleSchedule(
          calculateDiffTrackedVehicleScheduleRequest,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Create a tracked vehicle
     * @param {CreateTrackedVehicleRequest} createTrackedVehicleRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async createTrackedVehicle(
      createTrackedVehicleRequest: CreateTrackedVehicleRequest,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.createTrackedVehicle(
          createTrackedVehicleRequest,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Dispute the policy\'s consumption for the month. For now, only mileage will be supported.
     * @param {DisputeConsumptionRequest} disputeConsumptionRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async disputeConsumption(
      disputeConsumptionRequest: DisputeConsumptionRequest,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.disputeConsumption(
          disputeConsumptionRequest,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Return information about the Billing.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getBillingHealth(
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<HealthResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getBillingHealth(options);
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Return billing info of all DOTs
     * @param {MileageSource} [mileageSource]
     * @param {PolicyActivityStatus} [activityStatus]
     * @param {BillingFrequency} [frequency]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getBillingInfoForAllDOTs(
      mileageSource?: MileageSource,
      activityStatus?: PolicyActivityStatus,
      frequency?: BillingFrequency,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<Array<GetBillingInfoResponse>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getBillingInfoForAllDOTs(
          mileageSource,
          activityStatus,
          frequency,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Returns billing info for a policy identifier + issuance year combination
     * @param {string} policyIdentifier
     * @param {number} issuanceYear
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getBillingInfoForPolicyIdentifier(
      policyIdentifier: string,
      issuanceYear: number,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<GetBillingInfoForPolicyIdentifierResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getBillingInfoForPolicyIdentifier(
          policyIdentifier,
          issuanceYear,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Retrieves a list of the last 25 billing pipeline runs ordered from recently created.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getBillingPipelineRuns(
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<Array<PipelineRun>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getBillingPipelineRuns(options);
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Create a temporary public URL to download a billing report file.
     * @param {string} fileHandle
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getBillingReportFileLink(
      fileHandle: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<DownloadFileLinkResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getBillingReportFileLink(
          fileHandle,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Returns the inputs required to dispute the policy\'s consumption for the specified month of year.
     * @param {string} billingPeriodStartDate
     * @param {string} billingPeriodEndDate
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getDisputeConsumptionForm(
      billingPeriodStartDate: string,
      billingPeriodEndDate: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<Array<DisputeConsumptionFormItem>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getDisputeConsumptionForm(
          billingPeriodStartDate,
          billingPeriodEndDate,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Get the list of insureds considered for the pipelines falling inside the given date range.
     * @param {string} billingPeriodStartDate
     * @param {string} billingPeriodEndDate
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getInsuredsForPipelineRun(
      billingPeriodStartDate: string,
      billingPeriodEndDate: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<Array<BillingInsuredSummary>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getInsuredsForPipelineRun(
          billingPeriodStartDate,
          billingPeriodEndDate,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Get the status of the jobs for the given job run ids
     * @param {Array<string>} ids
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getJobRunIdStatuses(
      ids: Array<string>,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<Array<PipelineRunJobRunIdStatus>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getJobRunIdStatuses(ids, options);
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Returns mileage sources for a policy identifier + issuance year combination
     * @param {string} policyIdentifier
     * @param {number} issuanceYear
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getMileageSourcesForPolicyIdentifier(
      policyIdentifier: string,
      issuanceYear: number,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<Array<MileageSourceDetails>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getMileageSourcesForPolicyIdentifier(
          policyIdentifier,
          issuanceYear,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Returns, for each month, the final artifacts produced by billing product (Fleet Activity Reports zip and Bills CSV, together with whether or not there is any ongoing pipeline executing).
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getMonthlyArtifacts(
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<Array<MonthlyArtifactItem>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getMonthlyArtifacts(options);
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Fetch monthly billing info for policy identifier and issuance year
     * @param {string} policyIdentifier
     * @param {number} issuanceYear
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getMonthlyBillingInfo(
      policyIdentifier: string,
      issuanceYear: number,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<GetMonthlyBillingResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getMonthlyBillingInfo(
          policyIdentifier,
          issuanceYear,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Get billing report entities according to the input filters.
     * @param {ReportType} type
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getReports(
      type: ReportType,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<Report>>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.getReports(
        type,
        options,
      );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Returns the inputs required to manually ingest mileage
     * @param {string} billingPeriodStartDate
     * @param {string} billingPeriodEndDate
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getSelfReportersMileageIngestionForm(
      billingPeriodStartDate: string,
      billingPeriodEndDate: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<Array<SelfReporterMileageIngestionFormItem>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getSelfReportersMileageIngestionForm(
          billingPeriodStartDate,
          billingPeriodEndDate,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Get the list of tracked vehicles that satisfy the given filters, and that have been launched for IVM.
     * @param {string} [effectiveFromUpperLimit]
     * @param {Array<string>} [billingPolicyIds]
     * @param {boolean} [onlyInIVMExperiment] If true, only vehicles that have been launched for IVM will be returned.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getTrackedVehicles(
      effectiveFromUpperLimit?: string,
      billingPolicyIds?: Array<string>,
      onlyInIVMExperiment?: boolean,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<Array<TrackedVehicle>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getTrackedVehicles(
          effectiveFromUpperLimit,
          billingPolicyIds,
          onlyInIVMExperiment,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Get the list of vehicles with their insured value, and whether they\'re tracked for APD coverage
     * @param {string} billingPolicyId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getTrackedVehiclesForPolicyId(
      billingPolicyId: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<Array<TrackedVehicle>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getTrackedVehiclesForPolicyId(
          billingPolicyId,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Update billing info for a policy identifier + issuance year combination or application ID.
     * @param {PatchBillingInfoRequest} patchBillingInfoRequest
     * @param {string} [policyIdentifier]
     * @param {number} [issuanceYear]
     * @param {string} [applicationID]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async patchBillingInfo(
      patchBillingInfoRequest: PatchBillingInfoRequest,
      policyIdentifier?: string,
      issuanceYear?: number,
      applicationID?: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.patchBillingInfo(
          patchBillingInfoRequest,
          policyIdentifier,
          issuanceYear,
          applicationID,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     *
     * @param {SelfReportMileageRequest} selfReportMileageRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async selfReportMileage(
      selfReportMileageRequest: SelfReportMileageRequest,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.selfReportMileage(
          selfReportMileageRequest,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     *
     * @param {SetStaticParamRequest} setStaticParamRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async setStaticParam(
      setStaticParamRequest: SetStaticParamRequest,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.setStaticParam(
        setStaticParamRequest,
        options,
      );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Set the full tracked vehicles schedule for an account. Any vehicle not defined here will be marked as not covered. Useful for inbound schemas received from the insureds.
     * @param {SetTrackedVehicleScheduleRequest} setTrackedVehicleScheduleRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async setTrackedVehicleSchedule(
      setTrackedVehicleScheduleRequest: SetTrackedVehicleScheduleRequest,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.setTrackedVehicleSchedule(
          setTrackedVehicleScheduleRequest,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Transition a mileage source override\'s status
     * @param {string} mileageSourceOverrideId
     * @param {TransitionMileageSourceOverrideStatusRequest} transitionMileageSourceOverrideStatusRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async transitionMileageSourceOverrideStatus(
      mileageSourceOverrideId: string,
      transitionMileageSourceOverrideStatusRequest: TransitionMileageSourceOverrideStatusRequest,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<TransitionMileageSourceOverrideStatusResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.transitionMileageSourceOverrideStatus(
          mileageSourceOverrideId,
          transitionMileageSourceOverrideStatusRequest,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Create a new billing pipeline run
     * @param {TriggerBillingPipelineRunRequest} triggerBillingPipelineRunRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async triggerBillingPipelineRun(
      triggerBillingPipelineRunRequest: TriggerBillingPipelineRunRequest,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<TriggerBillingPipelineRunResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.triggerBillingPipelineRun(
          triggerBillingPipelineRunRequest,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Triggers the disputes pipeline job for the specified pipeline run ID.
     * @param {TriggerDisputesPipelineRequest} triggerDisputesPipelineRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async triggerDisputesPipeline(
      triggerDisputesPipelineRequest: TriggerDisputesPipelineRequest,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<TriggerDisputesPipelineResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.triggerDisputesPipeline(
          triggerDisputesPipelineRequest,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Creates or creates multiple mileage sources overrides
     * @param {UpsertMileageSourcesOverridesRequest} upsertMileageSourcesOverridesRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async upsertMileageSourcesOverrides(
      upsertMileageSourcesOverridesRequest: UpsertMileageSourcesOverridesRequest,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<UpsertMileageSourcesOverridesResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.upsertMileageSourcesOverrides(
          upsertMileageSourcesOverridesRequest,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
  };
};

/**
 * BillingApi - factory interface
 * @export
 */
export const BillingApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance,
) {
  const localVarFp = BillingApiFp(configuration);
  return {
    /**
     * Calculates the number of vehicles that would be removed from coverage, as well as how many would be added to coverage, given the provided schedule. It doesn\'t persist anything in the DB.
     * @param {CalculateDiffTrackedVehicleScheduleRequest} calculateDiffTrackedVehicleScheduleRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    calculateDiffTrackedVehicleSchedule(
      calculateDiffTrackedVehicleScheduleRequest: CalculateDiffTrackedVehicleScheduleRequest,
      options?: any,
    ): AxiosPromise<CalculateDiffTrackedVehicleScheduleResponse> {
      return localVarFp
        .calculateDiffTrackedVehicleSchedule(
          calculateDiffTrackedVehicleScheduleRequest,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Create a tracked vehicle
     * @param {CreateTrackedVehicleRequest} createTrackedVehicleRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    createTrackedVehicle(
      createTrackedVehicleRequest: CreateTrackedVehicleRequest,
      options?: any,
    ): AxiosPromise<void> {
      return localVarFp
        .createTrackedVehicle(createTrackedVehicleRequest, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Dispute the policy\'s consumption for the month. For now, only mileage will be supported.
     * @param {DisputeConsumptionRequest} disputeConsumptionRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    disputeConsumption(
      disputeConsumptionRequest: DisputeConsumptionRequest,
      options?: any,
    ): AxiosPromise<void> {
      return localVarFp
        .disputeConsumption(disputeConsumptionRequest, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Return information about the Billing.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getBillingHealth(options?: any): AxiosPromise<HealthResponse> {
      return localVarFp
        .getBillingHealth(options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Return billing info of all DOTs
     * @param {MileageSource} [mileageSource]
     * @param {PolicyActivityStatus} [activityStatus]
     * @param {BillingFrequency} [frequency]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getBillingInfoForAllDOTs(
      mileageSource?: MileageSource,
      activityStatus?: PolicyActivityStatus,
      frequency?: BillingFrequency,
      options?: any,
    ): AxiosPromise<Array<GetBillingInfoResponse>> {
      return localVarFp
        .getBillingInfoForAllDOTs(
          mileageSource,
          activityStatus,
          frequency,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Returns billing info for a policy identifier + issuance year combination
     * @param {string} policyIdentifier
     * @param {number} issuanceYear
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getBillingInfoForPolicyIdentifier(
      policyIdentifier: string,
      issuanceYear: number,
      options?: any,
    ): AxiosPromise<GetBillingInfoForPolicyIdentifierResponse> {
      return localVarFp
        .getBillingInfoForPolicyIdentifier(
          policyIdentifier,
          issuanceYear,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Retrieves a list of the last 25 billing pipeline runs ordered from recently created.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getBillingPipelineRuns(options?: any): AxiosPromise<Array<PipelineRun>> {
      return localVarFp
        .getBillingPipelineRuns(options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Create a temporary public URL to download a billing report file.
     * @param {string} fileHandle
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getBillingReportFileLink(
      fileHandle: string,
      options?: any,
    ): AxiosPromise<DownloadFileLinkResponse> {
      return localVarFp
        .getBillingReportFileLink(fileHandle, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Returns the inputs required to dispute the policy\'s consumption for the specified month of year.
     * @param {string} billingPeriodStartDate
     * @param {string} billingPeriodEndDate
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getDisputeConsumptionForm(
      billingPeriodStartDate: string,
      billingPeriodEndDate: string,
      options?: any,
    ): AxiosPromise<Array<DisputeConsumptionFormItem>> {
      return localVarFp
        .getDisputeConsumptionForm(
          billingPeriodStartDate,
          billingPeriodEndDate,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Get the list of insureds considered for the pipelines falling inside the given date range.
     * @param {string} billingPeriodStartDate
     * @param {string} billingPeriodEndDate
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getInsuredsForPipelineRun(
      billingPeriodStartDate: string,
      billingPeriodEndDate: string,
      options?: any,
    ): AxiosPromise<Array<BillingInsuredSummary>> {
      return localVarFp
        .getInsuredsForPipelineRun(
          billingPeriodStartDate,
          billingPeriodEndDate,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Get the status of the jobs for the given job run ids
     * @param {Array<string>} ids
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getJobRunIdStatuses(
      ids: Array<string>,
      options?: any,
    ): AxiosPromise<Array<PipelineRunJobRunIdStatus>> {
      return localVarFp
        .getJobRunIdStatuses(ids, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Returns mileage sources for a policy identifier + issuance year combination
     * @param {string} policyIdentifier
     * @param {number} issuanceYear
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getMileageSourcesForPolicyIdentifier(
      policyIdentifier: string,
      issuanceYear: number,
      options?: any,
    ): AxiosPromise<Array<MileageSourceDetails>> {
      return localVarFp
        .getMileageSourcesForPolicyIdentifier(
          policyIdentifier,
          issuanceYear,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Returns, for each month, the final artifacts produced by billing product (Fleet Activity Reports zip and Bills CSV, together with whether or not there is any ongoing pipeline executing).
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getMonthlyArtifacts(
      options?: any,
    ): AxiosPromise<Array<MonthlyArtifactItem>> {
      return localVarFp
        .getMonthlyArtifacts(options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Fetch monthly billing info for policy identifier and issuance year
     * @param {string} policyIdentifier
     * @param {number} issuanceYear
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getMonthlyBillingInfo(
      policyIdentifier: string,
      issuanceYear: number,
      options?: any,
    ): AxiosPromise<GetMonthlyBillingResponse> {
      return localVarFp
        .getMonthlyBillingInfo(policyIdentifier, issuanceYear, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get billing report entities according to the input filters.
     * @param {ReportType} type
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getReports(type: ReportType, options?: any): AxiosPromise<Array<Report>> {
      return localVarFp
        .getReports(type, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Returns the inputs required to manually ingest mileage
     * @param {string} billingPeriodStartDate
     * @param {string} billingPeriodEndDate
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getSelfReportersMileageIngestionForm(
      billingPeriodStartDate: string,
      billingPeriodEndDate: string,
      options?: any,
    ): AxiosPromise<Array<SelfReporterMileageIngestionFormItem>> {
      return localVarFp
        .getSelfReportersMileageIngestionForm(
          billingPeriodStartDate,
          billingPeriodEndDate,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Get the list of tracked vehicles that satisfy the given filters, and that have been launched for IVM.
     * @param {string} [effectiveFromUpperLimit]
     * @param {Array<string>} [billingPolicyIds]
     * @param {boolean} [onlyInIVMExperiment] If true, only vehicles that have been launched for IVM will be returned.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getTrackedVehicles(
      effectiveFromUpperLimit?: string,
      billingPolicyIds?: Array<string>,
      onlyInIVMExperiment?: boolean,
      options?: any,
    ): AxiosPromise<Array<TrackedVehicle>> {
      return localVarFp
        .getTrackedVehicles(
          effectiveFromUpperLimit,
          billingPolicyIds,
          onlyInIVMExperiment,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Get the list of vehicles with their insured value, and whether they\'re tracked for APD coverage
     * @param {string} billingPolicyId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getTrackedVehiclesForPolicyId(
      billingPolicyId: string,
      options?: any,
    ): AxiosPromise<Array<TrackedVehicle>> {
      return localVarFp
        .getTrackedVehiclesForPolicyId(billingPolicyId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Update billing info for a policy identifier + issuance year combination or application ID.
     * @param {PatchBillingInfoRequest} patchBillingInfoRequest
     * @param {string} [policyIdentifier]
     * @param {number} [issuanceYear]
     * @param {string} [applicationID]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    patchBillingInfo(
      patchBillingInfoRequest: PatchBillingInfoRequest,
      policyIdentifier?: string,
      issuanceYear?: number,
      applicationID?: string,
      options?: any,
    ): AxiosPromise<void> {
      return localVarFp
        .patchBillingInfo(
          patchBillingInfoRequest,
          policyIdentifier,
          issuanceYear,
          applicationID,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {SelfReportMileageRequest} selfReportMileageRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    selfReportMileage(
      selfReportMileageRequest: SelfReportMileageRequest,
      options?: any,
    ): AxiosPromise<void> {
      return localVarFp
        .selfReportMileage(selfReportMileageRequest, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {SetStaticParamRequest} setStaticParamRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    setStaticParam(
      setStaticParamRequest: SetStaticParamRequest,
      options?: any,
    ): AxiosPromise<void> {
      return localVarFp
        .setStaticParam(setStaticParamRequest, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Set the full tracked vehicles schedule for an account. Any vehicle not defined here will be marked as not covered. Useful for inbound schemas received from the insureds.
     * @param {SetTrackedVehicleScheduleRequest} setTrackedVehicleScheduleRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    setTrackedVehicleSchedule(
      setTrackedVehicleScheduleRequest: SetTrackedVehicleScheduleRequest,
      options?: any,
    ): AxiosPromise<void> {
      return localVarFp
        .setTrackedVehicleSchedule(setTrackedVehicleScheduleRequest, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Transition a mileage source override\'s status
     * @param {string} mileageSourceOverrideId
     * @param {TransitionMileageSourceOverrideStatusRequest} transitionMileageSourceOverrideStatusRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    transitionMileageSourceOverrideStatus(
      mileageSourceOverrideId: string,
      transitionMileageSourceOverrideStatusRequest: TransitionMileageSourceOverrideStatusRequest,
      options?: any,
    ): AxiosPromise<TransitionMileageSourceOverrideStatusResponse> {
      return localVarFp
        .transitionMileageSourceOverrideStatus(
          mileageSourceOverrideId,
          transitionMileageSourceOverrideStatusRequest,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Create a new billing pipeline run
     * @param {TriggerBillingPipelineRunRequest} triggerBillingPipelineRunRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    triggerBillingPipelineRun(
      triggerBillingPipelineRunRequest: TriggerBillingPipelineRunRequest,
      options?: any,
    ): AxiosPromise<TriggerBillingPipelineRunResponse> {
      return localVarFp
        .triggerBillingPipelineRun(triggerBillingPipelineRunRequest, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Triggers the disputes pipeline job for the specified pipeline run ID.
     * @param {TriggerDisputesPipelineRequest} triggerDisputesPipelineRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    triggerDisputesPipeline(
      triggerDisputesPipelineRequest: TriggerDisputesPipelineRequest,
      options?: any,
    ): AxiosPromise<TriggerDisputesPipelineResponse> {
      return localVarFp
        .triggerDisputesPipeline(triggerDisputesPipelineRequest, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Creates or creates multiple mileage sources overrides
     * @param {UpsertMileageSourcesOverridesRequest} upsertMileageSourcesOverridesRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    upsertMileageSourcesOverrides(
      upsertMileageSourcesOverridesRequest: UpsertMileageSourcesOverridesRequest,
      options?: any,
    ): AxiosPromise<UpsertMileageSourcesOverridesResponse> {
      return localVarFp
        .upsertMileageSourcesOverrides(
          upsertMileageSourcesOverridesRequest,
          options,
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * BillingApi - object-oriented interface
 * @export
 * @class BillingApi
 * @extends {BaseAPI}
 */
export class BillingApi extends BaseAPI {
  /**
   * Calculates the number of vehicles that would be removed from coverage, as well as how many would be added to coverage, given the provided schedule. It doesn\'t persist anything in the DB.
   * @param {CalculateDiffTrackedVehicleScheduleRequest} calculateDiffTrackedVehicleScheduleRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BillingApi
   */
  public calculateDiffTrackedVehicleSchedule(
    calculateDiffTrackedVehicleScheduleRequest: CalculateDiffTrackedVehicleScheduleRequest,
    options?: AxiosRequestConfig,
  ) {
    return BillingApiFp(this.configuration)
      .calculateDiffTrackedVehicleSchedule(
        calculateDiffTrackedVehicleScheduleRequest,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Create a tracked vehicle
   * @param {CreateTrackedVehicleRequest} createTrackedVehicleRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BillingApi
   */
  public createTrackedVehicle(
    createTrackedVehicleRequest: CreateTrackedVehicleRequest,
    options?: AxiosRequestConfig,
  ) {
    return BillingApiFp(this.configuration)
      .createTrackedVehicle(createTrackedVehicleRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Dispute the policy\'s consumption for the month. For now, only mileage will be supported.
   * @param {DisputeConsumptionRequest} disputeConsumptionRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BillingApi
   */
  public disputeConsumption(
    disputeConsumptionRequest: DisputeConsumptionRequest,
    options?: AxiosRequestConfig,
  ) {
    return BillingApiFp(this.configuration)
      .disputeConsumption(disputeConsumptionRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Return information about the Billing.
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BillingApi
   */
  public getBillingHealth(options?: AxiosRequestConfig) {
    return BillingApiFp(this.configuration)
      .getBillingHealth(options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Return billing info of all DOTs
   * @param {MileageSource} [mileageSource]
   * @param {PolicyActivityStatus} [activityStatus]
   * @param {BillingFrequency} [frequency]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BillingApi
   */
  public getBillingInfoForAllDOTs(
    mileageSource?: MileageSource,
    activityStatus?: PolicyActivityStatus,
    frequency?: BillingFrequency,
    options?: AxiosRequestConfig,
  ) {
    return BillingApiFp(this.configuration)
      .getBillingInfoForAllDOTs(
        mileageSource,
        activityStatus,
        frequency,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Returns billing info for a policy identifier + issuance year combination
   * @param {string} policyIdentifier
   * @param {number} issuanceYear
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BillingApi
   */
  public getBillingInfoForPolicyIdentifier(
    policyIdentifier: string,
    issuanceYear: number,
    options?: AxiosRequestConfig,
  ) {
    return BillingApiFp(this.configuration)
      .getBillingInfoForPolicyIdentifier(
        policyIdentifier,
        issuanceYear,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Retrieves a list of the last 25 billing pipeline runs ordered from recently created.
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BillingApi
   */
  public getBillingPipelineRuns(options?: AxiosRequestConfig) {
    return BillingApiFp(this.configuration)
      .getBillingPipelineRuns(options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Create a temporary public URL to download a billing report file.
   * @param {string} fileHandle
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BillingApi
   */
  public getBillingReportFileLink(
    fileHandle: string,
    options?: AxiosRequestConfig,
  ) {
    return BillingApiFp(this.configuration)
      .getBillingReportFileLink(fileHandle, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Returns the inputs required to dispute the policy\'s consumption for the specified month of year.
   * @param {string} billingPeriodStartDate
   * @param {string} billingPeriodEndDate
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BillingApi
   */
  public getDisputeConsumptionForm(
    billingPeriodStartDate: string,
    billingPeriodEndDate: string,
    options?: AxiosRequestConfig,
  ) {
    return BillingApiFp(this.configuration)
      .getDisputeConsumptionForm(
        billingPeriodStartDate,
        billingPeriodEndDate,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get the list of insureds considered for the pipelines falling inside the given date range.
   * @param {string} billingPeriodStartDate
   * @param {string} billingPeriodEndDate
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BillingApi
   */
  public getInsuredsForPipelineRun(
    billingPeriodStartDate: string,
    billingPeriodEndDate: string,
    options?: AxiosRequestConfig,
  ) {
    return BillingApiFp(this.configuration)
      .getInsuredsForPipelineRun(
        billingPeriodStartDate,
        billingPeriodEndDate,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get the status of the jobs for the given job run ids
   * @param {Array<string>} ids
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BillingApi
   */
  public getJobRunIdStatuses(ids: Array<string>, options?: AxiosRequestConfig) {
    return BillingApiFp(this.configuration)
      .getJobRunIdStatuses(ids, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Returns mileage sources for a policy identifier + issuance year combination
   * @param {string} policyIdentifier
   * @param {number} issuanceYear
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BillingApi
   */
  public getMileageSourcesForPolicyIdentifier(
    policyIdentifier: string,
    issuanceYear: number,
    options?: AxiosRequestConfig,
  ) {
    return BillingApiFp(this.configuration)
      .getMileageSourcesForPolicyIdentifier(
        policyIdentifier,
        issuanceYear,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Returns, for each month, the final artifacts produced by billing product (Fleet Activity Reports zip and Bills CSV, together with whether or not there is any ongoing pipeline executing).
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BillingApi
   */
  public getMonthlyArtifacts(options?: AxiosRequestConfig) {
    return BillingApiFp(this.configuration)
      .getMonthlyArtifacts(options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Fetch monthly billing info for policy identifier and issuance year
   * @param {string} policyIdentifier
   * @param {number} issuanceYear
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BillingApi
   */
  public getMonthlyBillingInfo(
    policyIdentifier: string,
    issuanceYear: number,
    options?: AxiosRequestConfig,
  ) {
    return BillingApiFp(this.configuration)
      .getMonthlyBillingInfo(policyIdentifier, issuanceYear, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get billing report entities according to the input filters.
   * @param {ReportType} type
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BillingApi
   */
  public getReports(type: ReportType, options?: AxiosRequestConfig) {
    return BillingApiFp(this.configuration)
      .getReports(type, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Returns the inputs required to manually ingest mileage
   * @param {string} billingPeriodStartDate
   * @param {string} billingPeriodEndDate
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BillingApi
   */
  public getSelfReportersMileageIngestionForm(
    billingPeriodStartDate: string,
    billingPeriodEndDate: string,
    options?: AxiosRequestConfig,
  ) {
    return BillingApiFp(this.configuration)
      .getSelfReportersMileageIngestionForm(
        billingPeriodStartDate,
        billingPeriodEndDate,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get the list of tracked vehicles that satisfy the given filters, and that have been launched for IVM.
   * @param {string} [effectiveFromUpperLimit]
   * @param {Array<string>} [billingPolicyIds]
   * @param {boolean} [onlyInIVMExperiment] If true, only vehicles that have been launched for IVM will be returned.
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BillingApi
   */
  public getTrackedVehicles(
    effectiveFromUpperLimit?: string,
    billingPolicyIds?: Array<string>,
    onlyInIVMExperiment?: boolean,
    options?: AxiosRequestConfig,
  ) {
    return BillingApiFp(this.configuration)
      .getTrackedVehicles(
        effectiveFromUpperLimit,
        billingPolicyIds,
        onlyInIVMExperiment,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get the list of vehicles with their insured value, and whether they\'re tracked for APD coverage
   * @param {string} billingPolicyId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BillingApi
   */
  public getTrackedVehiclesForPolicyId(
    billingPolicyId: string,
    options?: AxiosRequestConfig,
  ) {
    return BillingApiFp(this.configuration)
      .getTrackedVehiclesForPolicyId(billingPolicyId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Update billing info for a policy identifier + issuance year combination or application ID.
   * @param {PatchBillingInfoRequest} patchBillingInfoRequest
   * @param {string} [policyIdentifier]
   * @param {number} [issuanceYear]
   * @param {string} [applicationID]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BillingApi
   */
  public patchBillingInfo(
    patchBillingInfoRequest: PatchBillingInfoRequest,
    policyIdentifier?: string,
    issuanceYear?: number,
    applicationID?: string,
    options?: AxiosRequestConfig,
  ) {
    return BillingApiFp(this.configuration)
      .patchBillingInfo(
        patchBillingInfoRequest,
        policyIdentifier,
        issuanceYear,
        applicationID,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {SelfReportMileageRequest} selfReportMileageRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BillingApi
   */
  public selfReportMileage(
    selfReportMileageRequest: SelfReportMileageRequest,
    options?: AxiosRequestConfig,
  ) {
    return BillingApiFp(this.configuration)
      .selfReportMileage(selfReportMileageRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {SetStaticParamRequest} setStaticParamRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BillingApi
   */
  public setStaticParam(
    setStaticParamRequest: SetStaticParamRequest,
    options?: AxiosRequestConfig,
  ) {
    return BillingApiFp(this.configuration)
      .setStaticParam(setStaticParamRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Set the full tracked vehicles schedule for an account. Any vehicle not defined here will be marked as not covered. Useful for inbound schemas received from the insureds.
   * @param {SetTrackedVehicleScheduleRequest} setTrackedVehicleScheduleRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BillingApi
   */
  public setTrackedVehicleSchedule(
    setTrackedVehicleScheduleRequest: SetTrackedVehicleScheduleRequest,
    options?: AxiosRequestConfig,
  ) {
    return BillingApiFp(this.configuration)
      .setTrackedVehicleSchedule(setTrackedVehicleScheduleRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Transition a mileage source override\'s status
   * @param {string} mileageSourceOverrideId
   * @param {TransitionMileageSourceOverrideStatusRequest} transitionMileageSourceOverrideStatusRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BillingApi
   */
  public transitionMileageSourceOverrideStatus(
    mileageSourceOverrideId: string,
    transitionMileageSourceOverrideStatusRequest: TransitionMileageSourceOverrideStatusRequest,
    options?: AxiosRequestConfig,
  ) {
    return BillingApiFp(this.configuration)
      .transitionMileageSourceOverrideStatus(
        mileageSourceOverrideId,
        transitionMileageSourceOverrideStatusRequest,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Create a new billing pipeline run
   * @param {TriggerBillingPipelineRunRequest} triggerBillingPipelineRunRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BillingApi
   */
  public triggerBillingPipelineRun(
    triggerBillingPipelineRunRequest: TriggerBillingPipelineRunRequest,
    options?: AxiosRequestConfig,
  ) {
    return BillingApiFp(this.configuration)
      .triggerBillingPipelineRun(triggerBillingPipelineRunRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Triggers the disputes pipeline job for the specified pipeline run ID.
   * @param {TriggerDisputesPipelineRequest} triggerDisputesPipelineRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BillingApi
   */
  public triggerDisputesPipeline(
    triggerDisputesPipelineRequest: TriggerDisputesPipelineRequest,
    options?: AxiosRequestConfig,
  ) {
    return BillingApiFp(this.configuration)
      .triggerDisputesPipeline(triggerDisputesPipelineRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Creates or creates multiple mileage sources overrides
   * @param {UpsertMileageSourcesOverridesRequest} upsertMileageSourcesOverridesRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BillingApi
   */
  public upsertMileageSourcesOverrides(
    upsertMileageSourcesOverridesRequest: UpsertMileageSourcesOverridesRequest,
    options?: AxiosRequestConfig,
  ) {
    return BillingApiFp(this.configuration)
      .upsertMileageSourcesOverrides(
        upsertMileageSourcesOverridesRequest,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }
}
