/* tslint:disable */
/* eslint-disable */
/**
 * Business Auto Insurance API
 * Business Auto Insurance APIs
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { Configuration } from './configuration';
import globalAxios, {
  AxiosPromise,
  AxiosInstance,
  AxiosRequestConfig,
} from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import {
  DUMMY_BASE_URL,
  assertParamExists,
  setApiKeyToObject,
  setBasicAuthToObject,
  setBearerAuthToObject,
  setOAuthToObject,
  setSearchParams,
  serializeDataIfNeeded,
  toPathString,
  createRequestFunction,
} from './common';
// @ts-ignore
import {
  BASE_PATH,
  COLLECTION_FORMATS,
  RequestArgs,
  BaseAPI,
  RequiredError,
} from './base';

/**
 *
 * @export
 * @interface Address
 */
export interface Address {
  /**
   *
   * @type {string}
   * @memberof Address
   */
  street: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  city: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  state: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  county?: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  zip: string;
}
/**
 *
 * @export
 * @interface AncillaryCoverage
 */
export interface AncillaryCoverage {
  /**
   *
   * @type {CoverageType}
   * @memberof AncillaryCoverage
   */
  coverage: CoverageType;
  /**
   *
   * @type {CoverageType}
   * @memberof AncillaryCoverage
   */
  primaryCoverage: CoverageType;
  /**
   * Whether the ancillary coverage is enabled
   * @type {boolean}
   * @memberof AncillaryCoverage
   */
  isEnabled: boolean;
  /**
   *
   * @type {Array<number>}
   * @memberof AncillaryCoverage
   */
  limitOptions?: Array<number>;
  /**
   *
   * @type {Array<number>}
   * @memberof AncillaryCoverage
   */
  deductibleOptions?: Array<number>;
  /**
   *
   * @type {number}
   * @memberof AncillaryCoverage
   */
  selectedLimit?: number;
  /**
   * Default deductible value - use vehicleDeductibles for vehicle-specific deductibles
   * @type {number}
   * @memberof AncillaryCoverage
   */
  selectedDeductible?: number;
  /**
   * Vehicle-specific deductibles for this ancillary coverage. Only applicable when hasVehicleLevelDeductible is true.
   * @type {Array<VehicleDeductible>}
   * @memberof AncillaryCoverage
   */
  vehicleDeductibles?: Array<VehicleDeductible>;
  /**
   * Vehicle-specific limits for this ancillary coverage. Only applicable when hasVehicleLevelLimit is true.
   * @type {Array<VehicleLimit>}
   * @memberof AncillaryCoverage
   */
  vehicleLimits?: Array<VehicleLimit>;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum AppState {
  Created = 'Created',
  UnderReview = 'UnderReview',
  QuoteGenerated = 'QuoteGenerated',
  QuoteGenerating = 'QuoteGenerating',
  Approved = 'Approved',
  Declined = 'Declined',
  Closed = 'Closed',
  PolicyCreated = 'PolicyCreated',
  BindableQuoteGenerated = 'BindableQuoteGenerated',
  Panic = 'Panic',
}

/**
 *
 * @export
 * @interface BusinessAutoApplicationListRecord
 */
export interface BusinessAutoApplicationListRecord {
  /**
   *
   * @type {string}
   * @memberof BusinessAutoApplicationListRecord
   */
  id: string;
  /**
   *
   * @type {AppState}
   * @memberof BusinessAutoApplicationListRecord
   */
  state: AppState;
  /**
   *
   * @type {string}
   * @memberof BusinessAutoApplicationListRecord
   */
  companyName: string;
  /**
   *
   * @type {string}
   * @memberof BusinessAutoApplicationListRecord
   */
  effectiveDate: string;
  /**
   *
   * @type {string}
   * @memberof BusinessAutoApplicationListRecord
   */
  shortID: string;
  /**
   *
   * @type {TSPProvider}
   * @memberof BusinessAutoApplicationListRecord
   */
  tspProvider?: TSPProvider;
  /**
   * Whether this application belongs to a Nirvana internal agency
   * @type {boolean}
   * @memberof BusinessAutoApplicationListRecord
   */
  isInternal?: boolean;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum BusinessUse {
  Invalid = 'Invalid',
  Commercial = 'Commercial',
  Retail = 'Retail',
  Service = 'Service',
}

/**
 *
 * @export
 * @interface CompanyInfo
 */
export interface CompanyInfo {
  /**
   *
   * @type {string}
   * @memberof CompanyInfo
   */
  name: string;
  /**
   *
   * @type {USState}
   * @memberof CompanyInfo
   */
  usState: USState;
  /**
   *
   * @type {Address}
   * @memberof CompanyInfo
   */
  address?: Address;
  /**
   *
   * @type {number}
   * @memberof CompanyInfo
   */
  DOTNumber?: number;
  /**
   *
   * @type {string}
   * @memberof CompanyInfo
   */
  FEIN?: string;
  /**
   *
   * @type {number}
   * @memberof CompanyInfo
   */
  noOfPowerUnits?: number;
  /**
   *
   * @type {boolean}
   * @memberof CompanyInfo
   */
  hasIndividualNamedInsured?: boolean;
  /**
   *
   * @type {boolean}
   * @memberof CompanyInfo
   */
  hasWorkCompPolicy?: boolean;
  /**
   *
   * @type {number}
   * @memberof CompanyInfo
   */
  noOfEmployees?: number;
  /**
   *
   * @type {number}
   * @memberof CompanyInfo
   */
  perOfEmployeesOperatingOwnAutos?: number;
  /**
   *
   * @type {PrimaryIndustryClassification}
   * @memberof CompanyInfo
   */
  primaryIndustryClassification?: PrimaryIndustryClassification;
  /**
   * Secondary industry classifications if the company operates in multiple industries
   * @type {Array<SecondaryIndustryClassification>}
   * @memberof CompanyInfo
   */
  secondaryIndustryClassifications?: Array<SecondaryIndustryClassification>;
  /**
   *
   * @type {number}
   * @memberof CompanyInfo
   */
  annualCostOfHire?: number;
  /**
   *
   * @type {number}
   * @memberof CompanyInfo
   */
  maximumValueOfHiredAutos?: number;
}
/**
 *
 * @export
 * @interface Coverage
 */
export interface Coverage {
  /**
   *
   * @type {CoverageType}
   * @memberof Coverage
   */
  coverageType: CoverageType;
  /**
   *
   * @type {number}
   * @memberof Coverage
   */
  limit?: number;
  /**
   *
   * @type {number}
   * @memberof Coverage
   */
  deductible?: number;
  /**
   * Whether the coverage is enabled
   * @type {boolean}
   * @memberof Coverage
   */
  isEnabled: boolean;
}
/**
 *
 * @export
 * @interface CoverageModification
 */
export interface CoverageModification {
  /**
   *
   * @type {CoverageType}
   * @memberof CoverageModification
   */
  coverage: CoverageType;
  /**
   * The modification factor to apply
   * @type {number}
   * @memberof CoverageModification
   */
  modifier: number;
}
/**
 *
 * @export
 * @interface CoverageOption
 */
export interface CoverageOption {
  /**
   *
   * @type {CoverageType}
   * @memberof CoverageOption
   */
  coverage: CoverageType;
  /**
   * Whether this coverage supports vehicle-level limits
   * @type {boolean}
   * @memberof CoverageOption
   */
  hasVehicleLevelLimit: boolean;
  /**
   * Whether this coverage supports vehicle-level deductibles
   * @type {boolean}
   * @memberof CoverageOption
   */
  hasVehicleLevelDeductible: boolean;
  /**
   *
   * @type {Array<string>}
   * @memberof CoverageOption
   */
  vinList?: Array<string>;
}
/**
 *
 * @export
 * @interface CoveragePremium
 */
export interface CoveragePremium {
  /**
   *
   * @type {CoverageType}
   * @memberof CoveragePremium
   */
  coverageType: CoverageType;
  /**
   * Total premium amount for this coverage in cents
   * @type {number}
   * @memberof CoveragePremium
   */
  totalPremium: number;
  /**
   * Premium breakdown by sub-coverage groups (collapsible)
   * @type {Array<SubCoverageGroup>}
   * @memberof CoveragePremium
   */
  subCoverageGroups?: Array<SubCoverageGroup>;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum CoverageType {
  CoverageAutoLiability = 'CoverageAutoLiability',
  CoverageAutoPhysicalDamage = 'CoverageAutoPhysicalDamage',
  CoverageGeneralLiability = 'CoverageGeneralLiability',
  CoverageMotorTruckCargo = 'CoverageMotorTruckCargo',
  CoverageTrailerInterchange = 'CoverageTrailerInterchange',
  CoverageUninsuredMotoristBodilyInjury = 'CoverageUninsuredMotoristBodilyInjury',
  CoverageUnderinsuredMotoristBodilyInjury = 'CoverageUnderinsuredMotoristBodilyInjury',
  CoverageUninsuredMotoristPropertyDamage = 'CoverageUninsuredMotoristPropertyDamage',
  CoverageUnderinsuredMotoristPropertyDamage = 'CoverageUnderinsuredMotoristPropertyDamage',
  CoveragePersonalInjuryProtection = 'CoveragePersonalInjuryProtection',
  CoverageMedicalPayments = 'CoverageMedicalPayments',
  CoverageUmuimBodilyInjury = 'CoverageUMUIMBodilyInjury',
  CoverageUmuimPropertyDamage = 'CoverageUMUIMPropertyDamage',
  CoveragePropertyProtectionInsurance = 'CoveragePropertyProtectionInsurance',
  CoveragePersonalInjuryProtectionBasic = 'CoveragePersonalInjuryProtectionBasic',
  CoveragePersonalInjuryProtectionIncreased = 'CoveragePersonalInjuryProtectionIncreased',
  CoveragePipExcessAttendantCare = 'CoveragePIPExcessAttendantCare',
  CoverageReefer = 'CoverageReefer',
  CoverageReeferWithHumanError = 'CoverageReeferWithHumanError',
  CoverageEnhancedPackageTowingLimit = 'CoverageEnhancedPackageTowingLimit',
  CoverageGuestPersonalInjuryProtection = 'CoverageGuestPersonalInjuryProtection',
  CoverageReeferWithoutHumanError = 'CoverageReeferWithoutHumanError',
  CoverageStopGap = 'CoverageStopGap',
  CoverageBroadenedPollution = 'CoverageBroadenedPollution',
  CoverageBlanketAdditional = 'CoverageBlanketAdditional',
  CoverageUiia = 'CoverageUIIA',
  CoverageBlanketWaiverOfSubrogation = 'CoverageBlanketWaiverOfSubrogation',
  CoverageGlBlanketWaiverOfSubrogation = 'CoverageGLBlanketWaiverOfSubrogation',
  CoverageGlBlanketAdditional = 'CoverageGLBlanketAdditional',
  CoverageMtcBlanketWaiverOfSubrogation = 'CoverageMTCBlanketWaiverOfSubrogation',
  CoverageMtcBlanketAdditional = 'CoverageMTCBlanketAdditional',
  CoverageUmuim = 'CoverageUMUIM',
  CoverageUmbiuimbi = 'CoverageUMBIUIMBI',
  CoverageUm = 'CoverageUM',
  CoverageUim = 'CoverageUIM',
  CoverageTerrorism = 'CoverageTerrorism',
  CoverageDebrisRemoval = 'CoverageDebrisRemoval',
  CoverageNonOwnedTrailer = 'CoverageNonOwnedTrailer',
  CoverageApduiia = 'CoverageAPDUIIA',
  CoverageMtcuiia = 'CoverageMTCUIIA',
  CoverageApdTrailerInterchange = 'CoverageAPDTrailerInterchange',
  CoverageMtcTrailerInterchange = 'CoverageMTCTrailerInterchange',
  CoverageApdNonOwnedTrailer = 'CoverageAPDNonOwnedTrailer',
  CoverageMtcNonOwnedTrailer = 'CoverageMTCNonOwnedTrailer',
  CoverageUnattendedTruck = 'CoverageUnattendedTruck',
  CoverageEarnedFreight = 'CoverageEarnedFreight',
  CoverageRentalReimbursement = 'CoverageRentalReimbursement',
  CoverageTowingLaborAndStorage = 'CoverageTowingLaborAndStorage',
  CoverageHiredAuto = 'CoverageHiredAuto',
  CoverageCargoAtScheduledTerminals = 'CoverageCargoAtScheduledTerminals',
  CoverageCargoTrailerInterchange = 'CoverageCargoTrailerInterchange',
  CoverageLossMitigationExpenses = 'CoverageLossMitigationExpenses',
  CoverageMiscellaneousEquipment = 'CoverageMiscellaneousEquipment',
  CoveragePollutantCleanupAndRemoval = 'CoveragePollutantCleanupAndRemoval',
  CoverageBlanketAdditionalPnc = 'CoverageBlanketAdditionalPNC',
  CoverageNonOwnedAuto = 'CoverageNonOwnedAuto',
  CoverageWorkLossBenefits = 'CoverageWorkLossBenefits',
  CoverageFuneralExpenseBenefits = 'CoverageFuneralExpenseBenefits',
  CoverageAccidentalDeathBenefits = 'CoverageAccidentalDeathBenefits',
  CoverageExtraordinaryMedicalBenefits = 'CoverageExtraordinaryMedicalBenefits',
  CoverageMedicalExpenseBenefits = 'CoverageMedicalExpenseBenefits',
  CoverageHiredAutoLiab = 'CoverageHiredAutoLiab',
  CoverageHiredAutoPd = 'CoverageHiredAutoPD',
}

/**
 *
 * @export
 * @interface CoveragesInfo
 */
export interface CoveragesInfo {
  /**
   * Map of coverage type to coverage with limit and deductible
   * @type {{ [key: string]: Coverage; }}
   * @memberof CoveragesInfo
   */
  coverages: { [key: string]: Coverage };
}
/**
 * Driver factor - only applicable for 1-10 units
 * @export
 * @interface DriverFactor
 */
export interface DriverFactor {
  /**
   * The factor value applied by underwriter
   * @type {number}
   * @memberof DriverFactor
   */
  factor?: number;
}
/**
 *
 * @export
 * @interface ErrorMessage
 */
export interface ErrorMessage {
  /**
   *
   * @type {string}
   * @memberof ErrorMessage
   */
  message: string;
  /**
   *
   * @type {number}
   * @memberof ErrorMessage
   */
  code: number;
}
/**
 * Experience rating modifications
 * @export
 * @interface ExperienceMod
 */
export interface ExperienceMod {
  /**
   * Array of coverage-specific experience modification values
   * @type {Array<CoverageModification>}
   * @memberof ExperienceMod
   */
  mods?: Array<CoverageModification>;
}
/**
 *
 * @export
 * @interface FilingsInfo
 */
export interface FilingsInfo {
  /**
   *
   * @type {boolean}
   * @memberof FilingsInfo
   */
  hasMultiStateFilings: boolean;
  /**
   *
   * @type {boolean}
   * @memberof FilingsInfo
   */
  hasSingleStateFilings: boolean;
  /**
   *
   * @type {boolean}
   * @memberof FilingsInfo
   */
  hasFMCSAFilings: boolean;
  /**
   *
   * @type {boolean}
   * @memberof FilingsInfo
   */
  hasDOTFilings: boolean;
}
/**
 *
 * @export
 * @interface GetBusinessAutoAppResponse
 */
export interface GetBusinessAutoAppResponse {
  /**
   *
   * @type {string}
   * @memberof GetBusinessAutoAppResponse
   */
  id: string;
  /**
   *
   * @type {string}
   * @memberof GetBusinessAutoAppResponse
   */
  shortID: string;
  /**
   *
   * @type {AppState}
   * @memberof GetBusinessAutoAppResponse
   */
  state: AppState;
  /**
   *
   * @type {string}
   * @memberof GetBusinessAutoAppResponse
   */
  effectiveDate: string;
  /**
   *
   * @type {string}
   * @memberof GetBusinessAutoAppResponse
   */
  effectiveDateTo: string;
  /**
   *
   * @type {CompanyInfo}
   * @memberof GetBusinessAutoAppResponse
   */
  companyInfo: CompanyInfo;
  /**
   *
   * @type {Array<VehicleInfo>}
   * @memberof GetBusinessAutoAppResponse
   */
  vehiclesInfo: Array<VehicleInfo>;
  /**
   *
   * @type {CoveragesInfo}
   * @memberof GetBusinessAutoAppResponse
   */
  coveragesInfo: CoveragesInfo;
  /**
   *
   * @type {FilingsInfo}
   * @memberof GetBusinessAutoAppResponse
   */
  filingsInfo: FilingsInfo;
  /**
   *
   * @type {Producer}
   * @memberof GetBusinessAutoAppResponse
   */
  producerInfo: Producer;
  /**
   *
   * @type {string}
   * @memberof GetBusinessAutoAppResponse
   */
  createdAt: string;
  /**
   *
   * @type {UnderwritingOverrides}
   * @memberof GetBusinessAutoAppResponse
   */
  underwritingOverrides?: UnderwritingOverrides;
  /**
   *
   * @type {TelematicsInfo}
   * @memberof GetBusinessAutoAppResponse
   */
  telematicsInfo?: TelematicsInfo;
  /**
   * The unique identifier for the TSP connection handle
   * @type {string}
   * @memberof GetBusinessAutoAppResponse
   */
  tspConnHandleId?: string;
}
/**
 *
 * @export
 * @interface GetCoverageOptionsRequestBody
 */
export interface GetCoverageOptionsRequestBody {
  /**
   *
   * @type {Array<Coverage>}
   * @memberof GetCoverageOptionsRequestBody
   */
  coverages: Array<Coverage>;
}
/**
 *
 * @export
 * @interface GetPricingResponse
 */
export interface GetPricingResponse {
  /**
   * The unique identifier of the business auto application
   * @type {string}
   * @memberof GetPricingResponse
   */
  applicationId: string;
  /**
   * The unique identifier of the data context
   * @type {string}
   * @memberof GetPricingResponse
   */
  dataContextId: string;
  /**
   * Total premium amount across all coverages in cents
   * @type {number}
   * @memberof GetPricingResponse
   */
  totalPremium: number;
  /**
   * Premium breakdown by coverage type (collapsible)
   * @type {Array<CoveragePremium>}
   * @memberof GetPricingResponse
   */
  coverages: Array<CoveragePremium>;
  /**
   *
   * @type {SurchargeBreakdown}
   * @memberof GetPricingResponse
   */
  surcharges: SurchargeBreakdown;
  /**
   * When the pricing information was created
   * @type {string}
   * @memberof GetPricingResponse
   */
  createdAt: string;
}
/**
 *
 * @export
 * @interface GetPricingStatusResponse
 */
export interface GetPricingStatusResponse {
  /**
   * The unique identifier of the business auto application
   * @type {string}
   * @memberof GetPricingStatusResponse
   */
  applicationId: string;
  /**
   *
   * @type {PricingStatus}
   * @memberof GetPricingStatusResponse
   */
  status: PricingStatus;
  /**
   * When the pricing job was started
   * @type {string}
   * @memberof GetPricingStatusResponse
   */
  startedAt: string;
  /**
   * When the pricing job was completed (null if still pending)
   * @type {string}
   * @memberof GetPricingStatusResponse
   */
  completedAt?: string;
  /**
   * Error message if the pricing job failed
   * @type {string}
   * @memberof GetPricingStatusResponse
   */
  errorMessage?: string;
  /**
   * The unique identifier of the pricing job
   * @type {string}
   * @memberof GetPricingStatusResponse
   */
  jobId?: string;
}
/**
 * Generic error response from backend. Safe to render to end-user, and print to browser console.
 * @export
 * @interface ModelError
 */
export interface ModelError {
  /**
   * RequestId of the associated request (for investigation)
   * @type {string}
   * @memberof ModelError
   */
  requestId: string;
  /**
   * Message for the end user
   * @type {string}
   * @memberof ModelError
   */
  message: string;
  /**
   * Status code repeated again the response body for convenience.
   * @type {number}
   * @memberof ModelError
   */
  code: number;
}
/**
 * Request to update pre-bind information (FEIN or DOT number) for a business auto application
 * @export
 * @interface PatchPreBindInfoRequest
 */
export interface PatchPreBindInfoRequest {
  /**
   * Federal Employer Identification Number
   * @type {string}
   * @memberof PatchPreBindInfoRequest
   */
  fein: string;
  /**
   *
   * @type {number}
   * @memberof PatchPreBindInfoRequest
   */
  dotNumber?: number;
}
/**
 *
 * @export
 * @interface PatchStateTransitionRequest
 */
export interface PatchStateTransitionRequest {
  /**
   *
   * @type {AppState}
   * @memberof PatchStateTransitionRequest
   */
  transitionToState: AppState;
}
/**
 *
 * @export
 * @interface PatchStateTransitionResponse
 */
export interface PatchStateTransitionResponse {
  /**
   *
   * @type {string}
   * @memberof PatchStateTransitionResponse
   */
  applicationId: string;
  /**
   *
   * @type {AppState}
   * @memberof PatchStateTransitionResponse
   */
  newState: AppState;
}
/**
 * Request to update underwriting overrides for a business auto application
 * @export
 * @interface PatchUnderwritingOverridesRequest
 */
export interface PatchUnderwritingOverridesRequest {
  /**
   * Whether loss free credit is applied for AL coverage
   * @type {boolean}
   * @memberof PatchUnderwritingOverridesRequest
   */
  alLossFreeCredit?: boolean;
  /**
   * Whether loss free credit is applied for APD coverage
   * @type {boolean}
   * @memberof PatchUnderwritingOverridesRequest
   */
  apdLossFreeCredit?: boolean;
  /**
   * Verifiable experience period with no third party losses (in months)
   * @type {number}
   * @memberof PatchUnderwritingOverridesRequest
   */
  verifiableExperiencePeriod?: number;
  /**
   * Percentage of loss free credit applied
   * @type {number}
   * @memberof PatchUnderwritingOverridesRequest
   */
  lossFreeCreditPercentage?: number;
  /**
   *
   * @type {DriverFactor}
   * @memberof PatchUnderwritingOverridesRequest
   */
  driverFactor?: DriverFactor;
  /**
   *
   * @type {ScheduleMod}
   * @memberof PatchUnderwritingOverridesRequest
   */
  scheduleMods?: ScheduleMod;
  /**
   *
   * @type {ExperienceMod}
   * @memberof PatchUnderwritingOverridesRequest
   */
  experienceMod?: ExperienceMod;
  /**
   *
   * @type {QualityRatingGrade}
   * @memberof PatchUnderwritingOverridesRequest
   */
  qualityRating?: QualityRatingGrade;
  /**
   *
   * @type {Array<AncillaryCoverage>}
   * @memberof PatchUnderwritingOverridesRequest
   */
  ancillaryCoverages?: Array<AncillaryCoverage>;
}
/**
 *
 * @export
 * @interface PostBusinessAutoAppRequest
 */
export interface PostBusinessAutoAppRequest {
  /**
   *
   * @type {string}
   * @memberof PostBusinessAutoAppRequest
   */
  effectiveDate: string;
  /**
   *
   * @type {CompanyInfo}
   * @memberof PostBusinessAutoAppRequest
   */
  companyInfo: CompanyInfo;
  /**
   *
   * @type {Array<VehicleInfo>}
   * @memberof PostBusinessAutoAppRequest
   */
  vehiclesInfo?: Array<VehicleInfo>;
  /**
   *
   * @type {CoveragesInfo}
   * @memberof PostBusinessAutoAppRequest
   */
  coveragesInfo?: CoveragesInfo;
  /**
   *
   * @type {FilingsInfo}
   * @memberof PostBusinessAutoAppRequest
   */
  filingsInfo?: FilingsInfo;
  /**
   *
   * @type {string}
   * @memberof PostBusinessAutoAppRequest
   */
  producerId: string;
}
/**
 *
 * @export
 * @interface PostBusinessAutoAppResponse
 */
export interface PostBusinessAutoAppResponse {
  /**
   *
   * @type {string}
   * @memberof PostBusinessAutoAppResponse
   */
  id: string;
}
/**
 *
 * @export
 * @interface PostTriggerPricingJobResponse
 */
export interface PostTriggerPricingJobResponse {
  /**
   * Whether the pricing job was triggered successfully
   * @type {boolean}
   * @memberof PostTriggerPricingJobResponse
   */
  success?: boolean;
  /**
   * Response message
   * @type {string}
   * @memberof PostTriggerPricingJobResponse
   */
  message?: string;
  /**
   * The ID of the triggered pricing job
   * @type {string}
   * @memberof PostTriggerPricingJobResponse
   */
  jobId?: string;
}
/**
 * The status of the pricing operation
 * @export
 * @enum {string}
 */

export enum PricingStatus {
  Pending = 'Pending',
  Completed = 'Completed',
  Failed = 'Failed',
  NotStarted = 'NotStarted',
}

/**
 *
 * @export
 * @enum {string}
 */

export enum PrimaryIndustryClassification {
  Invalid = 'Invalid',
  ContractorTrucks = 'ContractorTrucks',
  WholesalersManufacturers = 'WholesalersManufacturers',
}

/**
 *
 * @export
 * @interface Producer
 */
export interface Producer {
  /**
   *
   * @type {string}
   * @memberof Producer
   */
  id: string;
  /**
   *
   * @type {string}
   * @memberof Producer
   */
  name: string;
  /**
   *
   * @type {string}
   * @memberof Producer
   */
  email?: string | null;
}
/**
 *
 * @export
 * @interface PutBusinessAutoAppRequest
 */
export interface PutBusinessAutoAppRequest {
  /**
   *
   * @type {string}
   * @memberof PutBusinessAutoAppRequest
   */
  effectiveDate: string;
  /**
   *
   * @type {CompanyInfo}
   * @memberof PutBusinessAutoAppRequest
   */
  companyInfo: CompanyInfo;
  /**
   *
   * @type {Array<VehicleInfo>}
   * @memberof PutBusinessAutoAppRequest
   */
  vehiclesInfo?: Array<VehicleInfo>;
  /**
   *
   * @type {CoveragesInfo}
   * @memberof PutBusinessAutoAppRequest
   */
  coveragesInfo?: CoveragesInfo;
  /**
   *
   * @type {FilingsInfo}
   * @memberof PutBusinessAutoAppRequest
   */
  filingsInfo?: FilingsInfo;
  /**
   *
   * @type {string}
   * @memberof PutBusinessAutoAppRequest
   */
  producerId: string;
}
/**
 * Quality rating grade
 * @export
 * @enum {string}
 */

export enum QualityRatingGrade {
  Invalid = 'Invalid',
  A = 'A',
  B = 'B',
  C = 'C',
  D = 'D',
  E = 'E',
  F = 'F',
}

/**
 *
 * @export
 * @enum {string}
 */

export enum RadiusClassification {
  /**
   * Invalid
   */
  Invalid = 'Invalid',
  /**
   * 0-100 miles radius
   */
  _0To100 = '0To100',
  /**
   * 101-300 miles radius
   */
  _101To300 = '101To300',
  /**
   * Greater than 301 miles radius
   */
  GreaterThan301 = 'GreaterThan301',
}

/**
 * Schedule rating modifications
 * @export
 * @interface ScheduleMod
 */
export interface ScheduleMod {
  /**
   * Array of coverage-specific schedule modification values
   * @type {Array<CoverageModification>}
   * @memberof ScheduleMod
   */
  mods?: Array<CoverageModification>;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum SecondaryIndustryClassification {
  Invalid = 'Invalid',
  ContractorTrucks = 'ContractorTrucks',
  SpecializedDeliveryVehicles = 'SpecializedDeliveryVehicles',
  CourierServiceVehicles = 'CourierServiceVehicles',
  FoodDeliveryTrucks = 'FoodDeliveryTrucks',
  WasteDisposalTrucks = 'WasteDisposalTrucks',
  WasteOilLiquidWasteTransporter = 'WasteOilLiquidWasteTransporter',
  FarmerTrucks = 'FarmerTrucks',
  CementMixers = 'CementMixers',
  HouseMovers = 'HouseMovers',
  MovingOperations = 'MovingOperations',
  LawnTreeServiceTrucks = 'LawnTreeServiceTrucks',
  CatererVehicles = 'CatererVehicles',
  MobileConcessionTrucks = 'MobileConcessionTrucks',
  WholesalersManufacturers = 'WholesalersManufacturers',
  SalvageHaulers = 'SalvageHaulers',
  GasOilHaulersNotForHire = 'GasOilHaulersNotForHire',
  CarCarriersNotForHire = 'CarCarriersNotForHire',
  NotOtherwiseClassifiedTruck = 'NotOtherwiseClassifiedTruck',
  LoggingTrucksForHire = 'LoggingTrucksForHire',
  LoggingTrucksNotForHire = 'LoggingTrucksNotForHire',
  TowTrucksIncidentalUse = 'TowTrucksIncidentalUse',
  TowTrucksFullTime = 'TowTrucksFullTime',
  DumpingOperationsForHire = 'DumpingOperationsForHire',
  DumpingOperationsNotForHire = 'DumpingOperationsNotForHire',
  ServiceUse = 'ServiceUse',
}

/**
 *
 * @export
 * @enum {string}
 */

export enum SpecialtyVehicleType {
  Invalid = 'Invalid',
  BoomTruck0To49Feet = 'BoomTruck0To49Feet',
  BoomTruck50To75Feet = 'BoomTruck50To75Feet',
  BoomTruck76To100Feet = 'BoomTruck76To100Feet',
  BoomTruck101To120Feet = 'BoomTruck101To120Feet',
  BoomTruckOver120Feet = 'BoomTruckOver120Feet',
  CableTelecomUtilityContractors = 'CableTelecomUtilityContractors',
  ExcavatingDrillingLightMediumHeavyTrucks = 'ExcavatingDrillingLightMediumHeavyTrucks',
  ExcavatingDrillingAllOtherUnits = 'ExcavatingDrillingAllOtherUnits',
  PetroleumDistributionContractors = 'PetroleumDistributionContractors',
  HotOilLiquidAsphaltTrucksOilField = 'HotOilLiquidAsphaltTrucksOilField',
  AllOtherOilFieldBodyTypes = 'AllOtherOilFieldBodyTypes',
  SepticTankServiceTrucksIndividual = 'SepticTankServiceTrucksIndividual',
  SepticTankServiceTrucksAllOther = 'SepticTankServiceTrucksAllOther',
  WeldersMetalworkingContractors = 'WeldersMetalworkingContractors',
  AllOtherContractorBodyTypes = 'AllOtherContractorBodyTypes',
  HotOilLiquidAsphaltTrucksAllOther = 'HotOilLiquidAsphaltTrucksAllOther',
  SpecializedDeliveryVehicles = 'SpecializedDeliveryVehicles',
  CourierServiceVehicles = 'CourierServiceVehicles',
  FoodDeliveryTrucks = 'FoodDeliveryTrucks',
  WasteDisposalTrucks = 'WasteDisposalTrucks',
  WasteOilLiquidWasteTransporters = 'WasteOilLiquidWasteTransporters',
  HarvesterGoatTrucks = 'HarvesterGoatTrucks',
  AllOtherFarmerTrucks = 'AllOtherFarmerTrucks',
  HouseMovers = 'HouseMovers',
  MovingOperations = 'MovingOperations',
  LawnTreeServiceTrucks = 'LawnTreeServiceTrucks',
  CatererVehicles = 'CatererVehicles',
  MobileConcessionTruckInVehicleVending = 'MobileConcessionTruckInVehicleVending',
  MobileConcessionTruckOtherFoodVending = 'MobileConcessionTruckOtherFoodVending',
  MobileConcessionTruckNoFoodSales = 'MobileConcessionTruckNoFoodSales',
  WholesalersManufacturers = 'WholesalersManufacturers',
  GasOilLpgPropaneBottled = 'GasOilLPGPropaneBottled',
  GasOilLpgPropaneBulk = 'GasOilLPGPropaneBulk',
  GasOilCrudeOilHaulers = 'GasOilCrudeOilHaulers',
  GasOilFuelOilHaulers = 'GasOilFuelOilHaulers',
  GasOilAllOtherGasHaulers = 'GasOilAllOtherGasHaulers',
  SalvageHaulers = 'SalvageHaulers',
  CarCarriersNotForHire = 'CarCarriersNotForHire',
  ServiceUseVehicles = 'ServiceUseVehicles',
  FireworkHaulersNotForHire = 'FireworkHaulersNotForHire',
  CustomHarvesters = 'CustomHarvesters',
  DriverTrainingTrucksTractors = 'DriverTrainingTrucksTractors',
  StreetSweepers = 'StreetSweepers',
  RentalEquipmentProvider = 'RentalEquipmentProvider',
  NotOtherwiseClassifiedTrucks = 'NotOtherwiseClassifiedTrucks',
  ArtisanContractors = 'ArtisanContractors',
  CarpentryContractors = 'CarpentryContractors',
  ExteriorBuildingConstructionContractors = 'ExteriorBuildingConstructionContractors',
  RoadConstructionContractors = 'RoadConstructionContractors',
  TrafficControlContractors = 'TrafficControlContractors',
  MobileMechanicContractors = 'MobileMechanicContractors',
  AllOtherUnits = 'AllOtherUnits',
}

/**
 *
 * @export
 * @enum {string}
 */

export enum StateUsage {
  Invalid = 'Invalid',
  Intrastate = 'Intrastate',
  Interstate = 'Interstate',
}

/**
 *
 * @export
 * @interface SubCoverageGroup
 */
export interface SubCoverageGroup {
  /**
   * List of sub-coverage types in this group
   * @type {Array<string>}
   * @memberof SubCoverageGroup
   */
  coverages: Array<string>;
  /**
   * Total premium amount for this sub-coverage group in cents
   * @type {number}
   * @memberof SubCoverageGroup
   */
  totalPremium: number;
}
/**
 *
 * @export
 * @interface SurchargeBreakdown
 */
export interface SurchargeBreakdown {
  /**
   * Individual surcharge breakdown by surcharge type
   * @type {Array<SurchargePremium>}
   * @memberof SurchargeBreakdown
   */
  surcharges: Array<SurchargePremium>;
  /**
   * Total amount of all surcharges combined in cents
   * @type {number}
   * @memberof SurchargeBreakdown
   */
  totalAmount: number;
}
/**
 *
 * @export
 * @interface SurchargePremium
 */
export interface SurchargePremium {
  /**
   *
   * @type {SurchargeType}
   * @memberof SurchargePremium
   */
  surchargeType: SurchargeType;
  /**
   * Surcharge amount
   * @type {number}
   * @memberof SurchargePremium
   */
  amount: number;
}
/**
 * Type of surcharge
 * @export
 * @enum {string}
 */

export enum SurchargeType {
  Ncrf = 'NCRF',
  Mcca = 'MCCA',
  StampingFee = 'STAMPING_FEE',
  SurplusTax = 'SURPLUS_TAX',
  FeeChargeStampingFee = 'FEE_CHARGE_STAMPING_FEE',
  FeeChargeSurplusTax = 'FEE_CHARGE_SURPLUS_TAX',
}

/**
 *
 * @export
 * @enum {string}
 */

export enum TSPProvider {
  Samsara = 'Samsara',
  KeepTruckin = 'KeepTruckin',
  Geotab = 'Geotab',
  AdvantageAssetTracking = 'AdvantageAssetTracking',
  AssuredTelematics = 'AssuredTelematics',
  EagleWireless = 'EagleWireless',
  GoFleet = 'GoFleet',
  Gridline = 'Gridline',
  OnTrakSolutions = 'OnTrakSolutions',
  RushEnterpises = 'RushEnterpises',
  TraxxisGps = 'TraxxisGPS',
  OmnitracsXrs = 'OmnitracsXRS',
  RandMcNally = 'RandMcNally',
  Azuga = 'Azuga',
  JjKeller = 'JJKeller',
  BigRoad = 'BigRoad',
  TeletracNavman = 'TeletracNavman',
  Trimble = 'Trimble',
  VerizonConnect = 'VerizonConnect',
  VerizonConnectReveal = 'VerizonConnectReveal',
  MountainEld = 'MountainEld',
  VerizonConnectFleet = 'VerizonConnectFleet',
  Webfleet = 'Webfleet',
  Eroad = 'EROAD',
  GpsInsight = 'GPSInsight',
  Zonar = 'Zonar',
  AgilisLinxup = 'AgilisLinxup',
  Other = 'Other',
  Transflo = 'Transflo',
  ActsoftEncore = 'ActsoftEncore',
  BlueInkTechnology = 'BlueInkTechnology',
  Geoforce = 'Geoforce',
  SimpleTruckEld = 'SimpleTruckELD',
  GorillaSafety = 'GorillaSafety',
  InTouchGps = 'InTouchGPS',
  EzFleet = 'EZFleet',
  Orbcomm = 'Orbcomm',
  Nextraq = 'Nextraq',
  ArgosConnectedSolutions = 'ArgosConnectedSolutions',
  AriFleet = 'ARIFleet',
  AtAndTFleet = 'ATAndTFleet',
  AtAndTFleetComplete = 'ATAndTFleetComplete',
  AttriX = 'AttriX',
  AwareGps = 'AwareGPS',
  BadgerFleetSolutions = 'BadgerFleetSolutions',
  BlueArrow = 'BlueArrow',
  CarrierHq = 'CarrierHQ',
  ClutchEld = 'ClutchELD',
  CommandGps = 'CommandGPS',
  CyntrXeldPlus = 'CyntrXELDPlus',
  DriveEdr = 'DriveEDR',
  EldFleet = 'ELDFleet',
  EldMandatePlus = 'ELDMandatePlus',
  EldMandatePro = 'ELDMandatePro',
  EldOne = 'ELDOne',
  EldRider = 'ELDRider',
  EnVueTelematics = 'EnVueTelematics',
  FleetComplete = 'FleetComplete',
  FleetNavSystems = 'FleetNavSystems',
  FleetProfitCenter = 'FleetProfitCenter',
  FleetBossGps = 'FleetBossGPS',
  Fleetistics = 'Fleetistics',
  FleetLocate21 = 'FleetLocate21',
  FleetLocateAdvancedAndCompliance = 'FleetLocateAdvancedAndCompliance',
  FleetLocateEld = 'FleetLocateELD',
  Fleetmaster = 'Fleetmaster',
  FleetSharp = 'FleetSharp',
  Flexport = 'Flexport',
  ForceByMojio = 'ForceByMojio',
  GlobalEld = 'GlobalELD',
  Glostone = 'Glostone',
  GoGps = 'GoGPS',
  GpsCommander = 'GPSCommander',
  GpsFleetFinder = 'GPSFleetFinder',
  GpsSolutions = 'GPSSolutions',
  GpsTab = 'GPSTab',
  GpsTrackingCanada = 'GPSTrackingCanada',
  GpsTrackit = 'GPSTrackit',
  GrayboxSolutions = 'GrayboxSolutions',
  HighPointGps = 'HighPointGPS',
  InsightMobileData = 'InsightMobileData',
  StreetEagle = 'StreetEagle',
  Intellishift = 'Intellishift',
  Vts = 'VTS',
  IntouchEld = 'IntouchELD',
  IoTab = 'IoTab',
  Lynx = 'Lynx',
  MasterEld = 'MasterELD',
  MonarchGps = 'MonarchGPS',
  Omnitracs = 'Omnitracs',
  OneStepGps = 'OneStepGPS',
  Orion = 'Orion',
  Positrace = 'Positrace',
  PowerFleet = 'PowerFleet',
  PrePassEld = 'PrePassELD',
  QualityGps = 'QualityGPS',
  RealEld = 'RealELD',
  RightTruckingEld = 'RightTruckingELD',
  RmjTechnologies = 'RMJTechnologies',
  RoadStarEld = 'RoadStarELD',
  SafetyVision = 'SafetyVision',
  SimpleElog = 'SimpleELOG',
  SmartDrive = 'SmartDrive',
  SmartWitness = 'SmartWitness',
  TMobile = 'TMobile',
  Tangerine = 'Tangerine',
  Tfmeld = 'TFMELD',
  TrackOnHos = 'TrackOnHOS',
  TruckerPathEldPro = 'TruckerPathELDPro',
  TruPathSystems = 'TruPathSystems',
  Vertrax = 'Vertrax',
  Zeld = 'ZELD',
  Zenduit = 'Zenduit',
  _3Md = '3MD',
  _888Eld = '888ELD',
  ApolloEld = 'ApolloELD',
  BlueStarEld = 'BlueStarELD',
  Cneld = 'CNELD',
  CteLogEld = 'CTELogELD',
  DreamEld = 'DreamELD',
  EldTab = 'ELDTab',
  ExpressWayEld = 'ExpressWayELD',
  EzeldSolutions = 'EZELDSolutions',
  EzLogz = 'EZLogz',
  Fleetmatics = 'Fleetmatics',
  ForwardThinkingEld = 'ForwardThinkingELD',
  GoodDealGps = 'GoodDealGPS',
  HorizonPathEld = 'HorizonPathELD',
  Kskeld = 'KSKELD',
  LogPlusEld = 'LogPlusELD',
  LookTruckEld = 'LookTruckELD',
  LytXDriveCam = 'LytXDriveCam',
  My20Eld = 'My20ELD',
  NetradyneInc = 'NetradyneInc',
  OaneEld = 'OaneELD',
  OnePlusEld = 'OnePlusELD',
  OptimaEld = 'OptimaELD',
  PhoenixEld = 'PhoenixELD',
  ReliableEld = 'ReliableELD',
  SwiftEld = 'SwiftELD',
  Tmeld = 'TMELD',
  TrackEnsureInc = 'TrackEnsureInc',
  UnityEld = 'UnityELD',
  VistaEld = 'VistaELD',
  VLogEld = 'VLogELD',
  WorldTruckingEld = 'WorldTruckingELD',
  Xeld = 'XELD',
}

/**
 *
 * @export
 * @interface TelematicsInfo
 */
export interface TelematicsInfo {
  /**
   *
   * @type {string}
   * @memberof TelematicsInfo
   */
  name: string;
  /**
   *
   * @type {string}
   * @memberof TelematicsInfo
   */
  email?: string;
  /**
   *
   * @type {string}
   * @memberof TelematicsInfo
   */
  linkEmailedAt?: string;
  /**
   *
   * @type {string}
   * @memberof TelematicsInfo
   */
  link: string;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum TrailerType {
  Invalid = 'Invalid',
  SemiTrailers = 'SemiTrailers',
  FullTrailers = 'FullTrailers',
  ServiceUtilityTrailers = 'ServiceUtilityTrailers',
  CustomHarvesterTrailers = 'CustomHarvesterTrailers',
}

/**
 * Two character short code for the US state the driver is licensed in.
 * @export
 * @enum {string}
 */

export enum USState {
  Al = 'AL',
  Ak = 'AK',
  Az = 'AZ',
  Ar = 'AR',
  Ca = 'CA',
  Co = 'CO',
  Ct = 'CT',
  De = 'DE',
  Dc = 'DC',
  Fl = 'FL',
  Ga = 'GA',
  Hi = 'HI',
  Id = 'ID',
  Il = 'IL',
  In = 'IN',
  Ia = 'IA',
  Ks = 'KS',
  Ky = 'KY',
  La = 'LA',
  Me = 'ME',
  Md = 'MD',
  Ma = 'MA',
  Mi = 'MI',
  Mn = 'MN',
  Ms = 'MS',
  Mo = 'MO',
  Mt = 'MT',
  Ne = 'NE',
  Nv = 'NV',
  Nh = 'NH',
  Nj = 'NJ',
  Nm = 'NM',
  Ny = 'NY',
  Nc = 'NC',
  Nd = 'ND',
  Oh = 'OH',
  Ok = 'OK',
  Or = 'OR',
  Pa = 'PA',
  Ri = 'RI',
  Sc = 'SC',
  Sd = 'SD',
  Tn = 'TN',
  Tx = 'TX',
  Ut = 'UT',
  Vt = 'VT',
  Va = 'VA',
  Wa = 'WA',
  Wv = 'WV',
  Wi = 'WI',
  Wy = 'WY',
}

/**
 * Underwriting factor overrides applied by underwriters
 * @export
 * @interface UnderwritingOverrides
 */
export interface UnderwritingOverrides {
  /**
   * Whether loss free credit is applied for AL coverage
   * @type {boolean}
   * @memberof UnderwritingOverrides
   */
  alLossFreeCredit?: boolean;
  /**
   * Whether loss free credit is applied for APD coverage
   * @type {boolean}
   * @memberof UnderwritingOverrides
   */
  apdLossFreeCredit?: boolean;
  /**
   * Verifiable experience period with no third party losses (in months)
   * @type {number}
   * @memberof UnderwritingOverrides
   */
  verifiableExperiencePeriod?: number;
  /**
   * Percentage of loss free credit applied
   * @type {number}
   * @memberof UnderwritingOverrides
   */
  lossFreeCreditPercentage?: number;
  /**
   *
   * @type {DriverFactor}
   * @memberof UnderwritingOverrides
   */
  driverFactor?: DriverFactor;
  /**
   *
   * @type {ScheduleMod}
   * @memberof UnderwritingOverrides
   */
  scheduleMods?: ScheduleMod;
  /**
   *
   * @type {ExperienceMod}
   * @memberof UnderwritingOverrides
   */
  experienceMod?: ExperienceMod;
  /**
   *
   * @type {QualityRatingGrade}
   * @memberof UnderwritingOverrides
   */
  qualityRating?: QualityRatingGrade;
  /**
   *
   * @type {Array<AncillaryCoverage>}
   * @memberof UnderwritingOverrides
   */
  ancillaryCoverages?: Array<AncillaryCoverage>;
}
/**
 *
 * @export
 * @interface VehicleDeductible
 */
export interface VehicleDeductible {
  /**
   * Vehicle Identification Number
   * @type {string}
   * @memberof VehicleDeductible
   */
  vin: string;
  /**
   * Selected deductible amount in cents for this specific vehicle
   * @type {number}
   * @memberof VehicleDeductible
   */
  selectedDeductible: number;
}
/**
 *
 * @export
 * @interface VehicleInfo
 */
export interface VehicleInfo {
  /**
   *
   * @type {string}
   * @memberof VehicleInfo
   */
  vin: string;
  /**
   *
   * @type {number}
   * @memberof VehicleInfo
   */
  year: number;
  /**
   *
   * @type {string}
   * @memberof VehicleInfo
   */
  make: string;
  /**
   *
   * @type {string}
   * @memberof VehicleInfo
   */
  model: string;
  /**
   *
   * @type {VehicleType}
   * @memberof VehicleInfo
   */
  vehicleType: VehicleType;
  /**
   *
   * @type {WeightClass}
   * @memberof VehicleInfo
   */
  weightClass: WeightClass;
  /**
   *
   * @type {SpecialtyVehicleType}
   * @memberof VehicleInfo
   */
  specialtyVehicleType: SpecialtyVehicleType;
  /**
   *
   * @type {VehicleUse}
   * @memberof VehicleInfo
   */
  vehicleUse: VehicleUse;
  /**
   *
   * @type {BusinessUse}
   * @memberof VehicleInfo
   */
  businessUse: BusinessUse;
  /**
   *
   * @type {TrailerType}
   * @memberof VehicleInfo
   */
  trailerType?: TrailerType;
  /**
   *
   * @type {StateUsage}
   * @memberof VehicleInfo
   */
  stateUsage: StateUsage;
  /**
   *
   * @type {RadiusClassification}
   * @memberof VehicleInfo
   */
  radiusClassification: RadiusClassification;
  /**
   *
   * @type {string}
   * @memberof VehicleInfo
   */
  principalGaragingLocationZipCode: string;
  /**
   *
   * @type {number}
   * @memberof VehicleInfo
   */
  apdDeductible?: number;
  /**
   *
   * @type {number}
   * @memberof VehicleInfo
   */
  statedValue?: number;
  /**
   *
   * @type {boolean}
   * @memberof VehicleInfo
   */
  isGlassLinedTankTruckOrTrailer?: boolean;
  /**
   *
   * @type {boolean}
   * @memberof VehicleInfo
   */
  isRefrigeratedTruckOrTrailer?: boolean;
  /**
   *
   * @type {boolean}
   * @memberof VehicleInfo
   */
  isDoubleTrailer?: boolean;
  /**
   *
   * @type {boolean}
   * @memberof VehicleInfo
   */
  hasAntiLockBrakes?: boolean;
}
/**
 *
 * @export
 * @interface VehicleLimit
 */
export interface VehicleLimit {
  /**
   * Vehicle Identification Number
   * @type {string}
   * @memberof VehicleLimit
   */
  vin: string;
  /**
   * Selected limit amount in cents for this specific vehicle
   * @type {number}
   * @memberof VehicleLimit
   */
  selectedLimit: number;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum VehicleType {
  Invalid = 'Invalid',
  Truck = 'Truck',
  Tractor = 'Tractor',
  Trailer = 'Trailer',
}

/**
 *
 * @export
 * @enum {string}
 */

export enum VehicleUse {
  Invalid = 'Invalid',
  TowingOperations = 'TowingOperations',
  DumpingOperations = 'DumpingOperations',
  LoggingOperations = 'LoggingOperations',
  OtherOperations = 'OtherOperations',
}

/**
 *
 * @export
 * @enum {string}
 */

export enum WeightClass {
  /**
   * Invalid
   */
  Invalid = 'Invalid',
  /**
   * GVW &lt;&#x3D;10,000 lbs
   */
  Light = 'Light',
  /**
   * GVW 10,001 - 20,000 lbs
   */
  Medium = 'Medium',
  /**
   * GVW 20,001 - 45000 lbs
   */
  Heavy = 'Heavy',
  /**
   * GVW &gt; 45000 lbs
   */
  ExtraHeavy = 'ExtraHeavy',
}

/**
 * BusinessAutoApi - axios parameter creator
 * @export
 */
export const BusinessAutoApiAxiosParamCreator = function (
  configuration?: Configuration,
) {
  return {
    /**
     * Creates a new business auto insurance application
     * @param {PostBusinessAutoAppRequest} postBusinessAutoAppRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    createBusinessAutoApplication: async (
      postBusinessAutoAppRequest: PostBusinessAutoAppRequest,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'postBusinessAutoAppRequest' is not null or undefined
      assertParamExists(
        'createBusinessAutoApplication',
        'postBusinessAutoAppRequest',
        postBusinessAutoAppRequest,
      );
      const localVarPath = `/nirvana/v0/business-auto/application`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        postBusinessAutoAppRequest,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get the list of available producers for business auto applications
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getAvailableProducers: async (
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      const localVarPath = `/nirvana/v0/business-auto/available_producers`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Retrieves a business auto insurance application by ID
     * @param {string} applicationId The unique identifier of the business auto application
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getBusinessAutoApplication: async (
      applicationId: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationId' is not null or undefined
      assertParamExists(
        'getBusinessAutoApplication',
        'applicationId',
        applicationId,
      );
      const localVarPath =
        `/nirvana/v0/business-auto/application/{applicationId}`.replace(
          `{${'applicationId'}}`,
          encodeURIComponent(String(applicationId)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * List all business auto insurance applications
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getBusinessAutoApplicationList: async (
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      const localVarPath = `/nirvana/v0/business-auto/application/list`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Returns coverage options including vehicle-level limits and deductibles for selected coverages in a business auto insurance application
     * @param {string} applicationId The unique identifier of the business auto application
     * @param {GetCoverageOptionsRequestBody} getCoverageOptionsRequestBody
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getBusinessAutoCoverageOptions: async (
      applicationId: string,
      getCoverageOptionsRequestBody: GetCoverageOptionsRequestBody,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationId' is not null or undefined
      assertParamExists(
        'getBusinessAutoCoverageOptions',
        'applicationId',
        applicationId,
      );
      // verify required parameter 'getCoverageOptionsRequestBody' is not null or undefined
      assertParamExists(
        'getBusinessAutoCoverageOptions',
        'getCoverageOptionsRequestBody',
        getCoverageOptionsRequestBody,
      );
      const localVarPath =
        `/nirvana/v0/business-auto/application/{applicationId}/coverage-options`.replace(
          `{${'applicationId'}}`,
          encodeURIComponent(String(applicationId)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        getCoverageOptionsRequestBody,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Returns pricing information for a business auto insurance application
     * @param {string} applicationId The unique identifier of the business auto application
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getBusinessAutoPricingInfo: async (
      applicationId: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationId' is not null or undefined
      assertParamExists(
        'getBusinessAutoPricingInfo',
        'applicationId',
        applicationId,
      );
      const localVarPath =
        `/nirvana/v0/business-auto/application/{applicationId}/price`.replace(
          `{${'applicationId'}}`,
          encodeURIComponent(String(applicationId)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Returns pricing status information for a business auto insurance application
     * @param {string} applicationId The unique identifier of the business auto application
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getBusinessAutoPricingStatus: async (
      applicationId: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationId' is not null or undefined
      assertParamExists(
        'getBusinessAutoPricingStatus',
        'applicationId',
        applicationId,
      );
      const localVarPath =
        `/nirvana/v0/business-auto/application/{applicationId}/pricing-status`.replace(
          `{${'applicationId'}}`,
          encodeURIComponent(String(applicationId)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @summary Trigger a business auto pricing job
     * @param {string} applicationId The unique identifier of the business auto application
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    postTriggerPricingJob: async (
      applicationId: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationId' is not null or undefined
      assertParamExists(
        'postTriggerPricingJob',
        'applicationId',
        applicationId,
      );
      const localVarPath =
        `/nirvana/v0/business-auto/application/{applicationId}/trigger_pricing_job`.replace(
          `{${'applicationId'}}`,
          encodeURIComponent(String(applicationId)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Updates an existing business auto insurance application
     * @param {string} applicationId The unique identifier of the business auto application
     * @param {PutBusinessAutoAppRequest} putBusinessAutoAppRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    putBusinessAutoApplication: async (
      applicationId: string,
      putBusinessAutoAppRequest: PutBusinessAutoAppRequest,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationId' is not null or undefined
      assertParamExists(
        'putBusinessAutoApplication',
        'applicationId',
        applicationId,
      );
      // verify required parameter 'putBusinessAutoAppRequest' is not null or undefined
      assertParamExists(
        'putBusinessAutoApplication',
        'putBusinessAutoAppRequest',
        putBusinessAutoAppRequest,
      );
      const localVarPath =
        `/nirvana/v0/business-auto/application/{applicationId}`.replace(
          `{${'applicationId'}}`,
          encodeURIComponent(String(applicationId)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        putBusinessAutoAppRequest,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Performs a state transition on a business auto insurance application
     * @param {string} applicationId The unique identifier of the business auto application
     * @param {PatchStateTransitionRequest} patchStateTransitionRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    stateTransitionBusinessAutoApplication: async (
      applicationId: string,
      patchStateTransitionRequest: PatchStateTransitionRequest,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationId' is not null or undefined
      assertParamExists(
        'stateTransitionBusinessAutoApplication',
        'applicationId',
        applicationId,
      );
      // verify required parameter 'patchStateTransitionRequest' is not null or undefined
      assertParamExists(
        'stateTransitionBusinessAutoApplication',
        'patchStateTransitionRequest',
        patchStateTransitionRequest,
      );
      const localVarPath =
        `/nirvana/v0/business-auto/application/{applicationId}/state_transition`.replace(
          `{${'applicationId'}}`,
          encodeURIComponent(String(applicationId)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PATCH',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        patchStateTransitionRequest,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Updates pre-bind information (FEIN or DOT number) for an existing business auto insurance application
     * @param {string} applicationId The unique identifier of the business auto application
     * @param {PatchPreBindInfoRequest} patchPreBindInfoRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateBusinessAutoApplicationPreBindInfo: async (
      applicationId: string,
      patchPreBindInfoRequest: PatchPreBindInfoRequest,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationId' is not null or undefined
      assertParamExists(
        'updateBusinessAutoApplicationPreBindInfo',
        'applicationId',
        applicationId,
      );
      // verify required parameter 'patchPreBindInfoRequest' is not null or undefined
      assertParamExists(
        'updateBusinessAutoApplicationPreBindInfo',
        'patchPreBindInfoRequest',
        patchPreBindInfoRequest,
      );
      const localVarPath =
        `/nirvana/v0/business-auto/application/{applicationId}/pre-bind-info`.replace(
          `{${'applicationId'}}`,
          encodeURIComponent(String(applicationId)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PATCH',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        patchPreBindInfoRequest,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Updates underwriting overrides for an existing business auto insurance application
     * @param {string} applicationId The unique identifier of the business auto application
     * @param {PatchUnderwritingOverridesRequest} patchUnderwritingOverridesRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateBusinessAutoApplicationUnderwritingOverrides: async (
      applicationId: string,
      patchUnderwritingOverridesRequest: PatchUnderwritingOverridesRequest,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationId' is not null or undefined
      assertParamExists(
        'updateBusinessAutoApplicationUnderwritingOverrides',
        'applicationId',
        applicationId,
      );
      // verify required parameter 'patchUnderwritingOverridesRequest' is not null or undefined
      assertParamExists(
        'updateBusinessAutoApplicationUnderwritingOverrides',
        'patchUnderwritingOverridesRequest',
        patchUnderwritingOverridesRequest,
      );
      const localVarPath =
        `/nirvana/v0/business-auto/application/{applicationId}/underwriting-overrides`.replace(
          `{${'applicationId'}}`,
          encodeURIComponent(String(applicationId)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PATCH',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        patchUnderwritingOverridesRequest,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * BusinessAutoApi - functional programming interface
 * @export
 */
export const BusinessAutoApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    BusinessAutoApiAxiosParamCreator(configuration);
  return {
    /**
     * Creates a new business auto insurance application
     * @param {PostBusinessAutoAppRequest} postBusinessAutoAppRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async createBusinessAutoApplication(
      postBusinessAutoAppRequest: PostBusinessAutoAppRequest,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<PostBusinessAutoAppResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.createBusinessAutoApplication(
          postBusinessAutoAppRequest,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Get the list of available producers for business auto applications
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getAvailableProducers(
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<Array<Producer>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getAvailableProducers(options);
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Retrieves a business auto insurance application by ID
     * @param {string} applicationId The unique identifier of the business auto application
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getBusinessAutoApplication(
      applicationId: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<GetBusinessAutoAppResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getBusinessAutoApplication(
          applicationId,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * List all business auto insurance applications
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getBusinessAutoApplicationList(
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<Array<BusinessAutoApplicationListRecord>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getBusinessAutoApplicationList(options);
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Returns coverage options including vehicle-level limits and deductibles for selected coverages in a business auto insurance application
     * @param {string} applicationId The unique identifier of the business auto application
     * @param {GetCoverageOptionsRequestBody} getCoverageOptionsRequestBody
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getBusinessAutoCoverageOptions(
      applicationId: string,
      getCoverageOptionsRequestBody: GetCoverageOptionsRequestBody,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<Array<CoverageOption>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getBusinessAutoCoverageOptions(
          applicationId,
          getCoverageOptionsRequestBody,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Returns pricing information for a business auto insurance application
     * @param {string} applicationId The unique identifier of the business auto application
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getBusinessAutoPricingInfo(
      applicationId: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<GetPricingResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getBusinessAutoPricingInfo(
          applicationId,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Returns pricing status information for a business auto insurance application
     * @param {string} applicationId The unique identifier of the business auto application
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getBusinessAutoPricingStatus(
      applicationId: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<GetPricingStatusResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getBusinessAutoPricingStatus(
          applicationId,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     *
     * @summary Trigger a business auto pricing job
     * @param {string} applicationId The unique identifier of the business auto application
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async postTriggerPricingJob(
      applicationId: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<PostTriggerPricingJobResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.postTriggerPricingJob(
          applicationId,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Updates an existing business auto insurance application
     * @param {string} applicationId The unique identifier of the business auto application
     * @param {PutBusinessAutoAppRequest} putBusinessAutoAppRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async putBusinessAutoApplication(
      applicationId: string,
      putBusinessAutoAppRequest: PutBusinessAutoAppRequest,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.putBusinessAutoApplication(
          applicationId,
          putBusinessAutoAppRequest,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Performs a state transition on a business auto insurance application
     * @param {string} applicationId The unique identifier of the business auto application
     * @param {PatchStateTransitionRequest} patchStateTransitionRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async stateTransitionBusinessAutoApplication(
      applicationId: string,
      patchStateTransitionRequest: PatchStateTransitionRequest,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<PatchStateTransitionResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.stateTransitionBusinessAutoApplication(
          applicationId,
          patchStateTransitionRequest,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Updates pre-bind information (FEIN or DOT number) for an existing business auto insurance application
     * @param {string} applicationId The unique identifier of the business auto application
     * @param {PatchPreBindInfoRequest} patchPreBindInfoRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async updateBusinessAutoApplicationPreBindInfo(
      applicationId: string,
      patchPreBindInfoRequest: PatchPreBindInfoRequest,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<GetBusinessAutoAppResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.updateBusinessAutoApplicationPreBindInfo(
          applicationId,
          patchPreBindInfoRequest,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Updates underwriting overrides for an existing business auto insurance application
     * @param {string} applicationId The unique identifier of the business auto application
     * @param {PatchUnderwritingOverridesRequest} patchUnderwritingOverridesRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async updateBusinessAutoApplicationUnderwritingOverrides(
      applicationId: string,
      patchUnderwritingOverridesRequest: PatchUnderwritingOverridesRequest,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<GetBusinessAutoAppResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.updateBusinessAutoApplicationUnderwritingOverrides(
          applicationId,
          patchUnderwritingOverridesRequest,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
  };
};

/**
 * BusinessAutoApi - factory interface
 * @export
 */
export const BusinessAutoApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance,
) {
  const localVarFp = BusinessAutoApiFp(configuration);
  return {
    /**
     * Creates a new business auto insurance application
     * @param {PostBusinessAutoAppRequest} postBusinessAutoAppRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    createBusinessAutoApplication(
      postBusinessAutoAppRequest: PostBusinessAutoAppRequest,
      options?: any,
    ): AxiosPromise<PostBusinessAutoAppResponse> {
      return localVarFp
        .createBusinessAutoApplication(postBusinessAutoAppRequest, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get the list of available producers for business auto applications
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getAvailableProducers(options?: any): AxiosPromise<Array<Producer>> {
      return localVarFp
        .getAvailableProducers(options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Retrieves a business auto insurance application by ID
     * @param {string} applicationId The unique identifier of the business auto application
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getBusinessAutoApplication(
      applicationId: string,
      options?: any,
    ): AxiosPromise<GetBusinessAutoAppResponse> {
      return localVarFp
        .getBusinessAutoApplication(applicationId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * List all business auto insurance applications
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getBusinessAutoApplicationList(
      options?: any,
    ): AxiosPromise<Array<BusinessAutoApplicationListRecord>> {
      return localVarFp
        .getBusinessAutoApplicationList(options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Returns coverage options including vehicle-level limits and deductibles for selected coverages in a business auto insurance application
     * @param {string} applicationId The unique identifier of the business auto application
     * @param {GetCoverageOptionsRequestBody} getCoverageOptionsRequestBody
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getBusinessAutoCoverageOptions(
      applicationId: string,
      getCoverageOptionsRequestBody: GetCoverageOptionsRequestBody,
      options?: any,
    ): AxiosPromise<Array<CoverageOption>> {
      return localVarFp
        .getBusinessAutoCoverageOptions(
          applicationId,
          getCoverageOptionsRequestBody,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Returns pricing information for a business auto insurance application
     * @param {string} applicationId The unique identifier of the business auto application
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getBusinessAutoPricingInfo(
      applicationId: string,
      options?: any,
    ): AxiosPromise<GetPricingResponse> {
      return localVarFp
        .getBusinessAutoPricingInfo(applicationId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Returns pricing status information for a business auto insurance application
     * @param {string} applicationId The unique identifier of the business auto application
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getBusinessAutoPricingStatus(
      applicationId: string,
      options?: any,
    ): AxiosPromise<GetPricingStatusResponse> {
      return localVarFp
        .getBusinessAutoPricingStatus(applicationId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary Trigger a business auto pricing job
     * @param {string} applicationId The unique identifier of the business auto application
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    postTriggerPricingJob(
      applicationId: string,
      options?: any,
    ): AxiosPromise<PostTriggerPricingJobResponse> {
      return localVarFp
        .postTriggerPricingJob(applicationId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Updates an existing business auto insurance application
     * @param {string} applicationId The unique identifier of the business auto application
     * @param {PutBusinessAutoAppRequest} putBusinessAutoAppRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    putBusinessAutoApplication(
      applicationId: string,
      putBusinessAutoAppRequest: PutBusinessAutoAppRequest,
      options?: any,
    ): AxiosPromise<void> {
      return localVarFp
        .putBusinessAutoApplication(
          applicationId,
          putBusinessAutoAppRequest,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Performs a state transition on a business auto insurance application
     * @param {string} applicationId The unique identifier of the business auto application
     * @param {PatchStateTransitionRequest} patchStateTransitionRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    stateTransitionBusinessAutoApplication(
      applicationId: string,
      patchStateTransitionRequest: PatchStateTransitionRequest,
      options?: any,
    ): AxiosPromise<PatchStateTransitionResponse> {
      return localVarFp
        .stateTransitionBusinessAutoApplication(
          applicationId,
          patchStateTransitionRequest,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Updates pre-bind information (FEIN or DOT number) for an existing business auto insurance application
     * @param {string} applicationId The unique identifier of the business auto application
     * @param {PatchPreBindInfoRequest} patchPreBindInfoRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateBusinessAutoApplicationPreBindInfo(
      applicationId: string,
      patchPreBindInfoRequest: PatchPreBindInfoRequest,
      options?: any,
    ): AxiosPromise<GetBusinessAutoAppResponse> {
      return localVarFp
        .updateBusinessAutoApplicationPreBindInfo(
          applicationId,
          patchPreBindInfoRequest,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Updates underwriting overrides for an existing business auto insurance application
     * @param {string} applicationId The unique identifier of the business auto application
     * @param {PatchUnderwritingOverridesRequest} patchUnderwritingOverridesRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateBusinessAutoApplicationUnderwritingOverrides(
      applicationId: string,
      patchUnderwritingOverridesRequest: PatchUnderwritingOverridesRequest,
      options?: any,
    ): AxiosPromise<GetBusinessAutoAppResponse> {
      return localVarFp
        .updateBusinessAutoApplicationUnderwritingOverrides(
          applicationId,
          patchUnderwritingOverridesRequest,
          options,
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * BusinessAutoApi - object-oriented interface
 * @export
 * @class BusinessAutoApi
 * @extends {BaseAPI}
 */
export class BusinessAutoApi extends BaseAPI {
  /**
   * Creates a new business auto insurance application
   * @param {PostBusinessAutoAppRequest} postBusinessAutoAppRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BusinessAutoApi
   */
  public createBusinessAutoApplication(
    postBusinessAutoAppRequest: PostBusinessAutoAppRequest,
    options?: AxiosRequestConfig,
  ) {
    return BusinessAutoApiFp(this.configuration)
      .createBusinessAutoApplication(postBusinessAutoAppRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get the list of available producers for business auto applications
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BusinessAutoApi
   */
  public getAvailableProducers(options?: AxiosRequestConfig) {
    return BusinessAutoApiFp(this.configuration)
      .getAvailableProducers(options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Retrieves a business auto insurance application by ID
   * @param {string} applicationId The unique identifier of the business auto application
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BusinessAutoApi
   */
  public getBusinessAutoApplication(
    applicationId: string,
    options?: AxiosRequestConfig,
  ) {
    return BusinessAutoApiFp(this.configuration)
      .getBusinessAutoApplication(applicationId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * List all business auto insurance applications
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BusinessAutoApi
   */
  public getBusinessAutoApplicationList(options?: AxiosRequestConfig) {
    return BusinessAutoApiFp(this.configuration)
      .getBusinessAutoApplicationList(options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Returns coverage options including vehicle-level limits and deductibles for selected coverages in a business auto insurance application
   * @param {string} applicationId The unique identifier of the business auto application
   * @param {GetCoverageOptionsRequestBody} getCoverageOptionsRequestBody
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BusinessAutoApi
   */
  public getBusinessAutoCoverageOptions(
    applicationId: string,
    getCoverageOptionsRequestBody: GetCoverageOptionsRequestBody,
    options?: AxiosRequestConfig,
  ) {
    return BusinessAutoApiFp(this.configuration)
      .getBusinessAutoCoverageOptions(
        applicationId,
        getCoverageOptionsRequestBody,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Returns pricing information for a business auto insurance application
   * @param {string} applicationId The unique identifier of the business auto application
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BusinessAutoApi
   */
  public getBusinessAutoPricingInfo(
    applicationId: string,
    options?: AxiosRequestConfig,
  ) {
    return BusinessAutoApiFp(this.configuration)
      .getBusinessAutoPricingInfo(applicationId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Returns pricing status information for a business auto insurance application
   * @param {string} applicationId The unique identifier of the business auto application
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BusinessAutoApi
   */
  public getBusinessAutoPricingStatus(
    applicationId: string,
    options?: AxiosRequestConfig,
  ) {
    return BusinessAutoApiFp(this.configuration)
      .getBusinessAutoPricingStatus(applicationId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary Trigger a business auto pricing job
   * @param {string} applicationId The unique identifier of the business auto application
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BusinessAutoApi
   */
  public postTriggerPricingJob(
    applicationId: string,
    options?: AxiosRequestConfig,
  ) {
    return BusinessAutoApiFp(this.configuration)
      .postTriggerPricingJob(applicationId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Updates an existing business auto insurance application
   * @param {string} applicationId The unique identifier of the business auto application
   * @param {PutBusinessAutoAppRequest} putBusinessAutoAppRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BusinessAutoApi
   */
  public putBusinessAutoApplication(
    applicationId: string,
    putBusinessAutoAppRequest: PutBusinessAutoAppRequest,
    options?: AxiosRequestConfig,
  ) {
    return BusinessAutoApiFp(this.configuration)
      .putBusinessAutoApplication(
        applicationId,
        putBusinessAutoAppRequest,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Performs a state transition on a business auto insurance application
   * @param {string} applicationId The unique identifier of the business auto application
   * @param {PatchStateTransitionRequest} patchStateTransitionRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BusinessAutoApi
   */
  public stateTransitionBusinessAutoApplication(
    applicationId: string,
    patchStateTransitionRequest: PatchStateTransitionRequest,
    options?: AxiosRequestConfig,
  ) {
    return BusinessAutoApiFp(this.configuration)
      .stateTransitionBusinessAutoApplication(
        applicationId,
        patchStateTransitionRequest,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Updates pre-bind information (FEIN or DOT number) for an existing business auto insurance application
   * @param {string} applicationId The unique identifier of the business auto application
   * @param {PatchPreBindInfoRequest} patchPreBindInfoRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BusinessAutoApi
   */
  public updateBusinessAutoApplicationPreBindInfo(
    applicationId: string,
    patchPreBindInfoRequest: PatchPreBindInfoRequest,
    options?: AxiosRequestConfig,
  ) {
    return BusinessAutoApiFp(this.configuration)
      .updateBusinessAutoApplicationPreBindInfo(
        applicationId,
        patchPreBindInfoRequest,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Updates underwriting overrides for an existing business auto insurance application
   * @param {string} applicationId The unique identifier of the business auto application
   * @param {PatchUnderwritingOverridesRequest} patchUnderwritingOverridesRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BusinessAutoApi
   */
  public updateBusinessAutoApplicationUnderwritingOverrides(
    applicationId: string,
    patchUnderwritingOverridesRequest: PatchUnderwritingOverridesRequest,
    options?: AxiosRequestConfig,
  ) {
    return BusinessAutoApiFp(this.configuration)
      .updateBusinessAutoApplicationUnderwritingOverrides(
        applicationId,
        patchUnderwritingOverridesRequest,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }
}
