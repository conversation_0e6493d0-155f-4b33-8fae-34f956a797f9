import { useContext, useEffect, useRef, useState, ReactNode } from 'react';
import { HiArrowLeft, HiArrowRight } from 'react-icons/hi';
import {
  <PERSON><PERSON>,
  AppBar,
  Button,
  CircularProgress,
  Container,
} from '@material-ui/core';
import { HorizontalStepper, VerticalStepper, Show } from '@nirvana/ui-kit';

import { ClaimsProvider } from '../../../../../apps/support/src/types/graphql-types';
import { ClaimType, NewClaimContext } from './contexts/newClaim';

import { Step, STEPS } from './constants';
import {
  IncidentDetails,
  ReporterInformation,
  ReviewAndSubmit,
  Overview,
} from './steps';
import SubmittedSuccessfully from './SubmittedSuccessfully';

const STEP_INDEX_REVIEW_AND_SUBMIT = 2;

export const NewClaimForm = ({
  useProvider = () => ({ getProvider: () => Promise.resolve(null) }),
  editingExistingDraft = false,
  onSubmit,
  onSaveDraft = () => {},
  onInvalidForm,
  isSubmittingFNOL,
  isSavingDraft = false,
  saveDraftEnabled = false,
  submittedSuccessfully,
  formType = 'insured',
  withSandboxToggle,
}: {
  useProvider?: () => {
    getProvider: (
      policyNumber: string,
      isSandboxFlow: boolean,
    ) => Promise<ClaimsProvider | null>;
  };
  editingExistingDraft?: boolean;
  onSubmit: (data: ClaimType) => void;
  onSaveDraft?: (data: ClaimType) => void;
  onInvalidForm: () => void;
  isSubmittingFNOL: boolean;
  isSavingDraft?: boolean;
  saveDraftEnabled?: boolean;
  submittedSuccessfully: boolean;
  formType?: 'insured' | 'support';
  withSandboxToggle: boolean;
}) => {
  const {
    canSubmit,
    prefillingForm,
    getValues,
    handleSubmit,
    isArchived,
    isLoadingDraftFnol,
    fnolId,
  } = useContext(NewClaimContext);
  const componentRef = useRef<null | HTMLDivElement>(null);
  const [currentStepIndex, setCurrentStepIndex] = useState<number>(-1);
  const [savedAsDraft, setSavedAsDraft] = useState<boolean>(false);

  const currentStep = STEPS[currentStepIndex];

  useEffect(() => {
    if (editingExistingDraft) {
      setCurrentStepIndex(STEP_INDEX_REVIEW_AND_SUBMIT);
    }
  }, [editingExistingDraft]);

  const scrollToTop = () => {
    if (componentRef.current) {
      componentRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
  };

  const policyNumberInputTypeFromFormType =
    formType === 'insured' ? 'select' : 'autocomplete';

  const screens: Record<Step, ReactNode> = {
    'incident-details': (
      <IncidentDetails
        policyNumberInputType={policyNumberInputTypeFromFormType}
        withSandboxToggle={withSandboxToggle}
      />
    ),
    'reporter-information': <ReporterInformation />,
    'review-and-submit': (
      <ReviewAndSubmit
        useProvider={useProvider}
        formType={formType}
        onEdit={(step) => {
          const index = STEPS.findIndex((s) => s.key === step);
          setCurrentStepIndex(index);
          scrollToTop();
        }}
      />
    ),
  };

  // If fnolId is not undefined || null || empty, it means the FNOL has already been submitted, and we should not
  // allow the user to submit it again
  const isFnolSubmitted = Boolean(fnolId);

  return (
    <>
      <Show when={isFnolSubmitted || isArchived}>
        <Alert
          action={
            <div>
              <a href="/claims/new-fnol">
                <Button color="inherit" size="small">
                  New FNOL
                </Button>
              </a>
              <a href="/claims/fnol?fnolType=submitted">
                <Button color="inherit" size="small">
                  All FNOLs
                </Button>
              </a>
            </div>
          }
          severity={isFnolSubmitted ? 'success' : 'info'}
        >
          {isFnolSubmitted
            ? 'This FNOL has been submitted.'
            : 'This FNOL has been archived.'}
        </Alert>
      </Show>

      <Show
        when={!submittedSuccessfully}
        fallback={<SubmittedSuccessfully isDraft={savedAsDraft} />}
      >
        <div className="flex flex-col mt-8 md:flex-row" ref={componentRef}>
          <Show
            when={!isLoadingDraftFnol && !prefillingForm}
            fallback={
              <div className="flex justify-center items-center w-full h-full">
                <CircularProgress />
              </div>
            }
          >
            <div>
              <Show when={currentStepIndex >= 0}>
                <div className="flex-1 p-4 pt-4 mb-4 overflow-auto border-none rounded-lg shadow-none bg-primary-extraLight md:pl-8 md:pr-20 md:py-10">
                  <div className="lg:hidden">
                    <HorizontalStepper
                      dataTestId="horizontal-stepper"
                      steps={STEPS}
                      activeStep={currentStepIndex}
                      onChange={(selectedStepIndex) => {
                        !isFnolSubmitted &&
                          setCurrentStepIndex(selectedStepIndex);
                      }}
                    />
                  </div>
                  <div className="hidden lg:block">
                    <VerticalStepper
                      dataTestId="vertical-stepper"
                      steps={STEPS}
                      activeStep={currentStepIndex}
                      onChange={(selectedStepIndex) => {
                        !isFnolSubmitted &&
                          setCurrentStepIndex(selectedStepIndex);
                      }}
                    />
                  </div>
                </div>
              </Show>
            </div>

            <div className="w-full p-8 pt-0 md:p-10 md:pt-0">
              <Show when={currentStepIndex < 0}>
                <Overview />
              </Show>

              <Show when={currentStepIndex >= 0}>
                <div className="w-full">
                  <div className="flex flex-col">
                    {screens[currentStep?.key]}
                  </div>
                </div>
              </Show>
            </div>
          </Show>
        </div>

        <AppBar
          color="inherit"
          elevation={1}
          position="fixed"
          sx={{ top: 'auto', bottom: 0, py: 1 }}
        >
          <Container className="flex justify-end">
            <form onSubmit={handleSubmit(onSubmit, onInvalidForm)}>
              <Show when={currentStepIndex > 0}>
                <Button
                  className="mr-4"
                  data-testid="previous-button"
                  disabled={isFnolSubmitted}
                  type="button"
                  variant="outlined"
                  onClick={() => {
                    setCurrentStepIndex(Math.max(currentStepIndex - 1, 0));
                    scrollToTop();
                  }}
                  startIcon={<HiArrowLeft />}
                >
                  Previous
                </Button>
              </Show>

              <Show when={saveDraftEnabled && currentStepIndex >= 0}>
                <Button
                  className="mr-4"
                  disabled={isSavingDraft || isFnolSubmitted}
                  onClick={() => {
                    setSavedAsDraft(true);
                    onSaveDraft(getValues());
                  }}
                  variant="outlined"
                >
                  Save as draft
                  <Show when={isSavingDraft}>
                    <CircularProgress className="absolute" size={24} />
                  </Show>
                </Button>
              </Show>

              <Show when={currentStepIndex !== STEPS.length - 1}>
                <Button
                  className="mr-16 lg:mr-8"
                  data-testid="next-button"
                  type="button"
                  variant="contained"
                  onClick={() => {
                    setCurrentStepIndex(
                      Math.min(currentStepIndex + 1, STEPS.length - 1),
                    );
                    scrollToTop();
                  }}
                  endIcon={<HiArrowRight />}
                  // Always allow to click on Start
                  disabled={!canSubmit && currentStepIndex >= 0}
                >
                  {currentStepIndex < 0 ? 'Start' : 'Proceed'}
                </Button>
              </Show>

              <Show when={currentStepIndex === STEPS.length - 1}>
                <Button
                  className="mr-16 lg:mr-8"
                  data-testid="submit-button"
                  disabled={isSubmittingFNOL || !canSubmit || isFnolSubmitted}
                  type="submit"
                  variant="contained"
                >
                  Submit
                  <Show when={isSubmittingFNOL}>
                    <CircularProgress className="absolute" size={24} />
                  </Show>
                </Button>
              </Show>
            </form>
          </Container>
        </AppBar>
      </Show>
    </>
  );
};
