import {
  PrimaryIndustryClassification,
  SecondaryIndustryClassification,
} from '@nirvana/api/bizAuto';
import { constants } from '@nirvana/ui-kit';
import { z } from 'zod';
import { FormField } from '../../shared/components/form-builder';
import { createOptionsFromEnum } from '../utils';

const { usStates } = constants;

const MAX_PERCENTAGE = 100;

export const FormOperationsSchema = z.object({
  filingsInfo: z
    .object({
      hasSingleStateFilings: z.boolean().optional(),
      hasMultiStateFilings: z.boolean().optional(),
      hasFMCSAFilings: z.boolean().optional(),
      hasDOTFilings: z.boolean().optional(),
    })
    .optional(),
  namesInsured: z
    .string({
      required_error: 'Names Insured is required',
    })
    .optional(),
  companyInfo: z.object({
    name: z
      .string({
        required_error: 'Company Name is required',
      })
      .min(1, {
        message: 'Company Name cannot be empty',
      }),
    address: z
      .object({
        street: z.string().optional(),
        city: z.string().optional(),
        state: z.string().optional(),
        zip: z.string().optional(),
      })
      .optional(),
    primaryIndustryClassification: z.string().optional(),
    secondaryIndustryClassifications: z.array(z.string()).optional(),
    DOTNumber: z.number().optional(),
    FEIN: z.string().optional(),
    usState: z.string({
      required_error: 'US State is required',
    }),
    noOfPowerUnits: z
      .number({
        invalid_type_error: 'Number of Power Units must be a number',
      })
      .min(0, {
        message: 'Number of Power Units cannot be negative',
      })
      .optional(),
    annualCostOfHire: z
      .number({
        invalid_type_error: 'Annual Cost of Hire must be a number',
      })
      .min(0, {
        message: 'Annual Cost of Hire cannot be negative',
      })
      .optional(),
    hasIndividualNamedInsured: z.boolean().optional(),
    perOfEmployeesOperatingOwnAutos: z
      .number({
        invalid_type_error: 'Percentage of Owner Operators must be a number',
      })
      .min(0, {
        message: 'Percentage of Owner Operators cannot be negative',
      })
      .max(MAX_PERCENTAGE, {
        message: `Percentage of Owner Operators cannot exceed ${MAX_PERCENTAGE}%`,
      })
      .optional(),
    noOfEmployees: z
      .number({
        invalid_type_error: 'Number of Employees must be a number',
      })
      .min(0, {
        message: 'Number of Employees cannot be negative',
      })
      .optional(),
    maximumValueOfHiredAutos: z
      .number({
        invalid_type_error: 'Maximum Value of Hired Autos must be a number',
      })
      .min(0, {
        message: 'Maximum Value of Hired Autos cannot be negative',
      })
      .optional(),
  }),
});

export const FormOperations: FormField[] = [
  {
    id: 'filingStatus',
    label: 'Select all filing statuses needed',
    name: 'filingStatus',
    type: 'checkbox-group',
    options: [
      {
        name: 'filingsInfo.hasSingleStateFilings',
        value: '1',
        label: 'Single State',
      },
      {
        name: 'filingsInfo.hasMultiStateFilings',
        value: '2',
        label: 'Multi State',
      },
      { name: 'filingsInfo.hasFMCSAFilings', value: '3', label: 'FMCSA' },
      { name: 'filingsInfo.hasDOTFilings', value: '4', label: 'DOT' },
    ],
  },
  {
    id: 'hasIndividualNamedInsured',
    label: 'Are any individuals named insureds?',
    name: 'companyInfo.hasIndividualNamedInsured',
    type: 'radio-group',
    options: [
      { value: '1', label: 'Yes' },
      { value: '0', label: 'No' },
    ],
  },
  {
    id: 'primaryIndustryClassification',
    label: 'Primary Industry Classification',
    name: 'companyInfo.primaryIndustryClassification',
    type: 'dropdown',
    options: createOptionsFromEnum(PrimaryIndustryClassification),
  },
  {
    id: 'secondaryIndustryClassifications',
    label: 'Secondary Industry Classification',
    // TODO: Change this to a multi-select dropdown when supported
    name: 'companyInfo.secondaryIndustryClassifications',
    type: 'dropdown',
    options: createOptionsFromEnum(SecondaryIndustryClassification),
    onChangeTransform: (value) => {
      // Transform the value to an array of strings if needed
      return Array.isArray(value) ? value : [value];
    },
    valueTransform: (value) => {
      // Transform the value to a string if needed
      return Array.isArray(value) && value.length ? value[0] : value;
    },
  },
  {
    id: 'numberOfPowerUnits',
    label: '# of Power Units',
    name: 'companyInfo.noOfPowerUnits',
    type: 'number',
  },
  {
    id: 'costOfHireAnnual',
    label: 'Cost of Hire (Annual)',
    name: 'companyInfo.annualCostOfHire',
    type: 'number',
    placeholder: 'Enter annual cost of hire',
  },
  {
    id: 'percentageOfOwnerOperators',
    label: '% of employees operating own autos',
    name: 'companyInfo.perOfEmployeesOperatingOwnAutos',
    type: 'number',
    placeholder: 'Enter percentage of owner operators',
  },
  {
    id: 'numberOfEmployees',
    label: '# of Employees',
    name: 'companyInfo.noOfEmployees',
    type: 'number',
    placeholder: 'Enter number of employees',
  },
  {
    id: 'maximumValueOfHiredAutos',
    label: 'Maximum Value of Hired Autos',
    name: 'companyInfo.maximumValueOfHiredAutos',
    type: 'number',
    placeholder: 'Enter maximum value of hired autos',
  },
];
