import { CoverageType } from '@nirvana/api/bizAuto';
import { constants } from '@nirvana/ui-kit';
import { z } from 'zod';
import {
  FormField,
  FormFieldOption,
} from '../../shared/components/form-builder';

const { usStates } = constants;

export const FormCoveragesSchema = z.object({
  effectiveDate: z
    .string({ required_error: 'Effective Date is required' })
    .refine((date) => !isNaN(Date.parse(date)), {
      message: 'Effective Date must be a valid date',
    }),
  producerId: z.string({ required_error: 'Please select a producer' }),
  coveragesInfo: z.object({
    coverages: z
      .record(
        z.nativeEnum(CoverageType),
        z.object({
          coverageType: z.nativeEnum(CoverageType),
          limit: z.number().optional(),
          deductible: z.number().optional(),
          isEnabled: z.boolean(),
        }),
      )
      .optional(),
  }),
});

export const getFormCoverages = ({
  producers = [],
}: {
  producers: FormFieldOption[];
}): FormField[] => [
  {
    id: 'companyName',
    label: 'Company Name',
    name: 'companyInfo.name',
    type: 'text',
    placeholder: 'Enter company name',
  },
  {
    id: 'usState',
    label: 'Garaging State',
    name: 'companyInfo.usState',
    type: 'dropdown',
    options: usStates.map((state) => ({
      label: state.name,
      value: state.code,
    })),
  },
  {
    id: 'effectiveDate',
    label: 'Effective Date',
    name: 'effectiveDate',
    type: 'date',
  },
  {
    id: 'producer',
    label: 'Producer',
    name: 'producerId',
    type: 'dropdown',
    options: producers,
  },
  {
    id: 'dotNumber',
    label: 'DOT Number',
    name: 'companyInfo.DOTNumber',
    type: 'number',
    placeholder: 'Enter DOT Number',
  },
  {
    id: 'fein',
    label: 'FEIN',
    name: 'companyInfo.FEIN',
    type: 'text',
    placeholder: 'Enter FEIN',
  },
  {
    id: 'alCsl',
    label: 'AL CSL',
    name: `coveragesInfo.coverages.${CoverageType.CoverageAutoLiability}.limit`,
    type: 'dropdown',
    onChangeTransform: (value) => {
      // Transform the value to a number if needed
      return value ? Number(value) : 0;
    },
    valueTransform: (value) => {
      // Transform the number back to a string for display
      return value !== undefined && value !== null ? String(value) : '';
    },
    options: [
      {
        label: '300k',
        value: 300000,
      },
      {
        label: '500k',
        value: 500000,
      },
      {
        label: '750k',
        value: 750000,
      },
      {
        label: '1M',
        value: 1000000,
      },
    ], // TODO: Populate with AL CSL options
  },
  {
    id: 'alDeductible',
    label: 'AL Deductible',
    name: `coveragesInfo.coverages.${CoverageType.CoverageAutoLiability}.deductible`,
    type: 'dropdown',
    onChangeTransform: (value) => {
      // Transform the value to a number if needed
      return value ? Number(value) : 0;
    },
    valueTransform: (value) => {
      // Transform the number back to a string for display
      return value !== undefined && value !== null ? String(value) : '';
    },
    options: [
      {
        label: '0',
        value: 0,
      },
    ], // TODO: Populate with AL deductible options
  },
  {
    id: 'coverageAPD',
    label: 'Add APD',
    name: `coveragesInfo.coverages.${CoverageType.CoverageAutoPhysicalDamage}.isEnabled`,
    type: 'checkbox',
  },
];
