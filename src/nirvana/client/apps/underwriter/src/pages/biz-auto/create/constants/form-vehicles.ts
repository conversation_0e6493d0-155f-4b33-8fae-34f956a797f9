import {
  BusinessUse,
  RadiusClassification,
  SpecialtyVehicleType,
  StateUsage,
  TrailerType,
  VehicleType,
  VehicleUse,
  WeightClass,
} from '@nirvana/api/bizAuto';
import { z } from 'zod';
import { FormField } from '../../shared/components/form-builder';
import { createOptionsFromEnum } from '../utils';

const MIN_YEAR = 1900;
const MAX_YEAR_DELTA = 2;

// Pre-compute static options to avoid recreation on every render
const WEIGHT_CLASS_OPTIONS = createOptionsFromEnum(WeightClass);
const VEHICLE_TYPE_OPTIONS = createOptionsFromEnum(VehicleType);
const STATE_USAGE_OPTIONS = createOptionsFromEnum(StateUsage);
const VEHICLE_USE_OPTIONS = createOptionsFromEnum(VehicleUse);
const BUSINESS_USE_OPTIONS = createOptionsFromEnum(BusinessUse);
const SPECIALTY_VEHICLE_TYPE_OPTIONS =
  createOptionsFromEnum(SpecialtyVehicleType);
const TRAILER_TYPE_OPTIONS = createOptionsFromEnum(TrailerType);

// Pre-compute radius classification options with transformations
const RADIUS_CLASSIFICATION_OPTIONS = createOptionsFromEnum(
  RadiusClassification,
).map((option) => {
  let label = option.label;
  label = label.replace(/^Radius/i, '').replace(/To/i, '-');
  label = `${label} miles`;
  return { ...option, label };
});

// Pre-compute APD deductible options
const APD_DEDUCTIBLE_OPTIONS = [
  {
    label: '$500',
    value: 500,
  },
  {
    label: '$1000',
    value: 1000,
  },
  {
    label: '$2,500',
    value: 2500,
  },
  {
    label: '$5,000',
    value: 5000,
  },
];

// Remove the dynamic disabled function - this will be passed from parent

// Create transform functions once to avoid recreation
const apdDeductibleOnChangeTransform = (value: string | boolean) => {
  // Transform the value to a number if needed
  return value ? Number(value) : 0;
};

const apdDeductibleValueTransform = (value: unknown) => {
  // Transform the number back to a string for display
  return value !== undefined && value !== null ? String(value) : '';
};

export const EMPTY_VEHICLE = {
  vin: '',
  year: undefined,
  make: '',
  model: '',
  weightClass: '',
  vehicleType: '',
  stateUsage: '',
  vehicleUse: '',
  businessUse: '',
  specialtyVehicleType: undefined,
  radiusClassification: '',
  trailerType: undefined,
  apdDeductible: undefined,
  principalGaragingLocationZipCode: '',
  statedValue: undefined,
  isGlassLinedTankTruckOrTrailer: false,
  isRefrigeratedTruckOrTrailer: false,
  isDoubleTrailer: false,
  hasAntiLockBrakes: false,
};

// Zod schema for a single vehicle
export const FormVehiclesSchema = z.object({
  // Required fields from VehicleInfo
  vin: z
    .string({
      required_error: 'Vehicle Identification Number (VIN) is required',
    })
    .min(1, { message: 'VIN cannot be empty' }),

  year: z
    .number({
      required_error: 'Vehicle year is required',
      invalid_type_error: 'Year must be a number',
    })
    .min(MIN_YEAR, { message: 'Year must be 1900 or later' })
    .max(new Date().getFullYear() + MAX_YEAR_DELTA, {
      message: `Year cannot be more than ${new Date().getFullYear() + MAX_YEAR_DELTA} years in the future`,
    }),

  make: z
    .string({
      required_error: 'Vehicle make is required',
    })
    .min(1, { message: 'Make cannot be empty' }),

  model: z
    .string({
      required_error: 'Vehicle model is required',
    })
    .min(1, { message: 'Model cannot be empty' }),

  vehicleType: z
    .string({
      required_error: 'Vehicle type is required',
    })
    .min(1, { message: 'Please select a vehicle type' }),

  weightClass: z
    .string({
      required_error: 'Weight class is required',
    })
    .min(1, { message: 'Please select a weight class' }),

  vehicleUse: z
    .string({
      required_error: 'Vehicle use is required',
    })
    .min(1, { message: 'Please select a vehicle use' }),

  businessUse: z
    .string({
      required_error: 'Business use is required',
    })
    .min(1, { message: 'Please select a business use' }),

  stateUsage: z
    .string({
      required_error: 'State usage is required',
    })
    .min(1, { message: 'Please select a state usage' }),

  radiusClassification: z
    .string({
      required_error: 'Radius classification is required',
    })
    .min(1, { message: 'Please select a radius classification' }),

  principalGaragingLocationZipCode: z
    .string({
      required_error: 'Garaging ZIP code is required',
    })
    .min(1, { message: 'Garaging ZIP code cannot be empty' }),

  specialtyVehicleType: z.string().optional(),

  // Optional fields from VehicleInfo
  trailerType: z.string().optional(),

  apdDeductible: z
    .number({
      invalid_type_error: 'APD Deductible must be a number',
    })
    .min(0, { message: 'APD Deductible cannot be negative' })
    .optional(),

  statedValue: z
    .number({
      invalid_type_error: 'Stated Value must be a number',
    })
    .min(0, { message: 'Stated Value cannot be negative' })
    .optional(),

  // Boolean fields that were missing in the original schema
  isGlassLinedTankTruckOrTrailer: z.boolean().optional().default(false),
  isRefrigeratedTruckOrTrailer: z.boolean().optional().default(false),
  isDoubleTrailer: z.boolean().optional().default(false),
  hasAntiLockBrakes: z.boolean().optional().default(false),
});

// Convert static array to a function that generates fields with proper array indices
export const getFormVehicles = (
  vehicleIndex: number,
  isApdDeductibleDisabled: boolean = false,
): FormField[] => [
  {
    id: `vehiclesInfo.${vehicleIndex}.vin`,
    label: 'VIN',
    name: `vehiclesInfo.${vehicleIndex}.vin`,
    type: 'text',
    placeholder: 'Enter VIN',
    defaultValue: '',
  },
  {
    id: `vehiclesInfo.${vehicleIndex}.year`,
    label: 'Year',
    name: `vehiclesInfo.${vehicleIndex}.year`,
    type: 'number',
    placeholder: 'Enter year',
  },
  {
    id: `vehiclesInfo.${vehicleIndex}.make`,
    label: 'Make',
    name: `vehiclesInfo.${vehicleIndex}.make`,
    type: 'text',
    placeholder: 'Enter make',
  },
  {
    id: `vehiclesInfo.${vehicleIndex}.model`,
    label: 'Model',
    name: `vehiclesInfo.${vehicleIndex}.model`,
    type: 'text',
    placeholder: 'Enter model',
  },
  {
    id: `vehiclesInfo.${vehicleIndex}.weightClass`,
    label: 'Weight Class',
    name: `vehiclesInfo.${vehicleIndex}.weightClass`,
    type: 'dropdown',
    options: WEIGHT_CLASS_OPTIONS,
  },
  {
    id: `vehiclesInfo.${vehicleIndex}.vehicleType`,
    label: 'Type',
    name: `vehiclesInfo.${vehicleIndex}.vehicleType`,
    type: 'dropdown',
    options: VEHICLE_TYPE_OPTIONS,
  },
  {
    id: `vehiclesInfo.${vehicleIndex}.stateUsage`,
    label: 'State Usage',
    name: `vehiclesInfo.${vehicleIndex}.stateUsage`,
    type: 'dropdown',
    options: STATE_USAGE_OPTIONS,
  },
  {
    id: `vehiclesInfo.${vehicleIndex}.vehicleUse`,
    label: 'Vehicle Use',
    name: `vehiclesInfo.${vehicleIndex}.vehicleUse`,
    type: 'dropdown',
    options: VEHICLE_USE_OPTIONS,
  },
  {
    id: `vehiclesInfo.${vehicleIndex}.businessUse`,
    label: 'Business Use',
    name: `vehiclesInfo.${vehicleIndex}.businessUse`,
    type: 'dropdown',
    options: BUSINESS_USE_OPTIONS,
  },
  {
    id: `vehiclesInfo.${vehicleIndex}.specialtyVehicleType`,
    label: 'Speciality Vehicle Type',
    name: `vehiclesInfo.${vehicleIndex}.specialtyVehicleType`,
    type: 'dropdown',
    options: SPECIALTY_VEHICLE_TYPE_OPTIONS,
  },
  {
    id: `vehiclesInfo.${vehicleIndex}.radiusClassification`,
    label: 'Radius',
    name: `vehiclesInfo.${vehicleIndex}.radiusClassification`,
    type: 'dropdown',
    options: RADIUS_CLASSIFICATION_OPTIONS,
  },
  {
    id: `vehiclesInfo.${vehicleIndex}.trailerType`,
    label: 'Trailer Type',
    name: `vehiclesInfo.${vehicleIndex}.trailerType`,
    type: 'dropdown',
    options: TRAILER_TYPE_OPTIONS,
  },
  {
    id: `vehiclesInfo.${vehicleIndex}.apdDeductible`,
    label: 'APD Deductible',
    name: `vehiclesInfo.${vehicleIndex}.apdDeductible`,
    type: 'dropdown',
    disabled: isApdDeductibleDisabled,
    onChangeTransform: apdDeductibleOnChangeTransform,
    valueTransform: apdDeductibleValueTransform,
    options: APD_DEDUCTIBLE_OPTIONS,
  },
  {
    id: `vehiclesInfo.${vehicleIndex}.principalGaragingLocationZipCode`,
    label: 'Garaging Zip code',
    name: `vehiclesInfo.${vehicleIndex}.principalGaragingLocationZipCode`,
    type: 'text',
    placeholder: 'Enter zip code',
  },
  {
    id: `vehiclesInfo.${vehicleIndex}.statedValue`,
    label: 'Stated Value',
    name: `vehiclesInfo.${vehicleIndex}.statedValue`,
    type: 'number',
    placeholder: 'Enter stated value',
  },
  {
    id: 'placeholder',
    label: '',
    name: '',
    type: 'placeholder',
  },
  {
    id: `vehiclesInfo.${vehicleIndex}.isGlassLinedTankTruckOrTrailer`,
    label: 'Glass Lined Tank Truck/Trailer',
    name: `vehiclesInfo.${vehicleIndex}.isGlassLinedTankTruckOrTrailer`,
    type: 'checkbox',
  },
  {
    id: `vehiclesInfo.${vehicleIndex}.isRefrigeratedTruckOrTrailer`,
    label: 'Refrigerated Truck/Trailer',
    name: `vehiclesInfo.${vehicleIndex}.isRefrigeratedTruckOrTrailer`,
    type: 'checkbox',
  },
  {
    id: `vehiclesInfo.${vehicleIndex}.isDoubleTrailer`,
    label: 'Double Trailer',
    name: `vehiclesInfo.${vehicleIndex}.isDoubleTrailer`,
    type: 'checkbox',
  },
  {
    id: `vehiclesInfo.${vehicleIndex}.hasAntiLockBrakes`,
    label: 'Has Anti-Lock Brakes',
    name: `vehiclesInfo.${vehicleIndex}.hasAntiLockBrakes`,
    type: 'checkbox',
  },
];
