import { zodResolver } from '@hookform/resolvers/zod';
import {
  CoverageType,
  PostBusinessAutoAppRequest,
  PutBusinessAutoAppRequest,
} from '@nirvana/api/bizAuto';
import { AxiosError } from 'axios';
import { useSnackbar } from 'notistack';
import React from 'react';
import { useForm } from 'react-hook-form';
import { useMutation, useQueryClient } from 'react-query';
import { useNavigate, useParams } from 'react-router-dom';
import { z } from 'zod';
import Layout from '../shared/components/layout';
import { useApplicationContext } from '../shared/use-application';
import SectionCoverage from './components/section-coverage';
import SectionMailingAddress from './components/section-mailing-address';
import SectionOperations from './components/section-operations';
import SectionVehicles from './components/section-vehicles';
import Sidebar from './components/sidebar';
import { FormCoveragesSchema } from './constants/form-coverages';
import { FormOperationsSchema } from './constants/form-operations';
import { EMPTY_VEHICLE, FormVehiclesSchema } from './constants/form-vehicles';
import { createApplication, updateApplication } from './queries';

const DEFAULT_VEHICLES_COUNT = 0; // Default number of vehicle rows to render

// Merge the coverage and vehicles schemas into a single form schema
const FormSchema = FormCoveragesSchema.merge(FormOperationsSchema).extend({
  vehiclesInfo: z.array(FormVehiclesSchema).optional(),
});

const BizAutoCreateApplication = () => {
  const { applicationId = '' } = useParams();
  const { enqueueSnackbar } = useSnackbar();
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const { applicationDetails } = useApplicationContext();

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      coveragesInfo: {
        coverages: {
          [CoverageType.CoverageAutoLiability]: {
            coverageType: CoverageType.CoverageAutoLiability,
            isEnabled: true,
          },
          [CoverageType.CoverageAutoPhysicalDamage]: {
            coverageType: CoverageType.CoverageAutoPhysicalDamage,
            isEnabled: false,
          },
        },
      },
      vehiclesInfo: Array.from({ length: DEFAULT_VEHICLES_COUNT }, () => ({
        ...EMPTY_VEHICLE,
      })),
    },
  });

  // Set form values when application details are available
  React.useEffect(() => {
    if (applicationDetails) {
      const formattedData = {
        ...applicationDetails,
        producerId: applicationDetails.producerInfo?.id || '',
      };

      setTimeout(() => {
        form.reset(formattedData as never);
      }, 0);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [applicationDetails, form.reset]);

  const { mutate: create } = useMutation(createApplication, {
    onSuccess: () => {
      enqueueSnackbar('Application created successfully', {
        variant: 'success',
      });

      // Redirect to the applications list after successful creation
      navigate('/biz-auto/applications');
    },
    onError: (error: AxiosError) => {
      enqueueSnackbar(
        `Failed to create application: ${error.response?.data.message}`,
        {
          variant: 'error',
        },
      );
    },
  });

  const { mutate: update } = useMutation(
    (values: PutBusinessAutoAppRequest) => {
      return updateApplication(applicationId, values);
    },
    {
      onSuccess: () => {
        // Invalidate the query to refresh the application details
        queryClient.invalidateQueries([
          'biz-auto',
          'application',
          applicationId,
        ]);
        enqueueSnackbar('Application updated successfully', {
          variant: 'success',
        });
      },
      onError: (error: AxiosError) => {
        enqueueSnackbar(
          `Failed to update application: ${error.response?.data.message}`,
          {
            variant: 'error',
          },
        );
      },
    },
  );

  const onSubmit = (values: z.infer<typeof FormSchema>) => {
    // Sanitize the form values
    if (values?.vehiclesInfo && values?.vehiclesInfo?.length === 0) {
      delete values.vehiclesInfo;
    }

    if (applicationId) {
      update(values as PutBusinessAutoAppRequest);
    } else {
      create(values as PostBusinessAutoAppRequest);
    }
  };

  return (
    <Layout form={form} onSubmit={onSubmit}>
      <div className="flex-1 space-y-6">
        <SectionCoverage />
        <SectionOperations />
        <SectionMailingAddress />
        <SectionVehicles />
      </div>

      <Sidebar />
    </Layout>
  );
};

export default BizAutoCreateApplication;
