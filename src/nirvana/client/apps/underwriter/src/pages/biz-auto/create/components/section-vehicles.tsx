import { AppState, VehicleInfo } from '@nirvana/api/bizAuto';
import { Button, Show } from '@nirvana/ui';
import { useCallback, useState } from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { HiD<PERSON><PERSON><PERSON>, HiPlus, HiTrash, HiUpload } from 'react-icons/hi';
import FormBuilder from '../../shared/components/form-builder';
import Widget from '../../shared/components/widget';
import { useApplicationContext } from '../../shared/use-application';
import { EMPTY_VEHICLE, getFormVehicles } from '../constants/form-vehicles';
import DuplicateVehicleDialog from './duplicate-vehicle-dialog';
import SheetFileUploaderDialog from './sheet-file-uploader-dialog';

const SectionVehicles = () => {
  const methods = useFormContext();
  const { applicationId = '', applicationState } = useApplicationContext();
  const isEditable = applicationState === AppState.Created || !applicationId;
  const [copyDialogOpen, setCopyDialogOpen] = useState(false);
  const [selectedVehicleIndex, setSelectedVehicleIndex] = useState<number>(-1);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);

  const { control, getValues } = methods;
  const { fields, append, remove, insert } = useFieldArray({
    control,
    name: 'vehiclesInfo',
  });

  // Dynamically determine the disabled state of the APD deductible field
  const apdCoverageEnabled = methods.watch(
    'coveragesInfo.coverages.CoverageAutoPhysicalDamage.isEnabled',
  );
  const isApdDeductibleDisabled = !apdCoverageEnabled;

  const handleAddVehicle = useCallback(() => {
    append(EMPTY_VEHICLE);
  }, [append]);

  const handleDuplicateVehicle = useCallback((sourceIndex: number) => {
    setSelectedVehicleIndex(sourceIndex);
    setCopyDialogOpen(true);
  }, []);

  const handleConfirmDuplicate = useCallback(
    (copies: number) => {
      if (selectedVehicleIndex >= 0) {
        const sourceVehicle = getValues(`vehiclesInfo.${selectedVehicleIndex}`);
        // Deep clone to avoid reference issues
        const clonedVehicle: Partial<VehicleInfo> = JSON.parse(
          JSON.stringify(sourceVehicle),
        );
        clonedVehicle.vin = '';
        clonedVehicle.year = undefined;
        clonedVehicle.make = '';
        clonedVehicle.model = '';
        clonedVehicle.statedValue = undefined;

        // Insert copies right after the source vehicle
        Array.from({ length: copies }).forEach((_, i) => {
          insert(selectedVehicleIndex + 1 + i, clonedVehicle);
        });
      }
      setCopyDialogOpen(false);
      setSelectedVehicleIndex(-1);
    },
    [selectedVehicleIndex, getValues, insert],
  );

  const handleCloseCopyDialog = useCallback(() => {
    setCopyDialogOpen(false);
    setSelectedVehicleIndex(-1);
  }, []);

  const handleEquipmentUpload = useCallback(() => {
    setUploadDialogOpen(true);
  }, []);

  const handleUploadSuccess = useCallback(
    (vehicles: Partial<VehicleInfo>[]) => {
      if (!vehicles?.length) {
        return;
      }

      // Add all vehicles from the upload
      vehicles.forEach((vehicle) => {
        append({
          ...EMPTY_VEHICLE,
          ...vehicle,
        });
      });

      setUploadDialogOpen(false);
    },
    [append],
  );

  return (
    <Widget title="Vehicles">
      <div className="space-y-6">
        {fields.map((field, index) => (
          <div
            key={field.id}
            className="p-4 bg-white border border-gray-200 rounded-md"
          >
            <div className="flex justify-between mb-4">
              <h3 className="text-lg font-medium">Vehicle {index + 1}</h3>
              <Show when={isEditable}>
                <div className="flex gap-2">
                  <Button
                    variant="secondary"
                    startIcon={<HiDuplicate />}
                    onClick={() => handleDuplicateVehicle(index)}
                    type="button"
                  >
                    Duplicate
                  </Button>
                  <Button
                    variant="danger"
                    startIcon={<HiTrash />}
                    onClick={() => remove(index)}
                    type="button"
                  >
                    Remove
                  </Button>
                </div>
              </Show>
            </div>

            {/* Use simple form fields - let React handle the optimization */}
            <FormBuilder
              fields={getFormVehicles(index, isApdDeductibleDisabled)}
              columns={4}
            />
          </div>
        ))}

        <Show when={isEditable}>
          <div className="flex gap-2">
            <Button
              startIcon={<HiPlus />}
              onClick={handleAddVehicle}
              type="button"
            >
              Add
            </Button>
            <Button
              startIcon={<HiUpload />}
              onClick={handleEquipmentUpload}
              type="button"
              variant="secondary"
            >
              Upload
            </Button>
          </div>
        </Show>
      </div>

      <DuplicateVehicleDialog
        open={copyDialogOpen}
        onClose={handleCloseCopyDialog}
        onConfirm={handleConfirmDuplicate}
        vehicleIndex={selectedVehicleIndex}
      />

      <SheetFileUploaderDialog
        open={uploadDialogOpen}
        onClose={() => setUploadDialogOpen(false)}
        onSuccess={handleUploadSuccess}
      />
    </Widget>
  );
};

export default SectionVehicles;
