import FormBuilder from '../../shared/components/form-builder';
import Widget from '../../shared/components/widget';
import { useApplicationContext } from '../../shared/use-application';
import { getFormCoverages } from '../constants/form-coverages';

const SectionCoverage = () => {
  const { producersOptions } = useApplicationContext();

  return (
    <Widget title="Coverage">
      <FormBuilder fields={getFormCoverages({ producers: producersOptions })} />
    </Widget>
  );
};
export default SectionCoverage;
