import { AppState } from '@nirvana/api/bizAuto';
import constate from 'constate';
import { useMemo } from 'react';
import { useQueries } from 'react-query';
import { useParams } from 'react-router-dom';
import { fetchApplicationDetails, fetchProducers } from '../create/queries';

function useApplication() {
  const { applicationId = '' } = useParams();

  const [producers, details] = useQueries([
    {
      queryKey: ['biz-auto-producers'],
      queryFn: fetchProducers,
    },
    {
      queryKey: ['biz-auto', 'application', applicationId],
      queryFn: () => fetchApplicationDetails(applicationId),
      enabled: !!applicationId,
    },
  ]);

  const producersOptions = useMemo(() => {
    return (
      producers?.data?.map((producer) => ({
        label: producer.name,
        value: producer.id,
      })) || []
    );
  }, [producers]);

  return {
    applicationId: details.data?.id,
    applicationState: details.data?.state,
    applicationDetails: details.data,
    isApplicationDetailsLoading: details.isLoading || details.isFetching,
    isDisabled:
      details.data?.state === AppState.Closed ||
      details.data?.state === AppState.Declined ||
      details.data?.state === AppState.Approved ||
      details.data?.state === AppState.PolicyCreated ||
      details.data?.state === AppState.BindableQuoteGenerated,
    producersOptions,
  };
}

export const [ApplicationProvider, useApplicationContext] =
  constate(useApplication);
