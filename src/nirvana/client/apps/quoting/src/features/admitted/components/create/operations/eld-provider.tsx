import {
  Box,
  Grid,
  Typography,
  Autocomplete,
  TextField,
  InputAdornment,
} from '@material-ui/core';
import { useEffect, useMemo, useState } from 'react';
import { matchSorter } from 'match-sorter';
import Star from 'src/assets/icons/star.svg?react';
import SearchIcon from 'src/assets/icons/search.svg?react';
import CrossIcon from 'src/assets/icons/cross-blue.svg?react';
import { Show } from '@nirvana/ui-kit';
import { Controller, useFormContext } from 'react-hook-form';
import { useQuery } from '@tanstack/react-query';
import { fetchTelematicsProviders } from 'src/features/admitted/queries/application';
import { ProgramType } from '@nirvana/api/non-fleet';
import { useParams } from 'react-router-dom';
import { TSPRecord } from '@nirvana/api/quoting';
import { Feature, useFeatureFlag } from 'src/helpers/featureFlags';

type EldState = {
  isPremierEldSelected?: boolean;
  selectedTelematicsProvider?: TSPRecord | string | null | unknown;
  inputValue?: string;
};

const EldProvider = () => {
  const { applicationId = '' } = useParams();
  const getFeatureValue = useFeatureFlag();
  const isPreTelematicsFlowEnabled = getFeatureValue(
    Feature.PRE_TELEMATICS_EXPERIMENT,
    false,
  );

  const { data: telematicsProviders, isLoading } = useQuery({
    queryKey: ['telematicsProviders', applicationId],
    queryFn: () =>
      fetchTelematicsProviders(
        applicationId,
        ProgramType.ProgramTypeNonFleetAdmitted,
        // this flag is for filter premierTelematicsProvider
        true,
      ),
  });

  const [eldProviderState, setEldProviderState] = useState<EldState>({
    isPremierEldSelected: false,
    selectedTelematicsProvider: null,
    inputValue: '',
  });

  const updateEldProviderState = (updates: EldState) => {
    setEldProviderState((prev) => ({ ...prev, ...updates }));
  };

  const { control, setValue, getValues, trigger } = useFormContext();

  const extendedTelematicsProviders = useMemo(() => {
    // since autocomplte works with options passed thus adding custom 'Other' for mismatched search
    const dontKnowOption = {
      tsp: 'Other',
      name: 'Other',
      consentKind: '',
    } as unknown;

    return [...(telematicsProviders ?? []), dontKnowOption];
  }, [telematicsProviders]);

  useEffect(() => {
    const { selectedPremierProvider, selectedProviderType } =
      getValues()?.operationsForm?.companyInfo ?? {};

    //  "Don’t know" selected
    if (selectedProviderType === 'Unknown') {
      updateEldProviderState({
        selectedTelematicsProvider: null,
        inputValue: 'Don’t know ELD provider',
        isPremierEldSelected: false,
      });
      return;
    }

    // "Other" selected then set to 'Other' manually
    if (selectedProviderType === 'NonPremier') {
      const otherOption = extendedTelematicsProviders.find(
        // @ts-ignore
        (opt) => opt?.tsp === 'Other',
      );
      updateEldProviderState({
        selectedTelematicsProvider: otherOption,
        // @ts-ignore
        inputValue: otherOption?.name || '',
        isPremierEldSelected: false,
      });
      return;
    }

    // else default to matched provider if present
    if (selectedPremierProvider) {
      const matched = telematicsProviders?.find(
        (opt) => opt?.tsp === selectedPremierProvider,
      );
      if (matched) {
        updateEldProviderState({
          selectedTelematicsProvider: matched,
          inputValue: matched?.name || '',
          isPremierEldSelected: true,
        });
      }
    }
  }, [getValues, telematicsProviders, extendedTelematicsProviders]);

  return (
    <>
      <Grid
        item
        container
        direction="row"
        wrap="nowrap"
        alignItems="flex-start"
        spacing={3}
      >
        <Grid item xs={5}>
          <Box>
            <Typography color="text.primary">
              Select insured's ELD or camera provider
            </Typography>
          </Box>
        </Grid>
        <Grid item xs={3}>
          <div className="w-80">
            <Controller
              name="operationsForm.companyInfo.selectedProviderType"
              control={control}
              rules={{ required: 'Please select an option' }}
              render={({ field: { value }, fieldState: { error } }) => (
                <Autocomplete
                  className="w-sm"
                  freeSolo={true}
                  options={extendedTelematicsProviders ?? []}
                  getOptionLabel={(option) => option.name}
                  value={value}
                  disablePortal
                  clearIcon={<CrossIcon fontSize="small" className="mr-2" />}
                  onChange={(_, newValue) => {
                    // since freesolo is true don;t allow random string to be set as ELDprovider
                    const isInvalidValue =
                      typeof newValue === 'string' || !newValue?.tsp;
                    if (isInvalidValue) {
                      setValue(
                        'operationsForm.companyInfo.selectedProviderType',
                        null,
                      );
                      setValue(
                        'operationsForm.companyInfo.selectedPremierProvider',
                        null,
                      );
                      updateEldProviderState({
                        selectedTelematicsProvider: null,
                        inputValue: '',
                        isPremierEldSelected: false,
                      });
                      trigger(
                        'operationsForm.companyInfo.selectedProviderType',
                      );
                      return;
                    }
                    const isOtherSelected = newValue?.tsp === 'Other';
                    updateEldProviderState({
                      selectedTelematicsProvider: newValue,
                      isPremierEldSelected: newValue && !isOtherSelected,
                      inputValue: newValue?.name || '',
                    });
                    if (newValue) {
                      setValue(
                        'operationsForm.companyInfo.selectedPremierProvider',
                        isOtherSelected ? null : newValue?.tsp,
                      );
                      if (!isOtherSelected) {
                        setValue(
                          'operationsForm.companyInfo.selectedProviderType',
                          'Premier',
                        );
                      } else {
                        setValue(
                          'operationsForm.companyInfo.selectedProviderType',
                          'NonPremier',
                        );
                      }
                    }
                    trigger('operationsForm.companyInfo.selectedProviderType');
                  }}
                  inputValue={eldProviderState.inputValue}
                  onBlur={() => {
                    // since freeSolo prop is true thus a value is required or dontknow
                    if (
                      !eldProviderState.selectedTelematicsProvider &&
                      eldProviderState.inputValue !== 'Don’t know ELD provider'
                    ) {
                      updateEldProviderState({
                        selectedTelematicsProvider: null,
                        inputValue: '',
                      });
                    }
                  }}
                  forcePopupIcon={!value}
                  loading={isLoading}
                  onInputChange={(_, newInput) => {
                    const isUndefinedValue = newInput === 'undefined';
                    !isUndefinedValue &&
                      updateEldProviderState({
                        inputValue: newInput || '',
                      });
                  }}
                  filterOptions={(options, state) => {
                    const input = state?.inputValue?.toLowerCase();

                    const filtered = matchSorter(options, input || '', {
                      keys: ['name'],
                    });

                    if (filtered.length === 0 && input) {
                      // to show "Other" option when no match found
                      return options.filter(
                        (option) => option?.tsp === 'Other',
                      );
                    }

                    return filtered;
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      placeholder="Search ELD"
                      error={!!error}
                      helperText={error ? 'Please select an option' : ''}
                      InputProps={{
                        ...params.InputProps,
                        startAdornment: (
                          <InputAdornment position="start">
                            <SearchIcon className="text-gray-500" />
                          </InputAdornment>
                        ),
                        className: 'py-1 text-sm rounded-lg',
                      }}
                    />
                  )}
                  // papercomponent is required since we have a static text "Dont'know" at bottom of options
                  PaperComponent={({ children }) => (
                    <Box className="w-full bg-white border border-gray-200 rounded-lg shadow-lg">
                      <div className="overflow-auto">{children}</div>

                      <div className="sticky bottom-0 flex items-center justify-between px-4 py-3 rounded-b-lg border-navy-50 bg-navy-50">
                        <Typography
                          color="secondary.main"
                          fontWeight="fontWeightSemiBold"
                        >
                          Don't know the <br /> insured's ELD?
                        </Typography>
                        <button
                          className="px-3 py-1 text-sm bg-white border border-gray-300 rounded-lg h-9 text-secondary-main"
                          // to prevent the blur before click (since MUI triggers blur before this click is registered)
                          onMouseDown={(e) => e.preventDefault()}
                          onClick={() => {
                            setValue(
                              'operationsForm.companyInfo.selectedProviderType',
                              'Unknown',
                            );
                            setValue(
                              'operationsForm.companyInfo.selectedPremierProvider',
                              null,
                            );
                            updateEldProviderState({
                              selectedTelematicsProvider: null,
                              isPremierEldSelected: false,
                              inputValue: 'Don’t know ELD provider',
                            });
                          }}
                        >
                          Proceed without it
                        </button>
                      </div>
                    </Box>
                  )}
                />
              )}
            />
          </div>
        </Grid>
      </Grid>
      <Show
        when={
          eldProviderState.isPremierEldSelected && isPreTelematicsFlowEnabled
        }
      >
        <Box className="flex items-center w-full gap-2 px-4 py-3 mt-6 ml-6 rounded-lg bg-tw-teal-100">
          <Star />
          <Grid item xs className="w-full">
            <Box>
              <Typography fontWeight="fontWeightMedium" display="inline">
                This application qualifies to get a{' '}
              </Typography>
              <Typography component="span" fontWeight="fontWeightBold">
                quote without a telematics connection, but it is required to
                bind.
              </Typography>
              <Typography display="inline">
                Connect within 3 days after submitting this application to{' '}
                <Typography component="span" fontWeight="fontWeightBold">
                  save up to 20%
                </Typography>{' '}
                on your quotes.
              </Typography>
            </Box>
          </Grid>
        </Box>
      </Show>
    </>
  );
};

export default EldProvider;
