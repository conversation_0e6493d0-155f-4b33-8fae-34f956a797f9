import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ClaimStatus,
  ClaimStatusChange,
  useClaimByIdQuery,
} from 'src/types/graphql-types';
import { Chip, Show } from '@nirvana/ui-kit';
import { getFormattedDate, formatDateAgo } from '@nirvana/core/utils';
import {
  MdNumbers,
  MdOutlineCalendarMonth,
  MdOutlineWatchLater,
  MdPersonOutline,
} from 'react-icons/md';
import { HiOutlineDocumentSearch } from 'react-icons/hi';
import { IconType } from 'react-icons';
import { useEffect, useRef, useState } from 'react';
import { Navigate, useParams } from 'react-router-dom';
import clsx from 'clsx';
import parseISO from 'date-fns/parseISO';
import startOfWeek from 'date-fns/startOfWeek';
import enUS from 'date-fns/locale/en-US';
import addDays from 'date-fns/addDays';
import { BrowserTitle } from 'src/components/BrowserTitle';
import isSameDay from 'date-fns/isSameDay';
import { CircularProgress } from '@material-ui/core';
import { usePermissionsForDOT } from 'src/hooks/usePermissionsForDOT';
import { useSnackbar } from 'notistack';
import { StatusChip } from './components/StatusChip';
import { useCollectClaimsFeedback } from './hooks/useCollectClaimsFeedback';
import { TimelineItem } from './components/TimeLineItem';
import { GenerateSummary } from './components/GenerateSummary';

const FEEDBACK_ENABLED_DELAY_MS = 2_000;

type ClaimDetailProps = {
  Icon?: IconType;
  name: string;
  value: string;
  href?: string;
  className?: string;
  helper?: string;
};

const ClaimDetail = ({
  Icon,
  name,
  value,
  href,
  className,
  helper,
}: ClaimDetailProps) => {
  return (
    <div className="flex items-center gap-1 py-1">
      {Icon ? (
        <div>
          <Icon className=" text-text-hint" />
        </div>
      ) : null}
      <h4>{name}</h4>
      <span className={clsx('text-secondary-main', className)} title={helper}>
        <Show when={href} fallback={value}>
          <a href={href} target="_blank" rel="noreferrer">
            {value}
          </a>
        </Show>
      </span>
    </div>
  );
};

type ClaimHistory = {
  title: string;
  date: Date;
  sortDate: Date;
  summary?: string;
  showFeedback?: boolean;
  currentFeedback?: boolean | null;
  id: string;
}[];

type ValidClaimStatus =
  | ClaimStatus.Closed
  | ClaimStatus.Reopen
  | ClaimStatus.Open;

const getTitleForStatus = (status: ValidClaimStatus) => {
  const texts = {
    [ClaimStatus.Open]: 'Claim Opened',
    [ClaimStatus.Closed]: 'Claim Closed',
    [ClaimStatus.Reopen]: 'Claim Reopened',
  };
  return texts[status];
};

function validClaimStatus(claimStatus: {
  value: ClaimStatus;
}): claimStatus is ClaimStatusChange & { value: ValidClaimStatus } {
  return [ClaimStatus.Closed, ClaimStatus.Reopen].includes(claimStatus.value);
}

export const ClaimDetails = () => {
  const { enableFeedbackModalForClaim } = useCollectClaimsFeedback();
  const [claimHistory, setClaimHistory] = useState<ClaimHistory>();
  const [lastUpdated, setLastUpdated] = useState<string>();
  const { claimId = '' } = useParams<{ claimId: string }>();
  const timeoutId = useRef<ReturnType<typeof setTimeout>>();
  const { data } = useClaimByIdQuery({
    variables: { id: claimId },
    skip: !claimId,
    onCompleted: (data) => {
      // we wait 2s before marking the claim as feeedback ready
      // this is to prevent cases when the users opens the wrong
      // claim and we ask them for feedback
      if (data.claimById?.canSubmitFeedback) {
        timeoutId.current = setTimeout(() => {
          enableFeedbackModalForClaim(claimId);
        }, FEEDBACK_ENABLED_DELAY_MS);
      }

      const lastClaimByDateId = data.summariesForClaimId.toSorted(
        (a, b) =>
          parseISO(b.intervalEnd).getTime() - parseISO(a.intervalEnd).getTime(),
      )?.[0]?.id;

      const claimHistory: ClaimHistory = data.summariesForClaimId
        .filter((summary) => summary.scheduled)
        .map((summary) => {
          const parsedIntervalEnd = parseISO(summary.intervalEnd);
          return {
            title: summary.title,
            /* We want to make sure the summary is shown on Monday of the week
             that we generated it, even if in sometimezones the date can be
             considered as Sunday.
            */
            date: startOfWeek(addDays(parsedIntervalEnd, 1), {
              locale: enUS,
              weekStartsOn: 1,
            }),
            sortDate: parsedIntervalEnd,
            summary: summary.summary,
            showFeedback:
              lastClaimByDateId === summary.id &&
              data.claimById?.canSubmitFeedback,
            currentFeedback: summary.feedback,
            id: summary.id,
          };
        });

      data.claimById?.statusChanges
        .filter((change) => validClaimStatus(change))
        .forEach((change) => {
          claimHistory.push({
            title: getTitleForStatus(change.value as ValidClaimStatus),
            date: parseISO(change.createdAt),
            sortDate: parseISO(change.createdAt),
            showFeedback: false,
            id: change.id,
          });
        });

      // We can't trust the history event Open, so we set it from the reportedAt
      if (data.claimById?.reportedAt) {
        claimHistory.push({
          title: getTitleForStatus(ClaimStatus.Open),
          date: parseISO(data.claimById.reportedAt),
          sortDate: parseISO(data.claimById.reportedAt),
          showFeedback: false,
          id: 'open',
        });
      }

      // In some cases claims are updated after they are closed
      // but as we are not showing the updates that it received, we use the earliest
      // between the modifiedAt and the status change to Closed
      const lastClosedAt = data.claimById?.statusChanges
        .toSorted(
          (a, b) =>
            parseISO(b.createdAt).getTime() - parseISO(a.createdAt).getTime(),
        )
        .filter((event) => event.value === ClaimStatus.Closed)
        .at(0)?.createdAt;

      let lastUpdated = data.claimById?.modifiedAt;

      if (
        data.claimById?.status === ClaimStatus.Closed &&
        lastClosedAt &&
        parseISO(lastClosedAt).getTime() <
          parseISO(data.claimById?.modifiedAt!).getTime()
      ) {
        lastUpdated = lastClosedAt;
      }

      setLastUpdated(lastUpdated);

      claimHistory.sort((a, b) => b.sortDate.getTime() - a.sortDate.getTime());

      setClaimHistory(claimHistory);
    },
  });

  useEffect(() => {
    return () => {
      if (timeoutId.current) {
        clearTimeout(timeoutId.current);
      }
    };
  }, []);

  const claimDetails = data?.claimById;
  const lastUnscheduledSummaryInLastDay = data?.summariesForClaimId
    .filter(
      (summary) =>
        !summary.scheduled &&
        isSameDay(new Date(), parseISO(summary.intervalEnd)),
    )
    .at(-1);

  if (!claimDetails) {
    return null;
  }

  return (
    <article
      className="max-w-2xl mx-auto font-medium"
      data-testid="claim-details"
    >
      <BrowserTitle section={`Claim #${claimDetails.claimNumber}`} />
      <header>
        <StatusChip status={claimDetails.status} />
        <h1 className="flex flex-wrap gap-2 mt-6 mb-4 text-2xl">
          <strong>Claim</strong>{' '}
          <span className="text-text-hint">#{claimDetails.claimNumber}</span>
          <Chip label={claimDetails.lineOfBusiness} />
        </h1>
        <section>
          <div className="flex flex-wrap gap-x-8">
            <ClaimDetail
              Icon={MdOutlineWatchLater}
              name="Last Action"
              helper={
                lastUpdated ? getFormattedDate(lastUpdated, 'MMM dd yyyy') : ''
              }
              value={lastUpdated ? formatDateAgo(lastUpdated) : 'Unknown'}
            />
            <ClaimDetail
              Icon={MdOutlineCalendarMonth}
              name="Created"
              value={getFormattedDate(claimDetails.reportedAt, 'MMM dd yyyy')}
            />
          </div>
          <div className="flex flex-wrap gap-x-8">
            <ClaimDetail
              Icon={MdPersonOutline}
              name="Submitted By"
              className="capitalize"
              value={claimDetails.reportedBy?.toLocaleLowerCase() || '-'}
            />
            <ClaimDetail
              Icon={MdNumbers}
              name="Policy ID"
              value={claimDetails?.policy?.policyNumber ?? ''}
            />
            <Show when={claimDetails.adjusterEmail}>
              <ClaimDetail
                Icon={HiOutlineDocumentSearch}
                name="Adjuster"
                href={`mailto:${claimDetails.adjusterEmail}`}
                value={claimDetails.adjusterName}
              />
            </Show>
          </div>
        </section>
      </header>
      <Show
        when={
          data.claimById?.status !== ClaimStatus.Closed &&
          data.claimById?.source === ClaimsProvider.Nars
        }
      >
        <GenerateSummary
          claimId={claimDetails.id}
          disabledReason={
            !data.claimById?.hasNotesSinceLastScheduleSummary
              ? 'there are no new updates since the last summary was generated.'
              : undefined
          }
          lastSummary={
            lastUnscheduledSummaryInLastDay
              ? {
                  ...lastUnscheduledSummaryInLastDay,
                  showFeedback: data.claimById?.canSubmitFeedback,
                }
              : undefined
          }
        />
      </Show>
      <section data-testid="weekly-updates">
        <h2 className="flex gap-4 mt-8 text-base text-secondary-main">
          <span>Weekly Updates</span>
        </h2>
        <p className="mt-2 text-xs text-text-hint">
          Note: Weekly updates are system generated and may not capture all
          details.
        </p>
        <ol className="pt-6 pl-4">
          {claimHistory?.map((update) => (
            <TimelineItem
              key={update.id}
              title={update.title}
              date={update.date}
              summary={update.summary}
              showFeedback={
                update.showFeedback && data.claimById?.canSubmitFeedback
              }
              currentFeedback={update.currentFeedback}
              itemId={update.id}
            />
          ))}
        </ol>
      </section>
    </article>
  );
};

export function ClaimDetailsGuard() {
  const { reportId } = useParams<{ reportId: string }>();
  const dotNumber = reportId as string;

  const snackbar = useSnackbar();

  const { permissions, loading } = usePermissionsForDOT(dotNumber);

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center h-screen">
        <CircularProgress />
      </div>
    );
  }

  if (!permissions.ClaimsDetails) {
    snackbar.enqueueSnackbar(
      'You do not have permissions to view this claim.',
      {
        variant: 'error',
        SnackbarProps: {
          id: 'claim-details-permission-denied-snackbar',
        },
      },
    );
    return <Navigate to="/" />;
  }

  return <ClaimDetails />;
}
