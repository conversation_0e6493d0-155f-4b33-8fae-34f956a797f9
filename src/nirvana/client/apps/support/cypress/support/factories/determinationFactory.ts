import { Factory } from 'fishery';
import { CoverageRunWithNotes } from '@nirvana/api/claims_agent/api';

export const determinationFactory = Factory.define<CoverageRunWithNotes[]>(
  () => [
    {
      created_at: '2025-01-01',
      created_by: 'test',
      coverage_notes: [
        {
          note_id: '123',
          original_content: {
            name: 'Coverage',
            assessment_score: 0.5,
            summary: 'Coverage is 50%',
            citation: {
              excerpt: 'Coverage is 50%',
              filename: 'policy.pdf',
              presigned_url: 'https://example.com/policy.pdf',
              pages: [1, 2, 3],
            },
          },
        },
      ],
    },
  ],
);
