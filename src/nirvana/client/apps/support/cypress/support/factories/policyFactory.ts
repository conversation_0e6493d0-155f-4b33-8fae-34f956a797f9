import { Factory } from 'fishery';
import { faker } from '@faker-js/faker';
import {
  Endorsement,
  Policy,
  PolicyAndEndorsementsQuery,
  PolicyCoverageEnums,
  PolicyDriver,
  PolicyState,
  PolicyVehicle,
  ProgramType,
} from '@nirvana/support/src/types/graphql-types';
import { GraphQlResponse } from '@nirvana/core/testUtils';

const driverFactory = Factory.define<PolicyDriver>(() => {
  const licenseNumberLength = 10;
  return {
    dateOfBirth: faker.date.past().toISOString().split('T')[0],
    dateOfHire: faker.date.past().toISOString().split('T')[0],
    firstName: faker.person.firstName(),
    isIncludedInPolicy: faker.datatype.boolean(),
    isOutOfState: faker.datatype.boolean(),
    isOwner: faker.datatype.boolean(),
    lastName: faker.person.lastName(),
    licenseNumber: faker.string.alphanumeric(licenseNumberLength),
    licenseState: faker.location.state(),
    yearsOfExperience: faker.number.int({ min: 0, max: 10 }),
  };
});

const vehicleFactory = Factory.define<PolicyVehicle>(() => {
  const vinLength = 17;
  return {
    make: faker.vehicle.manufacturer(),
    model: faker.vehicle.model(),
    statedValue: faker.number.int({ min: 1000, max: 100000 }),
    vehicleClass: faker.vehicle.type(),
    vehicleType: faker.vehicle.type(),
    vin: faker.string.alphanumeric(vinLength),
    weightClass: 'A',
    year: faker.date.past().getFullYear(),
  };
});

const endorsementsFactory = Factory.define<Endorsement>(() => {
  const documentNameLength = 10;
  return {
    id: faker.string.uuid(),
    documentID: faker.string.uuid(),
    approvedAt: faker.date.past().toISOString(),
    changeTypes: [faker.lorem.word()],
    effectiveInterval: {
      __typename: 'EndorsementEffectiveInterval',
      effectiveDate: faker.date.past().toISOString().split('T')[0],
      expirationDate: faker.date.future().toISOString().split('T')[0],
    },
    signedLink: {
      __typename: 'ExpirableLink',
      expiration: faker.date.future().toISOString(),
      link: `http:/example.com/${faker.string.alphanumeric(documentNameLength)}.pdf`,
    },
    underwriter: {
      __typename: 'BasicUser',
      email: faker.internet.email(),
      name: faker.person.fullName(),
    },
    supportingDocsAndForms: [],
  };
});

export const policyFactory = Factory.define<Policy>(() => {
  const id = faker.string.uuid();
  const startDate = faker.date.past().toISOString().split('T')[0];
  const endDate = faker.date.future().toISOString().split('T')[0];
  const coverages = [PolicyCoverageEnums.CoverageAutoLiability]; // You can expand this with more coverages
  const underwriterName = faker.person.fullName();
  const underwriterEmail = faker.internet.email();

  const signedLinkExpiration = faker.date.future().toISOString();

  const elementsCount = 3;
  const dotNumberLength = 10;
  const policyNumberLength = 10;
  const subCoveragesLength = 5;
  return {
    __typename: 'policy',
    id,
    programType: ProgramType.Fleet,
    documentID: faker.string.uuid(),
    isTest: false,
    startDate,
    endDate,
    coverages,
    subCoverages: [
      {
        __typename: 'CoverageWithSymbols',
        coverage: 'Auto Liability',
        symbols: [faker.string.alphanumeric(subCoveragesLength)],
      },
    ],
    signedLink: {
      __typename: 'ExpirableLink',
      expiration: signedLinkExpiration,
      link: 'http:/example.com/example.pdf',
    },
    underwriter: {
      __typename: 'BasicUser',
      email: underwriterEmail,
      name: underwriterName,
    },
    endorsements: endorsementsFactory.buildList(elementsCount),
    drivers: driverFactory.buildList(elementsCount),
    vehicles: vehicleFactory.buildList(elementsCount),
    insuredDOTNumber: faker.string.alphanumeric(dotNumberLength),
    insuredName: faker.company.name(),
    policyNumber: faker.string.alphanumeric(policyNumberLength),
    state: PolicyState.Active,
  };
});

export const policyFactoryResponse = Factory.define<
  GraphQlResponse<PolicyAndEndorsementsQuery>
>(() => ({
  data: {
    __typename: 'Query',
    policy: policyFactory.build(),
  },
  errors: null,
}));
