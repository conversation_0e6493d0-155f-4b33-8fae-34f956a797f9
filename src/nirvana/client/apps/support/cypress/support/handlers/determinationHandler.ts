import { CyHttpMessages } from '@nirvana/core/testUtils';
import { determinationFactory } from '../factories/determinationFactory';

// Constants for response delays and status codes
const NOT_CACHED_DELAY_MS = 20_000;
const DEFAULT_DELAY_MS = 1000;
const BAD_REQUEST_STATUS = 400;

export const determinationCachedHandler = (
  req: CyHttpMessages.IncomingHttpRequest,
) => {
  req.on('response', (res) => {
    res.setDelay(
      req.url.includes('NOTCACHED') ? NOT_CACHED_DELAY_MS : DEFAULT_DELAY_MS,
    );
  });
  if (req.url.includes('FAILING')) {
    return req.reply(BAD_REQUEST_STATUS);
  }
  req.reply(determinationFactory.build());
};

export const forcedFailureHandler = (
  req: CyHttpMessages.IncomingHttpRequest,
) => {
  req.reply(BAD_REQUEST_STATUS);
};

export const determinationHandler = (
  req: CyHttpMessages.IncomingHttpRequest,
) => {
  req.reply(determinationFactory.build()[0]);
};
