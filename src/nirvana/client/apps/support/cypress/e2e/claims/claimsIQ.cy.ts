import { RoutesHelper } from '@nirvana/core/testUtils';
import {
  claimById,
  claims,
  failingClaimIQUuid,
} from '../../support/handlers/claims';
import {
  determinationCached<PERSON>andler,
  determinationHandler,
  forcedFailureHandler,
} from '../../support/handlers/determinationHandler';

const routesHelper = new RoutesHelper();
const baseUrl = routesHelper.getBaseUrl();

const clickClaim = (id?: string) => {
  cy.wait('@claims');
  cy.get('table>tbody>tr[data-testid="skeleton-row"]').should('have.length', 0);

  if (id) {
    cy.get(`table>tbody>tr [data-testid="claim-${id}"]`).click();
  } else {
    cy.get('table>tbody>tr').first().click();
  }
};

describe('ClaimIQ', () => {
  beforeEach(() => {
    cy.login();
    routesHelper.overrideGraphqlResponse('claims', claims).as('claims');
    routesHelper
      .overrideGraphqlResponse('ClaimById', claimById)
      .as('claimById');

    routesHelper
      .overrideClaimsLLMResponse(
        '/**/coverage/determination/cached',
        determinationCachedHandler,
      )
      .as('determinationCached');

    cy.visit(`${baseUrl}/claims`);
  });

  it('shows the claimIQ container', () => {
    clickClaim();
    cy.get('[data-testid="claim-iq-container"]').should('be.visible');
  });

  describe('when the user starts claimIQ', () => {
    beforeEach(() => {
      clickClaim();
      cy.get('[data-testid="run-claim-iq-button"]').click();
    });

    it('shows the loading state', () => {
      cy.get('[data-testid="claim-iq-container"]').should('be.visible');
    });

    it('shows the results when the data is available', () => {
      cy.wait('@determinationCached');
      cy.get('[data-testid="claim-iq-container"]').should('be.visible');
      cy.get('[data-testid="determination-container"]', {
        timeout: 10000,
      }).should('be.visible');
    });
  });

  it("shows an error when it can't generate the coverage notes", () => {
    clickClaim(failingClaimIQUuid);
    cy.get('[data-testid="run-claim-iq-button"]').click();
    cy.get('[data-testid="claim-iq-container"]').should('be.visible');
    cy.wait('@determinationCached');
    cy.get('[data-testid="error-state"]').should('be.visible');
  });

  it('Allows to have multiple claims running in parallel', () => {
    clickClaim();

    cy.clock(new Date());
    cy.get('[data-testid="run-claim-iq-button"]').click();
    cy.get('[data-testid="claimsiq-loading-container"]').should('be.visible');
    clickClaim(failingClaimIQUuid);
    cy.get('[data-testid="run-claim-iq-button"]').click();
    cy.get('[data-testid="claimsiq-loading-container"]').should('be.visible');
    clickClaim();
    cy.get('[data-testid="claimsiq-loading-container"]').should('be.visible');
  });

  describe('when the data loaded', () => {
    beforeEach(() => {
      clickClaim();
      cy.get('[data-testid="run-claim-iq-button"]').click();
      cy.wait('@determinationCached');
      cy.get('[data-testid="determination-container"]').should('be.visible');
    });

    it('Keeps showing the data when the user goes to another tab', () => {
      cy.get('[data-testid="policy"]').click();
      cy.get('[data-testid="claimiq"]').click();
      cy.get('[data-testid="determination-container"]').should('be.visible');
    });

    it('allows to refresh the data', () => {
      routesHelper
        .overrideClaimsLLMResponse(
          '/**/coverage/determination',
          determinationHandler,
        )
        .as('determination');
      cy.get('[data-testid="refresh-button"]').click();
      cy.wait('@determination');
      cy.get('[data-testid="claimsiq-loading-container"]').should('be.visible');
    });

    it('shows an error but keeps showing the old data when the request fails', () => {
      routesHelper
        .overrideClaimsLLMResponse(
          '/**/coverage/determination',
          forcedFailureHandler,
        )
        .as('determination');
      cy.get('[data-testid="refresh-button"]').click();

      cy.wait('@determination');
      cy.get(':contains("Error updating coverage notes")').should('be.visible');
      cy.get('[data-testid="determination-container"]').should('be.visible');
    });
  });
});
