import { format, parseISO } from 'date-fns/esm';
import { createColumnHelper } from '@tanstack/react-table';

import TableV8, {
  ColumnsType,
} from '@nirvana/ui-kit/src/components/table/table-v8';
import { GetBordereauxReportsQuery } from 'types/graphql-types';
import { Button } from '@nirvana/ui';
import { Tooltip } from '@material-ui/core';

type BordereauxReport =
  GetBordereauxReportsQuery['getBordereauxReports'][number];

function getColumns(): ColumnsType<BordereauxReport> {
  const columnHelper = createColumnHelper<BordereauxReport>();
  return [
    columnHelper.accessor('carrier', {
      enableSorting: true,
      header: 'Carrier',
    }),
    columnHelper.accessor('generatedAt', {
      enableSorting: true,
      header: 'As of date',
      cell: ({ getValue }) => {
        return format(parseISO(getValue()), 'MMM dd, yyyy - hh:mm a');
      },
    }),
    columnHelper.accessor('generatedBy.firstName', {
      enableSorting: true,
      header: 'Requested By',
      cell: ({ row }) => {
        const { firstName, lastName, email } = row.original.generatedBy ?? {};
        return (
          <a href={`mailto:${email}`}>
            {firstName} {lastName}
          </a>
        );
      },
    }),
    columnHelper.accessor('downloadURL', {
      header: '',
      cell: ({ getValue, row }) => {
        const downloadURL = getValue();
        if (!downloadURL) {
          return (
            <Tooltip title={row.original.errorMessage ?? 'Unknown error'}>
              <p className="text-right text-red-500">
                Report unavailable (hover for details)
              </p>
            </Tooltip>
          );
        }

        return (
          <div className="flex justify-end">
            <Button asChild>
              <a href={downloadURL} target="_blank" rel="noopener noreferrer">
                Download
              </a>
            </Button>
          </div>
        );
      },
    }),
  ];
}

const PAGE_OPTIONS = {
  pageSize: 25,
};

interface Props {
  reports: BordereauxReport[];
  loading: boolean;
}

export function BordereauxReportsTable({ reports, loading }: Props) {
  return (
    <TableV8
      sorting={[{ id: 'generatedAt', desc: true }]}
      data={reports}
      columns={getColumns()}
      isLoading={loading}
      pageOptions={PAGE_OPTIONS}
    />
  );
}
