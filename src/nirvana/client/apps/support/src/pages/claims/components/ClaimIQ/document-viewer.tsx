import {
  HiDownload,
  HiOutlineDocumentReport,
  HiOutlineSparkles,
  HiX,
} from 'react-icons/hi';

import { Button, Show } from '@nirvana/ui';
import { downloadFile } from '@nirvana/core/utils';

import { useDocumentsQuery } from 'pages/claims/hooks/useDocumentsQuery';
import { usePanelVisibilityContext } from 'pages/claims/hooks/usePanelVisibility';
import { Panel } from 'react-resizable-panels';
import { PdfViewer } from '../Policy/PdfViewer';

export default function DocumentViewer() {
  const { isDocumentViewerOpen, setIsDocumentViewerOpen, documentData } =
    usePanelVisibilityContext();

  const { documentID, pageNumber, fileName, documentSummary } =
    documentData || {};
  const { documents } = useDocumentsQuery();

  if (!documentID) {
    return null;
  }

  const fileUrl = documents[documentID]?.url;

  return (
    <Show when={isDocumentViewerOpen}>
      <Panel id="document-viewer" order={3} defaultSize={25}>
        <div className="relative flex flex-col flex-1 h-full overflow-hidden bg-white border-l">
          <section id="document-summary" className="p-5 border-b">
            <div className="flex items-center">
              <div className="flex items-center justify-center p-1 mr-2 rounded-full text-primary-main bg-primary-extraLight">
                <HiOutlineDocumentReport />
              </div>
              <p className="flex-1">{fileName}</p>
              <Show when={fileUrl}>
                <Button
                  size="icon"
                  variant="text"
                  onClick={() =>
                    downloadFile(fileUrl, fileName ?? 'document.pdf')
                  }
                >
                  <HiDownload />
                </Button>
              </Show>
              <Button
                size="icon"
                variant="text"
                onClick={() => setIsDocumentViewerOpen(false)}
              >
                <HiX />
              </Button>
            </div>
            <Show when={documentSummary}>
              <div className="relative p-4 rounded-lg shadow-tw-sm mt-6">
                <HiOutlineSparkles className="text-tw-teal-600 inline-block mr-2" />
                {documentSummary}
              </div>
            </Show>
          </section>
          <Show
            when={fileUrl}
            fallback={
              <div className="h-full items-center justify-center flex">
                This document is not available.
              </div>
            }
          >
            <section
              className="flex-1 overflow-y-auto"
              id="parent-claims-iq-document-viewer"
            >
              <PdfViewer
                pdfUrl={fileUrl}
                fileId={documentID}
                // Hack to force a page change even when the page number is the same
                pageNumber={`${pageNumber}-${new Date().getTime()}`}
                mode="slim"
                id="claims-iq-document-viewer"
              />
            </section>
          </Show>
        </div>
      </Panel>
    </Show>
  );
}
