import React, { useEffect, useMemo, useRef, useState } from 'react';

import { usePanelVisibilityContext } from 'pages/claims/hooks/usePanelVisibility';

import { TIME_BETWEEN_CHECKS, steps } from './constants';
import { LoadingStep } from './LoadingStep';
import { stepCalculator } from './stepCalculator';
import CoverageWorking from './assets/CoverageWorking.gif';

export const Loading: React.FC<
  {
    loading: boolean;
    externalClaimId: string;
    refetchError: boolean;
  } & React.PropsWithChildren
> = ({ loading, children, externalClaimId, refetchError }) => {
  const { setLoadingClaimIQs, loadingClaimIQs } = usePanelVisibilityContext();
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const interval = useRef<number | undefined>(undefined);

  const startTime = useMemo(
    () => loadingClaimIQs[externalClaimId]?.current.startTime || new Date(),
    [loadingClaimIQs, externalClaimId],
  );

  // Initialize the loading state for the claim
  useEffect(() => {
    if (loadingClaimIQs[externalClaimId]?.current.startTime) {
      return;
    }
    setLoadingClaimIQs((prev) => {
      const newState = { ...prev };
      newState[externalClaimId] = {
        current: {
          startTime,
          finishTime: undefined,
        },
        succeded: {
          startTime: undefined,
          finishTime: undefined,
        },
      };
      return newState;
    });
  }, [startTime, externalClaimId, setLoadingClaimIQs, loadingClaimIQs]);

  useEffect(() => {
    if (interval.current) {
      clearInterval(interval.current);
    }
    interval.current = setInterval(() => {
      const currentStepIndex = stepCalculator(startTime, loading);
      setCurrentStepIndex(currentStepIndex);
      if (currentStepIndex === steps.length - 1 && !loading) {
        clearInterval(interval.current);
        setLoadingClaimIQs((prev) => {
          const newState = { ...prev };
          if (!newState[externalClaimId]?.current.finishTime) {
            newState[externalClaimId].current.finishTime = new Date();
          }
          return newState;
        });
      }
    }, TIME_BETWEEN_CHECKS);
    setCurrentStepIndex(stepCalculator(startTime, loading));

    return () => clearInterval(interval.current);
  }, [externalClaimId, loading, setLoadingClaimIQs, startTime]);

  const skipToData =
    !loading && loadingClaimIQs[externalClaimId]?.current.finishTime;

  if (skipToData || refetchError) {
    return children;
  }

  return (
    <div
      className="shadow-sm rounded-lg p-4 border"
      data-testid="claimsiq-loading-container"
    >
      <div className="flex items-center gap-2 mb-4 text-text-secondary">
        <img
          src={CoverageWorking}
          aria-hidden={true}
          className="w-8 h-8 inline-block"
        />
        <h2 className="overflow-hidden relative">
          <div className="absolute inset-0 -translate-x-full animate-shimmer bg-gradient-to-r from-transparent via-white/40 to-transparent" />
          Thinking...
        </h2>
        <span className="text-sm">{currentStepIndex} tasks completed</span>
      </div>
      {steps.slice(0, currentStepIndex + 1).map((step, index) => (
        <LoadingStep
          key={step.title}
          step={step}
          currentStepIndex={currentStepIndex}
          index={index}
        />
      ))}
    </div>
  );
};
