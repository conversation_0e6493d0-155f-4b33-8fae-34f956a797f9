import constate from 'constate';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

type DocumentData = {
  documentID?: string | null; // ID of the PDF document to display
  fileName?: string | null; // Name of the PDF document
  documentSummary: string; // Summary of the PDF document
  pageNumber?: number; // Page number to display in the PDF viewer
};

function usePanelVisibility() {
  const { claimId } = useParams();
  const [documentData, setDocumentData] = useState<DocumentData>();
  const [loadingClaimIQs, setLoadingClaimIQs] = useState<{
    [claimId: string]: {
      succeded: {
        startTime: Date | undefined;
        finishTime: Date | undefined;
      };
      current: {
        startTime: Date | undefined;
        finishTime: Date | undefined;
      };
    };
  }>({});

  const [isClaimListOpen, setIsClaimListOpen] = useState(true);
  const [isDocumentViewerOpen, setIsDocumentViewerOpen] = useState(false);
  const [hasPermissions, setHasPermissions] = useState(true);

  useEffect(() => {
    // Don't render DocumentViewer when `claimId` is not available
    if (!claimId) {
      setIsDocumentViewerOpen(false);
    }
  }, [claimId]);

  return {
    documentData,
    setDocumentData,
    isClaimListOpen,
    setIsClaimListOpen,
    isDocumentViewerOpen,
    setIsDocumentViewerOpen,
    hasPermissions,
    setHasPermissions,
    loadingClaimIQs,
    setLoadingClaimIQs,
  };
}

const [PanelVisibilityProvider, usePanelVisibilityContext] =
  constate(usePanelVisibility);

export { PanelVisibilityProvider, usePanelVisibilityContext };
