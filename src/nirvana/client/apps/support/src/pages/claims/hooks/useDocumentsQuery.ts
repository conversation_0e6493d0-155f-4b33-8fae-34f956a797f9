import { useMemo } from 'react';
import { useParams } from 'react-router-dom';
import {
  PolicyAndEndorsementsQuery,
  usePolicyAndEndorsementsQuery,
} from 'types/graphql-types';

type DocumentMapping = Record<string, { url: string }>;

type Policy = NonNullable<PolicyAndEndorsementsQuery['policy']>;
type Endorsements = Policy['endorsements'];

function getPolicyDocument(policy: Policy): DocumentMapping {
  return { [policy.documentID]: { url: policy.signedLink.link } };
}

function getEndorsementDocuments(endorsements: Endorsements): DocumentMapping {
  return endorsements.reduce(
    (acc, curr) => ({
      ...acc,
      [curr.documentID]: { url: curr.signedLink.link },
      ...curr.supportingDocsAndForms.reduce(
        (acc, curr) => ({
          ...acc,
          [curr.documentID]: { url: curr.signedLink.link },
        }),
        {},
      ),
    }),
    {},
  );
}

export function useDocumentsQuery() {
  const { claimId } = useParams();

  if (!claimId) {
    // eslint-disable-next-line no-console
    console.log('Claim ID is required to fetch documents');
  }

  const { data, loading, error } = usePolicyAndEndorsementsQuery({
    variables: { claimId: claimId ?? '' },
    skip: !claimId,
  });

  const documents = useMemo(() => {
    const mapping = {} as DocumentMapping;

    if (!data?.policy || error) {
      return mapping;
    }

    const { policy } = data;

    return {
      ...mapping,
      ...getPolicyDocument(policy),
      ...getEndorsementDocuments(policy.endorsements),
    };
  }, [data, error]);

  return { loading, documents };
}
