import { But<PERSON> } from '@nirvana/ui';
import { Link } from 'react-router-dom';

export default function Selector() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen gap-5 font-sans">
      <h1 className="text-3xl font-bold">Choose GraphQL Endpoint</h1>
      <p className="text-lg">
        Select which GraphQL endpoint you'd like to use:
      </p>

      <div className="flex gap-5">
        <Button asChild>
          <Link to="/console">Thunder</Link>
        </Button>

        <Button asChild variant="secondary">
          <Link to="/gqlgen">GQLGen (new)</Link>
        </Button>
      </div>
    </div>
  );
}
