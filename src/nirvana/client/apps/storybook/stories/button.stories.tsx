import * as React from 'react';
import type { Meta } from '@storybook/react';
import { HiExternalLink } from 'react-icons/hi';
import { ChevronLeft, ChevronRight } from '@material-ui/icons';

import { Button } from '@nirvana/ui';

const meta: Meta<typeof Button> = {
  title: 'Design System/Button',
  component: Button,
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['primary', 'secondary', 'danger', 'text', 'link'],
      description:
        'The variant of the button. Options are `primary`, `secondary`, `danger`, `text`, and `link`.',
    },
    size: {
      control: { type: 'select' },
      options: ['default', 'icon'],
      description: 'The size of the button. Options are `default`, and `icon`.',
    },
    loading: {
      control: 'boolean',
      description:
        'If true, the button will show a loading spinner instead of icon.',
    },
    startIcon: {
      description: 'An icon to display at the start of the button.',
      control: false,
    },
    endIcon: {
      description: 'An icon to display at the end of the button.',
      control: false,
    },
    className: {
      control: false,
      description: 'Additional CSS classes to apply to the button.',
    },
  },
};

export default meta;

export const PrimaryButtons = () => (
  <div className="flex items-center space-x-3">
    <Button>Next</Button>
    <Button startIcon={<ChevronLeft />}>Next</Button>
    <Button endIcon={<ChevronRight />}>Next</Button>
    <Button disabled>Next</Button>
    <Button disabled startIcon={<ChevronLeft />}>
      Next
    </Button>
    <Button disabled endIcon={<ChevronRight />}>
      Next
    </Button>
    <Button size="icon" startIcon={<ChevronLeft />} />
    <Button disabled size="icon" startIcon={<ChevronLeft />} />
    <Button loading>Next</Button>
  </div>
);

export const SecondaryButtons = () => {
  return (
    <div className="flex items-center space-x-3">
      <Button variant="secondary">Next</Button>
      <Button variant="secondary" startIcon={<ChevronLeft />}>
        Next
      </Button>
      <Button variant="secondary" endIcon={<ChevronRight />}>
        Next
      </Button>
      <Button variant="secondary" disabled>
        Next
      </Button>
      <Button variant="secondary" disabled startIcon={<ChevronLeft />}>
        Next
      </Button>
      <Button variant="secondary" disabled endIcon={<ChevronRight />}>
        Next
      </Button>
      <Button variant="secondary" size="icon" startIcon={<ChevronLeft />} />
      <Button
        disabled
        size="icon"
        variant="secondary"
        startIcon={<ChevronLeft />}
      />
      <Button variant="secondary" loading>
        Next
      </Button>
    </div>
  );
};

export const DangerButtons = () => {
  return (
    <div className="flex items-center space-x-3">
      <Button variant="danger">Next</Button>
      <Button variant="danger" startIcon={<ChevronLeft />}>
        Next
      </Button>
      <Button variant="danger" endIcon={<ChevronRight />}>
        Next
      </Button>
      <Button variant="danger" disabled>
        Next
      </Button>
      <Button variant="danger" disabled startIcon={<ChevronLeft />}>
        Next
      </Button>
      <Button variant="danger" disabled endIcon={<ChevronRight />}>
        Next
      </Button>
      <Button variant="danger" size="icon" startIcon={<ChevronLeft />} />
      <Button
        disabled
        size="icon"
        variant="danger"
        startIcon={<ChevronLeft />}
      />
      <Button variant="danger" loading>
        Next
      </Button>
    </div>
  );
};

export const InvisibleButtons = () => {
  return (
    <div className="flex items-center space-x-3">
      <Button variant="text">Next</Button>
      <Button variant="text" disabled>
        Next
      </Button>
      <Button variant="link">Next</Button>
      <Button disabled variant="link">
        Next
      </Button>
      <Button startIcon={<ChevronLeft />} variant="link">
        Next
      </Button>
      <Button disabled startIcon={<ChevronLeft />} variant="link">
        Next
      </Button>

      <Button endIcon={<ChevronRight />} variant="link">
        Next
      </Button>

      <Button asChild variant="link">
        <a
          target="_blank"
          rel="noopener noreferrer"
          href="https://nirvanatech.com"
        >
          Nirvana
          <HiExternalLink className="ml-1" />
        </a>
      </Button>
      <Button variant="text" loading>
        Nirvana
      </Button>
    </div>
  );
};
