package test_utils

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"

	"github.com/google/go-github/v52/github"
	"github.com/stretchr/testify/require"

	"nirvanatech.com/nirvana/nirvanamq/common"
	"nirvanatech.com/nirvana/nirvanamq/task"
)

// RequireMQIsConsistent checks MQ consistency as defined by the formal properties given at:
// https://coda.io/d/Engineering-Wiki_dmBWLoxkuyN/NirvanaMQ-Design-Document_suBJ9#_luMFh
// g here signifies cloud repo wrapper
func RequireMQIsConsistent(t *testing.T, g *InMemoryGitWrapper, gh *InMemoryGithubWrapper, nmq *task.NirvanaMQ) {
	ctx := context.Background()
	pos := 0
	pullRequests := make(map[int]bool)
	for node := nmq.MQ.Front; node != nil && pos < nmq.Config.SpeculativeDegree; node = node.Prev {
		pos++
		pr, err := gh.GetPR(ctx, node.PR)
		require.NoError(t, err) // pr exists
		_, found := pullRequests[node.PR]
		require.False(t, found, "PR should be unique") // 1A
		pullRequests[node.PR] = true

		require.Equal(t, pr.GetHead().GetSHA(), node.HeadSHA)                      // no sha mismatch
		require.Contains(t, []string{"clean", "unstable"}, pr.GetMergeableState()) // 1B
		require.False(t, node.PR == node.PRTemp, "Not possible in speculative range")
		// Draft PR must be present in all nodes of speculative range

		botDraftPR, err := gh.GetPR(ctx, node.PRTemp)
		require.NoError(t, err)                                              // bot draft pr exists
		require.Equal(t, botDraftPR.Head.GetRef(), node.TempPRName.String()) // verify same branch names
		// if pr is in front, then checks must be running, else running or succeeded
		var diff bool
		require.Contains(t, []string{"unknown", "clean"}, botDraftPR.GetMergeableState()) // 1C
		if node == nmq.MQ.Front {
			require.Equal(t, "unknown", botDraftPR.GetMergeableState())         // 1D
			diff, err = g.DiffExists(ctx, node.BaseRef, nmq.Config.MergeBranch) // 2B
		} else {
			diff, err = g.DiffExists(ctx, node.BaseRef, node.Next.TempPRName.HeadSHA) // 2A
		}
		require.NoError(t, err)
		require.False(t, diff)
		// headSHA of botPR must match as merge between baseRef and headSHA of PR
		prCommit := g.searchCommit(node.HeadSHA)
		require.NotNil(t, prCommit)
		baseRefCommit := g.searchCommit(node.BaseRef)
		require.NotNil(t, baseRefCommit)
		require.False(t, CommitsConflict(prCommit, baseRefCommit)) // 1E
		require.Equal(t,
			botDraftPR.GetHead().GetSHA(),
			NewInMemoryCommit(nil, prCommit, baseRefCommit).SHA) // 3A 3B
	}
}

func (gh *InMemoryGithubWrapper) GeneratePullRequestEvent(t *testing.T, pr int, action, sender, untrackedLabel string) common.MessageData {
	// if action is unlabeled, then tester must provide the label which was untracked
	ctx := context.Background()
	pullRequest, err := gh.GetPR(ctx, pr)
	require.NoError(t, err)
	prEvent := github.PullRequestEvent{
		Action:      &action,
		Number:      &pr,
		PullRequest: pullRequest,
		Sender: &github.User{
			Login: &sender,
		},
		Label: &github.Label{
			Name: &untrackedLabel,
		},
	}
	payload, err := json.Marshal(prEvent)
	require.NoError(t, err)
	return common.MessageData{
		Payload:   payload,
		EventType: "pull_request",
	}
}

func (gh *InMemoryGithubWrapper) GenerateCheckSuiteEvent(t *testing.T, pr int, status, conclusion, user string) common.MessageData {
	ctx := context.Background()
	pullRequest, err := gh.GetPR(ctx, pr)
	require.NoError(t, err)
	checkSuiteEvent := github.CheckSuiteEvent{
		Action: github.String("completed"),
		CheckSuite: &github.CheckSuite{
			HeadBranch: github.String(pullRequest.GetHead().GetRef()),
			Status:     github.String(status),
			Conclusion: github.String(conclusion),
			PullRequests: []*github.PullRequest{
				pullRequest,
			},
		},
		Sender: &github.User{
			Login: github.String(user),
		},
	}
	payload, err := json.Marshal(checkSuiteEvent)
	require.NoError(t, err)
	return common.MessageData{
		EventType: "check_suite",
		Payload:   payload,
	}
}

func (gh *InMemoryGithubWrapper) GeneratePullRequestReviewEvent(t *testing.T, pr int, state string) common.MessageData {
	ctx := context.Background()
	pullRequest, err := gh.GetPR(ctx, pr)
	require.NoError(t, err)
	pullRequestReviewEvent := github.PullRequestReviewEvent{
		Action: github.String("submitted"),
		Review: &github.PullRequestReview{
			State: github.String(state),
		},
		PullRequest: pullRequest,
	}
	payload, err := json.Marshal(pullRequestReviewEvent)
	require.NoError(t, err)
	return common.MessageData{
		EventType: "pull_request_review",
		Payload:   payload,
	}
}

func (gh *InMemoryGithubWrapper) GeneratePullRequestReviewThreadEvent(t *testing.T, pr int, action string) common.MessageData {
	ctx := context.Background()
	pullRequest, err := gh.GetPR(ctx, pr)
	require.NoError(t, err)
	pullRequestReviewThreadEvent := github.PullRequestReviewThreadEvent{
		Action:      github.String(action),
		PullRequest: pullRequest,
	}
	payload, err := json.Marshal(pullRequestReviewThreadEvent)
	require.NoError(t, err)
	return common.MessageData{
		EventType: "pull_request_review_thread",
		Payload:   payload,
	}
}

func (gh *InMemoryGithubWrapper) GeneratePushEvent(t *testing.T, user, branch string) common.MessageData {
	pushEvent := github.PushEvent{
		Ref: github.String(fmt.Sprintf("refs/heads/%s", branch)),
		Sender: &github.User{
			Login: github.String(user),
		},
	}
	payload, err := json.Marshal(pushEvent)
	require.NoError(t, err)
	return common.MessageData{
		EventType: "push",
		Payload:   payload,
	}
}
