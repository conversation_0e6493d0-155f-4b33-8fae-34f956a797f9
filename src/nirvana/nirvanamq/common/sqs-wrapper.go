package common

import (
	"context"
	"fmt"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
)

// SQSWrapper interface for SQS
type SQSWrapper interface {
	SendMessage(ctx context.Context, msg *sqs.SendMessageInput, optFns ...func(*sqs.Options)) (*sqs.SendMessageOutput, error)
}

// SQSMock is a mock implementation of SQS
type SQSMock struct {
	MessageOut []string
	ShouldFail bool // ShouldFail is used to simulate internal error in SQS
}

// NewSQSMock is constructor for SQSMock
func NewSQSMock() *SQSMock {
	return &SQSMock{
		MessageOut: []string{},
		ShouldFail: false,
	}
}

// SendMessage is a mock implementation of SendMessage for SQS Client
func (s *SQSMock) SendMessage(ctx context.Context, msg *sqs.SendMessageInput, optFns ...func(*sqs.Options)) (*sqs.SendMessageOutput, error) {
	if s.ShouldFail {
		return nil, fmt.Errorf("error adding message to queue")
	}
	s.MessageOut = append(s.MessageOut, *msg.MessageBody)
	return &sqs.SendMessageOutput{
		MessageId: aws.String(fmt.Sprintf("MessageId-%d", len(s.MessageOut))),
	}, nil
}

// GetMessages is used to get the messages sent to SQSMock
func (s *SQSMock) GetMessages() []string {
	return s.MessageOut
}

type MessageData struct {
	Payload   []byte `json:"payload"`
	EventType string `json:"event_type"`
}
