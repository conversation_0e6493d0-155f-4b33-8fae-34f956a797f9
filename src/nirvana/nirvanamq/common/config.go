package common

import (
	"encoding/base64"
	"os"
	"strconv"

	"github.com/cockroachdb/errors"
)

// Config is used to store configuration for the lambda function
type Config struct {
	SQSQueueURL       string `json:"queue_url"`
	S3Bucket          string `json:"bucket"`
	SecretToken       string `json:"secret_token"`
	AppID             int64  `json:"app_id"`
	PrivateKey        []byte `json:"private_key"`
	Repo              string `json:"repo"`
	Owner             string `json:"owner"`
	MergeMethod       string `json:"merge_method"`
	MergeBranch       string `json:"branch"`
	TrackLabel        string `json:"track_label"`
	SkipAheadLabel    string `json:"skip_ahead_label"`
	SpeculativeDegree int    `json:"speculative_degree"`
}

func NewProdLambdaConfig() (*Config, error) {
	queueURL := os.Getenv("SQS_QUEUE_URL")
	if queueURL == "" {
		return nil, errors.New("queue url not found")
	}
	s3bucket := os.Getenv("S3_BUCKET")
	if s3bucket == "" {
		return nil, errors.New("s3 bucket not found")
	}
	secretToken := os.Getenv("SECRET_TOKEN")
	if secretToken == "" {
		return nil, errors.New("secret token not found")
	}
	return &Config{
		SQSQueueURL: queueURL,
		S3Bucket:    s3bucket,
		SecretToken: secretToken,
	}, nil
}

func NewProdMQConfig() (*Config, error) {
	appID := os.Getenv("APP_ID")
	if appID == "" {
		return nil, errors.New("app id not found")
	}
	appid, err := strconv.Atoi(appID)
	if err != nil {
		return nil, errors.Wrap(err, "Unable to convert app id to int")
	}
	privateKey := os.Getenv("PRIVATE_KEY")
	if privateKey == "" {
		return nil, errors.New("private key not found")
	}
	decoded, err := base64.StdEncoding.DecodeString(privateKey)
	if err != nil {
		return nil, errors.Wrap(err, "Unable to decode private key from base64")
	}
	queueURL := os.Getenv("SQS_QUEUE_URL")
	if queueURL == "" {
		return nil, errors.New("queue url not found")
	}
	repo := os.Getenv("REPO")
	if repo == "" {
		return nil, errors.New("repo not found")
	}
	owner := os.Getenv("OWNER")
	if owner == "" {
		return nil, errors.New("owner not found")
	}
	mergeMethod := os.Getenv("MERGE_METHOD")
	if mergeMethod == "" {
		mergeMethod = "squash"
	}
	mergeBranch := os.Getenv("MERGE_BRANCH")
	if mergeBranch == "" {
		mergeBranch = "main"
	}
	trackLabel := os.Getenv("TRACK_LABEL")
	if trackLabel == "" {
		trackLabel = "merge_ready"
	}
	skipAheadLabel := os.Getenv("SKIP_AHEAD_LABEL")
	if skipAheadLabel == "" {
		skipAheadLabel = "skip_ahead"
	}
	speculative := os.Getenv("SPECULATIVE")
	if speculative == "" {
		speculative = "1"
	}
	spec, err := strconv.Atoi(speculative)
	if err != nil {
		return nil, errors.Wrap(err, "Unable to convert speculative to int")
	}
	if spec <= 0 {
		return nil, errors.New("speculative must be greater than 0")
	}
	return &Config{
		AppID:             int64(appid),
		PrivateKey:        decoded,
		Repo:              repo,
		Owner:             owner,
		MergeMethod:       mergeMethod,
		MergeBranch:       mergeBranch,
		TrackLabel:        trackLabel,
		SkipAheadLabel:    skipAheadLabel,
		SpeculativeDegree: spec,
		SQSQueueURL:       queueURL,
	}, nil
}

// NewTestConfig is used to create a new config struct for testing
func NewTestConfig() *Config {
	// SecretToken must not be changed as the testCases have their signature set using this value. If changed, it will lead to error in signature verification.
	return &Config{
		SQSQueueURL:       "http://test-queue",
		S3Bucket:          "test-bucket",
		SecretToken:       "thisisasecret",
		AppID:             12345,
		PrivateKey:        []byte("test-private-key"),
		Repo:              "test-repo",
		Owner:             "test-owner",
		MergeMethod:       "squash",
		MergeBranch:       "main",
		TrackLabel:        "merge_ready",
		SkipAheadLabel:    "skip_ahead",
		SpeculativeDegree: 5,
	}
}
