load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "static_param",
    srcs = ["set_static_param.go"],
    importpath = "nirvanatech.com/nirvana/api-server/handlers/billing/static_param",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/api-server/common",
        "//nirvana/api-server/interceptors/billing/deps",
        "//nirvana/billing/bpid",
        "//nirvana/billing/enums",
        "//nirvana/common-go/log",
        "//nirvana/common-go/time_utils",
        "//nirvana/db-api/db_wrappers/policy",
        "//nirvana/infra/authz",
        "//nirvana/openapi-specs/components/billing",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_shopspring_decimal//:decimal",
    ],
)
