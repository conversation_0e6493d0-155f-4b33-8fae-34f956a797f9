package static_param

import (
	"context"
	"database/sql"

	"github.com/cockroachdb/errors"
	"github.com/shopspring/decimal"

	"nirvanatech.com/nirvana/api-server/common"
	billing_interceptor "nirvanatech.com/nirvana/api-server/interceptors/billing/deps"
	"nirvanatech.com/nirvana/billing/bpid"
	billing_enums "nirvanatech.com/nirvana/billing/enums"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	"nirvanatech.com/nirvana/infra/authz"
	oapi_billing "nirvanatech.com/nirvana/openapi-specs/components/billing"
)

// HandleSetStaticParam sets the static param specified by the request's values. Do note that:
//  1. It receives a policy number as input for improved UX, but still stores the static param in a prefix-agnostic way.
//  2. Some values (e.g. GLPremium) will go through additional processing to be transformed into rates. This allows us to
//     provide APIs that are compatible with how users operate on these numbers (e.g. insurers talk in terms of
//     GLPremium and not in terms of GLRate).
func HandleSetStaticParam(
	ctx context.Context,
	deps billing_interceptor.Deps,
	req oapi_billing.SetStaticParamRequest,
) error {
	user := authz.UserFromContext(ctx)
	ctx = log.ContextWithFields(
		ctx,
		log.String("operation", "HandleSetStaticParam"),
		log.String("user", user.Email),
		log.Any("request", req),
	)
	log.Info(ctx, "setting static param")

	if err := validateSetStaticParamRequest(req); err != nil {
		log.Error(ctx, "invalid request", log.Err(err))
		return common.NewNirvanaBadRequestErrorf(err, "", "")
	}

	p, err := deps.PolicyClient.GetLatestPolicy(ctx, req.PolicyNumber, false)
	if err != nil {
		log.Error(ctx, "unable to get policy", log.Err(err))
		if errors.Is(err, sql.ErrNoRows) {
			return common.NewNirvanaNotFoundErrorf(err, common.EntityPolicy, req.PolicyNumber)
		}
		return err
	}
	bpId := bpid.NewBillingPolicyIdFromPolicyNumber(*p.PolicyNumber)

	tv, err := getTypedValue(req.Type, req.Value, *p)
	if err != nil {
		log.Error(ctx, "unable to get typed value", log.Err(err))
		return common.NewNirvanaBadRequestErrorf(err, "", "")
	}

	if err := deps.StaticParamClient.Set(ctx, bpId, *tv, time_utils.DateFromTime(req.ValidFrom.Time)); err != nil {
		log.Error(ctx, "unable to set typed value", log.Err(err))
		return errors.Wrap(err, "unable to set typed value")
	}
	return nil
}

// validateSetStaticParamRequest checks if the SetStaticParamRequest is valid, and returns an error if it is not.
func validateSetStaticParamRequest(req oapi_billing.SetStaticParamRequest) error {
	if req.PolicyNumber == "" {
		return errors.New("empty policy number")
	}
	if req.ValidFrom.IsZero() {
		return errors.New("zero-value ValidFrom")
	}
	return nil
}

// getTypedValue instantiates a billing_enums.TypedValue from the SetStaticParamRequest values.
func getTypedValue(
	paramType oapi_billing.StaticParamType,
	value float64,
	p policy.Policy,
) (*billing_enums.TypedValue, error) {
	var tv billing_enums.TypedValue
	d := decimal.NewFromFloat(value)
	switch paramType {
	case oapi_billing.ALRate:
		tv = billing_enums.StaticParamTypeALRate.TypedValue(d)
	case oapi_billing.APDRate:
		tv = billing_enums.StaticParamTypeAPDRate.TypedValue(d)
	case oapi_billing.MTCRate:
		tv = billing_enums.StaticParamTypeMTCRate.TypedValue(d)
	case oapi_billing.GLPremium:
		tv = billing_enums.StaticParamTypeGLRate.DailyTypedValue(d, time_utils.DateInterval{
			Start: time_utils.DateFromTime(p.EffectiveDate),
			End:   time_utils.DateFromTime(p.EffectiveDateTo),
		})
	case oapi_billing.TIV:
		tv = billing_enums.StaticParamTypeTIV.TypedValue(d)
	case oapi_billing.AgencyCommissionRate:
		tv = billing_enums.StaticParamTypeAgencyCommission.TypedValue(d)
	default:
		return nil, errors.Newf("unsupported paramType %s", paramType)
	}
	return &tv, nil
}
