package pipeline

import (
	"context"

	"github.com/cockroachdb/errors"

	billing_interceptor "nirvanatech.com/nirvana/api-server/interceptors/billing/deps"
	"nirvanatech.com/nirvana/billing/fleet_pipeline/jobs"
	"nirvanatech.com/nirvana/billing/fleet_pipeline/jobs/messages"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/jobber"
	"nirvanatech.com/nirvana/jobber/jtypes"
	oapi_billing "nirvanatech.com/nirvana/openapi-specs/components/billing"
)

// HandleTriggerDisputesPipeline triggers the disputes pipeline for the given billing pipeline run, whose reported
// consumption is being disputed and therefore needs to be amended.
func HandleTriggerDisputesPipeline(
	ctx context.Context,
	deps billing_interceptor.Deps,
	req oapi_billing.TriggerDisputesPipelineRequest,
) (*oapi_billing.TriggerDisputesPipelineResponse, error) {
	ctx = log.ContextWithFields(
		ctx,
		log.String("operation", "HandleTriggerDisputesPipeline"),
		log.Stringer("disputedPipelineRunId", req.DisputedPipelineRunId),
	)

	deps.NotificationClient.SendWithUser(ctx, "Triggering disputes pipeline")

	jobRunId, err := deps.Jobber.AddJobRun(ctx, jobber.NewAddJobRunParams(
		jobs.DisputesPipeline,
		&messages.DisputesPipelineMessage{
			DisputedPipelineRunId: req.DisputedPipelineRunId,
		},
		jtypes.NewMetadata(jtypes.Immediate),
	))
	if err != nil {
		log.Error(ctx, "unable to add job run for disputes pipeline", log.Err(err))
		return nil, errors.Wrapf(err, "unable to add job run for disputes pipeline")
	}

	return &oapi_billing.TriggerDisputesPipelineResponse{
		JobRunId: jobRunId.String(),
	}, nil
}
