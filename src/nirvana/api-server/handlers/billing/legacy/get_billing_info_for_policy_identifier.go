package legacy

import (
	"context"
	"database/sql"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/benb<PERSON><PERSON><PERSON>/clock"
	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	openapi_types "github.com/oapi-codegen/runtime/types"
	"github.com/shopspring/decimal"

	"nirvanatech.com/nirvana/api-server/handlers/application"
	billing_interceptor "nirvanatech.com/nirvana/api-server/interceptors/billing/deps"
	"nirvanatech.com/nirvana/billing/bpid"
	"nirvanatech.com/nirvana/billing/enums"
	billing_policy "nirvanatech.com/nirvana/billing/legacy/policy"
	"nirvanatech.com/nirvana/billing/monitoring/metrics"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/str_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy/program_data/fleet"
	"nirvanatech.com/nirvana/db-api/db_wrappers/support"
	sfdc_wrapper "nirvanatech.com/nirvana/external_client/salesforce/wrapper"
	"nirvanatech.com/nirvana/infra/authz"
	oapi_billing "nirvanatech.com/nirvana/openapi-specs/components/billing"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
)

var (
	ErrNoPrimaryBillingContact              = errors.New("DOT does not have primary billing contact")
	ErrEmptyCountOfDepositInstallmentsField = errors.New("count of deposit installments field is empty")
)

func HandleGetBillingInfoForPolicyIdentifier(
	ctx context.Context,
	deps billing_interceptor.Deps,
	policyIdentifier oapi_common.PolicyIdentifier,
	policyIssuanceYear oapi_common.PolicyIssuanceYear,
) GetBillingInfoForPolicyIdentifierResponse {
	ctx = log.ContextWithFields(
		ctx,
		log.String("operation", "HandleGetBillingInfoForPolicyIdentifier"),
		log.String("policyIdentifier", policyIdentifier),
		log.Int16("policyIssuanceYear", policyIssuanceYear),
	)

	var err error
	defer func() {
		deps.MetricsClient.IncOperation(ctx, metrics.OperationGetBillingInfoForPolicyIdentifier, err)
	}()

	handleServerError := func(message string, e error) GetBillingInfoForPolicyIdentifierResponse {
		log.Error(ctx, message, log.Err(e))
		return GetBillingInfoForPolicyIdentifierResponse{
			ServerError: &oapi_common.ErrorMessage{Message: message},
		}
	}

	policies, err := deps.PolicyWrapper.GetAllPolicies(ctx, &policy.GetRequest{
		Filters: []policy.Filter{
			policy.PolicyIdentifierIs(policyIdentifier),
			policy.PolicyIssuanceYearIs(policyIssuanceYear),
		},
		IncludeProgramData: true,
	})
	if err != nil {
		return handleServerError("Failed to load policies for identifier + issuance year", err)
	}
	if len(policies) == 0 {
		msg := "No policies found for identifier + issuance year"
		log.Error(ctx, msg)
		return GetBillingInfoForPolicyIdentifierResponse{
			NotFoundError: &oapi_common.ErrorMessage{Message: msg},
		}
	}

	samplePolicy := *policies[0]
	dotNumber := samplePolicy.CompanyInfo.DOTNumber

	agencyName, err := getAgencyName(ctx, samplePolicy)
	if err != nil {
		return handleServerError("Failed to load agency name", err)
	}

	agent, err := getAgent(ctx, deps, samplePolicy)
	if err != nil {
		return handleServerError("Failed to load agent", err)
	}

	primaryBillingContact, err := getPrimaryBillingContact(ctx, deps.SupportWrapper, dotNumber)
	if err != nil && !errors.Is(err, ErrNoPrimaryBillingContact) {
		return handleServerError("Failed to compose primary billing contact", err)
	}

	billingFrequency, err := getBillingFrequency(samplePolicy)
	if err != nil {
		return handleServerError("Failed to compose billing frequency", err)
	}

	rates, err := getBillingRates(ctx, deps, samplePolicy)
	if err != nil {
		return handleServerError("Failed to compose rates", err)
	}

	policySummaries, err := getPolicySummaries(ctx, deps.Clock, policies)
	if err != nil {
		return handleServerError("Failed to compose policy summaries", err)
	}

	overallEffectiveDate, overallEffectiveDateTo := getOverallDates(policySummaries)
	overallStatus, err := getOverallStatus(policySummaries)
	if err != nil {
		return handleServerError("Failed to compose overall status", err)
	}

	canCancel, err := deps.PolicyClient.CanCancel(ctx, samplePolicy.PolicyNumber.String())
	if err != nil {
		return handleServerError("Failed to check if policy can be cancelled", err)
	}

	canReinstate, err := deps.PolicyClient.CanReinstate(ctx, samplePolicy.PolicyNumber.String())
	if err != nil {
		return handleServerError("Failed to check if policy can be reinstated", err)
	}

	countOfDepositInstallments, err := getCountOfDepositInstallments(ctx, deps.SalesforceWrapper, samplePolicy.ApplicationId.String())
	if err != nil && !errors.Is(err, ErrEmptyCountOfDepositInstallmentsField) {
		err = errors.Wrap(err, "Failed to get count of deposit installments")
	} else {
		err = nil
	}

	response := &oapi_billing.GetBillingInfoForPolicyIdentifierResponse{
		ApplicationId:              samplePolicy.ApplicationId,
		AgencyName:                 *agencyName,
		AgentEmail:                 agent.Email,
		AgentName:                  agent.FullName(),
		BillingFrequency:           *billingFrequency,
		Carrier:                    samplePolicy.InsuranceCarrier.String(),
		EffectiveDate:              openapi_types.Date{Time: overallEffectiveDate},
		EffectiveDateTo:            openapi_types.Date{Time: overallEffectiveDateTo},
		InsuredName:                samplePolicy.CompanyInfo.Name,
		PrimaryBillingContact:      primaryBillingContact,
		PolicyIdentifier:           samplePolicy.PolicyNumber.GetPolicyIdentifier(),
		PolicySummaries:            policySummaries,
		Rates:                      *rates,
		Status:                     *overallStatus,
		CanCancel:                  canCancel,
		CanReinstate:               canReinstate,
		PolicySetId:                samplePolicy.PolicySetId,
		CountOfDepositInstallments: countOfDepositInstallments,
	}
	return GetBillingInfoForPolicyIdentifierResponse{
		Response: response,
	}
}

func getAgencyName(ctx context.Context, p policy.Policy) (*string, error) {
	if p.ProgramType != policy_enums.ProgramTypeFleet {
		// TODO(sasalatart): handle non-fleet
		return nil, errors.New("Only fleet program type supported for now")
	}

	programData, err := p.GetFleetProgramData(ctx)
	if err != nil {
		return nil, err
	}
	return &programData.AgencyInfo.Name, nil
}

func getAgent(ctx context.Context, deps billing_interceptor.Deps, p policy.Policy) (*authz.UserInfo, error) {
	app, err := deps.ApplicationWrapper.GetAppById(ctx, p.ApplicationId.String())
	if err != nil {
		return nil, errors.Wrapf(err, "Failed to get application %s", p.ApplicationId)
	}

	agentId, err := uuid.Parse(app.CreatedBy)
	if err != nil {
		return nil, err
	}
	return deps.AuthWrapper.FetchUserInfo(ctx, agentId)
}

func getPrimaryBillingContact(ctx context.Context, sw support.DataWrapper, dotNumber int64) (*oapi_billing.ContactSummary, error) {
	dotInfo, err := sw.FetchDotInfo(ctx, int(dotNumber))
	if err != nil && errors.Is(err, sql.ErrNoRows) {
		return nil, ErrNoPrimaryBillingContact
	}
	if err != nil {
		return nil, err
	}
	billingContacts, ok := dotInfo.Contacts[support.ContactTypeBilling]
	if !ok {
		return nil, ErrNoPrimaryBillingContact
	}
	var primaryBillingContact *support.ContactRecord
	for _, c := range billingContacts {
		if c.POCType == support.POCTypePrimary {
			primaryBillingContact = &c
			break
		}
	}
	if primaryBillingContact == nil {
		return nil, ErrNoPrimaryBillingContact
	}
	contact := oapi_billing.ContactSummary{
		FirstName: primaryBillingContact.FirstName,
		LastName:  primaryBillingContact.LastName,
		Email:     primaryBillingContact.Email,
	}
	return &contact, nil
}

// getOverallDates returns the earliest "effective date" and the latest "effective date to" among a list of policy summaries
func getOverallDates(policySummaries []oapi_billing.PolicySummary) (time.Time, time.Time) {
	earliestEffectiveDate := policySummaries[0].EffectiveDate.Time
	latestEffectiveDateTo := policySummaries[0].EffectiveDateTo.Time
	for _, p := range policySummaries {
		if p.EffectiveDate.Before(earliestEffectiveDate) {
			earliestEffectiveDate = p.EffectiveDate.Time
		}

		if p.EffectiveDateTo.After(latestEffectiveDateTo) {
			latestEffectiveDateTo = p.EffectiveDateTo.Time
		}
	}
	return earliestEffectiveDate, latestEffectiveDateTo
}

// getOverallStatus returns "Active" if there is at least one policy whose status is active, otherwise "Expiring Soon"
// if at least one policy is expiring soon, otherwise "Cancelled" if all the policies have been cancelled, and
// "Expiring Soon" in any other case
func getOverallStatus(
	policySummaries []oapi_billing.PolicySummary,
) (*oapi_common.PolicyStatus, error) {
	activeCount := 0
	cancelledCount := 0
	cancellationPendingCount := 0
	expiredCount := 0
	expiringSoonCount := 0
	upcomingCount := 0

	for _, p := range policySummaries {
		switch p.Status {
		case oapi_common.PolicyStatusActive:
			activeCount++
		case oapi_common.PolicyStatusCancelled:
			cancelledCount++
		case oapi_common.PolicyStatusCancellationPending:
			cancellationPendingCount++
		case oapi_common.PolicyStatusExpired:
			expiredCount++
		case oapi_common.PolicyStatusExpiringSoon:
			expiringSoonCount++
		case oapi_common.PolicyStatusUpcoming:
			upcomingCount++
		default:
			return nil, errors.Newf("Unexpected policy summary status: %s", p.Status)
		}
	}

	if activeCount > 0 {
		return pointer_utils.ToPointer(oapi_common.PolicyStatusActive), nil
	}

	if expiringSoonCount > 0 {
		return pointer_utils.ToPointer(oapi_common.PolicyStatusExpiringSoon), nil
	}

	if len(policySummaries) == cancelledCount {
		return pointer_utils.ToPointer(oapi_common.PolicyStatusCancelled), nil
	}

	if len(policySummaries) == cancellationPendingCount {
		return pointer_utils.ToPointer(oapi_common.PolicyStatusCancellationPending), nil
	}

	if len(policySummaries) == upcomingCount {
		return pointer_utils.ToPointer(oapi_common.PolicyStatusUpcoming), nil
	}

	// Covers scenarios where there are only expired policies, or a mixture between expired and cancelled
	return pointer_utils.ToPointer(oapi_common.PolicyStatusExpired), nil
}

func getPolicySummaries(ctx context.Context, clk clock.Clock, policies []*policy.Policy) ([]oapi_billing.PolicySummary, error) {
	var summaries []oapi_billing.PolicySummary
	for _, p := range policies {
		coverageName, err := getCoverageName(ctx, *p)
		if err != nil {
			return summaries, err
		}

		policyRESTStatus, err := application.BindPolicyStatusToRest(*p, clk.Now())
		if err != nil {
			return summaries, err
		}

		summaries = append(summaries, oapi_billing.PolicySummary{
			ID:              p.Id,
			CoverageName:    *coverageName,
			EffectiveDate:   openapi_types.Date{Time: p.EffectiveDate},
			EffectiveDateTo: openapi_types.Date{Time: p.EffectiveDateTo},
			PolicyNumber:    p.PolicyNumber.String(),
			Status:          *policyRESTStatus,
		})
	}
	return summaries, nil
}

func getCoverageName(ctx context.Context, p policy.Policy) (*string, error) {
	if p.ProgramType != policy_enums.ProgramTypeFleet {
		// TODO(sasalatart): handle non-fleet coverage names
		return nil, errors.New("Only fleet program type supported for now")
	}

	programData, err := p.GetFleetProgramData(ctx)
	if err != nil {
		return nil, err
	}

	coverageNames := formatCoverageNames(programData.CoverageInfo.PrimaryCoverages)
	return &coverageNames, nil
}

// formatCoverageNames formats a slice of coverages into a single string.
func formatCoverageNames(coverages []fleet.CoverageDetails) string {
	coverageNames := make([]string, 0, len(coverages))

	for _, coverage := range coverages {
		coverageNames = append(coverageNames, str_utils.PrettyEnumString(coverage.CoverageType.String(), "Coverage"))
	}

	concatenatedName := strings.Join(coverageNames, " + ")
	return concatenatedName
}

func getBillingRates(
	ctx context.Context,
	deps billing_interceptor.Deps,
	p policy.Policy,
) (*oapi_billing.BillingRates, error) {
	// we usually want the static params for today. But if the policy is not yet effective, we want them
	// for the effective date of the policy, since otherwise we wouldn't have anything.
	dateForStaticParams := time_utils.DateFromTime(
		time_utils.Maximum(
			deps.Clock.Now(),
			p.EffectiveDate,
		),
	)
	billingPolicyId := bpid.NewBillingPolicyIdFromPolicyNumber(*p.PolicyNumber)

	// Wrapper on top of GetValueForDate such that the absence of a static param is assumed to be 0.
	getStaticParamOrZero := func(paramType enums.StaticParamType) (decimal.Decimal, error) {
		v, err := deps.StaticParamClient.GetValueForDate(ctx, billingPolicyId, paramType, dateForStaticParams)
		if err != nil && errors.Is(err, sql.ErrNoRows) {
			return decimal.Zero, nil
		}
		if err != nil {
			return decimal.Zero, err
		}
		return v, nil
	}

	commissionRate, err := getStaticParamOrZero(enums.StaticParamTypeAgencyCommission)
	if err != nil {
		return nil, err
	}

	alRate, err := getStaticParamOrZero(enums.StaticParamTypeALRate)
	if err != nil {
		return nil, err
	}

	annualizedAPDPremium, err := deps.BillingPolicyClient.GetAnnualizedAPDPremium(ctx, billingPolicyId, dateForStaticParams)
	if err != nil {
		return nil, err
	}

	mtcRate, err := getStaticParamOrZero(enums.StaticParamTypeMTCRate)
	if err != nil {
		return nil, err
	}

	glRate, err := getStaticParamOrZero(enums.StaticParamTypeGLRate)
	if err != nil {
		return nil, err
	}
	originalNumberOfDays, err := billing_policy.GetOriginalNumberOfDays(ctx, deps.PolicyClient, p.PolicyNumber.String())
	if err != nil {
		return nil, err
	}
	annualizedGLPremium := glRate.Mul(originalNumberOfDays)

	br := p.BillingInfo.ToPrecise()
	commissionRateValue, _ := commissionRate.Float64()

	return &oapi_billing.BillingRates{
		ALRate:               rateToRest(alRate),
		APDPremium:           premiumToRest(annualizedAPDPremium),
		CommissionRate:       commissionRateValue,
		DepositAmount:        premiumToRest(br.DepositAmount),
		FlatCharge:           premiumToRest(br.FlatCharge),
		GLPremium:            premiumToRest(annualizedGLPremium),
		MTCRate:              rateToRest(mtcRate),
		StateSurcharge:       premiumToRest(br.StateSurcharge),
		YearlyEstimatedMiles: mileageToRest(br.ProjectedMiles),
		CollateralAmount:     p.BillingInfo.CollateralAmount,
	}, nil
}

func getBillingFrequency(p policy.Policy) (*oapi_common.BillingFrequency, error) {
	var billingFrequency oapi_common.BillingFrequency
	switch p.BillingInfo.PaymentMethod {
	case enums.PaymentMethodMonthlyReporter:
		billingFrequency = oapi_common.BillingFrequencyMonthly
	case enums.PaymentMethodPaidInFull:
		billingFrequency = oapi_common.BillingFrequencyPaidInFull
	default:
		return nil, errors.Newf("invalid billing payment method: %s", p.BillingInfo.PaymentMethod)
	}
	return &billingFrequency, nil
}

func getCountOfDepositInstallments(
	ctx context.Context,
	sfdcWrapper sfdc_wrapper.SalesforceWrapper,
	applicationId string,
) (*int, error) {
	sfdcFieldName := sfdc_wrapper.OpportunitySalesforceObjectFieldCountOfDepositInstallments

	sfdcOpportunityFields, err := sfdcWrapper.GetOpportunityFieldsForApplicationId(
		ctx,
		applicationId,
		sfdcFieldName,
	)
	if err != nil {
		return nil, errors.Wrapf(
			err,
			"Failed to get count of deposit installments for application id %s",
			applicationId,
		)
	}

	countOfDepositInstallmentsField, ok := sfdcOpportunityFields[sfdcFieldName]
	if !ok {
		return nil, ErrEmptyCountOfDepositInstallmentsField
	}

	countOfDepositInstallmentsStr, ok := countOfDepositInstallmentsField.(string)
	if !ok {
		return nil, errors.Newf(
			"count of deposit installments field has unexpected type %T: %v",
			countOfDepositInstallmentsField,
			countOfDepositInstallmentsField,
		)
	}

	countOfDepositInstallments, err := strconv.Atoi(countOfDepositInstallmentsStr)
	if err != nil {
		return nil, errors.Wrapf(
			err,
			"Failed to parse count of deposit installments field as integer: %s",
			countOfDepositInstallmentsStr,
		)
	}

	return &countOfDepositInstallments, nil
}

type GetBillingInfoForPolicyIdentifierResponse struct {
	ServerError   *oapi_common.ErrorMessage
	NotFoundError *oapi_common.ErrorMessage
	Response      *oapi_billing.GetBillingInfoForPolicyIdentifierResponse
}

func (g *GetBillingInfoForPolicyIdentifierResponse) StatusCode() int {
	switch {
	case g.Response != nil:
		return http.StatusOK
	case g.ServerError != nil:
		return http.StatusInternalServerError
	case g.NotFoundError != nil:
		return http.StatusNotFound
	default:
		return http.StatusInternalServerError
	}
}

func (g *GetBillingInfoForPolicyIdentifierResponse) Body() interface{} {
	switch {
	case g.Response != nil:
		return *g.Response
	case g.ServerError != nil:
		return *g.ServerError
	case g.NotFoundError != nil:
		return *g.NotFoundError
	default:
		return nil
	}
}
