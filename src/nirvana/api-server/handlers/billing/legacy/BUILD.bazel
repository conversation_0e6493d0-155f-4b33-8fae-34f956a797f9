load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "legacy",
    srcs = [
        "get_billing_info_for_all_dots.go",
        "get_billing_info_for_policy_identifier.go",
        "get_mileage_sources_for_policy_identifier.go",
        "get_monthly_billing_info.go",
        "patch_billing_info.go",
        "serde.go",
        "transition_mileage_source_override_status.go",
        "upsert_mileage_sources_overrides.go",
    ],
    importpath = "nirvanatech.com/nirvana/api-server/handlers/billing/legacy",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/api-server/common",
        "//nirvana/api-server/handlers/application",
        "//nirvana/api-server/helpers",
        "//nirvana/api-server/interceptors/billing/deps",
        "//nirvana/billing/bpid",
        "//nirvana/billing/enums",
        "//nirvana/billing/exposure/mileage",
        "//nirvana/billing/legacy/mileagesrc",
        "//nirvana/billing/legacy/policy",
        "//nirvana/billing/monitoring/metrics",
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/str_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/billing/mileage_source_override",
        "//nirvana/db-api/db_wrappers/billing/pipeline_run",
        "//nirvana/db-api/db_wrappers/billing/report",
        "//nirvana/db-api/db_wrappers/policy",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/db-api/db_wrappers/policy/program_data/fleet",
        "//nirvana/db-api/db_wrappers/support",
        "//nirvana/external_client/salesforce/wrapper",
        "//nirvana/infra/authz",
        "//nirvana/jobber/jtypes",
        "//nirvana/openapi-specs/api_server_billing",
        "//nirvana/openapi-specs/components/billing",
        "//nirvana/openapi-specs/components/common",
        "//nirvana/telematics",
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_oapi_codegen_runtime//types",
        "@com_github_shopspring_decimal//:decimal",
    ],
)

go_test(
    name = "legacy_test",
    srcs = [
        "get_billing_info_for_all_dots_test.go",
        "get_billing_info_for_policy_identifier_test.go",
        "get_mileage_sources_for_policy_identifier_test.go",
        "get_monthly_billing_info_test.go",
        "patch_billing_info_test.go",
        "transition_mileage_source_override_status_test.go",
        "upsert_mileage_sources_overrides_test.go",
    ],
    data = glob(["testdata/**"]),
    embedsrcs = [
        "testdata/mock_billing_info_for_dots_data.sql",
        "testdata/mock_billing_info_for_policy_identifier_data.sql",
        "testdata/mock_mileage_sources_overrides_data.sql",
    ],
    deps = [
        ":legacy",
        "//nirvana/api-server/interceptors/billing/deps",
        "//nirvana/billing/bpid",
        "//nirvana/billing/builders/pipeline_run",
        "//nirvana/billing/builders/report",
        "//nirvana/billing/enums",
        "//nirvana/billing/exposure/mileage",
        "//nirvana/common-go/feature_flag_lib",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/test_utils/builders/application",
        "//nirvana/common-go/test_utils/builders/policy",
        "//nirvana/common-go/time_utils",
        "//nirvana/common-go/uuid_utils",
        "//nirvana/db-api/db_wrappers/billing/pipeline_run",
        "//nirvana/db-api/db_wrappers/billing/report",
        "//nirvana/db-api/db_wrappers/policy",
        "//nirvana/external_client/salesforce/client",
        "//nirvana/external_client/salesforce/client/enums",
        "//nirvana/external_client/salesforce/wrapper",
        "//nirvana/infra/fx/testfixtures/common_fixture",
        "//nirvana/infra/fx/testfixtures/users_fixture",
        "//nirvana/infra/fx/testloader",
        "//nirvana/openapi-specs/api_server_billing",
        "//nirvana/openapi-specs/components/billing",
        "//nirvana/openapi-specs/components/common",
        "//nirvana/policy/enums",
        "//nirvana/telematics",
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_g8rswimmer_go_sfdc//soql",
        "@com_github_google_uuid//:uuid",
        "@com_github_oapi_codegen_runtime//types",
        "@com_github_shopspring_decimal//:decimal",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@com_github_stretchr_testify//suite",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
        "@org_uber_go_mock//gomock",
    ],
)
