package legacy_test

import (
	"context"
	_ "embed"
	"strconv"
	"testing"

	"github.com/benb<PERSON><PERSON><PERSON>/clock"
	"github.com/g8rswimmer/go-sfdc/soql"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"go.uber.org/mock/gomock"

	"nirvanatech.com/nirvana/api-server/handlers/billing/legacy"
	billing_interceptor "nirvanatech.com/nirvana/api-server/interceptors/billing/deps"
	"nirvanatech.com/nirvana/billing/bpid"
	"nirvanatech.com/nirvana/billing/enums"
	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	application_builder "nirvanatech.com/nirvana/common-go/test_utils/builders/application"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	"nirvanatech.com/nirvana/external_client/salesforce/client"
	sfdc_enums "nirvanatech.com/nirvana/external_client/salesforce/client/enums"
	sfdc_wrapper "nirvanatech.com/nirvana/external_client/salesforce/wrapper"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/common_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/users_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	oapi_billing "nirvanatech.com/nirvana/openapi-specs/components/billing"
	policy_enums "nirvanatech.com/nirvana/policy/enums"
)

//go:embed testdata/mock_billing_info_for_policy_identifier_data.sql
var mockBillingInfoForPolicyIdentifierData string

var mockBillingInfoForPolicyIdentifier = fx.Invoke(func(ff *common_fixture.CommonNirvanaFixture) {
	ff.SqlData = mockBillingInfoForPolicyIdentifierData
})

type getBillingInfoForPolicyIdentifierTestSuite struct {
	fx.In
	suite.Suite
	fxapp      *fxtest.App
	deps       billing_interceptor.Deps
	sfdcClient *client.MockSalesforceClient

	applicationId uuid.UUID

	glPolicyId  uuid.UUID
	alPolicyId  uuid.UUID
	mtcPolicyId uuid.UUID

	effectiveDate  time_utils.Date
	expirationDate time_utils.Date

	featureFlagClient feature_flag_lib.Client
	usersFixture      *users_fixture.UsersFixture
}

type sfdcClientExpectations struct {
	countOfDepositInstallments int
	callTimes                  int
}

func TestHandleGetBillingInfoForPolicyIdentifier(t *testing.T) {
	suite.Run(t, new(getBillingInfoForPolicyIdentifierTestSuite))
}

func (s *getBillingInfoForPolicyIdentifierTestSuite) SetupTest() {
	var env struct {
		fx.In

		Deps              billing_interceptor.Deps
		FeatureFlagClient feature_flag_lib.Client
		UsersFixture      *users_fixture.UsersFixture
		SalesforceClient  *client.MockSalesforceClient
	}

	s.fxapp = testloader.RequireStart(s.T(), &env, testloader.Use(mockBillingInfoForPolicyIdentifier))
	s.deps = env.Deps
	s.sfdcClient = env.SalesforceClient
	s.featureFlagClient = env.FeatureFlagClient
	s.usersFixture = env.UsersFixture

	s.applicationId = uuid.MustParse("a8b61ad4-bbfd-446f-a661-9f61a39140e9")

	s.glPolicyId = uuid.MustParse("be173381-a1d0-431b-a95f-28436102822a")
	s.alPolicyId = uuid.MustParse("4faabbf4-ba9f-4c1e-a2d7-088eea65031e")
	s.mtcPolicyId = uuid.MustParse("93975d9b-c3e0-40d3-92ce-436f8dd67f4d")

	// Matches timestamps of mocked database data
	now := time_utils.NewDate(2023, 7, 17)
	s.deps.Clock.(*clock.Mock).Set(now.ToTime())

	// we make the policies start on the next day, to assert
	// that we correctly retrieve values for upcoming policies.
	s.effectiveDate = now.AddDate(0, 0, 1)
	s.expirationDate = now.AddDate(1, 0, 1)

	s.prepareData()
}

func (s *getBillingInfoForPolicyIdentifierTestSuite) TearDownTest() {
	defer s.fxapp.RequireStop()
}

func (s *getBillingInfoForPolicyIdentifierTestSuite) TestHandleGetBillingInfoForPolicies_NotFound() {
	s.setSalesforceClientExpectations(s.applicationId, sfdcClientExpectations{
		countOfDepositInstallments: 1,
		callTimes:                  0,
	})

	response := legacy.HandleGetBillingInfoForPolicyIdentifier(
		context.Background(),
		s.deps,
		"nonexistent-policy-identifier",
		1990,
	)

	require.NotNil(s.T(), response.NotFoundError)
}

func (s *getBillingInfoForPolicyIdentifierTestSuite) TestHandleGetBillingInfoForPolicies_NoBillingContactDoesNotErrorOut() {
	// policy Identifier 0010002 was not prepared in the database, so it has the appID found in the .sql mock data file
	unpreparedApplicationId := uuid.MustParse("b75ea4a0-8126-4a1f-a487-a90306823f9d")

	s.setSalesforceClientExpectations(unpreparedApplicationId, sfdcClientExpectations{
		countOfDepositInstallments: 3,
		callTimes:                  1,
	})

	response := legacy.HandleGetBillingInfoForPolicyIdentifier(
		context.Background(),
		s.deps,
		"0010002",
		2023,
	)

	require.Nil(s.T(), response.NotFoundError)
	require.Nil(s.T(), response.ServerError)
	require.NotNil(s.T(), response.Response)
}

func (s *getBillingInfoForPolicyIdentifierTestSuite) TestHandleGetBillingInfoForPolicies_Values() {
	policyIdentifier := "0010001"

	s.setSalesforceClientExpectations(s.applicationId, sfdcClientExpectations{
		countOfDepositInstallments: 1,
		callTimes:                  1,
	})

	response := legacy.HandleGetBillingInfoForPolicyIdentifier(
		context.Background(),
		s.deps,
		policyIdentifier,
		2023,
	)

	require.Nil(s.T(), response.ServerError, "Unexpected server error")
	require.Nil(s.T(), response.NotFoundError, "Unexpected not found error")
	require.NotNil(s.T(), response.Response, "Unexpected nil response")

	got := response.Response

	s.Equal("RIVAS TRUCKING LLC", got.InsuredName)
	s.NotEmpty(got.ApplicationId)

	wantPrimaryBillingContact := oapi_billing.ContactSummary{
		FirstName: "Primary",
		LastName:  "Billing",
		Email:     "<EMAIL>",
	}
	s.Require().NotNil(got.PrimaryBillingContact)
	s.Equal(wantPrimaryBillingContact, *got.PrimaryBillingContact)

	s.Equal(policyIdentifier, got.PolicyIdentifier)
	s.Equal("Monthly", string(got.BillingFrequency))
	s.Equal("Active", string(got.Status))
	s.Equal(s.effectiveDate.String(), got.EffectiveDate.String())
	s.Equal(s.expirationDate.String(), got.EffectiveDateTo.String())
	s.Equal("SiriusPoint America Insurance Company", got.Carrier)

	s.Equal(500.0, *got.Rates.CollateralAmount)

	s.Require().Len(got.PolicySummaries, 3)
	var glPolicySummary oapi_billing.PolicySummary
	var alPolicySummary oapi_billing.PolicySummary
	var mtcPolicySummary oapi_billing.PolicySummary
	for _, p := range got.PolicySummaries {
		if p.ID == s.glPolicyId {
			glPolicySummary = p
		}
		if p.ID == s.alPolicyId {
			alPolicySummary = p
		}
		if p.ID == s.mtcPolicyId {
			mtcPolicySummary = p
		}
	}

	s.Equal("Auto Liability", alPolicySummary.CoverageName)
	s.Equal("Active", string(alPolicySummary.Status))
	s.Equal(s.effectiveDate.ToTime(), alPolicySummary.EffectiveDate.Time.UTC())
	s.Equal(s.expirationDate.ToTime(), alPolicySummary.EffectiveDateTo.Time.UTC())

	s.Equal("Motor Truck Cargo", mtcPolicySummary.CoverageName)
	s.Equal("Active", string(mtcPolicySummary.Status))
	s.Equal(s.effectiveDate.ToTime(), mtcPolicySummary.EffectiveDate.Time.UTC())
	s.Equal(s.expirationDate.ToTime(), mtcPolicySummary.EffectiveDateTo.Time.UTC())

	s.Equal("General Liability", glPolicySummary.CoverageName)
	s.Equal("Active", string(glPolicySummary.Status))
	s.Equal(s.effectiveDate.ToTime(), glPolicySummary.EffectiveDate.Time.UTC())
	s.Equal(s.expirationDate.ToTime(), glPolicySummary.EffectiveDateTo.Time.UTC())

	gotStaticParams := got.Rates
	s.Equal(0.10, gotStaticParams.CommissionRate)
	s.Equal(0.15, gotStaticParams.ALRate)
	s.Equal(200_000.0, gotStaticParams.APDPremium) // 1_000_000 * 0.20
	s.Equal(0.25, gotStaticParams.MTCRate)
	s.Equal(109.8, gotStaticParams.GLPremium) // 0.30 * 366 days

	s.Equal(1, *got.CountOfDepositInstallments)
}

// prepareData makes sure that policy timestamps (e.g. effective date, expiration date, issuance year, etc.)
// are consistent with the current date so that the test does not fail in the future (e.g. next year). It also
// internally further updates policies to make assertions easier.
func (s *getBillingInfoForPolicyIdentifierTestSuite) prepareData() {
	ctx := context.Background()

	app := application_builder.
		New(s.featureFlagClient).
		WithDefaultMockData().
		WithCreatedBy(s.usersFixture.AgencyAdmin.ID).
		WithID(s.applicationId).
		Build()
	err := s.deps.ApplicationWrapper.InsertApp(ctx, *app)
	s.Require().NoError(err)

	mustPreparePolicy := func(policyId uuid.UUID) {
		err := s.deps.PolicyWrapper.UpdatePolicy(ctx, &policy.UpdatePolicyRequest{
			PolicyID: policyId,
			UpdateFn: func(ctx context.Context, p *policy.Policy) (*policy.Policy, error) {
				p.ApplicationId = s.applicationId
				p.EffectiveDate = s.effectiveDate.ToTime()
				p.EffectiveDateTo = s.expirationDate.ToTime()
				p.PolicyNumber = s.newPolicyNumberFromThisYear(p)
				p.State = policy_enums.PolicyStateActive
				return p, nil
			},
		})
		s.Require().NoError(err)
	}

	mustPreparePolicy(s.alPolicyId)
	mustPreparePolicy(s.mtcPolicyId)
	mustPreparePolicy(s.glPolicyId)

	alPolicy, err := s.deps.PolicyWrapper.GetPolicyById(ctx, &policy.GetPolicyByIdRequest{
		ID: s.alPolicyId,
	})
	s.Require().NoError(err)

	alPN := alPolicy.PolicyNumber
	err = s.deps.StaticParamClient.InitPolicy(
		ctx,
		bpid.NewBillingPolicyId(alPN.GetPolicyIdentifier(), int16(alPN.GetPolicyIssuanceYear().Year())),
		[]enums.TypedValue{
			enums.StaticParamTypeAgencyCommission.TypedValue(decimal.NewFromFloat(0.10)),
			enums.StaticParamTypeALRate.TypedValue(decimal.NewFromFloat(0.15)),
			enums.StaticParamTypeAPDRate.TypedValue(decimal.NewFromFloat(0.20)),
			enums.StaticParamTypeMTCRate.TypedValue(decimal.NewFromFloat(0.25)),
			enums.StaticParamTypeGLRate.TypedValue(decimal.NewFromFloat(0.30)),
			enums.StaticParamTypeTIV.TypedValue(decimal.NewFromFloat(1_000_000)),
		},
		time_utils.DateFromTime(s.effectiveDate.ToTime()),
	)
	s.Require().NoError(err)
}

func (s *getBillingInfoForPolicyIdentifierTestSuite) newPolicyNumberFromThisYear(p *policy.Policy) *policy.NirvanaPolicyNumberImpl {
	policyNumber, _ := policy.NewNirvanaPolicyNumber(
		p.PolicyNumber.GetPolicyPrefix(),
		p.PolicyNumber.GetPolicyIdentifier(),
		s.deps.Clock.Now(),
	)
	return policyNumber
}

func (s *getBillingInfoForPolicyIdentifierTestSuite) setSalesforceClientExpectations(applicationId uuid.UUID, expectations sfdcClientExpectations) {
	fieldName := sfdc_wrapper.OpportunitySalesforceObjectFieldCountOfDepositInstallments

	query, err := soql.WhereEquals(sfdc_wrapper.SalesforceObjectFieldApplicationId, applicationId.String())
	s.Require().NoError(err, "Failed to create SOQL query")

	s.sfdcClient.EXPECT().QuerySalesforceObject(
		gomock.Any(),
		sfdc_enums.Opportunity,
		query,
		fieldName,
	).Return(map[string]interface{}{
		fieldName: strconv.Itoa(expectations.countOfDepositInstallments),
	}, nil).Times(expectations.callTimes)
}
