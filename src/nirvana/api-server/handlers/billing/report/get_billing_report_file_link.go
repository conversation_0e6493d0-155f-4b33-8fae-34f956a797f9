package report

import (
	"context"
	"net/http"
	"time"

	"github.com/google/uuid"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/common-go/file_upload_lib"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/db-api/db_wrappers/billing/report"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
)

func HandleGetBillingReportFileLink(
	ctx context.Context,
	fileUploadManager file_upload_lib.FileUploadManager[file_upload_lib.DefaultS3Keygen],
	reportWrapper report.DataWrapper,
	fileHandle uuid.UUID,
) (*oapi_common.DownloadFileLinkResponse, error) {
	ctx = log.ContextWithFields(ctx, log.Stringer("fileHandle", fileHandle))

	reports, err := reportWrapper.GetReports(ctx, report.HandleIdIs(fileHandle))
	if err != nil {
		log.Error(ctx, "error while searching report", log.Err(err))
		return nil, common.WrapAsHttpErrorf(
			err,
			http.StatusInternalServerError,
			"error while searching report: %s",
			fileHandle,
		)
	}
	if len(reports) == 0 {
		log.Error(ctx, "couldn't find report", log.Err(err))
		return nil, common.WrapAsHttpErrorf(
			err,
			http.StatusNotFound,
			"couldn't find report for file handle: %s",
			fileHandle,
		)
	}

	fileUrl, err := fileUploadManager.GenerateTemporaryDownloadLink(ctx, fileHandle, time.Minute*15)
	if err != nil {
		log.Error(ctx, "unable to generate file download link", log.Err(err))
		return nil, common.WrapAsHttpErrorf(
			err,
			http.StatusInternalServerError,
			"unable to generate file download link: %s",
			fileHandle,
		)
	}
	return &oapi_common.DownloadFileLinkResponse{
		Link: fileUrl,
	}, nil
}
