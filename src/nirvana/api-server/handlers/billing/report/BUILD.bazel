load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "report",
    srcs = [
        "get_billing_report_file_link.go",
        "get_reports.go",
    ],
    importpath = "nirvanatech.com/nirvana/api-server/handlers/billing/report",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/api-server/common",
        "//nirvana/api-server/interceptors/billing/deps",
        "//nirvana/billing/enums",
        "//nirvana/common-go/file_upload_lib",
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/db-api/db_wrappers/billing/report",
        "//nirvana/openapi-specs/api_server_billing",
        "//nirvana/openapi-specs/components/billing",
        "//nirvana/openapi-specs/components/common",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_oapi_codegen_runtime//types",
    ],
)
