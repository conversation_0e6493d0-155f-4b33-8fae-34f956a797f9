package transformer

import (
	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth/enums"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
)

// MapStatusToOAPIStatus converts the KeyStatus enum to the openapi KeyStatus
func MapStatusToOAPIStatus(status string) (*oapi_common.KeyStatus, error) {
	switch status {
	case enums.KeyStatusActive.String():
		return pointer_utils.ToPointer(oapi_common.KeyStatusActive), nil
	case enums.KeyStatusBlocked.String():
		return pointer_utils.ToPointer(oapi_common.KeyStatusBlocked), nil
	case enums.KeyStatusExpired.String():
		return pointer_utils.ToPointer(oapi_common.KeyStatusExpired), nil
	default:
		return nil, errors.New("unknown key status")
	}
}

// MapOAPIStatusToStatus converts the openapi KeyStatus to the KeyStatus enum
func MapOAPIStatusToStatus(status oapi_common.KeyStatus) (*enums.KeyStatus, error) {
	switch status {
	case oapi_common.KeyStatusActive:
		return pointer_utils.ToPointer(enums.KeyStatusActive), nil
	case oapi_common.KeyStatusBlocked:
		return pointer_utils.ToPointer(enums.KeyStatusBlocked), nil
	case oapi_common.KeyStatusExpired:
		return pointer_utils.ToPointer(enums.KeyStatusExpired), nil
	default:
		return nil, errors.New("invalid status")
	}
}
