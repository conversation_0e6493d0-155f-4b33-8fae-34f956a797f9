package auth

import (
	"context"
	"net/http"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/infra/auth/hubspot"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/interceptors/auth/deps"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/infra/authz"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
)

func HandleHubspotLogin(
	ctx context.Context,
	deps deps.Deps,
	request oapi_common.HubspotLoginRequest,
) (*oapi_common.HubspotLoginResponse, error) {
	authzUser := authz.UserFromContext(ctx)
	if !authzUser.IsAuthenticated() || authzUser.IsAnonymousServiceAccountUser() {
		return nil, common.NewHttpErrorf(http.StatusUnauthorized, "cannot authenticate anonymous users with hubspot")
	}
	redirectURL := pointer_utils.StringValOr(request.RedirectURL, "")
	absoluteLoginUrl, err := deps.HubspotClient.HubspotLoginURL(authzUser.LowerEmail, redirectURL)
	if err != nil {
		code := http.StatusBadRequest
		if errors.Is(err, hubspot.ErrorJWTInternalServerError) {
			code = http.StatusInternalServerError
		}
		return nil, common.WrapAsHttpError(err, code)
	}
	return &oapi_common.HubspotLoginResponse{
		AbsoluteLoginUrl: absoluteLoginUrl,
	}, nil
}
