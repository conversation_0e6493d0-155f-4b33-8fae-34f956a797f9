package audit_log

import (
	"context"
	"net/http"

	"nirvanatech.com/nirvana/api-server/interceptors/audit_log/deps"

	"github.com/google/uuid"

	"nirvanatech.com/nirvana/api-server/common"
	app_handler "nirvanatech.com/nirvana/api-server/handlers/application"
	"nirvanatech.com/nirvana/api-server/helpers"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/infra/authz"
	oapi_audit_log "nirvanatech.com/nirvana/openapi-specs/components/audit_log"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
)

func HandleGetModelPinConfigLogsForApplicationAuthz(
	ctx context.Context, deps deps.Deps, req GetModelPinConfigLogsForApplicationRequest,
) common.HandlerAuthzResponse {
	return app_handler.HasPermissionOverApp(
		ctx,
		deps.<PERSON>Wrapper,
		deps.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
		authz.UserFromContext(ctx),
		authz.ReadAction,
		req.ApplicationID,
	)
}

func HandleGetModelPinConfigLogsForApplication(
	ctx context.Context,
	deps deps.Deps,
	req GetModelPinConfigLogsForApplicationRequest,
) GetModelPinConfigLogsForApplicationResponse {
	appID, err := uuid.Parse(req.ApplicationID)
	if err != nil {
		log.Error(
			ctx, "HandleGetModelPinConfigLogsForApplication: Failed to parse app id",
			log.Err(err),
			log.String("applicatonID", req.ApplicationID),
		)
		errMessage := helpers.WrapErrorMessage(
			err, "Failed to parse applicationID",
		)
		return GetModelPinConfigLogsForApplicationResponse{ServerError: &errMessage}
	}
	auditLogs, err := deps.ModelPinConfigAuditLogWrapper.GetAllLogsByAppID(
		ctx, appID,
	)
	if err != nil {
		log.Error(
			ctx, "HandleGetModelPinConfigLogsForApplication: Failed to fetch logs for application",
			log.Err(err),
			log.String("applicatonID", req.ApplicationID),
		)
		errMessage := helpers.WrapErrorMessage(
			err, "Failed to fetch logs for application",
		)
		return GetModelPinConfigLogsForApplicationResponse{ServerError: &errMessage}
	}

	response := bindModelPinConfigAuditLogsToRest(auditLogs)

	return GetModelPinConfigLogsForApplicationResponse{Success: &response}
}

type GetModelPinConfigLogsForApplicationRequest struct {
	ApplicationID string
}

type GetModelPinConfigLogsForApplicationResponse struct {
	Success     *oapi_audit_log.GetModelPinConfigAuditLogsResponse
	ServerError *oapi_common.ErrorMessage
}

func (g *GetModelPinConfigLogsForApplicationResponse) StatusCode() int {
	switch {
	case g.Success != nil:
		return http.StatusOK
	case g.ServerError != nil:
		return http.StatusInternalServerError
	default:
		return http.StatusInternalServerError
	}
}

func (g *GetModelPinConfigLogsForApplicationResponse) Body() interface{} {
	switch {
	case g.Success != nil:
		return g.Success
	case g.ServerError != nil:
		return g.ServerError
	default:
		return nil
	}
}
