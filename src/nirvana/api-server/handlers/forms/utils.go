package forms

import (
	"context"
	"strconv"
	"time"

	"nirvanatech.com/nirvana/events"

	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	"nirvanatech.com/nirvana/external_client/salesforce/jobs/enums"
	policyutils "nirvanatech.com/nirvana/policy/fleet"
	"nirvanatech.com/nirvana/underwriting/app_review/actions/reasons"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/coverage_utils"

	"nirvanatech.com/nirvana/api-server/interceptors/forms/deps"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/external_client/salesforce/wrapper"
	oapi_forms "nirvanatech.com/nirvana/openapi-specs/components/forms"
	"nirvanatech.com/nirvana/policy_common/constants"
	"nirvanatech.com/nirvana/policy_common/forms_generator/compilation"
)

const appTypeInvalid = constants.ApplicationType(0)

func ConvertAppTypeFromOAPI(appType *oapi_forms.ApplicationType) (constants.ApplicationType, error) {
	// To ensure that the change is backward compatible as the web client doesn't populate this enum at the moment.
	// TODO(ayush): Remove this fallback once frontend changes have been made.
	if appType == nil {
		return constants.ApplicationTypeFleet, nil
	}

	switch *appType {
	case oapi_forms.ApplicationTypeFleet:
		return constants.ApplicationTypeFleet, nil
	case oapi_forms.ApplicationTypeNonFleet:
		return constants.ApplicationTypeNonFleet, nil
	case oapi_forms.ApplicationTypeNonFleetAdmitted:
		return constants.ApplicationTypeNonFleetAdmitted, nil
	case oapi_forms.ApplicationTypeBusinessAuto:
		return constants.ApplicationTypeBusinessAuto, nil
	default:
		return appTypeInvalid, errors.Newf("invalid ApplicationType %s", *appType)
	}
}

func isCoverageValidForPolicyPacket(cov *app_enums.Coverage, appType constants.ApplicationType) bool {
	functionalCoverages := coverage_utils.GetCoverages(appType)
	if cov != nil && !slice_utils.Contains(functionalCoverages, *cov) {
		return false
	}
	return true
}

func getPremiumDetailsAndCompanyNameFromFormComp(
	ctx context.Context,
	appWrapper application.DataWrapper,
	formComp compilation.FormsCompilation,
) (int32, []wrapper.CoveragePremium, string, error) {
	sub, err := appWrapper.GetSubmissionById(ctx, formComp.Metadata().SubmissionId.String())
	if err != nil {
		return 0, nil, "", errors.Wrapf(err, "failed to get submission with id %s", formComp.Metadata().SubmissionId)
	}

	indOpt, err := appWrapper.GetIndOptionById(ctx, sub.SelectedIndicationID)
	if err != nil {
		return 0, nil, "", errors.Wrapf(err, "failed to get indication option with id %s", sub.SelectedIndicationID)
	}

	return indOpt.TotalPremium, getCoveragePremiumsFromIndOpt(indOpt.Coverages), sub.CompanyInfo.Name, nil
}

func getCoveragePremiumsFromIndOpt(coverages []application.CoverageDetails) []wrapper.CoveragePremium {
	var coveragePremiums []wrapper.CoveragePremium
	for _, i := range coverages {
		if i.CoverageType.IsPrimaryCoverage() {
			coveragePremiums = append(coveragePremiums, wrapper.CoveragePremium{
				CoverageType: i.CoverageType,
				Premium:      *i.Premium,
			})
		}
	}

	return coveragePremiums
}

func buildOpportunityArgs(ctx context.Context, deps deps.Deps, formComp compilation.FormsCompilation) (*wrapper.SalesforceEventUpdateApplicationArgs, error) {
	appId := formComp.Metadata().ApplicationId.String()
	subId := formComp.Metadata().SubmissionId.String()
	var opportunityArgs wrapper.SalesforceEventUpdateApplicationArgs

	opportunityArgs.ApplicationID = appId
	opportunityArgs.FormCompilationId = formComp.Id().String()
	opportunityArgs.CloseDate = time.Now().Format(time_utils.ISOLayout)
	opportunityArgs.EventName = enums.FormsSignaturePacketBound

	sub, err := deps.ApplicationWrapper.GetSubmissionById(ctx, subId)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to submission with id %s", subId)
	}

	indOpt, err := deps.ApplicationWrapper.GetIndOptionById(ctx, sub.SelectedIndicationID)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get indication option from submission")
	}

	opportunityArgs.TotalPremium = indOpt.TotalPremium
	opportunityArgs.CoveragePremiums = getCoveragePremiumsFromIndOpt(indOpt.Coverages)

	appObj, err := deps.ApplicationWrapper.GetAppById(ctx, appId)
	if err != nil {
		return nil, errors.Wrapf(err, "failed for get application by id %s", appId)
	}

	policySetIdentifier, err := policyutils.GeneratePolicySetIdentifier(deps.ApplicationWrapper, *appObj)
	if err != nil {
		log.Error(ctx, "Couldn't generate policy set identifier", log.Err(err))
		return nil, errors.Wrap(err, "Couldn't generate policy set identifier")
	}

	policyNumber, err := policyutils.GeneratePolicyNumber(
		app_enums.CoverageAutoLiability,
		sub.CoverageInfo.EffectiveDate,
		*policySetIdentifier,
		sub.ModelPinConfig.Application.InsuranceCarrier,
	)
	if err != nil {
		log.Error(ctx, "Couldn't generate policy number", log.Err(err))
		return nil, errors.Wrap(err, "Couldn't generate policy number")
	}

	policyBilling, err := deps.PolicyWrapper.GetPolicyByPolicyNumber(ctx, &policy.GetPolicyByPolicyNumberRequest{
		PolicyNumber:       policyNumber.String(),
		IncludeProgramData: true,
	})
	if err != nil {
		log.Warn(ctx, "Couldn't get policy by policy number",
			log.String("policyNumber", policyNumber.String()), log.Err(err))
		// Not returning here as we update the opportunity without billing info
	}

	var billingInfo wrapper.BillingInfo
	if policyBilling != nil {
		if policyBilling.BillingInfo.DepositAmount != nil {
			billingInfo.DepositAmount = strconv.FormatFloat(*policyBilling.BillingInfo.DepositAmount, 'g', 5, 64)
		}

		if policyBilling.BillingInfo.CommissionRate != nil {
			billingInfo.CommissionRate = strconv.FormatFloat(*policyBilling.BillingInfo.CommissionRate, 'g', 5, 64)
		}

		billingInfo.PaymentMethod = policyBilling.BillingInfo.PaymentMethod.String()

		if policyBilling.BillingInfo.DepositPaymentMethod != nil {
			billingInfo.DepositPaymentMethod = policyBilling.BillingInfo.DepositPaymentMethod.String()
		}
	}

	opportunityArgs.BillingInfo = &billingInfo

	appReview, err := deps.ApplicationReviewWrapper.GetApprovedApplicationReview(ctx, appId)
	if err != nil {
		log.Error(ctx, "Couldn't get approved app review", log.String("applicationId", appId), log.Err(err))
		return nil, errors.Wrapf(err, "Couldn't get approved app review for appId %s", appId)
	}

	if appReview.EndStateReasons != nil && appReview.EndStateReasons.BindStateReasons != nil {
		var primaryReason string
		var secondaryReason string
		for _, reason := range reasons.ApplicationBoundReasons {
			if reason.ReasonCode == appReview.EndStateReasons.BindStateReasons.PrimaryWinReasonId {
				primaryReason = reason.ReasonText
			}
			if reason.ReasonCode == appReview.EndStateReasons.BindStateReasons.SecondaryWinReasonId {
				secondaryReason = reason.ReasonText
			}
		}

		opportunityArgs.PrimaryWinReason = primaryReason
		opportunityArgs.SecondaryWinReason = secondaryReason
		opportunityArgs.WinReason = appReview.EndStateReasons.BindStateReasons.ExternalNote
	}

	if appReview.CameraSubsidyDetails != nil {
		opportunityArgs.NumberOfCameras = pointer_utils.Int32(int32(appReview.CameraSubsidyDetails.NumberOfCameras))
	}

	coverageSummary, err := events.GenerateCoverageSummary(ctx, deps.ApplicationWrapper, deps.ApplicationReviewWrapper, appReview.Id, subId)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to generate coverage summary for reviewId %s", appReview.Id)
	}

	opportunityArgs.CoverageSummary = coverageSummary
	opportunityArgs.Fronter = appObj.ModelPinConfig.Application.InsuranceCarrier
	opportunityArgs.CreatedDate = time.Now()

	return &opportunityArgs, nil
}
