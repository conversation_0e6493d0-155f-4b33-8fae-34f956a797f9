package forms

import (
	"context"
	"fmt"
	"net/http"
	program_enum "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/policy_common/constants"

	"github.com/google/uuid"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/handlers/application"
	"nirvanatech.com/nirvana/api-server/helpers"
	"nirvanatech.com/nirvana/api-server/interceptors/forms/deps"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/infra/authz"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
	oapi_forms "nirvanatech.com/nirvana/openapi-specs/components/forms"
)

func PostFormsCompilationUploadAuthz(
	ctx context.Context,
	deps deps.Deps, req PostFormsCompilationUploadRequest,
) common.HandlerAuthzResponse {
	user := authz.UserFromContext(ctx)
	formCompUUID, err := uuid.Parse(req.FormCompilationID)
	if err != nil {
		log.Error(ctx, "Unable to parse FormCompilationID to uuid", log.Err(err))
		return common.HandlerAuthzResponse{
			Error: fmt.Errorf("unable to parse %s", req.FormCompilationID),
		}
	}

	// TODO: Refactor this once resource based AuthZ for BizAuto is implemented properly
	formComp, err := deps.FormsWrapper.GetFormCompilationById(ctx, formCompUUID)
	if err != nil {
		log.Error(ctx, "GetFormCompilationAuthz: unable to get form compilation", log.Err(err))
		return common.HandlerAuthzResponse{
			AuthzError: &oapi_common.ErrorMessage{
				Code: http.StatusUnprocessableEntity,
			},
		}
	}
	appUUID := (*formComp).Metadata().ApplicationId
	if appUUID == nil {
		log.Error(ctx, "GetFormCompilationAuthz: form compilation does not have an application ID")
		return common.HandlerAuthzResponse{
			AuthzError: &oapi_common.ErrorMessage{
				Code: http.StatusUnprocessableEntity,
			},
		}
	}
	appType := (*formComp).Metadata().ApplicationType
	if appType == constants.ApplicationTypeBusinessAuto {
		return hasPermissionOverApp(ctx, deps, program_enum.ProgramTypeBusinessAuto, *appUUID, authz.WriteAction)
	}

	return checkForPermissionOnFormCompilation(
		ctx, deps, user, authz.WriteAction, &formCompUUID, nil,
	)
}

func HandlePostFormsCompilationUpload(
	ctx context.Context,
	deps deps.Deps,
	req PostFormsCompilationUploadRequest,
) PostFormsCompilationUploadResponse {
	ctx = log.ContextWithFields(ctx, FormCompilationId(req.FormCompilationID))
	accountID, err := uuid.Parse(req.FormCompilationID)
	if err != nil {
		log.Error(ctx, "Unable to parse FormCompilationID to uuid", log.Err(err))
		errMessage := helpers.WrapErrorMessage(err, fmt.Sprintf(
			"Unable to parse %s", string(req.FormCompilationID)))
		return PostFormsCompilationUploadResponse{ServerError: &errMessage}
	}
	fileInfo := req.FileInfo
	fileWithMetadata := req.FilesGetter.GetFileIterator(ctx)()
	fileHandle, err := helpers.UploadFile(
		ctx, deps.FileUploadManager, accountID, fileInfo, fileWithMetadata)
	if err != nil {
		log.Error(ctx, "Unable to upload Document", log.Err(err))
		errMessage := helpers.WrapErrorMessage(err, fmt.Sprintf(
			"Unable to upload file type %s and destination group %s",
			fileInfo.Type, fileInfo.DestinationGroup))
		return PostFormsCompilationUploadResponse{ServerError: &errMessage}
	}
	handleID := oapi_forms.HandleID(fileHandle.String())
	log.Info(ctx, "Successfully uploaded file", HandleId(fileHandle.String()))
	return PostFormsCompilationUploadResponse{
		Success: &handleID,
	}
}

type PostFormsCompilationUploadRequest struct {
	FormCompilationID string
	FileInfo          oapi_common.FileInfo
	FilesGetter       application.PostFilesRequestFilesGetter
}

type PostFormsCompilationUploadResponse struct {
	Success     *oapi_forms.HandleID
	Error       *oapi_common.ErrorMessage
	ServerError *oapi_common.ErrorMessage
}

func (u *PostFormsCompilationUploadResponse) StatusCode() int {
	switch {
	case u.Success != nil:
		return http.StatusOK
	case u.Error != nil:
		return http.StatusUnprocessableEntity
	case u.ServerError != nil:
		return http.StatusInternalServerError
	default:
		return http.StatusInternalServerError
	}
}

func (u *PostFormsCompilationUploadResponse) Body() interface{} {
	switch {
	case u.Success != nil:
		return *u.Success
	case u.Error != nil:
		return *u.Error
	case u.ServerError != nil:
		return *u.ServerError
	default:
		return nil
	}
}

var _ common.HandlerResponse = (*PostFormsCompilationResponse)(nil)
