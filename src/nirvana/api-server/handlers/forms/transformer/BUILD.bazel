load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "transformer",
    srcs = [
        "form_schedule.go",
        "form_type.go",
    ],
    importpath = "nirvanatech.com/nirvana/api-server/handlers/forms/transformer",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/rule_engine",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/forms/enums",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/forms/model",
        "//nirvana/forms/util",
        "//nirvana/openapi-specs/api_server_forms",
        "//nirvana/openapi-specs/components/common",
        "//nirvana/openapi-specs/components/forms",
        "//nirvana/policy_common/constants",
        "//nirvana/policy_common/forms_generator/cmd/model",
        "//nirvana/policy_common/forms_generator/compilation",
        "//nirvana/policy_common/forms_generator/forms",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_hyperjumptech_grule_rule_engine//pkg",
        "@com_github_oapi_codegen_runtime//types",
    ],
)

go_test(
    name = "transformer_test",
    srcs = ["form_schedule_test.go"],
    embed = [":transformer"],
    deps = [
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/forms/enums",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/forms/model",
        "//nirvana/openapi-specs/components/common",
        "//nirvana/openapi-specs/components/forms",
        "//nirvana/policy_common/constants",
        "//nirvana/policy_common/forms_generator/cmd/model",
        "//nirvana/policy_common/forms_generator/compilation",
        "//nirvana/policy_common/forms_generator/forms",
        "@com_github_hyperjumptech_grule_rule_engine//pkg",
        "@com_github_oapi_codegen_runtime//types",
    ],
)
