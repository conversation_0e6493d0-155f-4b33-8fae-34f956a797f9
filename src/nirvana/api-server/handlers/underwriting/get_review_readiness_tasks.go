package underwriting

import (
	"context"
	"sort"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	openapi_types "github.com/oapi-codegen/runtime/types"

	"nirvanatech.com/nirvana/infra/authz"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/underwriting/task"

	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"

	"nirvanatech.com/nirvana/common-go/log"

	"nirvanatech.com/nirvana/api-server/interceptors/underwriting/deps"

	oapi_uw "nirvanatech.com/nirvana/openapi-specs/components/underwriting"
)

func HandleGetReviewReadinessTasks(
	ctx context.Context,
	deps deps.Deps,
	reviewId string,
) GetReviewReadinessTasksResponse {
	ctx = log.ContextWithFields(ctx, log.HandlerName("HandleGetReviewReadinessTasks"), log.AppReviewID(reviewId))

	review, err := deps.ApplicationReviewWrapper.GetReview(ctx, reviewId)
	if err != nil {
		log.Error(ctx, "Failed to get review", log.Err(err))
		return GetReviewReadinessTasksResponse{Error: getClientError(reviewId, err)}
	}
	tasks, err := deps.ReviewReadinessTaskManager.GetTasks(ctx, reviewId)
	if err != nil {
		log.Error(ctx, "Failed to get review readiness tasks", log.Err(err))
		return GetReviewReadinessTasksResponse{Error: getClientError(reviewId, err)}
	}

	assigneeIDToAssigneeMap, err := getAssigneeIDToAssigneeMap(ctx, deps, tasks)
	if err != nil {
		log.Error(ctx, "Failed to get assignee id to assignee map", log.Err(err))
		return GetReviewReadinessTasksResponse{Error: getClientError(reviewId, err)}
	}

	response, err := convertTasksToRESTResponse(ctx, review, tasks, assigneeIDToAssigneeMap)
	if err != nil {
		log.Error(ctx, "Failed to convert tasks to REST response", log.Err(err))
		return GetReviewReadinessTasksResponse{Error: getClientError(reviewId, err)}
	}
	return GetReviewReadinessTasksResponse{Data: response}
}

func convertTasksToRESTResponse(ctx context.Context, review *uw.ApplicationReview, tasks []task.Task, assigneeIDToAssigneeMap map[string]authz.User) (*oapi_uw.ReviewReadinessTaskListResponse, error) {
	oapiTasks := make([]oapi_uw.ReviewReadinessTask, 0)
	totalTasks := len(tasks)
	completedTasks := 0

	taskNameToSortOrderMap, err := getTaskSortOrder(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get task sort order")
	}
	sort.Slice(tasks, func(i, j int) bool {
		return taskNameToSortOrderMap[tasks[i].Properties().Name] < taskNameToSortOrderMap[tasks[j].Properties().Name]
	})

	for _, tsk := range tasks {
		log.Info(ctx, "Converting task to REST", log.String("taskName", tsk.Properties().Name.String()))
		oapiTaskName, err := convertTaskNameToREST(tsk.Properties().Name)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to convert task name %s to REST", tsk.Properties().Name)
		}

		taskStatus, err := tsk.Status(ctx, false)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to get task status")
		}
		isTaskDone, err := task.IsTaskDone(ctx, tsk)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to check if task is done")
		}
		if isTaskDone {
			completedTasks++
		}
		oapiTaskStatus, err := convertTasksStatusToREST(*taskStatus)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to convert task status %s to REST", taskStatus)
		}

		completionMethod, err := convertCompletionMethodToREST(tsk.Properties().CompletionMethod)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to convert completion method %s to REST", tsk.Properties().CompletionMethod)
		}

		isEnabled, disableReason, err := tsk.IsEnabled(ctx)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to check if task is enabled")
		}
		var disableReasonMsg *string
		if !isEnabled {
			// fallback message
			disableReasonMsg = pointer_utils.ToPointer("Task disabled because some pre-conditions are not met")
			if disableReason != nil {
				disableReasonMsg = &disableReason.Message
			}
		}

		assignee, ok := assigneeIDToAssigneeMap[tsk.Properties().AssigneeUserID]
		if !ok {
			return nil, errors.Newf("assignee not found for assignee id %s", tsk.Properties().AssigneeUserID)
		}
		oapiTask := oapi_uw.ReviewReadinessTask{
			Name:   *oapiTaskName,
			Status: *oapiTaskStatus,
			Assignee: oapi_uw.ApplicationReviewUser{
				Email: openapi_types.Email(assignee.Email),
				Id:    assignee.ID.String(),
				Name:  assignee.FullName(),
			},
			Notes:            tsk.Properties().Notes,
			CompletionMethod: *completionMethod,
			IsEnabled:        isEnabled,
			DisableReason:    disableReasonMsg,
		}
		oapiTasks = append(oapiTasks, oapiTask)
	}
	appReviewState, err := bindStateToOAPI(review.State, review.ReviewReadinessState)
	if err != nil {
		return nil, errors.Wrap(err, "failed to bind app review state to OAPI")
	}
	return &oapi_uw.ReviewReadinessTaskListResponse{
		TotalTasks:     int32(totalTasks),
		CompletedTasks: int32(completedTasks),
		AppReviewState: *appReviewState,
		Tasks:          oapiTasks,
	}, nil
}

func getAssigneeIDToAssigneeMap(ctx context.Context, deps deps.Deps, tasks []task.Task) (map[string]authz.User, error) {
	userIDs := make([]uuid.UUID, 0)
	for _, tsk := range tasks {
		assigneeID := tsk.Properties().AssigneeUserID
		// convert to UUID
		assigneeUUID, err := uuid.Parse(assigneeID)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to parse assignee id %s", assigneeID)
		}
		userIDs = append(userIDs, assigneeUUID)
	}
	userIDToUserMap, err := getUsersForUserIds(ctx, deps, userIDs)
	if err != nil {
		return nil, errors.Wrap(err, "unable to fetch users")
	}
	return userIDToUserMap, nil
}

func getTaskSortOrder(_ context.Context) (map[uw.TaskName]int, error) {
	orderedTaskNames := []uw.TaskName{uw.TaskNameSuccessfulTelematicsConnection, uw.TaskNameVINVisibility, uw.TaskNameEquipment}
	// ensure that all the tasks are included in the ordered list above
	if len(orderedTaskNames) != len(uw.TaskNameValues()) {
		return nil, errors.Newf("some tasks are missing in ordered task names, expected tasks %v", uw.TaskNameValues())
	}
	taskNameToOrderMap := make(map[uw.TaskName]int)
	for i, taskName := range orderedTaskNames {
		taskNameToOrderMap[taskName] = i
	}
	return taskNameToOrderMap, nil
}

func convertTaskNameToREST(taskName uw.TaskName) (*oapi_uw.ReviewReadinessTaskName, error) {
	switch taskName { //nolint:exhaustive
	case uw.TaskNameSuccessfulTelematicsConnection:
		return pointer_utils.ToPointer(oapi_uw.TaskSuccessfulTelematicsConnection), nil
	case uw.TaskNameEquipment:
		return pointer_utils.ToPointer(oapi_uw.TaskEquipment), nil
	case uw.TaskNameVINVisibility:
		return pointer_utils.ToPointer(oapi_uw.TaskVINVisibility), nil
	}
	return nil, errors.Newf("cannot convert task name %s to REST", taskName)
}

func convertCompletionMethodToREST(completionMethod task.CompletionMethod) (*oapi_uw.ReviewReadinessTaskCompletionMethod, error) {
	switch completionMethod { //nolint:exhaustive
	case task.CompletionMethodManual:
		return pointer_utils.ToPointer(oapi_uw.TaskCompletionMethodManual), nil
	case task.CompletionMethodAutomatic:
		return pointer_utils.ToPointer(oapi_uw.TaskCompletionMethodAutomatic), nil
	}
	return nil, errors.Newf("cannot convert task completion method %s to REST", completionMethod)
}

func convertTasksStatusToREST(taskStatus uw.TaskStatus) (*oapi_uw.ReviewReadinessTaskStatus, error) {
	switch taskStatus { //nolint:exhaustive
	case uw.TaskStatusPending:
		return pointer_utils.ToPointer(oapi_uw.TaskStatusPending), nil
	case uw.TaskStatusDone:
		return pointer_utils.ToPointer(oapi_uw.TaskStatusDone), nil
	case uw.TaskStatusSkipped:
		return pointer_utils.ToPointer(oapi_uw.TaskStatusSkipped), nil
	}
	return nil, errors.Newf("cannot convert task status %s to REST", taskStatus)
}

type GetReviewReadinessTasksResponse struct {
	Data  *oapi_uw.ReviewReadinessTaskListResponse
	Error error
}
