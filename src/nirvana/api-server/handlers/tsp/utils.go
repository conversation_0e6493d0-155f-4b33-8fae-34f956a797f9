package tsp

import (
	"context"
	"database/sql"
	"fmt"
	"slices"
	"strconv"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	openapi_types "github.com/oapi-codegen/runtime/types"
	"github.com/volatiletech/null/v8"
	ltype "google.golang.org/genproto/googleapis/logging/type"

	"nirvanatech.com/nirvana/api-server/helpers"
	"nirvanatech.com/nirvana/api-server/interceptors/application/deps"
	"nirvanatech.com/nirvana/api-server/quoting_jobber"
	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/uuid_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	safety_wrapper "nirvanatech.com/nirvana/db-api/db_wrappers/safety"
	db_sharing "nirvanatech.com/nirvana/db-api/db_wrappers/sharing"
	"nirvanatech.com/nirvana/events"
	"nirvanatech.com/nirvana/events/safety_events"
	"nirvanatech.com/nirvana/gqlschema/models"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/jobber"
	"nirvanatech.com/nirvana/jobber/jtypes"
	oapi_app "nirvanatech.com/nirvana/openapi-specs/components/application"
	"nirvanatech.com/nirvana/quoting/jobs"
	"nirvanatech.com/nirvana/quoting/jobs/messages"
	quoting_triggers "nirvanatech.com/nirvana/quoting/jobs/triggers"
	telematics_utils "nirvanatech.com/nirvana/quoting/telematics"
	"nirvanatech.com/nirvana/sharing"
	"nirvanatech.com/nirvana/telematics"
	telematics_enums "nirvanatech.com/nirvana/telematics/enums"
	uw_jobs "nirvanatech.com/nirvana/underwriting/jobs"
)

// parsedTSPConnectionRequest represents the validated & parsed format for
// creating a TSP connection, once the request's contents have been validated
// and "modernized" (if necessary). Here, modernized means that the request
// has been converted from the legacy format (where the TSP specific form
// fields are used) to the new format (where we use consentKind specific form fields).
type parsedTSPConnectionRequest struct {
	// Salient information from the incoming request as an instance of a
	// protobuf message
	requestMetadata *ltype.HttpRequest

	// parsed contents (openapi enums converted to internal enums)
	consentKind    telematics.ConsentKind
	tsp            telematics.TSP
	additionalInfo *oapi_app.TSPConnectionAdditionalInfo

	// "modernized" contents - consentKind specific fields instead of TSP specific ones
	apiKeyAuth            *oapi_app.ApiKeyAuthConnData
	basicAuth             *oapi_app.BasicAuthConnData
	basicAuthWithLoginId  *oapi_app.BasicAuthWithLoginIdConnData
	basicAuthWithDatabase *oapi_app.BasicAuthWithDatabaseConnData
	speedgauge            *oapi_app.SpeedgaugeBaseConnData
	speedgaugeGeotab      *oapi_app.GeoTabConnData
}

// The tspOAuthConnectionState struct will be base64-encoded and passed to the
// oAuth.Dispatch.Begin() method as the state parameter, as per the requirement
// the state will be passed as a query parameter which can be decoded and
// required fields can be used.
// Current use case:
// The below fields are essential in the state for tspOAuth providers.
// The frontend is now capable of decoding the state query parameter from the
// redirect URL of Samsara and KT and passing the programType in the request
// body of the post_telematics_connection_complete API.
// WARNING: We want to keep the number of fields in this struct as minimal as
// possible because we are uncertain about the constraints of query parameter size.
// MANDATORY: Do not remove the HandleID, as this is the only field which makes
// the state unique if we have multiple connections for a application.
type tspOAuthConnectionState struct {
	AppID           uuid.UUID                `json:"appID"`
	HandleID        uuid.UUID                `json:"handleID"`
	ShareableLinkId uuid.UUID                `json:"token"`
	ProgramType     policy_enums.ProgramType `json:"programType"`
}

// modernizeIfLegacy modernizes the incoming request if it is in the legacy
// format. If the request is already in the new format, this function is a no-op.
//
// If a request is detected as legacy, this function:
//   - sets the tsp, data provider and consent kind fields in the request, and
//   - replaces the TSP specific form fields with the corresponding consentKind
//     specific ones.
func (req PostTSPConnectionRequest) parse(ctx context.Context) (*parsedTSPConnectionRequest, error) {
	// If either of ConsentKind or TSP is non-nil, we switch to the new flow.
	// TODO: Get rid of this switch once we are fully migrated to the new flow,
	// and consentKind and TSP are required fields.
	tspEnum, err := telematics.TSPString(string(req.TSPConnection.Tsp))
	if err != nil {
		log.Error(ctx, "Bad request. Invalid TSP", log.Err(err))
		return nil, errors.Newf("Invalid TSP in request %v", req)
	}
	consentKind, err := bindConsentKindFromRest(req.TSPConnection.ConsentKind)
	if err != nil {
		log.Error(ctx, "Bad request. Invalid ConsentKind", log.Err(err))
		return nil, errors.Newf("Invalid ConsentKind in request %v", req)
	}
	switch consentKind {
	case telematics.ConsentKindOAuth:
		// no parsing required for OAuth since we would simply "Begin" the
		// OAuth flow and return a URL the user would need to be redirected to.
	case telematics.ConsentKindSpeedgauge:
		if req.TSPConnection.Speedgauge == nil {
			log.Error(ctx, "Bad request. Missing Speedgauge connection data in request")
			return nil, errors.Newf("Missing Speedgauge connection data in request %v", req)
		}
	case telematics.ConsentKindSpeedgaugeGeotab:
		if req.TSPConnection.SpeedgaugeGeotab == nil {
			log.Error(ctx, "Bad request. Missing SpeedgaugeGeotab connection data in request")
			return nil, errors.Newf("Missing SpeedgaugeGeotab connection data in request %v", req)
		}
	case telematics.ConsentKindApiKey:
		if req.TSPConnection.ApiKeyAuth == nil {
			log.Error(ctx, "Bad request. Missing ApiKeyAuth connection data in request")
			return nil, errors.Newf("Missing ApiKeyAuth connection data in request %v", req)
		}
	case telematics.ConsentKindBasicAuth:
		if req.TSPConnection.BasicAuth == nil {
			log.Error(ctx, "Bad request. Missing BasicAuth connection data in request")
			return nil, errors.Newf("Missing BasicAuth connection data in request %v", req)
		}
	case telematics.ConsentKindBasicAuthWithLoginId:
		if req.TSPConnection.BasicAuthWithLoginId == nil {
			log.Error(ctx, "Bad request. Missing BasicAuthWithLoginId connection data in request")
			return nil, errors.Newf("Missing BasicAuthWithLoginId connection data in request %v", req)
		}
	case telematics.ConsentKindBasicAuthWithDatabase:
		if req.TSPConnection.BasicAuthWithDatabase == nil {
			log.Error(ctx, "Bad request. Missing BasicAuthWithDatabase connection data in request")
			return nil, errors.Newf("Missing BasicAuthWithDatabase connection data in request %v", req)
		}
	default:
		log.Error(ctx, "Bad request. Invalid ConsentKind", log.Err(err))
		return nil, errors.Newf("Invalid ConsentKind in request %v", req)
	}
	return &parsedTSPConnectionRequest{
		requestMetadata:       req.RequestMetadata,
		consentKind:           consentKind,
		tsp:                   tspEnum,
		apiKeyAuth:            req.TSPConnection.ApiKeyAuth,
		basicAuth:             req.TSPConnection.BasicAuth,
		basicAuthWithLoginId:  req.TSPConnection.BasicAuthWithLoginId,
		basicAuthWithDatabase: req.TSPConnection.BasicAuthWithDatabase,
		speedgauge:            req.TSPConnection.Speedgauge,
		speedgaugeGeotab:      req.TSPConnection.SpeedgaugeGeotab,
		additionalInfo:        req.TSPConnection.AdditionalInfo,
	}, nil
}

func (req *parsedTSPConnectionRequest) toSpeedgaugeConnData() (*SpeedgaugeConnData, error) {
	var resp SpeedgaugeConnData
	// ConsentKindGeotab doesn't need any additional info, so we ignore it.
	if req.additionalInfo != nil && req.consentKind == telematics.ConsentKindSpeedgauge {
		resp.UserEnteredTSPName = null.StringFrom(req.additionalInfo.FreeTextTSPName)
	}
	switch req.consentKind {
	case telematics.ConsentKindSpeedgauge:
		if req.speedgauge == nil {
			return nil, errors.New("expected non-nil speedgauge connection data")
		}
		resp.TSPAccountDetails = SpeedgaugeBaseConnData{
			Name:  req.speedgauge.Name,
			Email: string(req.speedgauge.Email),
		}
	case telematics.ConsentKindSpeedgaugeGeotab:
		if req.speedgaugeGeotab == nil {
			return nil, errors.New("expected non-nil speedgauge geotab connection data")
		}
		resp.TSPAccountDetails = SpeedgaugeBaseConnData{
			Name:  req.speedgaugeGeotab.Name,
			Email: string(req.speedgaugeGeotab.Email),
		}
	default:
		return nil, errors.Newf("unsupported consent kind %s for speedgauge connection", req.consentKind.String())
	}
	return &resp, nil
}

// isUserSignUpEnabled returns true if this user should be prompted to create a new user account.
func isUserSignUpEnabled(ctx context.Context, deps deps.Deps, userIDStr string) bool {
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		log.Error(ctx, "Failed to parse appInfo createdBy as UUID", log.String("userID", userIDStr))
		return false
	}
	user, err := deps.AuthWrapper.FetchAuthzUser(ctx, userID)
	if err != nil {
		log.Error(ctx, "Failed to fetch authz user for appInfo.CreatedBy", log.String("userID", userIDStr))
		return false
	}

	isTelematicsSignUpScreenEnabled, err := deps.FeatureFlagClient.BoolVariation(
		feature_flag_lib.BuildLookupAttributes(*user),
		feature_flag_lib.FeatureTelematicsSignUpScreen,
		false)
	if err != nil {
		log.Error(ctx, "Failed to fetch feature flag for user sign up", log.Err(err), log.String("userID", userIDStr))
		return false
	}

	return isTelematicsSignUpScreenEnabled
}

func fetchOrCreateSharedSafetyLink(
	ctx context.Context,
	deps deps.Deps,
	signUpEnabled bool,
	appInfo AppInfo,
	programType policy_enums.ProgramType,
) (
	*db_sharing.ShareableLink, *safety_wrapper.SafetyReport, error,
) {
	if programType == policy_enums.ProgramTypeBusinessAuto {
		log.Info(ctx, "Skipping shared safety link creation for Business Auto program type "+
			"since we do not have the concept of DOT Number in all cases")
		return nil, nil, nil
	}
	safetyLink := sharing.SharedLinkSessionFromContext(ctx)

	if signUpEnabled && safetyLink == nil {
		log.Warn(ctx, "Telematics complete call made with legacy appID based token instead of shared link token", log.String("appId", appInfo.AppID.String()))
		// TODO: We should start returning errors only after we stop seeing the above warning logs for a week or two.
		// return nil, nil, errors.Newf("Missing shared link session for telematics completion handler")
	}
	if safetyLink == nil {
		now := deps.Clock.Now()
		createdBy := uuid_utils.ParseUUIDValOr(appInfo.CreatedBy, appInfo.UnderwriterID)

		fleet, err := deps.FleetWrapper.FetchFleetByDOT(ctx, strconv.FormatInt(appInfo.DOTNumber, 10))
		if err != nil {
			return nil, nil, err
		}

		shareableSafetyLink := sharing.NewSafetyTelematicsSignUpLink(
			fleet.ID,
			createdBy,
			now,
			pointer_utils.ToPointer(now.AddDate(0, 1, 0)),
		)
		safetyLink = pointer_utils.ToPointer(shareableSafetyLink)

		// We insert this shareable link into our database, but we do not require any sharing roles since
		// we are attaching the fleet role directly to the service account user.
		if err := deps.SharingWrapper.InsertShareableLink(ctx, *safetyLink); err != nil {
			return nil, nil, err
		}
		// Segment event with shared link creation details
		eventDeps := events.EventDeps{SegmentClient: deps.SegmentClient}
		newShareableSafetyLinkCreatedEvent := safety_events.NewShareableSafetyLinkCreated(" ",
			safetyLink.ID,
			appInfo.AppID,
			createdBy,
			appInfo.UnderwriterID,
			safetyLink.LinkType.String(),
			safetyLink.ServiceAccountUserID,
			appInfo.DOTNumber,
			now,
			programType,
		)
		if err := newShareableSafetyLinkCreatedEvent.Upload(ctx, eventDeps); err != nil {
			log.Error(ctx, "error to upload event to Segment", log.Err(err))
		}
	}

	// Create a service account user with the fleet admin role for this DOT number.
	// Note that we create this service account user regardless of whether sign-up is enabled, since
	// we want to back all quoting shared links with service account users.
	serviceAccountUser, err := fetchOrCreateFleetAdminServiceAccountUser(ctx, deps, safetyLink, appInfo.DOTNumber)
	if err != nil {
		return nil, nil, err
	}
	dotNumber := strconv.FormatInt(appInfo.DOTNumber, 10)

	// Create the safety report for this new service account user. Note that FetchOrCreateFleetSafetyReport() does an
	// authz check, so we first set this user in the context.
	ctx = authz.WithUser(ctx, *serviceAccountUser)
	safetyReport, err := deps.SafetyReporter.FetchOrCreateFleetSafetyReport(ctx, dotNumber, serviceAccountUser.ID, pointer_utils.ToPointer(true))
	if err != nil {
		return nil, nil, err
	}

	return safetyLink, pointer_utils.ToPointer(models.SafetyReportToDB(*safetyReport)), nil
}

func fetchOrCreateFleetAdminServiceAccountUser(
	ctx context.Context,
	deps deps.Deps,
	sharedLink *db_sharing.ShareableLink,
	dotNumber int64,
) (*authz.User, error) {
	serviceAccountUserID := sharedLink.ServiceAccountUserID
	flt, err := deps.SafetyReporter.FetchOrCreateFleet(ctx, strconv.FormatInt(dotNumber, 10))
	if err != nil {
		return nil, err
	}
	domain := fmt.Sprintf("/fleets/%s/*", flt.ID)
	fleetRole, err := authz.NewFleetRole(serviceAccountUserID, authz.FleetAdminRole, domain, flt.ID)
	if err != nil {
		return nil, err
	}

	u, err := deps.AuthWrapper.FetchAuthzUser(ctx, serviceAccountUserID)
	if err == nil {
		// Ensure that the ServiceAccountUser has the fleet admin role.
		if slices.Contains(u.FleetIDs(), flt.ID) {
			return u, nil
		}
		// Add the fleet admin role to the existing service account user.
		if err := deps.AuthWrapper.CreateRole(ctx, fleetRole); err != nil {
			return nil, errors.Wrapf(err, "failed to add fleet admin role to service account user %s", serviceAccountUserID)
		}
		// This should return successfully since the service account user already exists.
		return deps.AuthWrapper.FetchAuthzUser(ctx, serviceAccountUserID)
	} else if !errors.Is(err, sql.ErrNoRows) {
		// We expect sql.ErrNoRows in the common case, so this is an unexpected error.
		return u, err
	}

	authzUser, err := sharing.AuthzUserForSharedLink(ctx, deps.AuthWrapper, sharedLink)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to fetch authz user for shared link")
	}
	user := &auth.User{
		UserInfo:       authzUser.UserInfo,
		PasswordDigest: nil,
	}
	if err := deps.AuthWrapper.CreateUser(ctx, user, *fleetRole); err != nil {
		return nil, errors.Wrapf(err, "Failed to create fleet admin for shared link service account user")
	}
	return deps.AuthWrapper.FetchAuthzUser(ctx, serviceAccountUserID)
}

// persistTelematicsConsent creates the telematics consent on our DB and updates the handleID in the corresponding
// application
func persistTelematicsConsent(
	ctx context.Context,
	deps deps.Deps,
	applicationId uuid.UUID,
	handleId uuid.UUID,
	programType policy_enums.ProgramType,
	tspEnum telematics.TSP,
) error {
	// We start by getting the application information which we will need for the database operations.
	appInfo, err := GetAppInfoFromToken(
		ctx,
		applicationId.String(),
		programType,
		deps.ApplicationWrapper,
		deps.AdmittedAppWrapper,
		deps.BusinessAutoAppWrapper,
	)
	if err != nil {
		errMessage := errors.Wrapf(
			err,
			"unable to obtain application information ApplicationId: %s",
			applicationId,
		)
		return errMessage
	}
	if programType != policy_enums.ProgramTypeBusinessAuto {
		// In case we found an application, we should upsert the fleet_telematics_consent.
		if err = deps.SafetyReporter.UpsertFleetTelematicsConsentForDot(
			ctx,
			strconv.FormatInt(appInfo.DOTNumber, 10),
			handleId,
			appInfo.AgencyID,
			false,
		); err != nil {
			return errors.Wrapf(
				err,
				"unable to insert fleet telematics consents for application ID: %s",
				applicationId,
			)
		}
	}
	// In the case there wasn't any problem with the upsert of the telematics consent, we can safely persist that
	// consent handleId in the application.
	if err = telematics_utils.PersistApplicationHandleId(
		ctx,
		deps.ApplicationWrapper,
		deps.AdmittedAppWrapper,
		deps.BusinessAutoAppWrapper,
		deps.AppReviewStateMachineWrapper,
		applicationId,
		programType,
		handleId,
		tspEnum,
		deps.MetricsClient,
	); err != nil {
		return errors.Wrapf(
			err,
			"Failed to persist connection information on applicationId %s",
			applicationId,
		)
	}

	// If the program type is not Business Auto, we will trigger the connection successful email job.
	if programType != policy_enums.ProgramTypeBusinessAuto {
		// We should send the successful consent email because we already persisted everything related to the connection
		if err := quoting_triggers.TriggerConnectionSuccessfulEmailJob(
			ctx,
			deps.Jobber,
			applicationId,
			appInfo.AgencyID,
			programType,
		); err != nil {
			// We will only shadow-write the error because we don't want to stop the connection flow in case we've failed
			// to send the email
			log.Error(ctx, "failed to trigger SendTelematicsConnectionSuccessfulEmailJob", log.Err(err))
		}
	}
	return nil
}

func handleJobError(
	ctx context.Context,
	err error,
	handleId uuid.UUID,
	req PostTSPConnectionRequest,
	actionDescription string,
	applicationId string,
) *PostTSPConnectionResponse {
	if err != nil {
		log.Error(
			ctx, fmt.Sprintf("unable to %s", actionDescription),
			log.Err(err), log.Stringer("handleID", handleId), log.Any("request", req),
		)
		if !errors.Is(err, uw_jobs.ErrTriggerPDFailed) {
			errMessage := helpers.WrapErrorMessage(
				err,
				fmt.Sprintf("unable to %s for application Id: %s", actionDescription, applicationId),
			)
			return &PostTSPConnectionResponse{InternalError: &errMessage}
		}
	}
	return nil
}

// triggerVerifyTSPConnection should attempt to create a job run for VerifyTSPConnection which will have a delayed
// start time of 1 hour. The job has a requirement of a maximum quantity of 3 per application. For doing this
// we utilize the jobber Creator UUID with a combination of "VerifyTSPQuotingConnection-<applicationID>-<id>" with id
// being an integer from 0 to 2. In case the job run already exists during an hour time frame, we skip the trigger. This
// will prevent duplicate jobs during the hour and also, it will cap the amount of emails sent to the application
// stakeholders.
func triggerVerifyTSPConnection(
	ctx context.Context,
	deps *deps.Deps,
	programType policy_enums.ProgramType,
	applicationID uuid.UUID,
	tsp telematics.TSP,
) error {
	jobMessage := messages.VerifyTSPConnectionMessage{
		ApplicationID: applicationID,
		TSP:           tsp,
		ConsentScope:  telematics_enums.QuotingConsent,
		ProgramType:   programType,
	}
	addJobRunParams := jobber.NewAddJobRunParams(
		jobs.VerifyTSPConnection,
		&jobMessage,
		jtypes.Metadata{
			RunType:            jtypes.OneOff,
			RequestedStartTime: null.TimeFrom(time.Now().Add(time.Hour * 1)),
		},
	)
	jobUniqueCreatorUUID, skipJob := validateTriggerVerifyTSPConnection(ctx, deps.Jobber, applicationID)
	if skipJob {
		log.Info(ctx, "Skipping telematics_jobs trigger. This can be caused by reaching the limit of job runs or "+
			"enqueue jobs meanwhile there's one that hasn't been executed.")
		return nil
	}
	addJobRunParams.CreatorUuid = &jobUniqueCreatorUUID
	jobRunId, err := deps.Jobber.AddJobRun(
		ctx,
		addJobRunParams,
	)
	if err != nil {
		log.Error(ctx, "Failed to add VerifyTSPConnection job", log.Err(err),
			log.Any("addJobRunParams", addJobRunParams),
		)
		return errors.Wrapf(
			err,
			"Failed to add VerifyTSPConnection job for application ID: %s",
			applicationID)
	}
	log.Info(ctx, "Added VerifyTSPConnection job", log.Stringer("JobRunId", jobRunId))
	return nil
}

// validateTriggerVerifyTSPConnection is a function that should validate if the job gets be enqueued or skipped. In
// the cases that it should be enqueued it returns a CreatorUUID which is a stable uuid built upon the jobKey,
// applicationID and integer id.
func validateTriggerVerifyTSPConnection(
	ctx context.Context,
	jobber quoting_jobber.Client,
	applicationId uuid.UUID,
) (taskCreatorUUID uuid.UUID, skipJob bool) {
	for i := 0; i < 3; i++ {
		taskCreatorUUID = uuid_utils.StableUUID("VerifyTSPConnection-" + applicationId.String() + "-" + strconv.Itoa(i))
		JobRun, err := jobber.GetJobRunByCreatorUUID(ctx, taskCreatorUUID)
		// We didn't find any job, or we had a problem retrieving it. We will immediately return the taskCreatorUUID and
		// the skipJob flag as false. Worst case scenario, the job already existed and the enqueued job gets omitted.
		// Best scenario, be properly enqueue the job.
		if err != nil || JobRun == nil {
			return taskCreatorUUID, false
		}
		// If we find the job, its RequestedStartTime is valid and the RequestedStartTime is later than the current
		// time, then we should skip the job because the trigger is being called during the hour timeframe of a
		// previously enqueued job with CreatorUUID = taskCreatorUUID.
		if JobRun != nil && JobRun.RequestedStartTime.Valid && JobRun.RequestedStartTime.Time.After(time.Now()) {
			return taskCreatorUUID, true
		}
	}
	return taskCreatorUUID, true
}

type TelematicsConsentInfo struct {
	DotNumber       int64
	CompanyName     string
	AgencyId        *uuid.UUID
	AgencyName      *string
	CreatorId       *uuid.UUID
	ProducerName    *string
	ProducerEmail   *openapi_types.Email
	InsuredName     *string
	InsuredEmail    *openapi_types.Email
	ApplicationVINs []string
}

func fetchTelematicsConsentInfo(
	ctx context.Context,
	deps deps.Deps,
	applicationId uuid.UUID,
	programType policy_enums.ProgramType,
) (*TelematicsConsentInfo, error) {
	switch programType {
	case policy_enums.ProgramTypeFleet:
		return fetchTelematicsConsentInfoForFleet(ctx, deps, applicationId)
	case policy_enums.ProgramTypeNonFleetAdmitted:
		return fetchTelematicsConsentInfoForNonFleet(ctx, deps, applicationId)
	case policy_enums.ProgramTypeBusinessAuto:
		return fetchTelematicsConsentInfoForBusinessAuto(ctx, deps, applicationId)
	case policy_enums.ProgramTypeNonFleetCanopiusNRB, policy_enums.ProgramTypeInvalid:
		return nil, errors.Newf("program type %s is not supported", programType)
	default:
		return nil, errors.Newf("invalid program type %s", programType)
	}
}

func fetchTelematicsConsentInfoForFleet(
	ctx context.Context,
	deps deps.Deps,
	applicationId uuid.UUID,
) (*TelematicsConsentInfo, error) {
	app, err := deps.ApplicationWrapper.GetAppById(ctx, applicationId.String())
	if err != nil {
		return nil, errors.Wrapf(err, "failed to fetch fleet application by ApplicationId: %s", applicationId)
	}
	if app.ProducerID == nil {
		return nil, errors.Wrapf(err, "empty ProducerId on ApplicationId: %s", app.ID)
	}
	producerId, err := uuid.Parse(*app.ProducerID)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to parse ProducerId on ApplicationId: %s", app.ID)
	}
	producerInfo, err := deps.AuthWrapper.FetchUserInfo(ctx, producerId)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to fetch user information for ProducerID: %s", producerId)
	}
	agency, err := deps.AgencyWrapper.FetchAgency(ctx, app.AgencyID)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to fetch agency with AgencyID: %s", app.AgencyID)
	}
	var insuredEmail *openapi_types.Email
	var insuredName *string
	if app.AdditionalInsuredInfo != nil && app.AdditionalInsuredInfo.Email.Valid && !app.AdditionalInsuredInfo.Email.IsZero() {
		insuredEmail = pointer_utils.ToPointer(openapi_types.Email(app.AdditionalInsuredInfo.Email.String))
		insuredName = pointer_utils.ToPointer(app.AdditionalInsuredInfo.Name)
	}
	return &TelematicsConsentInfo{
		DotNumber:       app.CompanyInfo.DOTNumber,
		CompanyName:     app.CompanyInfo.Name,
		AgencyId:        pointer_utils.ToPointer(agency.ID),
		AgencyName:      pointer_utils.ToPointer(agency.Name),
		CreatorId:       pointer_utils.ToPointer(uuid.MustParse(app.CreatedBy)),
		ProducerEmail:   pointer_utils.ToPointer(openapi_types.Email(producerInfo.Email)),
		ProducerName:    pointer_utils.ToPointer(producerInfo.FullName()),
		InsuredName:     insuredName,
		InsuredEmail:    insuredEmail,
		ApplicationVINs: app.EquipmentInfo.VINsList(),
	}, nil
}

func fetchTelematicsConsentInfoForNonFleet(
	ctx context.Context,
	deps deps.Deps,
	applicationId uuid.UUID,
) (*TelematicsConsentInfo, error) {
	nfApp, err := deps.AdmittedAppWrapper.GetAppById(ctx, applicationId)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to fetch non-fleet application by ApplicationId: %s", applicationId)
	}
	producerInfo, err := deps.AuthWrapper.FetchUserInfo(ctx, nfApp.ProducerID)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to parse ProducerId on NF ApplicationId: %s", nfApp.ProducerID)
	}
	agency, err := deps.AgencyWrapper.FetchAgency(ctx, nfApp.AgencyID)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to fetch agency with AgencyID: %s", nfApp.AgencyID)
	}
	nfAppVins := make([]string, 0)
	for _, vehicle := range nfApp.Info.EquipmentInfo.Vehicles {
		nfAppVins = append(nfAppVins, vehicle.VIN)
	}
	return &TelematicsConsentInfo{
		DotNumber:   int64(nfApp.Info.CompanyInfo.DOTNumber),
		CompanyName: nfApp.Info.GetCompanyName(),
		AgencyId:    pointer_utils.ToPointer(agency.ID),
		AgencyName:  pointer_utils.ToPointer(agency.Name),
		// For cases where apps are created via API,
		// created by is the system and hence we are using the marketer id here
		CreatorId:       pointer_utils.ToPointer(nfApp.MarketerId),
		ProducerEmail:   pointer_utils.ToPointer(openapi_types.Email(producerInfo.Email)),
		ProducerName:    pointer_utils.ToPointer(producerInfo.FullName()),
		InsuredName:     pointer_utils.ToPointer(nfApp.Info.TSPInfo.InsuredInfo.Name),
		InsuredEmail:    pointer_utils.ToPointer(openapi_types.Email(nfApp.Info.TSPInfo.InsuredInfo.Email)),
		ApplicationVINs: nfAppVins,
	}, nil
}

func fetchTelematicsConsentInfoForBusinessAuto(
	ctx context.Context,
	deps deps.Deps,
	applicationId uuid.UUID,
) (*TelematicsConsentInfo, error) {
	app, err := deps.BusinessAutoAppWrapper.GetByID(ctx, applicationId)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to fetch business auto application by ApplicationId: %s", applicationId)
	}

	producerInfo, err := deps.AuthWrapper.FetchUserInfo(ctx, app.ProducerID)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to fetch user information for ProducerID: %s", app.ProducerID)
	}

	agency, err := deps.AgencyWrapper.FetchAgency(ctx, app.AgencyID)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to fetch agency with AgencyID: %s", app.AgencyID)
	}

	// Extract VINs from business auto vehicles
	var appVins []string
	// Ensure that VehiclesInfo is not nil before accessing it
	if app.VehiclesInfo != nil {
		appVins = make([]string, 0, len(*app.VehiclesInfo))
		for _, vehicle := range *app.VehiclesInfo {
			appVins = append(appVins, vehicle.VIN)
		}
	}

	// Handle DOT number - business auto uses *int
	var dotNumber int64
	if app.CompanyInfo.DOTNumber != nil {
		dotNumber = int64(*app.CompanyInfo.DOTNumber)
	}

	// Handle telematics info if available
	var insuredName *string
	var insuredEmail *openapi_types.Email
	if app.TelematicsInfo != nil {
		insuredName = pointer_utils.ToPointer(app.TelematicsInfo.FullName)
		insuredEmail = pointer_utils.ToPointer(openapi_types.Email(app.TelematicsInfo.Email))
	}

	return &TelematicsConsentInfo{
		DotNumber:       dotNumber,
		CompanyName:     app.CompanyInfo.Name,
		AgencyId:        pointer_utils.ToPointer(agency.ID),
		AgencyName:      pointer_utils.ToPointer(agency.Name),
		CreatorId:       pointer_utils.ToPointer(app.CreatedBy),
		ProducerEmail:   pointer_utils.ToPointer(openapi_types.Email(producerInfo.Email)),
		ProducerName:    pointer_utils.ToPointer(producerInfo.FullName()),
		InsuredName:     insuredName,
		InsuredEmail:    insuredEmail,
		ApplicationVINs: appVins,
	}, nil
}

type SpeedgaugeConnData struct {
	TSPAccountDetails  SpeedgaugeBaseConnData
	UserEnteredTSPName null.String
}

type SpeedgaugeBaseConnData struct {
	Name  string
	Email string
}
