package tsp

import (
	"context"
	"fmt"
	"net/http"
	application_model "nirvanatech.com/nirvana/nirvanaapp/models/application"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/api-server/common"
	app_interceptor "nirvanatech.com/nirvana/api-server/interceptors/application/deps"
	business_auto_model "nirvanatech.com/nirvana/business-auto/model"
	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	oapi_app "nirvanatech.com/nirvana/openapi-specs/components/application"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
	"nirvanatech.com/nirvana/quoting/telematics"
)

func HandleGetTelematicsFleetInfoAuthz(
	deps app_interceptor.Deps,
	req GetTelematicsFleetInfoRequest,
) common.HandlerAuthzResponse {
	// No authz since this is an anonymous endpoint.
	return common.HandlerAuthzResponse{IsAuthorized: true}
}

// enableValidationErrors returns true if we should return fleet_info validation
// errors back to the frontend, or false if we should suppress the errors.
// This is only required for our initial rollout, and should be reverted after
// we GA the VIN matching validation errors feature.
func enableValidationErrors(ctx context.Context, deps app_interceptor.Deps, createdBy string) (bool, error) {
	createdByID, err := uuid.Parse(createdBy)
	if err != nil {
		return false, errors.Wrapf(err, "Failed to parse createdBy UUID '%s'", createdBy)
	}
	user, err := deps.AuthWrapper.FetchAuthzUser(ctx, createdByID)
	if err != nil {
		return false, errors.Wrapf(err, "Failed to fetch application user %s", createdBy)
	}
	return deps.FeatureFlagClient.BoolVariation(
		feature_flag_lib.BuildLookupAttributes(*user),
		feature_flag_lib.FeatureTelematicsValidationErrors,
		false,
	)
}

func HandleGetTelematicsFleetInfo(
	ctx context.Context,
	deps app_interceptor.Deps,
	req GetTelematicsFleetInfoRequest,
) GetTelematicsFleetInfoResponse {
	programType, err := ConvertProgramTypeFromOAPI(req.ProgramType)
	if err != nil {
		log.Error(ctx, "HandleGetTelematicsFleetInfo: Failed to convert ProgramType",
			log.Err(err), log.String("handleID", req.HandleID))
		return GetTelematicsFleetInfoResponse{Error: &oapi_common.ErrorMessage{
			Code:    http.StatusInternalServerError,
			Message: "Failed to get ProgramType",
		}}
	}
	var response *oapi_app.TelematicsFleetInfoResponse
	switch programType {
	case policy_enums.ProgramTypeFleet:
		response, err = telematics.GetTelematicsFleetInfo(ctx,
			req.HandleID,
			deps.ApplicationWrapper,
			deps.TelematicsDataPlatformClient,
			deps.VehiclesServiceClient,
			deps.FetcherClientFactory,
			deps.ProcessorClientFactory,
			deps.ReadFromStoreInterceptorFactory,
			deps.WriteToStoreInterceptorFactory,
		)
		if err != nil {
			log.Error(ctx, "HandleGetTelematicsFleetInfo: Failed to get telematics info",
				log.Err(err), log.String("handleID", req.HandleID))
			return GetTelematicsFleetInfoResponse{Error: &oapi_common.ErrorMessage{
				Code:    http.StatusInternalServerError,
				Message: "Failed to get telematics info",
			}}
		}
	case policy_enums.ProgramTypeNonFleetAdmitted:
		response, err = telematics.GetTelematicsFleetInfoForAdmittedApp(
			ctx,
			req.HandleID,
			deps.AdmittedAppWrapper,
			deps.TelematicsDataPlatformClient,
			deps.FetcherClientFactory,
			deps.ProcessorClientFactory,
		)
		if err != nil {
			log.Error(ctx, "HandleGetTelematicsFleetInfo: Failed to get "+
				"telematics info for admitted program.",
				log.Err(err), log.String("handleID", req.HandleID))
			return GetTelematicsFleetInfoResponse{Error: &oapi_common.ErrorMessage{
				Code:    http.StatusInternalServerError,
				Message: "Failed to get telematics info",
			}}
		}
	case policy_enums.ProgramTypeBusinessAuto:
		// For now, Business Auto can use the same logic as Non-Fleet Admitted
		// since they both work with individual applications rather than fleets
		// TODO: Implement specific Business Auto telematics fleet info logic if needed
		// check with Cristobal
		log.Info(ctx, "HandleGetTelematicsFleetInfo: Business Auto telematics info not yet implemented, returning empty response")
		response = &oapi_app.TelematicsFleetInfoResponse{
			ValidationErrors: &[]oapi_app.TelematicsFleetInfoResponseValidationErrors{},
		}
	case policy_enums.ProgramTypeNonFleetCanopiusNRB, policy_enums.ProgramTypeInvalid:
		log.Error(ctx, "HandleGetTelematicsFleetInfo: Program Type not supported",
			log.Any("programType:", programType.String()))
		return GetTelematicsFleetInfoResponse{Error: &oapi_common.ErrorMessage{
			Code:    http.StatusInternalServerError,
			Message: "Failed to get telematics info.",
		}}
	default:
		log.Error(ctx, "HandleGetTelematicsFleetInfo: Invalid Program Type")
		return GetTelematicsFleetInfoResponse{Error: &oapi_common.ErrorMessage{
			Code:    http.StatusInternalServerError,
			Message: "Failed to get telematics info.",
		}}
	}
	var hasValidationErrors bool
	if response.ValidationErrors != nil && len(*response.ValidationErrors) > 0 {
		hasValidationErrors = true
		log.Info(ctx, "HandleGetTelematicsFleetInfo: Found validation errors",
			log.String("handleID", req.HandleID),
			log.String("validationErrors", fmt.Sprint(*response.ValidationErrors)))
	}
	// If the app has validation errors then update the telematics connection column
	if err := persistReconnectionRequiredStatus(
		ctx, deps, req.HandleID, hasValidationErrors, programType); err != nil {
		log.Error(ctx, "HandleGetTelematicsFleetInfo: Failed to persist reconnection required status",
			log.Err(err), log.String("handleID", req.HandleID))
		return GetTelematicsFleetInfoResponse{Error: &oapi_common.ErrorMessage{
			Code:    http.StatusInternalServerError,
			Message: "Failed to persist reconnection required status",
		}}
	}
	log.Info(
		ctx, "HandleGetTelematicsFleetInfo: telematics validation status",
		log.Bool("hasValidationErrors", hasValidationErrors),
		log.String("handleID", req.HandleID),
		log.String("programType", programType.String()),
	)
	handleID, err := uuid.Parse(req.HandleID)
	if err != nil {
		log.Error(ctx, "HandleGetTelematicsFleetInfo: Failed to parse handleId",
			log.Err(err), log.String("handleID", req.HandleID))
		return GetTelematicsFleetInfoResponse{Error: &oapi_common.ErrorMessage{
			Code:    http.StatusInternalServerError,
			Message: "Failed to parse handleId",
		}}
	}
	appInfo, err := GetAppInfoFromHandleId(ctx, handleID, programType, deps)
	if err != nil {
		log.Error(ctx, "HandleGetTelematicsFleetInfo: Failed to get appInfo",
			log.Err(err), log.String("handleID", req.HandleID))
		return GetTelematicsFleetInfoResponse{Error: &oapi_common.ErrorMessage{
			Code:    http.StatusInternalServerError,
			Message: "Failed to get application info",
		}}
	}
	enabled, err := enableValidationErrors(ctx, deps, appInfo.CreatedBy)
	if !enabled {
		log.Info(ctx, "HandleGetTelematicsFleetInfo: skipping telematics validation errors", log.Err(err))
		// Note: We want to return a non-null JSON array in the API response,
		// which is why we initialize an empty slice instead of returning nil.
		response.ValidationErrors = &[]oapi_app.TelematicsFleetInfoResponseValidationErrors{}
	}
	return GetTelematicsFleetInfoResponse{
		Success: response,
	}
}

func persistReconnectionRequiredStatus(
	ctx context.Context,
	deps app_interceptor.Deps,
	handleId string,
	hasValidationErrors bool,
	programType policy_enums.ProgramType,
) error {
	// If the app has validation errors then update the telematics connection column
	// with a reconnection required status.
	switch programType {
	case policy_enums.ProgramTypeFleet:
		apps, err := deps.ApplicationWrapper.GetAllApplicationsByHandleId(ctx, handleId)
		if err != nil {
			return errors.Wrapf(err, "Failed to get applications by handleID %s", handleId)
		}
		handleID, err := uuid.Parse(handleId)
		if err != nil {
			return errors.Wrapf(err, "Failed to parse handleID %s", handleId)
		}
		for _, app := range apps {
			err = deps.ApplicationWrapper.UpdateApp(ctx, app.ID,
				func(a application.Application) (application.Application, error) {
					// If the app doesn't have validation errors then we update the telematics info field
					if !hasValidationErrors {
						return a, nil
					}
					telematicsInfo := a.TelematicsInfo
					if telematicsInfo == nil {
						telematicsInfo = new(application.TelematicsInfo)
					}
					telematicsInfo.TelematicsDataStatus = app_enums.TelematicsDataStatusReconnectionRequired
					telematicsInfo.TelematicsConnHandleId = handleID
					a.TelematicsInfo = telematicsInfo
					return a, nil
				})
			if err != nil {
				return errors.Wrapf(err, "Failed to update app %s", app.ID)
			}
		}
	case policy_enums.ProgramTypeNonFleetAdmitted:
		handleID, err := uuid.Parse(handleId)
		if err != nil {
			return errors.Wrapf(err, "Failed to parse handleID %s", handleId)
		}
		apps, err := deps.AdmittedAppWrapper.GetAllApplicationsByTSPConnHandleId(ctx, handleID)
		if err != nil {
			return errors.Wrapf(err, "Failed to get applications by handleID %s", handleId)
		}
		for _, app := range apps {
			err := deps.AdmittedAppWrapper.UpdateApp(ctx, app.ID,
				func(a nf_app.Application[*admitted_app.AdmittedApp],
				) (nf_app.Application[*admitted_app.AdmittedApp], error) {
					// If the app doesn't have validation errors then we update the telematics info field
					if !hasValidationErrors {
						return a, nil
					}
					telematicsInfo := a.Info.TSPInfo.TelematicsInfo
					if telematicsInfo == nil {
						telematicsInfo = new(application.TelematicsInfo)
					}
					telematicsInfo.TelematicsDataStatus = app_enums.TelematicsDataStatusReconnectionRequired
					telematicsInfo.TelematicsConnHandleId = handleID
					a.Info.TSPInfo.TelematicsInfo = telematicsInfo
					return a, nil
				})
			if err != nil {
				return errors.Wrapf(err, "Failed to update admitted app %s", app.ID)
			}
		}
	case policy_enums.ProgramTypeBusinessAuto:
		handleID, err := uuid.Parse(handleId)
		if err != nil {
			return errors.Wrapf(err, "Failed to parse handleID %s", handleId)
		}
		app, err := deps.BusinessAutoAppWrapper.GetApplicationByTSPConnHandleId(ctx, handleID)
		if err != nil {
			return errors.Wrapf(err, "Failed to get business auto application by handleID %s", handleId)
		}
		err = deps.BusinessAutoAppWrapper.UpdateApp(ctx, app.ID,
			func(a *business_auto_model.BusinessAutoApp) (*business_auto_model.BusinessAutoApp, error) {
				// If the app doesn't have validation errors then return as-is
				if !hasValidationErrors {
					return a, nil
				}
				// Initialize TelematicsInfo if nil
				if a.TelematicsInfo == nil {
					a.TelematicsInfo = &business_auto_model.TelematicsInfo{}
				}
				// Initialize PipelineInfo if needed
				if a.TelematicsInfo.PipelineInfo == nil {
					a.TelematicsInfo.PipelineInfo = &application_model.TelematicsInfo{}
				}
				// Set the proper telematics data status and handle ID like fleet/nonfleet
				a.TelematicsInfo.PipelineInfo.TelematicsDataStatus = app_enums.TelematicsDataStatusReconnectionRequired
				a.TelematicsInfo.PipelineInfo.TelematicsConnHandleId = handleID
				a.TSPConnHandleId = &handleID
				return a, nil
			})
		if err != nil {
			return errors.Wrapf(err, "Failed to update business auto app %s", app.ID)
		}
	case policy_enums.ProgramTypeNonFleetCanopiusNRB, policy_enums.ProgramTypeInvalid:
		return errors.Newf("Program type not supported %s", programType.String())
	}
	return nil
}

type GetTelematicsFleetInfoRequest struct {
	HandleID    string
	ProgramType *oapi_common.ProgramType
}
type GetTelematicsFleetInfoResponse struct {
	Success *oapi_app.TelematicsFleetInfoResponse
	Error   *oapi_common.ErrorMessage
}

func (u *GetTelematicsFleetInfoResponse) StatusCode() int {
	switch {
	case u.Success != nil:
		return http.StatusOK
	case u.Error != nil:
		return u.Error.Code
	default:
		return http.StatusInternalServerError
	}
}

func (u *GetTelematicsFleetInfoResponse) Body() interface{} {
	switch {
	case u.Success != nil:
		return *u.Success
	case u.Error != nil:
		return *u.Error
	default:
		return nil
	}
}

var _ common.HandlerResponse = (*GetTelematicsFleetInfoResponse)(nil)
