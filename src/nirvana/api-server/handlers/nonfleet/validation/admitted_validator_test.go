package validation

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"nirvanatech.com/nirvana/openapi-specs/components/application"

	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app/enums"
	nf_oapi "nirvanatech.com/nirvana/openapi-specs/components/nonfleet"
)

func Test_validateOperationForm(t *testing.T) {
	type args struct {
		form *nf_oapi.AdmittedAppUpdateForm
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "HiredAuto with APD but low AnnualCostOfHire",
			args: args{
				form: &nf_oapi.AdmittedAppUpdateForm{
					OperationsForm: &nf_oapi.AdmittedAppOperationsForm{
						HasHiredAuto: pointer_utils.Bool(true),
						CompanyInfo: &nf_oapi.CompanyInfo{
							AnnualCostOfHire: pointer_utils.ToPointer(3000),
						},
						Coverages: &[]nf_oapi.CoverageDetails{
							{
								CoverageType: oapi_common.CoverageAutoPhysicalDamage,
							},
						},
						TerminalLocation: &nf_oapi.AdmittedAppTerminalLocation{},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "Valid OperationsForm with HiredAuto and APD",
			args: args{
				form: &nf_oapi.AdmittedAppUpdateForm{
					OperationsForm: &nf_oapi.AdmittedAppOperationsForm{
						HasHiredAuto: pointer_utils.Bool(true),
						CompanyInfo: &nf_oapi.CompanyInfo{
							AnnualCostOfHire: pointer_utils.ToPointer(6000),
						},
						Coverages: &[]nf_oapi.CoverageDetails{
							{
								CoverageType: oapi_common.CoverageAutoPhysicalDamage,
								IsRequired:   true,
							},
						},
						TerminalLocation: &nf_oapi.AdmittedAppTerminalLocation{},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "Missing TerminalLocation should return error",
			args: args{
				form: &nf_oapi.AdmittedAppUpdateForm{
					OperationsForm: &nf_oapi.AdmittedAppOperationsForm{
						HasHiredAuto: pointer_utils.Bool(false),
						CompanyInfo: &nf_oapi.CompanyInfo{
							AnnualCostOfHire: pointer_utils.ToPointer(8000),
						},
						Coverages:        &[]nf_oapi.CoverageDetails{},
						TerminalLocation: nil,
					},
				},
			},
			wantErr: true,
		},
		{
			name: "Valid form with non-HiredAuto and TerminalLocation is present",
			args: args{
				form: &nf_oapi.AdmittedAppUpdateForm{
					OperationsForm: &nf_oapi.AdmittedAppOperationsForm{
						HasHiredAuto: pointer_utils.Bool(false),
						CompanyInfo: &nf_oapi.CompanyInfo{
							AnnualCostOfHire: pointer_utils.ToPointer(8000),
						},
						Coverages:        &[]nf_oapi.CoverageDetails{},
						TerminalLocation: &nf_oapi.AdmittedAppTerminalLocation{},
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := validateOperationForm(tt.args.form); (err != nil) != tt.wantErr {
				t.Errorf("validateOperationForm() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_validateEquipmentInfo(t *testing.T) {
	equipments1 := []nf_oapi.AdmittedAppVehicleDetails{
		{
			Vin:          "1",
			Make:         "Toyota",
			VehicleType:  nf_oapi.VehicleTypeTractor,
			VehicleClass: nf_oapi.VehicleClassTruckTractor,
			WeightClass:  pointer_utils.String(enums.WeightClass8.String()),
		},
		{
			Vin:          "2",
			Make:         "Toyota",
			VehicleType:  nf_oapi.VehicleTypeTractor,
			VehicleClass: nf_oapi.VehicleClassTruckTractor,
			WeightClass:  pointer_utils.String(enums.WeightClass8.String()),
		},
	}

	equipments2 := append(equipments1, nf_oapi.AdmittedAppVehicleDetails{
		Vin:          "3",
		Make:         "Toyota",
		VehicleType:  nf_oapi.VehicleTypeTractor,
		VehicleClass: nf_oapi.VehicleClassTruckTractor,
		WeightClass:  pointer_utils.String(enums.WeightClass8.String()),
		StatedValue:  pointer_utils.ToPointer(0),
	})

	equipments3 := append(equipments1, nf_oapi.AdmittedAppVehicleDetails{
		Vin:          "3",
		Make:         "Toyota",
		VehicleType:  nf_oapi.VehicleTypeTruck,
		VehicleClass: nf_oapi.VehicleClassAutoHauler,
		WeightClass:  pointer_utils.String(enums.WeightClass3.String()),
		StatedValue:  pointer_utils.ToPointer(35000),
	})

	tests := []struct {
		name    string
		form    *nf_oapi.AdmittedAppEquipmentsForm
		hasAPD  bool
		wantErr bool
	}{
		{
			name:    "empty equipment list",
			wantErr: false,
		},
		{
			name:    "valid equipment info",
			form:    &nf_oapi.AdmittedAppEquipmentsForm{Vehicles: &equipments1},
			hasAPD:  false,
			wantErr: false,
		},
		{
			name:    "invalid equipment list for APD with stated value = 0",
			form:    &nf_oapi.AdmittedAppEquipmentsForm{Vehicles: &equipments2},
			hasAPD:  true,
			wantErr: true,
		},
		{
			name:    "invalid equipment list for vehicleType, vehicleClass and weightClass",
			form:    &nf_oapi.AdmittedAppEquipmentsForm{Vehicles: &equipments3},
			hasAPD:  true,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := validateEquipmentInfo(tt.form, tt.hasAPD); (err != nil) != tt.wantErr {
				t.Errorf("validateEquipmentInfo() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_validateIndicationForm(t *testing.T) {
	type args struct {
		form *nf_oapi.AdmittedAppUpdateForm
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "Invalid Package with HiredAuto",
			args: args{
				form: &nf_oapi.AdmittedAppUpdateForm{
					OperationsForm: &nf_oapi.AdmittedAppOperationsForm{HasHiredAuto: pointer_utils.Bool(true)},
					IndicationForm: &nf_oapi.AdmittedAppIndicationForm{
						SelectedIndication: &nf_oapi.AdmittedAppSelectedIndication{
							PackageName: pointer_utils.ToPointer(application.IndicationOptionTagStandard),
						},
						Coverages: &nf_oapi.AdmittedAppCoverageForm{
							CoverageAutoLiability: &nf_oapi.CoverageDetails{
								Limit: pointer_utils.ToPointer(500000),
							},
							CoverageMotorTruckCargo: &nf_oapi.CoverageDetails{
								Limit: pointer_utils.ToPointer(500000),
							},
						},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "Valid Package and low AL Limit with HiredAuto",
			args: args{
				form: &nf_oapi.AdmittedAppUpdateForm{
					OperationsForm: &nf_oapi.AdmittedAppOperationsForm{HasHiredAuto: pointer_utils.Bool(true)},
					IndicationForm: &nf_oapi.AdmittedAppIndicationForm{
						SelectedIndication: &nf_oapi.AdmittedAppSelectedIndication{
							PackageName: pointer_utils.ToPointer(application.IndicationOptionTagComplete),
						},
						Coverages: &nf_oapi.AdmittedAppCoverageForm{
							CoverageAutoLiability: &nf_oapi.CoverageDetails{
								Limit: pointer_utils.ToPointer(500000),
							},
							CoverageMotorTruckCargo: &nf_oapi.CoverageDetails{
								Limit: pointer_utils.ToPointer(500000),
							},
						},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "Valid IndicationForm and coverages",
			args: args{
				form: &nf_oapi.AdmittedAppUpdateForm{
					OperationsForm: &nf_oapi.AdmittedAppOperationsForm{HasHiredAuto: pointer_utils.Bool(true)},
					IndicationForm: &nf_oapi.AdmittedAppIndicationForm{
						SelectedIndication: &nf_oapi.AdmittedAppSelectedIndication{
							PackageName: pointer_utils.ToPointer(application.IndicationOptionTagComplete),
						},
						Coverages: &nf_oapi.AdmittedAppCoverageForm{
							CoverageAutoLiability: &nf_oapi.CoverageDetails{
								Limit: pointer_utils.ToPointer(1000000),
							},
							CoverageMotorTruckCargo: &nf_oapi.CoverageDetails{
								Limit: pointer_utils.ToPointer(1000000),
							},
						},
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := validateIndicationForm(tt.args.form); (err != nil) != tt.wantErr {
				t.Errorf("validateIndicationForm() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestValidateSSN(t *testing.T) {
	tests := []struct {
		name        string
		ssn         string
		expectError bool
		errorMsg    string
	}{
		{
			name:        "valid SSN",
			ssn:         "***********",
			expectError: false,
		},
		{
			name:        "empty SSN",
			ssn:         "",
			expectError: true,
			errorMsg:    "SSN cannot be empty",
		},
		{
			name:        "invalid format - missing dashes",
			ssn:         "123456789",
			expectError: true,
			errorMsg:    "SSN must be in the format XXX-XX-XXXX",
		},
		{
			name:        "invalid format - too many parts",
			ssn:         "***********-00",
			expectError: true,
			errorMsg:    "SSN must be in the format XXX-XX-XXXX",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateSSN(tt.ssn)
			if tt.expectError {
				assert.EqualError(t, err, tt.errorMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
