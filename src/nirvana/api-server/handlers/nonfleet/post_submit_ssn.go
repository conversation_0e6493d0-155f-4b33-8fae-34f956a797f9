package nonfleet

import (
	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/handlers/nonfleet/utils"
	"nirvanatech.com/nirvana/api-server/handlers/nonfleet/validation"
	"nirvanatech.com/nirvana/api-server/interceptors/nonfleet_application/deps"
	"nirvanatech.com/nirvana/common-go/crypto_utils"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/jobber"
	"nirvanatech.com/nirvana/jobber/jtypes"
	quotingjobs "nirvanatech.com/nirvana/nonfleet/quoting-jobs"
)

func HandlePostSubmitSSNAuthz(
	eCtx echo.Context,
	deps deps.Deps,
	req PostSubmitSSNRequest,
) common.HandlerAuthzResponse {
	ctx := eCtx.Request().Context()
	return utils.HasPermissionOverApp(
		ctx,
		authz.UserFromContext(ctx),
		authz.WriteAction,
		req.ApplicationID,
		enums.ProgramTypeNonFleetAdmitted,
		deps.AdmittedAppWrapper,
		deps.AuthzChecker,
	)
}

func HandlePostSubmitSSN(
	eCtx echo.Context,
	deps deps.Deps,
	req PostSubmitSSNRequest,
) error {
	appID := req.ApplicationID.String()
	ctx := log.ContextWithFields(eCtx.Request().Context(), log.AppID(appID))

	if err := validation.ValidateSSN(req.SSN); err != nil {
		return common.NewNirvanaBadRequestErrorWithReason(err, "validation failed for SSN")
	}

	encryptedSSN, err := crypto_utils.Encrypt(ctx, deps.CryptoClient, []byte(req.SSN), crypto_utils.KeyIdUserSSN)
	if err != nil {
		log.Error(
			ctx,
			"Failed to encrypt ssn",
			log.Err(err),
		)
		return common.NewNirvanaInternalServerErrorf(err, common.EntityApplication, appID)
	}

	encryptedSSNLastFour, err := crypto_utils.Encrypt(ctx, deps.CryptoClient, []byte(req.SSN[len(req.SSN)-4:]), crypto_utils.KeyIdUserSSNLastFour)
	if err != nil {
		log.Error(
			ctx,
			"Failed to encrypt ssn last four",
			log.Err(err),
		)
		return common.NewNirvanaInternalServerErrorf(err, common.EntityApplication, appID)
	}

	if err = deps.AdmittedAppWrapper.UpdateApp(
		ctx, req.ApplicationID,
		func(app application.Application[*admitted_app.AdmittedApp]) (application.Application[*admitted_app.AdmittedApp], error) {
			app.Info.CompanyInfo.BusinessOwner.EncryptedSSN = encryptedSSN
			app.Info.CompanyInfo.BusinessOwner.EncryptedSSNLastFour = encryptedSSNLastFour
			return app, nil
		},
	); err != nil {
		log.Error(
			ctx,
			"Failed to update application with ssn",
			log.Err(err),
		)
		return common.NewNirvanaInternalServerErrorf(err, common.EntityApplication, appID)
	}

	log.Info(ctx, "Successfully updated application with ssn")

	addJobRunParams := jobber.NewAddJobRunParams(
		quotingjobs.NCFCreditFeatures,
		&quotingjobs.NCFCreditFeaturesMessage{
			ApplicationID: req.ApplicationID,
		},
		jtypes.NewMetadata(jtypes.Immediate),
	)
	if _, err = deps.Jobber.AddJobRun(ctx, addJobRunParams); err != nil {
		log.Error(
			ctx,
			"Failed to add NCF credit features job run",
			log.Err(err),
		)
		return common.NewNirvanaInternalServerErrorf(err, common.EntityApplication, appID)
	}

	return nil
}

type PostSubmitSSNRequest struct {
	ApplicationID uuid.UUID
	SSN           string
}
