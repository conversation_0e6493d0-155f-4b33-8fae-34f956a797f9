package business_auto

import (
	"context"
	"database/sql"
	"sort"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	openapitypes "github.com/oapi-codegen/runtime/types"
	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/interceptors/business_auto/deps"
	"nirvanatech.com/nirvana/business-auto/enums"
	"nirvanatech.com/nirvana/business-auto/model"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	sharing_wrapper "nirvanatech.com/nirvana/db-api/db_wrappers/sharing"
	"nirvanatech.com/nirvana/openapi-specs/components/application"
	openapi "nirvanatech.com/nirvana/openapi-specs/components/business-auto"
	openapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
	nirvana_openapi "nirvanatech.com/nirvana/openapi-specs/components/nirvana"
	"nirvanatech.com/nirvana/quoting/ancillary_coverages"
	"nirvanatech.com/nirvana/rating/rtypes"
	"nirvanatech.com/nirvana/sharing"
)

// TODO: REMOVE THIS LATER AND THINK OF A BETTER SOLUTION HERE
// CURRENTLY WE ARE KEEPING THIS TO SUPPORT FE TO HAVE IS_ENABLED FLAG
// SupportedPrimaryCoverages defines all primary coverages supported for business auto
var SupportedPrimaryCoverages = []app_enums.Coverage{
	app_enums.CoverageAutoLiability,
	app_enums.CoverageAutoPhysicalDamage,
}

// HandleGetApplicationAuthz handles authorization for the get application endpoint.
// This operation checks if the user has read permission over the application.
func HandleGetApplicationAuthz(
	ctx context.Context,
	deps deps.Deps,
	applicationId uuid.UUID,
) common.HandlerAuthzResponse {
	return common.HandlerAuthzResponse{IsAuthorized: true}
}

func HandleGetApplication(
	ctx context.Context,
	deps deps.Deps,
	appID uuid.UUID,
) (*openapi.GetBusinessAutoAppResponse, error) {
	app, err := deps.AppWrapper.GetByID(ctx, appID)
	if err != nil {
		log.Error(ctx, "Failed to get application",
			log.Err(err))

		if errors.Is(err, sql.ErrNoRows) {
			return nil, common.NewNirvanaNotFoundErrorf(err, common.EntityApplication, appID.String())
		}

		return nil, common.NewNirvanaInternalServerErrorf(err, common.EntityApplication, appID.String())
	}

	response, err := convertToGetResponse(ctx, deps.AuthWrapper, deps.SharingWrapper, app)
	if err != nil {
		log.Error(ctx, "Failed to convert application to response",
			log.Err(err))
		return nil, common.NewNirvanaInternalServerErrorf(err, common.EntityApplication, appID.String())
	}

	return response, nil
}

func convertToGetResponse(
	ctx context.Context,
	authWrapper auth.DataWrapper,
	sharingWrapper sharing_wrapper.DataWrapper,
	app *model.BusinessAutoApp,
) (*openapi.GetBusinessAutoAppResponse, error) {
	companyInfo := convertCompanyInfoToOAPI(app.CompanyInfo)
	vehiclesInfo, err := convertVehiclesInfoToOAPI(app.VehiclesInfo)
	if err != nil {
		log.Error(ctx, "Failed to convert vehicles info to OAPI",
			log.Err(err))
		return nil, common.NewNirvanaInternalServerErrorf(err, common.EntityApplication, app.ID.String())
	}

	coveragesInfo, err := convertCoveragesInfoToOAPI(app.CoveragesInfo)
	if err != nil {
		log.Error(ctx, "Failed to convert coverages info to OAPI",
			log.Err(err))
		return nil, common.NewNirvanaInternalServerErrorf(err, common.EntityApplication, app.ID.String())
	}

	ancillaryCoverages, err := convertAncillaryCoveragesToOAPI(
		ctx,
		app.EffectiveDurationStart,
		app.CoveragesInfo,
		app.CompanyInfo.USState,
	)
	if err != nil {
		log.Error(ctx, "Failed to convert ancillary coverages to OAPI",
			log.Err(err))
		return nil, common.NewNirvanaInternalServerErrorf(err, common.EntityApplication, app.ID.String())
	}

	filingsInfo := convertFilingsInfoToOAPI(app.FilingsInfo)
	underwritingOverrides, err := convertUnderwritingOverridesToOAPI(app.UnderwritingOverrides, ancillaryCoverages)
	if err != nil {
		log.Error(ctx, "Failed to convert underwriting overrides to OAPI",
			log.Err(err))
		return nil, common.NewNirvanaInternalServerErrorf(err, common.EntityApplication, app.ID.String())
	}

	producerInfo, err := getProducerInfo(ctx, authWrapper, app.ProducerID)
	if err != nil {
		log.Error(ctx, "Failed to get producer info",
			log.Err(err),
			log.String("producerId", app.ProducerID.String()))
		return nil, common.NewNirvanaInternalServerErrorf(err, common.EntityProducer, app.ProducerID.String())
	}

	state, err := convertUWStateToOpenAPIAppState(app.UWState)
	if err != nil {
		log.Error(ctx, "Failed to convert UWState to OpenAPI AppState",
			log.Err(err))
		return nil, common.NewNirvanaInternalServerErrorf(err, common.EntityApplication, app.ID.String())
	}

	response := &openapi.GetBusinessAutoAppResponse{
		Id:      app.ID,
		ShortID: string(app.ShortID),
		State:   state,
		EffectiveDate: openapitypes.Date{
			Time: app.EffectiveDurationStart,
		},
		EffectiveDateTo: openapitypes.Date{
			Time: app.EffectiveDurationEnd,
		},
		CompanyInfo:           companyInfo,
		VehiclesInfo:          vehiclesInfo,
		CoveragesInfo:         *coveragesInfo,
		FilingsInfo:           filingsInfo,
		ProducerInfo:          *producerInfo,
		UnderwritingOverrides: underwritingOverrides,
		CreatedAt:             app.CreatedAt,
	}

	// Add optional telematics fields - using correct OpenAPI types
	if app.TelematicsInfo != nil {
		consentURL, err := sharing.FetchApplicationConsentURL(
			ctx,
			sharingWrapper,
			app.ID,
			policy_enums.ProgramTypeBusinessAuto,
		)
		if err != nil {
			log.Error(
				ctx,
				"failed to fetch quoting consent url",
				log.Stringer("ProgramType", policy_enums.ProgramTypeBusinessAuto),
				log.Stringer("ApplicationId", app.ID),
			)
			return nil, errors.Wrap(err, "failed to fetch quoting consent url")
		}

		response.TelematicsInfo = &application.TelematicsInfo{
			Name:  app.TelematicsInfo.FullName,
			Email: pointer_utils.ToPointer(openapitypes.Email(app.TelematicsInfo.Email)),
			Link:  consentURL,
		}
	}

	if app.TSPConnHandleId != nil {
		handleId := *app.TSPConnHandleId
		response.TspConnHandleId = &handleId
	}

	return response, nil
}

func convertCompanyInfoToOAPI(companyInfo model.CompanyInfo) openapi.CompanyInfo {
	companyInfoAPI := openapi.CompanyInfo{
		Name:                            companyInfo.Name,
		NoOfPowerUnits:                  companyInfo.NoOfPowerUnits,
		DOTNumber:                       companyInfo.DOTNumber,
		FEIN:                            companyInfo.FEIN,
		HasIndividualNamedInsured:       companyInfo.HasIndividualNamedInsured,
		HasWorkCompPolicy:               companyInfo.HasWorkCompPolicy,
		NoOfEmployees:                   companyInfo.NoOfEmployees,
		PerOfEmployeesOperatingOwnAutos: companyInfo.PerOfEmployeesOperatingOwnAutos,
		AnnualCostOfHire:                companyInfo.AnnualCostOfHire,
		MaximumValueOfHiredAutos:        companyInfo.MaximumValueOfHiredAutos,
		UsState:                         openapi_common.USState(companyInfo.USState.ToCode()),
	}

	// Convert address if present (mapping application.Address to nirvana Address schema)
	if companyInfo.Address != nil {
		companyInfoAPI.Address = &nirvana_openapi.Address{
			Street: companyInfo.Address.Street,
			City:   companyInfo.Address.City,
			State:  companyInfo.Address.State,
			Zip:    companyInfo.Address.Zip,
		}
	}

	// Convert primary industry classification (only if it's a valid enum value)
	if companyInfo.PrimaryIndustryClassification != nil {
		primaryClassification := openapi.PrimaryIndustryClassification(companyInfo.PrimaryIndustryClassification.String())
		companyInfoAPI.PrimaryIndustryClassification = &primaryClassification
	}

	// Convert secondary industry classifications
	if len(companyInfo.SecondaryIndustryClassifications) > 0 {
		secondaryClassifications := make([]openapi.SecondaryIndustryClassification, len(companyInfo.SecondaryIndustryClassifications))
		for i, classification := range companyInfo.SecondaryIndustryClassifications {
			secondaryClassifications[i] = openapi.SecondaryIndustryClassification(classification.String())
		}
		companyInfoAPI.SecondaryIndustryClassifications = &secondaryClassifications
	}

	return companyInfoAPI
}

func convertVehiclesInfoToOAPI(vehicles *[]model.VehicleInfo) ([]openapi.VehicleInfo, error) {
	if vehicles == nil {
		return []openapi.VehicleInfo{}, nil
	}

	vehiclesInfo := make([]openapi.VehicleInfo, 0, len(*vehicles))
	for _, v := range *vehicles {
		// Convert radius classification using the proper converter
		radiusClassification, err := convertRadiusClassificationToOAPI(v.RadiusClassification)
		if err != nil {
			// Return error instead of using default value
			return nil, errors.Wrapf(err, "failed to convert radius classification for vehicle with VIN %s", v.VIN)
		}

		vehicleInfo := openapi.VehicleInfo{
			Vin:                              v.VIN,
			Year:                             v.Year,
			Make:                             v.Make,
			Model:                            v.Model,
			VehicleType:                      openapi.VehicleType(v.VehicleType.String()),
			WeightClass:                      openapi.WeightClass(v.WeightClass.String()),
			VehicleUse:                       openapi.VehicleUse(v.VehicleUse.String()),
			BusinessUse:                      openapi.BusinessUse(v.BusinessUse.String()),
			StateUsage:                       openapi.StateUsage(v.StateUsage.String()),
			RadiusClassification:             *radiusClassification,
			PrincipalGaragingLocationZipCode: v.PrincipalGaragingLocationZipCode,
			SpecialtyVehicleType:             openapi.SpecialtyVehicleType(v.SpecialtyVehicleType.String()),
		}
		if v.TrailerType != nil {
			trailerType := openapi.TrailerType(v.TrailerType.String())
			vehicleInfo.TrailerType = &trailerType
		}
		if v.StatedValue != nil {
			vehicleInfo.StatedValue = v.StatedValue
		}
		if v.APDDeductible != nil {
			vehicleInfo.ApdDeductible = v.APDDeductible
		}
		if v.IsGlassLinedTankTruckOrTrailer != nil {
			vehicleInfo.IsGlassLinedTankTruckOrTrailer = v.IsGlassLinedTankTruckOrTrailer
		}
		if v.IsRefrigeratedTruckOrTrailer != nil {
			vehicleInfo.IsRefrigeratedTruckOrTrailer = v.IsRefrigeratedTruckOrTrailer
		}
		if v.IsDoubleTrailer != nil {
			vehicleInfo.IsDoubleTrailer = v.IsDoubleTrailer
		}
		if v.HasAntiLockBrakes != nil {
			vehicleInfo.HasAntiLockBrakes = v.HasAntiLockBrakes
		}

		vehiclesInfo = append(vehiclesInfo, vehicleInfo)
	}

	return vehiclesInfo, nil
}

func convertCoveragesInfoToOAPI(coveragesInfo *model.CoveragesInfo) (*openapi.CoveragesInfo, error) {
	if coveragesInfo == nil {
		return &openapi.CoveragesInfo{Coverages: make(map[string]openapi.Coverage)}, nil
	}

	coverages := make(map[string]openapi.Coverage)

	deductiblesMap := buildCoverageLevelDeductible(coveragesInfo.Deductibles)

	// Create a map of existing primary coverages for quick lookup
	existingPrimaryCoverages := make(map[app_enums.Coverage]model.PrimaryCoverage)
	for _, primaryCoverage := range coveragesInfo.PrimaryCoverages {
		existingPrimaryCoverages[primaryCoverage.ID] = primaryCoverage
	}

	// Loop through all supported primary coverages
	for _, supportedCoverage := range SupportedPrimaryCoverages {
		coverageTypeKey := supportedCoverage.String()
		coverage := openapi.Coverage{
			CoverageType: openapi_common.CoverageType(supportedCoverage.String()),
		}

		// Check if this coverage exists in the application
		primaryCoverage, isEnabled := existingPrimaryCoverages[supportedCoverage]
		coverage.IsEnabled = isEnabled

		if isEnabled {
			// For AL coverage, find the combined BI+PD limit and set deductible to 0
			if primaryCoverage.ID == app_enums.CoverageAutoLiability {
				// Get the combined BI+PD limit (BI is only present in the combined limit)
				combinedLimit := coveragesInfo.GetLimitForSubcoverage(app_enums.CoverageBodilyInjury)
				if combinedLimit != nil {
					coverage.Limit = pointer_utils.ToPointer(int64(*combinedLimit))
					coverage.Deductible = pointer_utils.ToPointer(int64(0)) // Always 0 for AL
				}
			} else if primaryCoverage.ID == app_enums.CoverageAutoPhysicalDamage {
				// For APD coverage, look for Collision and Comprehensive subcoverages
				// Check if there are any deductibles for Collision or Comprehensive
				var foundDeductible *int64

				// Look for deductibles in Collision or Comprehensive subcoverages
				for _, subCoverageID := range primaryCoverage.SubCoverageIDs {
					if subCoverageID == app_enums.CoverageCollision || subCoverageID == app_enums.CoverageComprehensive {
						if deductible, exists := deductiblesMap[subCoverageID]; exists && deductible != nil {
							foundDeductible = pointer_utils.ToPointer(*deductible)
							break // Use the first deductible found
						}
					}
				}

				if foundDeductible != nil {
					coverage.Deductible = foundDeductible
				}
			}
			// Note: Currently only AL and APD have limit/deductible processing
			// GL and MTC will be handled when their limit/deductible logic is implemented
		}

		coverages[coverageTypeKey] = coverage
	}
	return &openapi.CoveragesInfo{Coverages: coverages}, nil
}

// convertAncillaryCoveragesToOAPI converts internal model coverages info to OpenAPI ancillary coverages format
func convertAncillaryCoveragesToOAPI(
	ctx context.Context,
	effectiveDate time.Time,
	coveragesInfo *model.CoveragesInfo,
	state us_states.USState,
) (*[]openapi.AncillaryCoverage, error) {
	if coveragesInfo == nil {
		log.Info(ctx, "convertAncillaryCoveragesToOAPI: coveragesInfo is nil, returning empty ancillary coverages")
		return nil, nil // nolint:nilnil
	}

	// Get all available ancillary coverages for BusinessAuto program type
	availableAncillaryCoverages, err := getAvailableAncillaryCoverages(*coveragesInfo, state, effectiveDate)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get available ancillary coverages")
	}

	// Build a map of existing ancillary coverages for quick lookup
	existingAncillaryCoverages := buildExistingAncillaryCoveragesMap(*coveragesInfo)

	// Convert each available ancillary coverage
	ancillaryCoverages := make([]openapi.AncillaryCoverage, 0, len(availableAncillaryCoverages))
	for _, ancCoverage := range availableAncillaryCoverages {
		convertedCoverage := convertSingleAncillaryCoverage(state, ancCoverage, existingAncillaryCoverages, coveragesInfo)
		ancillaryCoverages = append(ancillaryCoverages, convertedCoverage)
	}

	return &ancillaryCoverages, nil
}

// getAvailableAncillaryCoverages fetches all available ancillary coverages for the given state and coverages
func getAvailableAncillaryCoverages(
	coveragesInfo model.CoveragesInfo,
	state us_states.USState,
	applicationEffectiveDate time.Time,
) (map[app_enums.Coverage]ancillary_coverages.AncillaryCoverage, error) {
	primaryCoverages := getPrimaryCoveragesFromCoveragesInfo(coveragesInfo)

	// Note: Currently there are no BusinessAuto ancillary coverages, but this will be populated later
	availableAncillaryCoverages, err := ancillary_coverages.GetAllAncillaryCoverages(
		state,
		nil,
		policy_enums.ProgramTypeBusinessAuto,
		primaryCoverages,
		applicationEffectiveDate,
		true,
		rtypes.RatemlModelVersion{},
		rtypes.ProviderNico,
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get available ancillary coverages")
	}

	return availableAncillaryCoverages, nil
}

// buildExistingAncillaryCoveragesMap creates a map of existing ancillary coverages with their limits and deductibles
func buildExistingAncillaryCoveragesMap(coveragesInfo model.CoveragesInfo) map[app_enums.Coverage]existingAncillaryCoverage {
	existingCoverages := make(map[app_enums.Coverage]existingAncillaryCoverage)

	// Build maps for quick lookup of limits and deductibles
	limitsMap := buildCoverageLevelLimitsMap(coveragesInfo.Limits)
	deductiblesMap := buildCoverageLevelDeductible(coveragesInfo.Deductibles)

	// Find all ancillary coverages in the application
	for _, primaryCoverage := range coveragesInfo.PrimaryCoverages {
		for _, subCoverageID := range primaryCoverage.SubCoverageIDs {
			// Check if this is an ancillary coverage
			if _, isAncillary := app_enums.AncCoverageToPrimaryCoverage[subCoverageID]; isAncillary {
				existingCoverages[subCoverageID] = existingAncillaryCoverage{
					isEnabled:  true,
					limit:      limitsMap[subCoverageID],
					deductible: deductiblesMap[subCoverageID],
				}
			}
		}
	}

	return existingCoverages
}

// convertSingleAncillaryCoverage converts a single ancillary coverage to OpenAPI format
func convertSingleAncillaryCoverage(
	state us_states.USState,
	ancCoverage ancillary_coverages.AncillaryCoverage,
	existingCoverages map[app_enums.Coverage]existingAncillaryCoverage,
	coveragesInfo *model.CoveragesInfo,
) openapi.AncillaryCoverage {
	// Convert limit options
	limitOptions := convertLimitOptions(ancCoverage.LimitOptions)

	// Convert deductible options
	deductibleOptions := convertDeductibleOptions(ancCoverage.DeductibleOptions)

	// This is to support cases where we have fixed deductible values
	if ancCoverage.Deductible != nil {
		deductibleOptions = &[]int64{int64(*ancCoverage.Deductible)} // Use selected deductible as the only option
	}

	// Check if this coverage exists in the application
	existing, isEnabled := existingCoverages[ancCoverage.Coverage]
	var selectedLimit *int64
	var selectedDeductible *int64
	if isEnabled {
		// Use the existing limit and deductible from the application
		selectedLimit = existing.limit
		selectedDeductible = existing.deductible
	} else {
		// Coverage is available but not enabled - use default limit from generated ancillary coverages
		if ancCoverage.DefaultLimit != nil {
			defaultLimit := int64(*ancCoverage.DefaultLimit)
			selectedLimit = &defaultLimit
		}

		// TODO: Think around a better solution here
		if ancCoverage.Deductible != nil {
			selectedDeductible = pointer_utils.ToPointer(int64(*ancCoverage.Deductible))
		}
	}

	// Extract vehicle-specific deductibles for this coverage
	vehicleDeductibles := extractVehicleDeductiblesForCoverage(ancCoverage.Coverage, coveragesInfo)

	// Extract vehicle-specific limits for this coverage
	vehicleLimits := extractVehicleLimitsForCoverage(ancCoverage.Coverage, coveragesInfo)

	// Find the primary coverage for this ancillary coverage
	primaryCoverage := app_enums.AncCoverageToPrimaryCoverage[ancCoverage.Coverage]

	return openapi.AncillaryCoverage{
		Coverage:           openapi_common.CoverageType(ancCoverage.Coverage.String()),
		PrimaryCoverage:    openapi_common.CoverageType(primaryCoverage.String()),
		IsEnabled:          isEnabled, // This will be false for available but not selected coverages
		LimitOptions:       limitOptions,
		DeductibleOptions:  deductibleOptions,
		SelectedLimit:      selectedLimit,
		SelectedDeductible: selectedDeductible,
		VehicleDeductibles: vehicleDeductibles,
		VehicleLimits:      vehicleLimits,
	}
}

// convertLimitOptions converts int32 limit options to *[]int64, returns nil if input is nil or empty
func convertLimitOptions(limitOptions []int32) *[]int64 {
	if len(limitOptions) == 0 {
		return nil
	}

	result := make([]int64, len(limitOptions))
	for i, option := range limitOptions {
		result[i] = int64(option)
	}
	return &result
}

// convertDeductibleOptions converts int32 deductible options to *[]int64, returns nil if input is nil or empty
func convertDeductibleOptions(deductibleOptions []int32) *[]int64 {
	if len(deductibleOptions) == 0 {
		return nil
	}

	result := make([]int64, len(deductibleOptions))
	for i, option := range deductibleOptions {
		result[i] = int64(option)
	}
	return &result
}

// extractVehicleDeductiblesForCoverage extracts vehicle-specific deductibles for a specific coverage
func extractVehicleDeductiblesForCoverage(
	coverage app_enums.Coverage,
	coveragesInfo *model.CoveragesInfo,
) *[]openapi.VehicleDeductible {
	if coveragesInfo == nil || len(coveragesInfo.Deductibles) == 0 {
		return nil
	}

	vehicleDeductibles := make([]openapi.VehicleDeductible, 0)

	// Look through all deductibles for vehicle-specific ones for this coverage
	for _, deductible := range coveragesInfo.Deductibles {
		// Check if this deductible is for the specified coverage and has a VIN
		if deductible.VIN != nil {
			// Check if this deductible applies to the specified coverage
			for _, subCoverageID := range deductible.SubCoverageIDs {
				if subCoverageID == coverage {
					// Found a vehicle-specific deductible for this coverage
					deductibleAmount := int64(deductible.Amount)
					vehicleDeductibles = append(vehicleDeductibles, openapi.VehicleDeductible{
						Vin:                *deductible.VIN,
						SelectedDeductible: deductibleAmount,
					})
					break // Only add once per deductible entry
				}
			}
		}
	}

	if len(vehicleDeductibles) == 0 {
		return nil
	}

	// Sort by VIN to ensure consistent ordering
	sort.Slice(vehicleDeductibles, func(i, j int) bool {
		return vehicleDeductibles[i].Vin < vehicleDeductibles[j].Vin
	})

	return &vehicleDeductibles
}

// extractVehicleLimitsForCoverage extracts vehicle-specific limits for a specific coverage
func extractVehicleLimitsForCoverage(
	coverage app_enums.Coverage,
	coveragesInfo *model.CoveragesInfo,
) *[]openapi.VehicleLimit {
	if coveragesInfo == nil || len(coveragesInfo.Limits) == 0 {
		return nil
	}

	vehicleLimits := make([]openapi.VehicleLimit, 0)

	// Look through all limits for vehicle-specific ones for this coverage
	for _, limit := range coveragesInfo.Limits {
		// Check if this limit is for the specified coverage and has a VIN
		if limit.VIN != nil {
			// Check if this limit applies to the specified coverage
			for _, subCoverageID := range limit.SubCoverageIDs {
				if subCoverageID == coverage {
					// Found a vehicle-specific limit for this coverage
					limitAmount := int64(limit.Amount)
					vehicleLimits = append(vehicleLimits, openapi.VehicleLimit{
						Vin:           *limit.VIN,
						SelectedLimit: limitAmount,
					})
					break // Only add once per limit entry
				}
			}
		}
	}

	if len(vehicleLimits) == 0 {
		return nil
	}

	// Sort by VIN to ensure consistent ordering
	sort.Slice(vehicleLimits, func(i, j int) bool {
		return vehicleLimits[i].Vin < vehicleLimits[j].Vin
	})

	return &vehicleLimits
}

// existingAncillaryCoverage represents an existing ancillary coverage in the application
type existingAncillaryCoverage struct {
	isEnabled  bool
	limit      *int64
	deductible *int64
}

// getPrimaryCoveragesFromCoveragesInfo extracts primary coverages from the coverages info
func getPrimaryCoveragesFromCoveragesInfo(coveragesInfo model.CoveragesInfo) []app_enums.Coverage {
	primaryCoverages := make([]app_enums.Coverage, 0, len(coveragesInfo.PrimaryCoverages))
	for _, primaryCoverage := range coveragesInfo.PrimaryCoverages {
		primaryCoverages = append(primaryCoverages, primaryCoverage.ID)
	}
	return primaryCoverages
}

func convertFilingsInfoToOAPI(info *model.FilingsInfo) openapi.FilingsInfo {
	if info == nil {
		// Return default (zero) values if FilingsInfo is nil
		return openapi.FilingsInfo{}
	}

	return openapi.FilingsInfo{
		HasMultiStateFilings:  info.HasMultiStateFilings,
		HasSingleStateFilings: info.HasSingleStateFilings,
		HasFMCSAFilings:       info.HasFMCSAFilings,
		HasDOTFilings:         info.HasDOTFilings,
	}
}

func getProducerInfo(
	ctx context.Context,
	authWrapper auth.DataWrapper,
	producerID uuid.UUID,
) (*openapi.Producer, error) {
	producerInfo, err := authWrapper.FetchUserInfo(ctx, producerID)
	if err != nil {
		return nil, common.NewNirvanaInternalServerErrorf(err, common.EntityProducer, producerID.String())
	}

	return &openapi.Producer{
		Id:   producerID,
		Name: producerInfo.FullName(),
	}, nil
}

func convertUnderwritingOverridesToOAPI(
	underwritingOverrides *model.UnderwritingOverrides,
	ancillaryCoverages *[]openapi.AncillaryCoverage,
) (*openapi.UnderwritingOverrides, error) {
	result := &openapi.UnderwritingOverrides{}
	if underwritingOverrides == nil {
		result.AncillaryCoverages = ancillaryCoverages
		return result, nil // nolint:nilnil
	}
	// Check CoveragesWithLossFreeCredits to set AL and APD loss free credit flags
	if underwritingOverrides.CoveragesWithLossFreeCredits != nil {
		alLossFreeCredit := false
		apdLossFreeCredit := false

		for _, coverage := range underwritingOverrides.CoveragesWithLossFreeCredits {
			switch coverage {
			case app_enums.CoverageAutoLiability:
				alLossFreeCredit = true
			case app_enums.CoverageAutoPhysicalDamage:
				apdLossFreeCredit = true
			default:
				return nil, errors.Newf("unexpected coverage with loss free credit: %s", coverage)
			}
		}

		result.AlLossFreeCredit = &alLossFreeCredit
		result.ApdLossFreeCredit = &apdLossFreeCredit
	}

	// Set loss free credit percentage if available
	if underwritingOverrides.LossFreeCreditPercentage != nil {
		result.LossFreeCreditPercentage = underwritingOverrides.LossFreeCreditPercentage
	}

	// Convert DriverFactor
	if underwritingOverrides.DriverFactor != nil {
		result.DriverFactor = &openapi.DriverFactor{
			Factor: underwritingOverrides.DriverFactor.Factor,
		}
	}

	// Convert ScheduleMods
	if underwritingOverrides.ScheduleMods != nil {
		result.ScheduleMods = convertScheduleModsToAPI(underwritingOverrides.ScheduleMods)
	}

	// Convert ExperienceMod
	if underwritingOverrides.ExperienceMod != nil {
		result.ExperienceMod = convertExperienceModsToAPI(underwritingOverrides.ExperienceMod)
	}

	// Convert QualityRating (now QualityRatingGrade)
	if underwritingOverrides.QualityRatingGrade != nil {
		grade := openapi.QualityRatingGrade(underwritingOverrides.QualityRatingGrade.String())
		result.QualityRating = &grade
	}

	// Include ancillary coverages
	if ancillaryCoverages != nil {
		result.AncillaryCoverages = ancillaryCoverages
	}

	return result, nil
}

// uwStateToOpenAPIMapping maps internal UWState enum values to OpenAPI AppState values
var uwStateToOpenAPIMapping = map[enums.UWState]openapi.AppState{
	enums.UWStateCreated:                openapi.Created,
	enums.UWStateUnderReview:            openapi.UnderReview,
	enums.UWStateQuoteGenerated:         openapi.QuoteGenerated,
	enums.UWStateQuoteGenerating:        openapi.QuoteGenerating,
	enums.UWStateApproved:               openapi.Approved,
	enums.UWStateDeclined:               openapi.Declined,
	enums.UWStateClosed:                 openapi.Closed,
	enums.UWStatePolicyCreated:          openapi.PolicyCreated,
	enums.UWStateBindableQuoteGenerated: openapi.BindableQuoteGenerated,
	enums.UWStatePanic:                  openapi.Panic,
}

func convertUWStateToOpenAPIAppState(state enums.UWState) (openapi.AppState, error) {
	if appState, exists := uwStateToOpenAPIMapping[state]; exists {
		return appState, nil
	}
	return "", errors.Newf("unknown UWState: %s", state.String())
}
