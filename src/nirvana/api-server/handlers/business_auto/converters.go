package business_auto

import (
	"time"

	"github.com/cockroachdb/errors"
	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/business-auto/coverage"
	"nirvanatech.com/nirvana/business-auto/enums"
	"nirvanatech.com/nirvana/business-auto/model"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	insurancecore_coverages "nirvanatech.com/nirvana/insurance-core/coverage"
	nirvanaapp_enums "nirvanatech.com/nirvana/nirvanaapp/enums"
	"nirvanatech.com/nirvana/nirvanaapp/models/application"
	openapi_businessauto "nirvanatech.com/nirvana/openapi-specs/components/business-auto"
	openapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
)

// MapOpenAPIAppStateToEnum converts the OpenAPI AppState representation to the
// internal enums.UWState value.
func MapOpenAPIAppStateToEnum(state openapi_businessauto.AppState) (enums.UWState, error) {
	return enums.UWStateString(string(state))
}

// MapEnumToOpenAPIAppState converts the internal enums.UWState value to the
// OpenAPI AppState representation.
func MapEnumToOpenAPIAppState(state enums.UWState) openapi_businessauto.AppState {
	return openapi_businessauto.AppState(state.String())
}

func convertCompanyInfo(companyInfo openapi_businessauto.CompanyInfo) (*model.CompanyInfo, error) {
	// Validate required fields
	if companyInfo.Name == "" {
		return nil, errors.New("company name is required")
	}

	// Convert primary industry classification (optional field - can be nil)
	var primaryIndustryClassification *enums.IndustryClassification
	if companyInfo.PrimaryIndustryClassification != nil {
		pc, err := enums.IndustryClassificationString(
			string(*companyInfo.PrimaryIndustryClassification))
		if err != nil {
			return nil, errors.Newf("invalid primary industry classification: %w", err)
		}
		primaryIndustryClassification = &pc
	}

	// Convert secondary industry classifications with nil check
	secondaryIndustryClassifications, err := convertIndustryClassifications(companyInfo.SecondaryIndustryClassifications)
	if err != nil {
		return nil, errors.Newf("failed to convert secondary industry classifications: %w", err)
	}

	usState, err := us_states.StrToUSState(string(companyInfo.UsState))
	if err != nil {
		return nil, errors.Newf("invalid usState: %w", err)
	}

	// Convert address if provided (mapping nirvana Address schema to application.Address)
	var address *application.Address
	if companyInfo.Address != nil {
		address = &application.Address{
			Street: companyInfo.Address.Street,
			City:   companyInfo.Address.City,
			State:  companyInfo.Address.State,
			Zip:    companyInfo.Address.Zip,
		}
	}

	return &model.CompanyInfo{
		Name:                             companyInfo.Name,
		DOTNumber:                        companyInfo.DOTNumber,
		FEIN:                             companyInfo.FEIN,
		NoOfPowerUnits:                   companyInfo.NoOfPowerUnits,
		HasIndividualNamedInsured:        companyInfo.HasIndividualNamedInsured,
		HasWorkCompPolicy:                companyInfo.HasWorkCompPolicy,
		NoOfEmployees:                    companyInfo.NoOfEmployees,
		PerOfEmployeesOperatingOwnAutos:  companyInfo.PerOfEmployeesOperatingOwnAutos,
		PrimaryIndustryClassification:    primaryIndustryClassification,
		SecondaryIndustryClassifications: secondaryIndustryClassifications,
		AnnualCostOfHire:                 companyInfo.AnnualCostOfHire,
		MaximumValueOfHiredAutos:         companyInfo.MaximumValueOfHiredAutos,
		USState:                          usState,
		Address:                          address,
	}, nil
}

func convertIndustryClassifications(
	classifications *[]openapi_businessauto.SecondaryIndustryClassification,
) ([]enums.IndustryClassification, error) {
	if classifications == nil {
		return nil, nil
	}
	secondaryIndustryClassifications := make([]enums.IndustryClassification, len(*classifications))
	for i, c := range *classifications {
		industryClassification, err := enums.IndustryClassificationString(string(c))
		if err != nil {
			return nil, err
		}

		secondaryIndustryClassifications[i] = industryClassification
	}
	return secondaryIndustryClassifications, nil
}

func convertVehiclesInfo(vehicles *[]openapi_businessauto.VehicleInfo, coveragesInfo *openapi_businessauto.CoveragesInfo) (*[]model.VehicleInfo, error) {
	if vehicles == nil {
		return nil, nil //nolint:nilnil
	}

	// Track VINs to detect duplicates
	seenVINs := make(map[string]bool)
	result := make([]model.VehicleInfo, len(*vehicles))

	for i, v := range *vehicles {
		// Check for duplicate VIN
		if v.Vin != "" {
			if seenVINs[v.Vin] {
				return nil, errors.Newf("duplicate VIN found: %s", v.Vin)
			}
			seenVINs[v.Vin] = true
		}

		weightClass, err := enums.WeightClassString(string(v.WeightClass))
		if err != nil {
			return nil, err
		}

		vehicleUse, err := enums.VehicleUseString(string(v.VehicleUse))
		if err != nil {
			return nil, err
		}

		businessUse, err := enums.BusinessUseString(string(v.BusinessUse))
		if err != nil {
			return nil, err
		}

		stateUsage, err := enums.StateUsageString(string(v.StateUsage))
		if err != nil {
			return nil, err
		}

		radiusClassification, err := convertRadiusClassification(v.RadiusClassification)
		if err != nil {
			return nil, err
		}

		vehicleType, err := nirvanaapp_enums.VehicleTypeString(string(v.VehicleType))
		if err != nil {
			return nil, err
		}

		specialtyVehicleType, err := enums.SpecialtyVehicleTypeString(string(v.SpecialtyVehicleType))
		if err != nil {
			return nil, err
		}
		var trailerType *enums.TrailerType
		if v.TrailerType != nil {
			tt, err := enums.TrailerTypeString(string(*v.TrailerType))
			if err != nil {
				return nil, err
			}
			trailerType = &tt
		}

		result[i] = model.VehicleInfo{
			VIN:                              v.Vin,
			Year:                             v.Year,
			Make:                             v.Make,
			Model:                            v.Model,
			VehicleType:                      vehicleType,
			WeightClass:                      weightClass,
			SpecialtyVehicleType:             specialtyVehicleType,
			VehicleUse:                       vehicleUse,
			BusinessUse:                      businessUse,
			TrailerType:                      trailerType,
			StateUsage:                       stateUsage,
			RadiusClassification:             radiusClassification,
			PrincipalGaragingLocationZipCode: v.PrincipalGaragingLocationZipCode,
			APDDeductible:                    v.ApdDeductible,
			StatedValue:                      v.StatedValue,
			IsGlassLinedTankTruckOrTrailer:   v.IsGlassLinedTankTruckOrTrailer,
			IsRefrigeratedTruckOrTrailer:     v.IsRefrigeratedTruckOrTrailer,
			IsDoubleTrailer:                  v.IsDoubleTrailer,
			HasAntiLockBrakes:                v.HasAntiLockBrakes,
		}
	}

	// Validate APD deductibles at vehicle level if APD coverage is selected
	if err := validateAPDDeductibles(coveragesInfo, &result); err != nil {
		return nil, err
	}

	return &result, nil
}

func convertFilingsInfo(filingsInfo *openapi_businessauto.FilingsInfo) *model.FilingsInfo {
	if filingsInfo == nil {
		return nil
	}
	return &model.FilingsInfo{
		HasMultiStateFilings:  filingsInfo.HasMultiStateFilings,
		HasSingleStateFilings: filingsInfo.HasSingleStateFilings,
		HasFMCSAFilings:       filingsInfo.HasFMCSAFilings,
		HasDOTFilings:         filingsInfo.HasDOTFilings,
	}
}

// convertScheduleModsFromAPI converts OpenAPI array-based ScheduleMod to internal map structure
func convertScheduleModsFromAPI(apiMod *openapi_businessauto.ScheduleMod) (*model.ScheduleMod, error) {
	if apiMod == nil || apiMod.Mods == nil {
		// Return an empty ScheduleMod instead of nil to avoid nilnil lint issue.
		return &model.ScheduleMod{Mods: map[app_enums.Coverage]*float64{}}, nil
	}

	result := make(map[app_enums.Coverage]*float64)
	for _, mod := range *apiMod.Mods {
		// Convert string coverage to enum
		coverage, err := app_enums.CoverageString(string(mod.Coverage))
		if err != nil {
			return nil, err
		}
		modifierVal := mod.Modifier
		result[coverage] = &modifierVal
	}

	// Return empty struct even if no result to avoid nilnil.
	return &model.ScheduleMod{Mods: result}, nil
}

// convertScheduleModsToAPI converts internal map structure to OpenAPI array-based ScheduleMod
func convertScheduleModsToAPI(internalMods *model.ScheduleMod) *openapi_businessauto.ScheduleMod {
	if internalMods == nil || internalMods.Mods == nil {
		return nil
	}

	var mods []openapi_businessauto.CoverageModification
	for coverage, modifier := range internalMods.Mods {
		if modifier != nil {
			mods = append(mods, openapi_businessauto.CoverageModification{
				Coverage: openapi_common.CoverageType(coverage.String()),
				Modifier: *modifier,
			})
		}
	}

	if len(mods) == 0 {
		return nil
	}

	return &openapi_businessauto.ScheduleMod{
		Mods: &mods,
	}
}

// convertExperienceModsFromAPI converts OpenAPI array-based ExperienceMod to internal map structure
func convertExperienceModsFromAPI(apiMod *openapi_businessauto.ExperienceMod) (*model.ExperienceMod, error) {
	if apiMod == nil || apiMod.Mods == nil {
		return &model.ExperienceMod{Mods: map[app_enums.Coverage]*float64{}}, nil
	}

	result := make(map[app_enums.Coverage]*float64)
	for _, mod := range *apiMod.Mods {
		// Convert string coverage to enum
		coverage, err := app_enums.CoverageString(string(mod.Coverage))
		if err != nil {
			return nil, err
		}
		modifierVal := mod.Modifier
		result[coverage] = &modifierVal
	}

	return &model.ExperienceMod{Mods: result}, nil
}

// convertExperienceModsToAPI converts internal map structure to OpenAPI array-based ExperienceMod
func convertExperienceModsToAPI(internalMods *model.ExperienceMod) *openapi_businessauto.ExperienceMod {
	if internalMods == nil || internalMods.Mods == nil {
		return nil
	}

	var mods []openapi_businessauto.CoverageModification
	for coverage, modifier := range internalMods.Mods {
		if modifier != nil {
			mods = append(mods, openapi_businessauto.CoverageModification{
				Coverage: openapi_common.CoverageType(coverage.String()),
				Modifier: *modifier,
			})
		}
	}

	if len(mods) == 0 {
		return nil
	}

	return &openapi_businessauto.ExperienceMod{
		Mods: &mods,
	}
}

func convertRadiusClassificationToOAPI(rc enums.RadiusClassification) (*openapi_businessauto.RadiusClassification, error) {
	switch rc {
	case enums.RadiusClassification0To100:
		return pointer_utils.ToPointer(openapi_businessauto.RadiusClassificationN0To100), nil
	case enums.RadiusClassification101To300:
		return pointer_utils.ToPointer(openapi_businessauto.RadiusClassificationN101To300), nil
	case enums.RadiusClassificationGreaterThan301:
		return pointer_utils.ToPointer(openapi_businessauto.RadiusClassificationGreaterThan301), nil
	default:
		return nil, errors.Newf("unknown radius class %v", rc)
	}
}

func convertRadiusClassification(rc openapi_businessauto.RadiusClassification) (enums.RadiusClassification, error) {
	switch rc {
	case openapi_businessauto.RadiusClassificationN0To100:
		return enums.RadiusClassification0To100, nil
	case openapi_businessauto.RadiusClassificationN101To300:
		return enums.RadiusClassification101To300, nil
	case openapi_businessauto.RadiusClassificationGreaterThan301:
		return enums.RadiusClassificationGreaterThan301, nil
	default:
		return 0, errors.Newf("unknown radius classification: %v", rc)
	}
}

// convertCoveragesInfoFromOAPI converts OpenAPI coverages info to internal model
// It extracts primary coverages, limits, and deductibles from coverageInfo
// and builds vehicle-specific deductibles from vehiclesInfo
// It also includes mandatory ancillary coverages that belong to the primary coverages
func convertCoveragesInfoFromOAPI(
	effectiveDate time.Time,
	usState us_states.USState,
	primaryCoveragesInfo *openapi_businessauto.CoveragesInfo,
	vehiclesInfo *[]model.VehicleInfo,
) (*model.CoveragesInfo, error) {
	if primaryCoveragesInfo == nil || len(primaryCoveragesInfo.Coverages) == 0 {
		// nolint:nilnil
		return nil, nil
	}

	selectedCoverages := make([]app_enums.Coverage, 0, len(primaryCoveragesInfo.Coverages))
	limits := make(map[app_enums.Coverage]float64)
	deductibles := make(map[app_enums.Coverage]float64)

	// Extract primary coverages, limits, and deductibles from coverageInfo
	for coverageTypeKey, cov := range primaryCoveragesInfo.Coverages {
		// Convert coverage type key string to enum
		if !cov.IsEnabled {
			continue // Skip disabled coverages
		}
		coverageType, err := app_enums.CoverageString(coverageTypeKey)
		if err != nil {
			return nil, errors.Wrapf(err, "invalid coverage type: %s", coverageTypeKey)
		}

		// Add to selected coverages
		selectedCoverages = append(selectedCoverages, coverageType)

		// Extract limit and deductible from the coverage object
		if cov.Limit != nil {
			limits[coverageType] = float64(*cov.Limit)
		}
		if cov.Deductible != nil {
			deductibles[coverageType] = float64(*cov.Deductible)
		}
	}

	// Build vehicle-specific deductibles
	vehicleDeductibles, err := buildVehicleDeductibles(selectedCoverages, vehiclesInfo, nil)
	if err != nil {
		return nil, err
	}

	// Build the model.CoveragesInfo
	return coverage.BuildCoverageInfo(effectiveDate, usState, selectedCoverages, limits, deductibles, nil, nil, vehicleDeductibles)
}

// validateAPDDeductibles validates APD deductibles at vehicle level if APD is selected
func validateAPDDeductibles(
	coveragesInfo *openapi_businessauto.CoveragesInfo,
	vehiclesInfo *[]model.VehicleInfo,
) error {
	// Check if APD coverage is selected
	hasAPD := false
	if coveragesInfo != nil {
		for _, cov := range coveragesInfo.Coverages {
			if cov.IsEnabled {
				coverageType, err := app_enums.CoverageString(string(cov.CoverageType))
				if err == nil && coverageType == app_enums.CoverageAutoPhysicalDamage {
					hasAPD = true
					break
				}
			}
		}
	}

	if !hasAPD {
		return nil // No validation needed if APD is not selected
	}

	// If APD is selected, validate that each vehicle has an APD deductible
	if vehiclesInfo == nil || len(*vehiclesInfo) == 0 {
		return errors.New("vehicles info is required when APD coverage is selected")
	}

	return nil
}

// buildVehicleDeductibles builds vehicle-specific deductibles with a unified approach:
// - For Collision/Comprehensive: uses APD deductibles from vehiclesInfo if APD coverage is present (no overrides)
// - For all other coverages: uses deductibles from underwriting overrides
func buildVehicleDeductibles(
	primaryCoverages []app_enums.Coverage,
	vehiclesInfo *[]model.VehicleInfo,
	underwritingOverrides *openapi_businessauto.UnderwritingOverrides,
) (map[app_enums.Coverage]map[string]float64, error) {
	vehicleDeductibles := make(map[app_enums.Coverage]map[string]float64)

	// Step 1: Build APD vehicle-specific deductibles for Collision/Comprehensive if APD is selected
	if vehiclesInfo != nil {
		hasAPD := false
		for _, cov := range primaryCoverages {
			if cov == app_enums.CoverageAutoPhysicalDamage {
				hasAPD = true
				break
			}
		}

		apdSubCoverages := insurancecore_coverages.GetSubCoverageFromPrimaryCoverage(app_enums.CoverageAutoPhysicalDamage)

		if hasAPD {
			for _, vehicle := range *vehiclesInfo {
				// TODO: Remove this check when we add APD at vehicle level
				if vehicle.StatedValue == nil || vehicle.APDDeductible == nil {
					continue
				}

				deductibleAmount := float64(*vehicle.APDDeductible)
				for _, subCoverage := range apdSubCoverages {
					// Initialize map for this coverage if needed
					if vehicleDeductibles[subCoverage] == nil {
						vehicleDeductibles[subCoverage] = make(map[string]float64)
					}

					// Add vehicle-specific deductible
					vehicleDeductibles[subCoverage][vehicle.VIN] = deductibleAmount
				}
			}
		}
	}

	// Step 2: Add vehicle deductibles from underwriting overrides (excluding COLL/COMP)
	if underwritingOverrides != nil && underwritingOverrides.AncillaryCoverages != nil {
		for _, ancCov := range *underwritingOverrides.AncillaryCoverages {
			if !ancCov.IsEnabled || ancCov.VehicleDeductibles == nil {
				continue
			}

			// Convert coverage type
			coverageType, err := app_enums.CoverageString(string(ancCov.Coverage))
			if err != nil {
				return nil, common.NewNirvanaBadRequestErrorWithReason(err, errors.Newf("Invalid coverage type: %s", ancCov.Coverage).Error())
			}

			// Extract vehicle deductibles for this coverage
			for _, vehicleDeductible := range *ancCov.VehicleDeductibles {
				deductibleAmount := float64(vehicleDeductible.SelectedDeductible)

				// Initialize map for this coverage if needed
				if vehicleDeductibles[coverageType] == nil {
					vehicleDeductibles[coverageType] = make(map[string]float64)
				}

				// Add vehicle-specific deductible
				vehicleDeductibles[coverageType][vehicleDeductible.Vin] = deductibleAmount
			}
		}
	}

	return vehicleDeductibles, nil
}

// buildVehicleLimits builds vehicle-specific limits from underwriting overrides
// It extracts vehicle limits for coverages that have VehicleLimits specified
func buildVehicleLimits(
	underwritingOverrides *openapi_businessauto.UnderwritingOverrides,
) (map[app_enums.Coverage]map[string]float64, error) {
	vehicleLimits := make(map[app_enums.Coverage]map[string]float64)

	// Extract vehicle limits from underwriting overrides
	if underwritingOverrides != nil && underwritingOverrides.AncillaryCoverages != nil {
		for _, ancCov := range *underwritingOverrides.AncillaryCoverages {
			if !ancCov.IsEnabled || ancCov.VehicleLimits == nil {
				continue
			}

			// Convert coverage type
			coverageType, err := app_enums.CoverageString(string(ancCov.Coverage))
			if err != nil {
				return nil, common.NewNirvanaBadRequestErrorWithReason(err, errors.Newf("Invalid coverage type: %s", ancCov.Coverage).Error())
			}

			// Extract vehicle limits for this coverage
			for _, vehicleLimit := range *ancCov.VehicleLimits {
				limitAmount := float64(vehicleLimit.SelectedLimit)

				// Initialize map for this coverage if needed
				if vehicleLimits[coverageType] == nil {
					vehicleLimits[coverageType] = make(map[string]float64)
				}

				// Add vehicle-specific limit
				vehicleLimits[coverageType][vehicleLimit.Vin] = limitAmount
			}
		}
	}

	return vehicleLimits, nil
}
