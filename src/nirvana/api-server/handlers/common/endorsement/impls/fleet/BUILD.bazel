load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "fleet",
    srcs = ["policy_processor.go"],
    importpath = "nirvanatech.com/nirvana/api-server/handlers/common/endorsement/impls/fleet",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/insurance-bundle/model",
        "//nirvana/policy/constants",
        "@com_github_cockroachdb_errors//:errors",
    ],
)
