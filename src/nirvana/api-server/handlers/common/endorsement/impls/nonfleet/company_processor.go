package nonfleet

import (
	"strings"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/api-server/handlers/application/endorsementapp/converters"
	commonproto "nirvanatech.com/nirvana/common-go/proto"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	"nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp"
	endorsementrequest "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-request"
	"nirvanatech.com/nirvana/insurance-bundle/model"
	"nirvanatech.com/nirvana/insurance-bundle/model/endorsement"
	inscoremodel "nirvanatech.com/nirvana/insurance-core/proto"
	nfTypes "nirvanatech.com/nirvana/nonfleet/model/endorsement"
	endorsementappoapi "nirvanatech.com/nirvana/openapi-specs/components/endorsementapp/intake"
	"nirvanatech.com/nirvana/openapi-specs/components/nirvana"
)

type CompanyProcessor struct{}

func (c CompanyProcessor) TransformTerminalLocationChangeToEndorsementChange(
	changedAddress *endorsementappoapi.AddressChange,
	policyNumbers []string,
	effectiveInterval *commonproto.Interval,
) *endorsementapp.Change {
	if changedAddress.ChangeType == endorsementappoapi.AddressChangeChangeTypeUnchanged {
		return nil
	}

	terminalAddressChange := &nfTypes.NonFleetChange_TerminalAddressChange{
		TerminalAddressChange: &nfTypes.TerminalAddressChange{
			NewAddress: converters.NirvanaAddressToProto(changedAddress.Address),
		},
	}
	changeToRegister := endorsementapp.Change{
		Id:            uuid.New().String(),
		PolicyNumbers: policyNumbers,
		Data: &endorsement.ChangeData{
			Data: &endorsement.ChangeData_NonFleetChange{
				NonFleetChange: &nfTypes.NonFleetChange{
					ChangeType: nfTypes.NonFleetChangeType_NonFleetChangeType_TerminalAddress,
					Data:       terminalAddressChange,
				},
			},
		},
		EffectiveInterval: effectiveInterval,
		IsActive:          true,
	}

	changeToRegister.Description = strings.Join(terminalAddressChange.Describe(), "; ")

	return &changeToRegister
}

func (c CompanyProcessor) TransformMailingAddressChangeToEndorsementChange(
	changedAddress *endorsementappoapi.AddressChange,
	policyNumbers []string,
	effectiveInterval *commonproto.Interval,
) *endorsementapp.Change {
	if changedAddress.ChangeType == endorsementappoapi.AddressChangeChangeTypeUnchanged {
		return nil
	}

	mailingAddressChange := &nfTypes.NonFleetChange_MailingAddressChange{
		MailingAddressChange: &nfTypes.MailingAddressChange{
			NewAddress: converters.NirvanaAddressToProto(changedAddress.Address),
		},
	}

	changeToRegister := endorsementapp.Change{
		Id:            uuid.New().String(),
		PolicyNumbers: policyNumbers,
		Data: &endorsement.ChangeData{
			Data: &endorsement.ChangeData_NonFleetChange{
				NonFleetChange: &nfTypes.NonFleetChange{
					ChangeType: nfTypes.NonFleetChangeType_NonFleetChangeType_MailingAddress,
					Data:       mailingAddressChange,
				},
			},
		},
		EffectiveInterval: effectiveInterval,
		IsActive:          true,
	}

	changeToRegister.Description = strings.Join(mailingAddressChange.Describe(), "; ")

	return &changeToRegister
}

// createInsuredRecord creates a new endorsement change record for an additional insured
func (c CompanyProcessor) createInsuredRecord(
	nameToIdentifier map[string]string,
	additionalInsureds []*endorsementappoapi.AdditionalInsuredChange,
	policyNumbers []string,
	effectiveInterval *commonproto.Interval,
) *endorsementapp.Change {
	var insuredsToAdd []*inscoremodel.Insured
	for _, ai := range additionalInsureds {
		insuredsToAdd = append(insuredsToAdd, &inscoremodel.Insured{
			Id:      nameToIdentifier[ai.AdditionalInsured.Name],
			Type:    inscoremodel.InsuredType_InsuredType_AdditionalInsured,
			Name:    &inscoremodel.InsuredName{BusinessName: ai.AdditionalInsured.Name},
			Address: converters.NirvanaAddressToProto(ai.AdditionalInsured.Address),
		})
	}

	insuredChange := &endorsement.CoreChange_InsuredChange{
		InsuredChange: &endorsement.InsuredChange{
			InsuredType: inscoremodel.InsuredType_InsuredType_AdditionalInsured,
			Add:         insuredsToAdd,
		},
	}

	record := &endorsementapp.Change{
		Id:                uuid.New().String(),
		PolicyNumbers:     policyNumbers,
		EffectiveInterval: effectiveInterval,
		IsActive:          true,
		Data: &endorsement.ChangeData{
			Data: &endorsement.ChangeData_CoreChange{
				CoreChange: &endorsement.CoreChange{
					CoreChangeType: endorsement.CoreChangeType_CoreChangeType_Insured,
					Data:           insuredChange,
				},
			},
		},
	}
	record.Description = strings.Join(insuredChange.Describe(), ", ")

	return record
}

// createOrUpdateClauseRecord handles the creation or updating of Additional Insured (AI) and
// Waiver of Subrogation (WoS) clauses. It processes a list of insureds,
// managing both new clause creation and updates to existing clauses based on the current policy state.
func (c CompanyProcessor) createOrUpdateClauseRecord(
	nameToIdentifier map[string]string,
	insureds []*endorsementappoapi.AdditionalInsuredChange,
	originalClauses *inscoremodel.ClauseList,
	policyNumbers []string,
	effectiveInterval *commonproto.Interval,
) *endorsementapp.Change {
	// Try to update existing clauses first
	if clausesToUpdate := c.updateExistingClauses(
		nameToIdentifier,
		insureds,
		originalClauses,
	); len(clausesToUpdate) > 0 {
		return c.createChangeRecord(clausesToUpdate, nil, policyNumbers, effectiveInterval)
	}

	// If no existing clauses were updated, create new ones
	clausesToAdd := c.createNewClauses(nameToIdentifier, insureds)
	return c.createChangeRecord(nil, clausesToAdd, policyNumbers, effectiveInterval)
}

// updateExistingClauses updates existing Additional Insured (AI) and Waiver of Subrogation (WoS) clauses
// with new participants based on the provided insureds list.
//
// Parameters:
//   - nameToIdentifier: A map of insured names to their corresponding identifiers
//   - insureds: A slice of AdditionalInsuredChange containing information about insureds to be added
//   - originalClauses: The existing ClauseList containing current AI and WoS clauses
//
// Returns:
//   - A slice of updated Clause objects that need to be modified in the system
//
// The function handles two types of insureds:
//  1. Blanket Additional Insureds (updating ClauseTypeAdditionalInsured and ClauseTypeWaiverOfSubrogation)
//  2. Specified Additional Insureds (updating ClauseTypeSpecifiedAdditionalInsured and ClauseTypeSpecifiedWaiverOfSubrogation)
//
// For each type, it:
//   - Finds existing AI and WoS clauses
//   - Adds new participants to these clauses
//   - Returns only the clauses that were modified
func (c CompanyProcessor) updateExistingClauses(
	nameToIdentifier map[string]string,
	insureds []*endorsementappoapi.AdditionalInsuredChange,
	originalClauses *inscoremodel.ClauseList,
) []*inscoremodel.Clause {
	var clausesToUpdate []*inscoremodel.Clause

	// Group insureds by type
	insuredsByType := make(map[nirvana.AdditionalInsuredType][]*endorsementappoapi.AdditionalInsuredChange)
	for _, insured := range insureds {
		insuredsByType[insured.AdditionalInsured.InsuredType] = append(
			insuredsByType[insured.AdditionalInsured.InsuredType],
			insured,
		)
	}

	// Process each insured type
	for insuredType, typeInsureds := range insuredsByType {
		if len(typeInsureds) == 0 {
			continue
		}

		clauseTypes, exists := clauseTypeMap[insuredType]
		if !exists {
			continue
		}

		existingWoSClause := originalClauses.GetClause(clauseTypes.wosType.String())

		var existingAIClauses []*inscoremodel.Clause
		for _, aiType := range clauseTypes.aiTypes {
			if clause := originalClauses.GetClause(aiType.String()); clause != nil {
				existingAIClauses = append(existingAIClauses, clause)
			}
		}

		if len(existingAIClauses) == 0 && existingWoSClause == nil {
			continue
		}

		// Track if any participants were added to WOS clause
		wosParticipantsAdded := false

		// Update clauses with new participants
		for _, insured := range typeInsureds {
			participant := &inscoremodel.Participant{
				Type: inscoremodel.ParticipantType_ParticipantTypeInsured,
				Id:   nameToIdentifier[insured.AdditionalInsured.Name],
			}

			// Update all AI clauses
			for _, aiClause := range existingAIClauses {
				aiClause.ParticipantScope.ApplicableParticipants = append(
					aiClause.ParticipantScope.ApplicableParticipants,
					participant,
				)
			}

			if existingWoSClause != nil && insured.HasWoS {
				existingWoSClause.ParticipantScope.ApplicableParticipants = append(
					existingWoSClause.ParticipantScope.ApplicableParticipants,
					participant,
				)
				wosParticipantsAdded = true
			}
		}

		// Collect updated clauses
		clausesToUpdate = append(clausesToUpdate, existingAIClauses...)
		// Only include WOS clause if participants were actually added to it
		if existingWoSClause != nil && wosParticipantsAdded {
			clausesToUpdate = append(clausesToUpdate, existingWoSClause)
		}
	}

	return clausesToUpdate
}

// createNewClauses creates new Additional Insured (AI) and Waiver of Subrogation (WoS) clauses
// for the provided insureds when no existing clauses are present.
//
// Parameters:
//   - nameToIdentifier: A map of insured names to their corresponding identifiers
//   - insureds: A slice of AdditionalInsuredChange containing information about insureds to be added
//
// Returns:
//   - A slice of new Clause objects that need to be added to the system
//
// The function creates clauses for two types of insureds:
//  1. Blanket Additional Insureds (creating ClauseTypeAdditionalInsured and ClauseTypeWaiverOfSubrogation)
//  2. Specified Additional Insureds (creating ClauseTypeSpecifiedAdditionalInsured and ClauseTypeSpecifiedWaiverOfSubrogation)
//
// For each type, it:
//   - Creates an AI clause with all participants
//   - Creates a WoS clause (if any insureds have HasWoS=true)
//   - Sets appropriate clause types and participant scope
//
// Each clause is created with:
//   - A unique clause ID based on its type
//   - Specific participant scope with applicable participants
//   - Appropriate clause type based on the insured type
func (c CompanyProcessor) createNewClauses(
	nameToIdentifier map[string]string,
	insureds []*endorsementappoapi.AdditionalInsuredChange,
) []*inscoremodel.Clause {
	// Group insureds by type
	insuredsByType := make(map[nirvana.AdditionalInsuredType][]*endorsementappoapi.AdditionalInsuredChange)
	for _, insured := range insureds {
		insuredsByType[insured.AdditionalInsured.InsuredType] = append(
			insuredsByType[insured.AdditionalInsured.InsuredType],
			insured,
		)
	}

	var clausesToAdd []*inscoremodel.Clause

	// Process each insured type
	for insuredType, typeInsureds := range insuredsByType {
		if len(typeInsureds) == 0 {
			continue
		}

		clauseTypes, exists := clauseTypeMap[insuredType]
		if !exists {
			continue
		}

		var aiParticipants, wosParticipants []*inscoremodel.Participant

		// Create participants for this insured type
		for _, insured := range typeInsureds {
			participant := &inscoremodel.Participant{
				Type: inscoremodel.ParticipantType_ParticipantTypeInsured,
				Id:   nameToIdentifier[insured.AdditionalInsured.Name],
			}

			aiParticipants = append(aiParticipants, participant)

			if insured.HasWoS {
				wosParticipants = append(wosParticipants, participant)
			}
		}

		// Add all applicable AI clauses
		for _, aiType := range clauseTypes.aiTypes {
			clausesToAdd = append(clausesToAdd, &inscoremodel.Clause{
				Id:   &inscoremodel.ClauseId{Id: aiType.String()},
				Type: aiType,
				ParticipantScope: &inscoremodel.ParticipantScope{
					Type:                   inscoremodel.ParticipantScopeApplicabilityType_ParticipantScopeApplicabilityTypeSpecific,
					ApplicableParticipants: aiParticipants,
				},
			})
		}

		// Add WoS clause if needed
		if len(wosParticipants) > 0 {
			clausesToAdd = append(clausesToAdd, &inscoremodel.Clause{
				Id:   &inscoremodel.ClauseId{Id: clauseTypes.wosType.String()},
				Type: clauseTypes.wosType,
				ParticipantScope: &inscoremodel.ParticipantScope{
					Type:                   inscoremodel.ParticipantScopeApplicabilityType_ParticipantScopeApplicabilityTypeSpecific,
					ApplicableParticipants: wosParticipants,
				},
			})
		}
	}

	return clausesToAdd
}

// Helper function to create a change record for clause updates or additions
func (c CompanyProcessor) createChangeRecord(
	clausesToUpdate, clausesToAdd []*inscoremodel.Clause,
	policyNumbers []string,
	effectiveInterval *commonproto.Interval,
) *endorsementapp.Change {
	clauseChange := &endorsement.CoreChange_ClauseChange{
		ClauseChange: &endorsement.ClauseChange{
			Update: clausesToUpdate,
			Add:    clausesToAdd,
		},
	}

	record := &endorsementapp.Change{
		Id:                uuid.New().String(),
		PolicyNumbers:     policyNumbers,
		EffectiveInterval: effectiveInterval,
		IsActive:          true,
		Data: &endorsement.ChangeData{
			Data: &endorsement.ChangeData_CoreChange{
				CoreChange: &endorsement.CoreChange{
					CoreChangeType: endorsement.CoreChangeType_CoreChangeType_Clause,
					Data:           clauseChange,
				},
			},
		},
	}
	record.Description = strings.Join(clauseChange.Describe(), ", ")

	return record
}

// TransformAdditionalInsuredChangesToEndorsementChanges transforms additional insured changes into endorsement changes
func (c CompanyProcessor) TransformAdditionalInsuredChangesToEndorsementChanges(
	changedInsureds []*endorsementappoapi.AdditionalInsuredChange,
	policyNumbers []string,
	effectiveInterval *commonproto.Interval,
	originalClauses *inscoremodel.ClauseList,
) []*endorsementapp.Change {
	var changesToRegister []*endorsementapp.Change

	// We only care about added insureds for now
	changedInsureds = slice_utils.Filter(changedInsureds, func(ins *endorsementappoapi.AdditionalInsuredChange) bool {
		return ins.ChangeType == endorsementappoapi.AdditionalInsuredChangeChangeTypeAdded
	})

	// Map insured names to their unique identifiers
	nameToIdentifier := make(map[string]string)
	for _, insured := range changedInsureds {
		nameToIdentifier[insured.AdditionalInsured.Name] = uuid.New().String()
	}
	// Create insured and clause records
	insuredRecord := c.createInsuredRecord(nameToIdentifier, changedInsureds, policyNumbers, effectiveInterval)

	clauseRecord := c.createOrUpdateClauseRecord(
		nameToIdentifier,
		changedInsureds,
		originalClauses,
		policyNumbers,
		effectiveInterval,
	)

	changesToRegister = append(changesToRegister, clauseRecord, insuredRecord)

	return changesToRegister
}

func (c CompanyProcessor) FilterTerminalLocationChangeFromEndorsementRequest(
	endReqObj *endorsementrequest.Request,
) (*endorsementapp.Change, error) {
	var terminalAddressChanges []*endorsementapp.Change
	for i := range endReqObj.Changes {
		if endReqObj.Changes[i].Data.GetNonFleetChange().GetTerminalAddressChange() != nil {
			terminalAddressChanges = append(terminalAddressChanges, &endReqObj.Changes[i])
		}
	}

	if len(terminalAddressChanges) == 0 {
		return nil, nil //nolint:nilnil
	}

	if len(terminalAddressChanges) > 1 {
		return nil, errors.New("multiple terminal address changes found in endorsement request")
	}
	return terminalAddressChanges[0], nil
}

func (c CompanyProcessor) FilterMailingAddressChangeFromEndorsementRequest(
	endReqObj *endorsementrequest.Request,
) (*endorsementapp.Change, error) {
	var mailingAddressChanges []*endorsementapp.Change
	for i := range endReqObj.Changes {
		if endReqObj.Changes[i].Data.GetNonFleetChange().GetMailingAddressChange() != nil {
			mailingAddressChanges = append(mailingAddressChanges, &endReqObj.Changes[i])
		}
	}

	if len(mailingAddressChanges) == 0 {
		return nil, nil //nolint:nilnil
	}

	if len(mailingAddressChanges) > 1 {
		return nil, errors.New("multiple mailing address changes found in endorsement request")
	}

	return mailingAddressChanges[0], nil
}

func (c CompanyProcessor) FilterAdditionalInsuredChangeFromEndorsementRequest(
	endReqObj *endorsementrequest.Request,
) ([]*endorsementapp.Change, error) {
	var additionalInsuredChanges []*endorsementapp.Change
	for i := range endReqObj.Changes {
		if endReqObj.Changes[i].Data.GetCoreChange().GetInsuredChange() != nil ||
			endReqObj.Changes[i].Data.GetCoreChange().GetClauseChange() != nil {
			additionalInsuredChanges = append(additionalInsuredChanges, &endReqObj.Changes[i])
		}
	}

	return additionalInsuredChanges, nil
}

func (c CompanyProcessor) ExtractInitialTerminalLocationFromPolicy(policy *model.Policy) endorsementappoapi.AddressChange {
	nfData := policy.GetProgramData().GetNonFleetData()
	terminalAddress := converters.ProtoToNirvanaAddress(nfData.GetCompanyInfo().GetTerminalAddress())

	return endorsementappoapi.AddressChange{
		Address:    terminalAddress,
		ChangeType: endorsementappoapi.AddressChangeChangeTypeUnchanged,
	}
}

func (c CompanyProcessor) ExtractInitialMailingAddressFromPolicy(policy *model.Policy) endorsementappoapi.AddressChange {
	nfData := policy.GetProgramData().GetNonFleetData()
	mailingAddress := converters.ProtoToNirvanaAddress(nfData.GetCompanyInfo().GetMailingAddress())

	return endorsementappoapi.AddressChange{
		Address:    mailingAddress,
		ChangeType: endorsementappoapi.AddressChangeChangeTypeUnchanged,
	}
}

func (c CompanyProcessor) ExtractInitialAdditionalInsuredsFromPolicy(policy *model.Policy) []endorsementappoapi.AdditionalInsuredChange {
	var additionalInsureds []endorsementappoapi.AdditionalInsuredChange
	_, hasBlanketWaiverOfSubrogation := policy.Clauses.GetBlanketClauseStatus()

	// Get all insureds from the Auto Liability policy
	for _, nonPrimaryInsured := range policy.NonPrimaryInsureds {
		hasWoS := hasBlanketWaiverOfSubrogation ||
			policy.Clauses.GetSpecifiedClauseStatus(
				inscoremodel.ClauseType_ClauseTypeWaiverOfSubrogation,
				inscoremodel.ParticipantType_ParticipantTypeInsured,
				nonPrimaryInsured.GetId(),
			)

		additionalInsureds = append(additionalInsureds, endorsementappoapi.AdditionalInsuredChange{
			AdditionalInsured: nirvana.AdditionalInsured{
				Address: converters.ProtoToNirvanaAddress(nonPrimaryInsured.GetAddress()),
				Name:    nonPrimaryInsured.Name.BusinessName,
			},
			ChangeType: endorsementappoapi.AdditionalInsuredChangeChangeTypeUnchanged,
			HasWoS:     hasWoS,
		})
	}

	return additionalInsureds
}

func (c CompanyProcessor) ExtractCompanyUSState(policy *model.Policy) (us_states.USState, error) {
	companyUSState := policy.ProgramData.GetNonFleetData().CompanyInfo.UsState
	companyUSStateEnum, err := us_states.StrToUSState(companyUSState)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to convert company US state string to enum")
	}

	return companyUSStateEnum, nil
}

var clauseTypeMap = map[nirvana.AdditionalInsuredType]struct { // nolint:exhaustive
	aiTypes []inscoremodel.ClauseType
	wosType inscoremodel.ClauseType
}{
	nirvana.BlanketAdditionalInsured: {
		aiTypes: []inscoremodel.ClauseType{
			inscoremodel.ClauseType_ClauseTypeAdditionalInsured,
		},
		wosType: inscoremodel.ClauseType_ClauseTypeWaiverOfSubrogation,
	},
	nirvana.SpecifiedAdditionalInsured: {
		aiTypes: []inscoremodel.ClauseType{
			inscoremodel.ClauseType_ClauseTypeSpecifiedAdditionalInsured,
		},
		wosType: inscoremodel.ClauseType_ClauseTypeSpecifiedWaiverOfSubrogation,
	},
	nirvana.SpecifiedAdditionalInsuredPrimaryNonContributory: {
		aiTypes: []inscoremodel.ClauseType{
			inscoremodel.ClauseType_ClauseTypeSpecifiedAdditionalInsuredPrimaryNonContributory,
			inscoremodel.ClauseType_ClauseTypeSpecifiedAdditionalInsured,
		},
		wosType: inscoremodel.ClauseType_ClauseTypeSpecifiedWaiverOfSubrogation,
	},
}
