package endorsement

import (
	"context"

	"github.com/google/uuid"

	"nirvanatech.com/nirvana/api-server/common"
	commonib "nirvanatech.com/nirvana/api-server/handlers/common/ib"
	endorsementrequest "nirvanatech.com/nirvana/application/endorsementapp/endorsement-request"
	"nirvanatech.com/nirvana/insurance-bundle/service"
	endorsementappintake "nirvanatech.com/nirvana/openapi-specs/components/endorsementapp/intake"
)

func GetEndorsementRequestCoverageChanges(
	ctx context.Context,
	endorsementRequestManager endorsementrequest.Manager,
	insuranceBundleManagerClient service.InsuranceBundleManagerClient,
	endorsementRequestID uuid.UUID,
) ([]endorsementappintake.SubCoverageChangeGroup, error) {
	endReqObj, err := GetEndorsementRequest(ctx, endorsementRequestManager, endorsementRequestID)
	if err != nil {
		return nil, err
	}

	ib, err := commonib.GetInsuranceBundleByInternalID(
		ctx, insuranceBundleManagerClient, endReqObj.Base.ID.String())
	if err != nil {
		return nil, err
	}

	// We use the last segment of the insurance bundle as that
	// represents the current state of the policy as in the minds of agents
	ibLastSegment := ib.GetLastSegment()
	if ibLastSegment == nil {
		return nil, common.NewNirvanaInternalServerWithReason(nil, commonib.ErrGetLastSegment.Error())
	}

	coverageProcessor, err := GetProcessor[CoverageProcessor](ib.ProgramType)
	if err != nil {
		return nil, common.NewNirvanaInternalServerWithReason(err, ErrGetCoverageProcessor.Error())
	}

	// We fetch the coverages from the last segment of the insurance bundle
	subCoverageChangeGroups := coverageProcessor.ExtractInitialCoverages(ctx, ibLastSegment)

	// TODO: We now need to apply the changes in the current endorsement request which are related to a coverage
	// modification. These include LimitChange, DeductibleChange & SubCoverageChange
	// We need to combine these changes to the above subCoverageChangeGroups

	return subCoverageChangeGroups, nil
}
