package endorsement

import (
	"context"
	"sort"

	"github.com/google/uuid"

	"nirvanatech.com/nirvana/api-server/common"
	commonib "nirvanatech.com/nirvana/api-server/handlers/common/ib"
	endorsementrequest "nirvanatech.com/nirvana/application/endorsementapp/endorsement-request"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/insurance-bundle/service"
	endorsementappintake "nirvanatech.com/nirvana/openapi-specs/components/endorsementapp/intake"
)

func GetEndorsementRequestDriverChanges(
	ctx context.Context,
	endorsementRequestManager endorsementrequest.Manager,
	insuranceBundleManagerClient service.InsuranceBundleManagerClient,
	endorsementRequestID uuid.UUID,
) ([]endorsementappintake.DriverChange, error) {
	endReqObj, err := GetEndorsementRequest(ctx, endorsementRequestManager, endorsementRequestID)
	if err != nil {
		return nil, err
	}

	ib, err := commonib.GetInsuranceBundleByInternalID(
		ctx, insuranceBundleManagerClient, endReqObj.Base.ID.String())
	if err != nil {
		return nil, err
	}

	// We use the last segment of the insurance bundle as that
	// represents the current state of the policy as in the minds of agents
	ibLastSegment := ib.GetLastSegment()
	if ibLastSegment == nil {
		return nil, common.NewNirvanaInternalServerWithReason(nil, commonib.ErrGetLastSegment.Error())
	}

	driverProcessor, err := GetProcessor[DriverChangeProcessor](ib.ProgramType)
	if err != nil {
		return nil, common.NewNirvanaInternalServerWithReason(err, ErrGetDriverProcessor.Error())
	}

	// 1. Get initial Driver change groups from the insurance bundle
	// 2. Process the endorsement request changes for Drivers (additions, updates, removals)
	// 3. Merge the initial Driver list with the new changes
	initialDriverChangeGroups := driverProcessor.ExtractInitialDrivers(ctx, ibLastSegment)
	driverLicensesToRemove, driverChangeGroupsAfterProcessing := driverProcessor.ExtractAndCategorizeDriverEndorsementChanges(
		ctx,
		endReqObj,
		initialDriverChangeGroups,
	)

	return mergeDriverChanges(driverChangeGroupsAfterProcessing, driverLicensesToRemove), nil
}

// mergeDriverChanges combines initial unchanged Drivers with new changes (additions & update)
// and removals. It creates a final list of Driver changes, handling conflicts and removals.
func mergeDriverChanges(
	driverChangeGroups []endorsementappintake.DriverChange,
	driverLicensesToRemove []string,
) []endorsementappintake.DriverChange {
	latestDriverChanges := make(map[string]endorsementappintake.DriverChange)

	// First pass: store original Drivers
	for _, dc := range driverChangeGroups {
		if dc.ChangeType == endorsementappintake.DriverChangeChangeTypeUnchanged {
			latestDriverChanges[dc.Driver.LicenseNumber] = dc
		}
	}

	// Second pass: process changes
	for _, dc := range driverChangeGroups {
		dl := dc.Driver.LicenseNumber

		if slice_utils.Contains(driverLicensesToRemove, dl) {
			latestDriverChanges[dl] = endorsementappintake.DriverChange{
				ChangeType: endorsementappintake.DriverChangeChangeTypeDeleted,
				Driver:     dc.Driver,
				OldValue:   &dc.Driver,
			}
		} else {
			switch dc.ChangeType {
			case endorsementappintake.DriverChangeChangeTypeAdded:
				latestDriverChanges[dl] = dc
			case endorsementappintake.DriverChangeChangeTypeUpdated:
				if original, exists := latestDriverChanges[dl]; exists {
					latestDriverChanges[dl] = endorsementappintake.DriverChange{
						ChangeType: endorsementappintake.DriverChangeChangeTypeUpdated,
						Driver:     dc.Driver,
						OldValue:   &original.Driver,
					}
				} else {
					latestDriverChanges[dl] = dc
				}
			case endorsementappintake.DriverChangeChangeTypeUnchanged,
				endorsementappintake.DriverChangeChangeTypeDeleted:
				// Do nothing
			}
		}
	}

	return sortDriverChangesByType(latestDriverChanges)
}

// sortDriverChangesByType sorts the Driver changes by their change type.
// The order is: Added, Updated, Deleted, Unchanged.
// Within each group, the changes are sorted by LicenseNumber.
func sortDriverChangesByType(
	changes map[string]endorsementappintake.DriverChange,
) []endorsementappintake.DriverChange {
	sorted := make([]endorsementappintake.DriverChange, 0, len(changes))

	changeTypes := []endorsementappintake.DriverChangeChangeType{
		endorsementappintake.DriverChangeChangeTypeAdded,
		endorsementappintake.DriverChangeChangeTypeUpdated,
		endorsementappintake.DriverChangeChangeTypeDeleted,
		endorsementappintake.DriverChangeChangeTypeUnchanged,
	}

	for _, changeType := range changeTypes {
		// Collect all changes of current type
		typeGroup := make([]endorsementappintake.DriverChange, 0)
		for _, dc := range changes {
			if dc.ChangeType == changeType {
				typeGroup = append(typeGroup, dc)
			}
		}

		// Sort by LicenseNumber
		sort.Slice(typeGroup, func(i, j int) bool {
			return typeGroup[i].Driver.LicenseNumber < typeGroup[j].Driver.LicenseNumber
		})

		sorted = append(sorted, typeGroup...)
	}

	return sorted
}
