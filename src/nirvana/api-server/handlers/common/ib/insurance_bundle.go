package ib

import (
	"context"
	"database/sql"

	"github.com/cockroachdb/errors"
	"nirvanatech.com/nirvana/api-server/common"
	ibmodel "nirvanatech.com/nirvana/insurance-bundle/model"
	ibservice "nirvanatech.com/nirvana/insurance-bundle/service"
)

func getInsuranceBundle(
	ctx context.Context,
	insuranceBundleManagerClient ibservice.InsuranceBundleManagerClient,
	identifier *ibservice.GetInsuranceBundleRequest_PrimaryFilter,
	id string,
) (*ibmodel.InsuranceBundle, error) {
	bundle, err := insuranceBundleManagerClient.GetInsuranceBundle(ctx, &ibservice.GetInsuranceBundleRequest{
		PrimaryFilter: identifier,
	})
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, common.NewNirvanaNotFoundErrorf(err, common.EntityInsuranceBundle, id)
		}
		return nil, common.NewNirvanaInternalServerWithReason(err, ErrGetBundle.Error())
	}
	return bundle.InsuranceBundle, nil
}

func GetInsuranceBundleByInternalID(
	ctx context.Context,
	insuranceBundleManagerClient ibservice.InsuranceBundleManagerClient,
	bundleInternalID string,
) (*ibmodel.InsuranceBundle, error) {
	identifier := &ibservice.GetInsuranceBundleRequest_PrimaryFilter{
		Identifier: &ibservice.GetInsuranceBundleRequest_PrimaryFilter_InternalId{
			InternalId: bundleInternalID,
		},
	}
	return getInsuranceBundle(ctx, insuranceBundleManagerClient, identifier, bundleInternalID)
}

// GetInsuranceBundleByApplicationID Please use this for low throughput operations onl since ApplicationId does not have a index on it,
// If we are using this, please add a index before you do so.
func GetInsuranceBundleByApplicationID(
	ctx context.Context,
	insuranceBundleManagerClient ibservice.InsuranceBundleManagerClient,
	applicationID string,
) (*ibmodel.InsuranceBundle, error) {
	identifier := &ibservice.GetInsuranceBundleRequest_PrimaryFilter{
		Identifier: &ibservice.GetInsuranceBundleRequest_PrimaryFilter_ApplicationId{
			ApplicationId: applicationID,
		},
	}
	return getInsuranceBundle(ctx, insuranceBundleManagerClient, identifier, applicationID)
}
