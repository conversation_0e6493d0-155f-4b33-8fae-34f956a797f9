package utils

import (
	"context"
	"fmt"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/helpers"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/file_upload"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/infra/authz/checker"
)

func NewFileResource(
	ctx context.Context,
	fileUploadWrapper file_upload.DataWrapper,
	appWrapper application.DataWrapper,
	fileHandle uuid.UUID,
) (authz.Resource, error) {
	file, err := fileUploadWrapper.GetByHandle(ctx, fileHandle)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to get file by handle %s", fileHandle)
	}
	appIds, err := getAppIDsForFileHandle(ctx, appWrapper, fileHandle, file.AccountID)
	if err != nil {
		log.Error(ctx, "unable to get apps for account", log.String("account id", file.AccountID.String()),
			log.String("handle", fileHandle.String()), log.Err(err))
		return nil, errors.Wrapf(err, "unable to get apps for account", file.AccountID)
	}
	return &FileResource{
		FileObject: *file,
		appIDs:     appIds,
	}, nil
}

func NewUwFileResource(
	ctx context.Context,
	fileUploadWrapper file_upload.DataWrapper,
	appReviewWrapper uw.ApplicationReviewWrapper,
	fileHandle uuid.UUID,
) (authz.Resource, error) {
	file, err := fileUploadWrapper.GetByHandle(ctx, fileHandle)
	if err != nil {
		log.Error(ctx, "unable to get file by handle",
			log.String("handle", fileHandle.String()), log.Err(err))
		return nil, errors.Wrapf(err, "unable to get file by handle %s", fileHandle)
	}
	reviewObj, err := appReviewWrapper.GetReview(ctx, file.AccountID.String())
	if err != nil {
		return nil, errors.Wrapf(err, "unable to get reviewObj for reviewId %s", file.AccountID)
	}
	return &UwFileResource{
		FileObject: *file,
		appId:      reviewObj.ApplicationID,
		agencyId:   reviewObj.Application.AgencyID,
	}, nil
}

func getAppIDsForFileHandle(ctx context.Context, appWrapper application.DataWrapper, fileHandle, agencyId uuid.UUID) ([]string, error) {
	var apps []string
	all, err := appWrapper.GetAllApplicationsByAgencyId(ctx, agencyId)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to fetch all apps for agency %s", agencyId.String())
	}
	for _, a := range all {
		if a.EquipmentInfo.EquipmentList.GetFileHandle() == nil {
			log.Warn(ctx,
				"Empty file handle for equipment list",
				log.AppID(a.ID),
				log.Any("ImplerMetadata", a.EquipmentInfo.EquipmentList.ImplerMetadata),
				log.Any("FlatfileMetadata", a.EquipmentInfo.EquipmentList.FlatfileMetadata),
			)
		}
		if a.DriversInfo.GetFileHandle() == nil {
			log.Warn(ctx,
				"Empty file handle for drivers info",
				log.AppID(a.ID),
				log.Any("ImplerMetadata", a.EquipmentInfo.EquipmentList.ImplerMetadata),
				log.Any("FlatfileMetadata", a.EquipmentInfo.EquipmentList.FlatfileMetadata),
			)
		}
		if a.EquipmentInfo != nil && fileHandle == pointer_utils.UUIDValOr(
			a.EquipmentInfo.EquipmentList.GetFileHandle(), uuid.Nil) {
			apps = append(apps, a.ID)
		} else if a.DriversInfo != nil && fileHandle == pointer_utils.UUIDValOr(
			a.DriversInfo.GetFileHandle(), uuid.Nil) {
			apps = append(apps, a.ID)
		} else if a.AdditionalLossInfo != nil {
			for _, lossFileMetadata := range a.AdditionalLossInfo.Files {
				if fileHandle == pointer_utils.UUIDValOr(lossFileMetadata.Handle, uuid.Nil) {
					apps = append(apps, a.ID)
				}
			}
		}
	}
	return apps, nil
}

type FileResource struct {
	file_upload.FileObject
	appIDs []string
}

func (f *FileResource) ResourcePaths() []string {
	// Add file as agency resource
	paths := []string{fmt.Sprintf("/agency/%s/files/%s", f.AccountID.String(), f.Handle.String())}
	// Add file as application resource
	for _, a := range f.appIDs {
		paths = append(paths, fmt.Sprintf("/agency/%s/applications/%s/files/%s",
			f.AccountID.String(), a, f.Handle.String()))
	}
	return paths
}

var _ authz.Resource = &FileResource{}

type UwFileResource struct {
	file_upload.FileObject
	appId    string
	agencyId uuid.UUID
}

func (u *UwFileResource) ResourcePaths() []string {
	// Add Uw file as a review resource
	paths := []string{fmt.Sprintf("/agency/%s/applications/%s/review/%s/files/%s",
		u.agencyId.String(), u.appId, u.AccountID.String(), u.FileObject.AccountID.String())}
	return paths
}

var _ authz.Resource = (*UwFileResource)(nil)

func HasPermissionOverApp(
	ctx context.Context,
	appWrapper application.DataWrapper,
	admittedWrapper nf_app.Wrapper[*admitted_app.AdmittedApp],
	authzChecker *checker.Checker,
	user authz.User,
	action authz.Action,
	appId uuid.UUID,
	programType policy_enums.ProgramType,
) common.HandlerAuthzResponse {
	var hasPermission bool
	var err error
	// nolint:exhaustive
	switch programType {
	case policy_enums.ProgramTypeFleet:
		hasPermission, err = ApplicationHasPermissionOverApp(ctx, appWrapper, authzChecker, user, action, appId)
	case policy_enums.ProgramTypeNonFleetAdmitted:
		hasPermission, err = AdmittedHasPermissionOverApp(ctx, admittedWrapper, authzChecker, user, action, appId)
	// TODO: ADD AUTHZ HERE
	case policy_enums.ProgramTypeBusinessAuto:
		hasPermission, err = true, nil
	default:
		return common.HandlerAuthzResponse{AuthzError: &helpers.GenericServerError}
	}
	if err != nil {
		return common.HandlerAuthzResponse{AuthzError: &helpers.GenericServerError}
	}
	if !hasPermission {
		log.Info(
			ctx,
			"not authorized",
			log.String("action", string(action)),
			log.Stringer("userId", user.ID),
			log.Stringer("appId", appId),
			log.Stringer("programType", programType),
		)
		unauthorizedError := common.NewNirvanaUnauthorizedErrorf(
			common.ErrUserUnauthorized,
			common.EntityApplication,
			appId.String(),
		)
		return common.HandlerAuthzResponse{
			AuthzError: &helpers.GenericAuthzError,
			Error:      unauthorizedError,
		}
	}
	return common.HandlerAuthzResponse{IsAuthorized: true}
}

func ApplicationHasPermissionOverApp(
	ctx context.Context,
	appWrapper application.DataWrapper,
	authzChecker *checker.Checker,
	user authz.User,
	action authz.Action,
	appId uuid.UUID,
) (bool, error) {
	app, err := appWrapper.GetAppById(ctx, appId.String())
	if err != nil {
		log.Error(ctx, "unable to fetch app", log.Stringer("appId", appId), log.Err(err))
		return false, err
	}
	return authzChecker.CheckPermission(user, app, action)
}

func AdmittedHasPermissionOverApp(
	ctx context.Context,
	admittedWrapper nf_app.Wrapper[*admitted_app.AdmittedApp],
	authzChecker *checker.Checker,
	user authz.User,
	action authz.Action,
	appId uuid.UUID,
) (bool, error) {
	app, err := admittedWrapper.GetAppById(ctx, appId)
	if err != nil {
		log.Error(ctx, "unable to fetch app", log.Stringer("appId", appId), log.Err(err))
		return false, err
	}
	return authzChecker.CheckPermission(user, app, action)
}
