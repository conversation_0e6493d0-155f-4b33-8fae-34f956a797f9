package common

import (
	"net/http"

	"nirvanatech.com/nirvana/common-go/log"

	"nirvanatech.com/nirvana/api-server/helpers"
)

// ExtractAuthorizedResponse returns (isAuthorized, code, body)
func ExtractAuthorizedResponse(
	response HandlerAuthzResponse,
) (bool, int, interface{}) {
	if response.IsAuthorized {
		return true, 0, nil
	}
	if response.AuthzError != nil {
		return false, http.StatusUnauthorized, *response.AuthzError
	} else if response.ServerError != nil {
		errMessage := helpers.WrapErrorMessage(
			response.ServerError, "Server error")
		return false, http.StatusInternalServerError, errMessage
	} else {
		log.Sugared.Errorf("Unexpected authz response = %v", response)
		return false, http.StatusInternalServerError, helpers.GenericServerError
	}
}
