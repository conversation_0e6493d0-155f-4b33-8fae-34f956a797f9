package common

import (
	"fmt"
	"net/http"
	"testing"

	"github.com/cockroachdb/errors"
	"github.com/stretchr/testify/require"
)

func TestErrorWrapping(t *testing.T) {
	// It's okay to wrap the error again after annotating.
	err := errors.Wrap(
		WrapAsHttpErrorf(
			errors.New("internal"), http.StatusInternalServerError, "internal",
		),
		"re-wrapped",
	)
	asError := new(HttpHandlerError)
	require.True(t, errors.As(err, &asError))
	require.Equal(t, http.StatusInternalServerError, asError.StatusCode)
	require.Equal(t, "internal", asError.Message)

	fmt.Printf("%+v\n", err)
	/*
		re-wrapped: http error: statuscode=500 message="internal": internal
		(1) attached stack trace
		  -- stack trace:
		  | nirvanatech.com/nirvana/api-server/common.TestErrorWrapping
		  | 	/nirvana/src/nirvana/api-server/common/errors_test.go:14
		  | [...repeated from below...]
		Wraps: (2) re-wrapped
		Wraps: (3) http error: statuscode=500 message="internal"
		Wraps: (4) attached stack trace
		  -- stack trace:
		  | nirvanatech.com/nirvana/api-server/common.TestErrorWrapping
		  | 	/nirvana/src/nirvana/api-server/common/errors_test.go:16
		  | testing.tRunner
		  | 	/opt/homebrew/Cellar/go/1.21.1/libexec/src/testing/testing.go:1595
		  | runtime.goexit
		  | 	/opt/homebrew/Cellar/go/1.21.1/libexec/src/runtime/asm_arm64.s:1197
		Wraps: (5) internal
		Error types: (1) *withstack.withStack (2) *errutil.withPrefix (3) *common.HttpHandlerError (4) *withstack.withStack (5) *errutil.leafError
	*/

	// It's okay to wrap a nil error
	err = WrapAsHttpErrorf(nil, http.StatusInternalServerError, "internal")
	asError = new(HttpHandlerError)
	require.True(t, errors.As(err, &asError))
	require.Equal(t, http.StatusInternalServerError, asError.StatusCode)
	require.Equal(t, "internal", asError.Message)
	fmt.Printf("%+v\n", err)
	/*
		http error: statuscode=500 message="internal": <nil>
		(1) http error: statuscode=500 message="internal": <nil>
		Error types: (1) *common.HttpHandlerError
	*/
}
