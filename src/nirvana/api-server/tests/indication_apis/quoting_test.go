package indication_apis

import (
	"context"
	"math"
	"strconv"
	"testing"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/launchdarkly/go-sdk-common/v3/ldvalue"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"

	application_deps "nirvanatech.com/nirvana/api-server/interceptors/application/deps"
	"nirvanatech.com/nirvana/api-server/test_utils"
	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	file_enums "nirvanatech.com/nirvana/common-go/file_upload_lib/enums"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	common_test_utils "nirvanatech.com/nirvana/common-go/test_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/external_data_management/data_processing/vin/testing/testfixtures"
	"nirvanatech.com/nirvana/golden_dataset"
	"nirvanatech.com/nirvana/infra/constants"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/emailer_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/feature_store_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/fmcsa_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/lni_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	oapi_app "nirvanatech.com/nirvana/openapi-specs/components/application"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
	"nirvanatech.com/nirvana/quoting/app_state_machine/app_logic"
	state_enums "nirvanatech.com/nirvana/quoting/app_state_machine/enums"
	"nirvanatech.com/nirvana/rating/models/models_release"
	"nirvanatech.com/nirvana/rating/rtypes"
	"nirvanatech.com/nirvana/telematics"
)

const busyWaitSleep = 10 * time.Microsecond

// TestIndicationApisNonIsolatedTestSuite runs all tests in the same suite to share a single fx testloader App.
// This approach improves test performance by reducing setup/teardown time.
// If isolated environment is required for a test, consider creating a separate suite.
func TestIndicationApisNonIsolatedTestSuite(t *testing.T) {
	suite.Run(t, new(indicationApisTestSuite))
}

type indicationApisTestSuite struct {
	common_test_utils.StatsHandler
	suite.Suite
	h                 *test_utils.ApiServerHarness
	deps              application_deps.Deps
	fxapp             *fxtest.App
	featureFlagClient *feature_flag_lib.MockClient
}

func (s *indicationApisTestSuite) TearDownSuite() {
	s.fxapp.RequireStop()
}

func (s *indicationApisTestSuite) SetupSuite() {
	var env struct {
		fx.In
		Harness *test_utils.ApiServerHarness
		*emailer_fixture.NoopEmailerFixture
		*feature_store_fixture.FeatureStoreFixture
		*fmcsa_fixture.FmcsaFixture
		*lni_fixture.GoldenDOTsAuthorityHistoryFixture
		*testfixtures.NhtsaToIsoMappingV1Fixture
		*lni_fixture.ActiveOrPendingInsuranceFixture
		FeatureFlagClient *feature_flag_lib.MockClient
	}
	s.fxapp = testloader.RequireStart(s.T(), &env)
	s.h = env.Harness

	s.featureFlagClient = env.FeatureFlagClient
	s.featureFlagClient.SetValue(feature_flag_lib.FeatureValidateDuplicateApplications, ldvalue.Bool(false))

	s.deps = s.h.ApplicationDeps
	err := s.h.CreateDefaultAgency()
	s.Require().NoError(err)
	err = s.h.CreateDefaultAgent()
	s.Require().NoError(err)
	err = s.h.CreateDefaultBD()
	s.Require().Nil(err)
	err = s.h.CreateDefaultSupportUser()
	s.Require().NoError(err)
	err = s.h.CreateDefaultSuperUser()
	s.Require().NoError(err)
	err = s.h.CreateDefaultHighestAuthorityUW()
	s.Require().NoError(err)
	_, err = s.h.CreateDefaultHighestAuthorityUWWithName("Karleigh", "Schroeder", "<EMAIL>")
	s.Require().NoError(err)
	err = s.h.CreateDefaultBD()
	s.Require().NoError(err)
	err = s.h.CreateDefaultProducerUser()
	s.Require().NoError(err)
	s.Require().NoError(s.h.CreateDefaultFormSchedules())
}

// Test the indication flow without being authorized (trying to access an
// application from another user)
func (s *indicationApisTestSuite) TestAppIndicationFormNoAuthz() {
	_, err := s.h.CreateRandomUserAndSession()
	s.Require().Nil(err)

	appTestWrapper, err := s.h.TestWrappers.AppTestWrapperCreator()
	s.Require().NoError(err)
	_, err = appTestWrapper.CreateApplication()
	s.Require().NoError(err)

	_, err = s.h.CreateRandomUserAndSession()
	s.Require().Nil(err)
	_, err = appTestWrapper.GenerateIndication()
	s.Require().Contains(err.Error(), "Not authorized")
}

// TestGoldenDatasetAppIndicationOptions tests the GoldenDotNumbers. They
// should effectively pass through all the indication flow
func (s *indicationApisTestSuite) TestGoldenDatasetAppIndicationOptions() {
	_, err := s.h.CreateRandomUserAndSession()
	s.Require().Nil(err)
	// convert this test to table driven test
	type dots struct {
		dotNumber int64
	}
	var tests []dots
	for _, a := range golden_dataset.GoldenDotNumbers {
		tests = append(tests, dots{
			dotNumber: a,
		})
	}
	for _, tt := range tests {
		tt := tt
		s.T().Run(strconv.FormatInt(tt.dotNumber, 10), func(t *testing.T) {
			testAppIndicationOptions(s, tt.dotNumber)
		},
		)
	}
}

// testAppIndicationOptions runs the entire flow from creating an application to select and
// indication option.
func testAppIndicationOptions(s *indicationApisTestSuite, dotNumber int64) string {
	appTestWrapper, err := s.h.TestWrappers.AppTestWrapperCreator()
	s.Require().NoError(err)
	appObj, err := appTestWrapper.CreateApplicationC(test_utils.CreateAppicationParams{DotNumber: dotNumber})
	s.Require().NoError(err)

	var coveragesToTest []oapi_app.CoverageRecord
	var numCovsWithVariables int
	// TODO: Remove once MTC is launched for all states
	if _, err := app_logic.GetCoverageLimit(app_enums.CoverageMotorTruckCargo, appObj.CompanyInfo.USState); err == nil {
		coveragesToTest = test_utils.CoveragesALAndAPDAndGLAndMTC
		numCovsWithVariables = 4
	} else {
		coveragesToTest = test_utils.CoveragesALAndAPDAndGL
		numCovsWithVariables = 3
	}

	appObj, err = appTestWrapper.GenerateIndicationC(
		test_utils.GenerateIndicationParams{Coverages: coveragesToTest})
	s.Require().NoError(err)

	appObj, err = appTestWrapper.GenerateAndSelectIndicationC(
		test_utils.GenerateAndSelectIndicationParams{Coverages: coveragesToTest})
	s.Require().Nil(err)
	opts, err := s.h.Api.GetIndicationOptions(appObj.ID)
	s.Require().Nil(err)
	// TODO: remove after we remove deprecated current deductibles & options
	// Keeping to test backward compatibility for now
	s.Require().NotNil(opts.CurrentDeductibles)
	s.Require().Len(opts.CurrentDeductibles, len(coveragesToTest))
	s.Require().NotNil(opts.DeductiblesOptions)
	s.Require().Len(opts.DeductiblesOptions, len(coveragesToTest))

	s.Require().Len(opts.CoverageVariablesOptions, numCovsWithVariables)
	al, err := opts.GetCoverageVariableOptions(oapi_common.CoverageAutoLiability)
	s.Require().NoError(err)
	s.Require().NotNil(al.Deductibles)
	s.Require().Equal(
		al.Deductibles.Current,
		*appObj.CoverageInfo.GetCoverage(app_enums.CoverageAutoLiability).Deductible,
	)
	s.Require().Len(al.Deductibles.Options, 4)
	s.Require().Nil(al.Limits)
	if slice_utils.Contains(
		coveragesToTest,
		oapi_app.CoverageRecord{CoverageType: oapi_common.CoverageAutoPhysicalDamage},
	) {
		apd, err := opts.GetCoverageVariableOptions(oapi_common.CoverageAutoPhysicalDamage)
		s.Require().NoError(err)
		s.Require().NotNil(apd.Deductibles)
		s.Require().Equal(
			apd.Deductibles.Current,
			*appObj.CoverageInfo.GetCoverage(app_enums.CoverageAutoPhysicalDamage).Deductible,
		)
		s.Require().Len(apd.Deductibles.Options, 3)
		s.Require().Nil(apd.Limits)
	}
	if slice_utils.Contains(
		coveragesToTest,
		oapi_app.CoverageRecord{CoverageType: oapi_common.CoverageMotorTruckCargo},
	) {
		mtc, err := opts.GetCoverageVariableOptions(oapi_common.CoverageMotorTruckCargo)
		s.Require().NoError(err)
		s.Require().NotNil(mtc.Deductibles)
		s.Require().Equal(
			mtc.Deductibles.Current,
			*appObj.CoverageInfo.GetCoverage(app_enums.CoverageMotorTruckCargo).Deductible,
		)
		s.Require().Len(mtc.Deductibles.Options, 4)
		s.Require().NotNil(mtc.Limits)
		s.Require().Equal(
			mtc.Limits.Current,
			*appObj.CoverageInfo.GetCoverage(app_enums.CoverageMotorTruckCargo).Limit,
		)
		s.Require().Len(mtc.Limits.Options, 4)
	}
	return appObj.ID
}

// This test runs the entire flow from creating an application to changing the
// all the different deductibles in the Indication Options screen, following
// these steps:
// 1. POST to create application
// 2. PUT to fill indication form with mock data
// 3. POST to submit the application to generate a submission
// 4. GET indication options and store deductibles for comparison
// 5. Repeat step 2, 3, and 4 for all possible deductibles for both AL, APD, MTC
func (s *indicationApisTestSuite) TestAppForAllDeductibles() {
	session, err := s.h.CreateRandomUserAndSession()
	s.Require().Nil(err)
	agencyId := s.h.Api.GetAgencyId()
	s.Require().NotNil(agencyId)
	// Step 1
	appetiteForm := s.h.MockAppetiteFormC(2081158, test_utils.DefaultEffectiveDate, 30)
	appResp, err := s.h.Api.PostApplication(appetiteForm, *agencyId)
	s.Require().Nil(err)
	appId := appResp.ApplicationID
	s.Require().NotNil(appId)
	fileHandleId, err := s.h.CreateFileHandle("TestKey",
		*agencyId, file_enums.FileDestinationGroupQuoting)
	s.Require().Nil(err)
	app, err := s.h.ApplicationDeps.ApplicationWrapper.GetAppById(s.h.Ctx, appId)
	s.Require().Nil(err)

	// Step 2
	covs := test_utils.CoveragesALAndAPDAndMTC
	operationsForm := test_utils.MockOperationsFormC(session.UserID, fileHandleId, covs, nil)
	clsAndComForm := test_utils.MockClassesAndCommoditiesFormC(covs)
	lossRunSumForm := test_utils.MockLossRunSummaryFormC(covs)
	for _, cov := range app.CoverageInfo.GetPrimaryCoverages() {
		deds := app_logic.GetDeductibleOptionsFromCoverageAndState(cov, app.State)
		for _, d := range deds {
			var alDed, apdDed, mtcDed *int32
			switch cov {
			case app_enums.CoverageAutoLiability:
				alDed = &d
			case app_enums.CoverageAutoPhysicalDamage:
				apdDed = &d
			case app_enums.CoverageMotorTruckCargo:
				mtcDed = &d
			}
			operationsForm.CoveragesRequired = &[]oapi_app.CoverageRecord{
				{
					CoverageType: oapi_common.CoverageAutoLiability,
					Deductible:   alDed,
				},
				{
					CoverageType: oapi_common.CoverageAutoPhysicalDamage,
					Deductible:   apdDed,
				},
				{
					CoverageType: oapi_common.CoverageMotorTruckCargo,
					Deductible:   mtcDed,
				},
			}
			err = s.h.Api.PutIndicationForm(
				appId,
				oapi_app.IndicationForm{
					OperationsForm:            operationsForm,
					ClassesAndCommoditiesForm: clsAndComForm,
					LossRunSummaryForm:        lossRunSumForm,
				})
			s.Require().Nil(err)
			// Step 3
			err = s.h.Api.PostIndicationSubmit(appId)
			s.Require().Nil(err)
			s.Require().Nil(s.waitForModelRun(appId))
		}
	}
}

func (s *indicationApisTestSuite) TestRenewalAppIndicationOptions() {
	// Create app
	quoteTestWrapper, err := s.h.TestWrappers.QuoteTestWrapperCreator()
	s.Require().NoError(err)
	appObj, err := s.h.ApplicationDeps.ApplicationWrapper.GetAppById(s.h.Ctx, quoteTestWrapper.AppId)
	s.Require().NoError(err)
	// manually setting the state to bound
	err = s.h.ApplicationDeps.ApplicationWrapper.UpdateApp(s.h.Ctx, quoteTestWrapper.AppId,
		func(a application.Application) (application.Application, error) {
			a.AgencyID = constants.NirvanaHQAgencyID
			a.State = state_enums.AppStatePolicyCreated
			return a, nil
		})
	s.Require().NoError(err)
	err = s.h.LoginDefaultSuperUser()
	s.Require().NoError(err)
	createdResp, conflictedResp, err := s.h.Api.PostApplicationApplicationIDV2Renew(s.h.Ctx, oapi_app.ApplicationID(appObj.ID))
	s.Require().NoError(err)
	var renewalAppId string
	if createdResp != nil {
		renewalAppId = createdResp.ApplicationID
	} else {
		renewalAppId = conflictedResp.ExistingApplicationId
	}
	renewalAppObj, err := s.h.ApplicationDeps.ApplicationWrapper.GetAppById(s.h.Ctx, renewalAppId)
	s.Require().NoError(err)
	s.Require().Equal(renewalAppObj.State, state_enums.AppStateUnsubmitted)
	opts, err := s.h.Api.GetIndicationOptions(renewalAppId)
	s.Require().Nil(err)
	s.Require().NotNil(opts.CurrentDeductibles)
	s.Require().Len(opts.CurrentDeductibles, 3)
	s.Require().NotNil(opts.DeductiblesOptions)
	s.Require().Len(opts.DeductiblesOptions, 4)
	s.Require().Len(opts.CoverageVariablesOptions, 3)
	s.Require().NotNil(opts.LimitsOptions)
	s.Require().Len(*opts.LimitsOptions, 1)
}

// This test runs indications testing all the possible limit options we offer
// for updates, making sure that the current default value updates correctly
// after each run.
// For now, we only offer MTC limit updates.
func (s *indicationApisTestSuite) TestAppForAllLimits() {
	session, err := s.h.CreateRandomUserAndSession()
	s.Require().Nil(err)
	agencyId := s.h.Api.GetAgencyId()
	s.Require().NotNil(agencyId)
	appetiteForm := s.h.MockAppetiteFormC(2081158, test_utils.DefaultEffectiveDate, 30)
	appResp, err := s.h.Api.PostApplication(appetiteForm, *agencyId)
	s.Require().Nil(err)
	appId := appResp.ApplicationID
	s.Require().NotNil(appId)
	fileHandleId, err := s.h.CreateFileHandle("TestKey",
		*agencyId, file_enums.FileDestinationGroupQuoting)
	s.Require().Nil(err)
	appObj, err := s.h.Metadata.GetApplication(appId)
	s.Require().Nil(err)

	coverages := test_utils.CoveragesALAndAPDAndGLAndMTC
	operationsForm := test_utils.MockOperationsFormC(session.UserID, fileHandleId, coverages, nil)
	clsAndComForm := test_utils.MockClassesAndCommoditiesFormC(coverages)
	lossRunSumForm := test_utils.MockLossRunSummaryFormC(coverages)
	for cov, limits := range app_logic.LimitOptionsAmounts[appObj.CompanyInfo.USState] {
		for _, limit := range limits {
			var mtcLimit *int32
			if cov == app_enums.CoverageMotorTruckCargo {
				mtcLimit = &limit
			}
			for idx := range coverages {
				if coverages[idx].CoverageType == oapi_common.CoverageMotorTruckCargo {
					coverages[idx].Limit = mtcLimit
				}
			}
			err = s.h.Api.PutIndicationForm(
				appId,
				oapi_app.IndicationForm{
					OperationsForm:            operationsForm,
					ClassesAndCommoditiesForm: clsAndComForm,
					LossRunSummaryForm:        lossRunSumForm,
				})
			s.Require().Nil(err)
			err = s.h.Api.PostIndicationSubmit(appId)
			s.Require().Nil(err)
			s.Require().Nil(s.waitForModelRun(appId))
			opts, err := s.h.Api.GetIndicationOptions(appId)
			s.Require().Nil(err)
			if mtcLimit != nil {
				mtc, err := opts.GetCoverageVariableOptions(oapi_common.CoverageMotorTruckCargo)
				s.Require().NoError(err)
				s.Require().NotNil(mtc.Limits)
				s.Require().Equal(mtc.Limits.Current, *mtcLimit)
			}
		}
	}
}

// TestGLDeductibles tests that GL is always following AL deductibles.
func (s *indicationApisTestSuite) TestGLDeductibles() {
	session, err := s.h.CreateRandomUserAndSession()
	s.Require().Nil(err)
	agencyId := s.h.Api.GetAgencyId()
	s.Require().NotNil(agencyId)
	// Create app
	appetiteForm := s.h.MockAppetiteForm()
	appResp, err := s.h.Api.PostApplication(appetiteForm, *agencyId)
	s.Require().Nil(err)
	appId := appResp.ApplicationID
	s.Require().NotNil(appId)
	fileHandleId, err := s.h.CreateFileHandle("TestKey",
		*agencyId, file_enums.FileDestinationGroupQuoting)
	s.Require().Nil(err)
	app, err := s.h.ApplicationDeps.ApplicationWrapper.GetAppById(s.h.Ctx, appId)
	s.Require().Nil(err)

	// Generate indication
	operationsForm := test_utils.MockOperationsFormC(session.UserID, fileHandleId, test_utils.CoveragesALAndGL, nil)
	clsAndComForm := test_utils.MockClassesAndCommoditiesForm()
	lossRunSumForm := test_utils.MockLossRunSummaryFormC(test_utils.CoveragesALAndGL)
	for _, ded := range app_logic.GetDeductibleOptionsFromCoverageAndState(app_enums.CoverageAutoLiability, app.State) {
		operationsForm.CoveragesRequired = &[]oapi_app.CoverageRecord{
			{
				CoverageType: oapi_common.CoverageAutoLiability,
				Deductible:   pointer_utils.Int32(ded),
			},
			{
				CoverageType: oapi_common.CoverageGeneralLiability,
			},
		}
		err = s.h.Api.PutIndicationForm(
			appId,
			oapi_app.IndicationForm{
				OperationsForm:            operationsForm,
				ClassesAndCommoditiesForm: clsAndComForm,
				LossRunSummaryForm:        lossRunSumForm,
			})
		s.Require().Nil(err)
		err = s.h.Api.PostIndicationSubmit(appId)
		s.Require().Nil(err)
		s.Require().Nil(s.waitForModelRun(appId))
		// Get options and verify GL deductible
		opts, err := s.h.Api.GetIndicationOptions(appId)
		s.Require().Nil(err)
		alDedAfter, err := getDedFromOpts(opts, oapi_common.CoverageAutoLiability)
		s.Require().Nil(err)
		glDedAfter, err := getDedFromOpts(opts, oapi_common.CoverageGeneralLiability)
		s.Require().Nil(err)
		s.Require().NotNil(alDedAfter, glDedAfter)
		s.Require().Equal(*alDedAfter, ded)
	}
}

func (s *indicationApisTestSuite) waitForModelRun(appId string) error {
	appObj, err := s.h.Metadata.GetApplication(appId)
	if err != nil {
		return err
	}
	for appObj.IndicationSubmissionID == nil {
		time.Sleep(busyWaitSleep)
		appObj, err = s.h.Metadata.GetApplication(appId)
		if err != nil {
			return err
		}
	}
	subObj, err := s.h.Metadata.GetSubmission(*appObj.IndicationSubmissionID)
	if err != nil {
		return err
	}
	for subObj.JobRunId == nil {
		time.Sleep(busyWaitSleep)
		subObj, err = s.h.Metadata.GetSubmission(*appObj.IndicationSubmissionID)
		if err != nil {
			return err
		}
	}
	return s.deps.Jobber.WaitForJobRunCompletion(context.TODO(), *subObj.JobRunId)
}

// This test runs the entire flow from creating an application to changing the
// deductibles in the Indication Options screen, following these steps:
// 1. POST to create application
// 2. PUT to fill indication form with mock data
// 3. POST to submit the application to generate a submission
// 4. GET indication options and store deductibles for comparison
// 5. Repeat step 2 (with new deductibles), 3, and 4 and verify
func (s *indicationApisTestSuite) TestAppChangeDeductibles() {
	session, err := s.h.CreateRandomUserAndSession()
	s.Require().Nil(err)
	agencyId := s.h.Api.GetAgencyId()
	s.Require().NotNil(agencyId)
	// Step 1
	appetiteForm := s.h.MockAppetiteForm()
	appResp, err := s.h.Api.PostApplication(appetiteForm, *agencyId)
	s.Require().Nil(err)
	appId := appResp.ApplicationID
	s.Require().NotNil(appId)

	// Step 2
	fileHandleId, err := s.h.CreateFileHandle("TestKey",
		*agencyId, file_enums.FileDestinationGroupQuoting)
	s.Require().Nil(err)
	operationsForm := test_utils.MockOperationsForm(session.UserID, fileHandleId)
	clsAndComForm := test_utils.MockClassesAndCommoditiesForm()
	lossRunSumForm := test_utils.MockLossRunSummaryForm()
	err = s.h.Api.PutIndicationForm(
		appId,
		oapi_app.IndicationForm{
			OperationsForm:            operationsForm,
			ClassesAndCommoditiesForm: clsAndComForm,
			LossRunSummaryForm:        lossRunSumForm,
		})
	s.Require().Nil(err)

	// Step 3
	err = s.h.Api.PostIndicationSubmit(appId)
	s.Require().Nil(err)
	s.Require().Nil(s.waitForModelRun(appId))

	// Step 4
	opts, err := s.h.Api.GetIndicationOptions(appId)
	s.Require().Nil(err)
	s.Require().NotNil(opts)
	s.Require().NotNil(opts.Options)
	s.Require().Len(opts.Options, 3)

	alDedBefore, err := getDedFromOpts(opts, oapi_common.CoverageAutoLiability)
	s.Require().Nil(err)

	// Step 5

	// TODO: Fix the bug
	// newDed := int32(250)
	// operationsForm.CoveragesRequired[0].Deductible = &newDed
	// When deductible is set like above uw_test while running all
	// the api-server tests returns error
	// "deductible %!s(int32=‹250›) not present in options"
	// While both uw_test and quoting_test pass independently.
	app, err := s.h.ApplicationDeps.ApplicationWrapper.GetAppById(s.h.Ctx, appId)
	s.Require().Nil(err)
	covRequired := *operationsForm.CoveragesRequired
	covRequired[0].Deductible = pointer_utils.Int32(
		app_logic.GetDeductibleOptionsFromCoverageAndState(app_enums.CoverageAutoLiability, app.State)[0])
	err = s.h.Api.PutIndicationForm(
		appId,
		oapi_app.IndicationForm{
			OperationsForm:            operationsForm,
			ClassesAndCommoditiesForm: clsAndComForm,
			LossRunSummaryForm:        lossRunSumForm,
		})
	operationsForm.CoveragesRequired = &covRequired
	s.Require().Nil(err)
	err = s.h.Api.PostIndicationSubmit(appId)
	s.Require().Nil(err)
	s.Require().Nil(s.waitForModelRun(appId))
	opts, err = s.h.Api.GetIndicationOptions(appId)
	s.Require().Nil(err)
	alDedAfter, err := getDedFromOpts(opts, oapi_common.CoverageAutoLiability)
	s.Require().Nil(err)
	s.Require().NotNil(alDedBefore, alDedAfter)
}

func getDedFromOpts(
	opts *test_utils.IndicationOptionsWrapper,
	targetCov oapi_common.CoverageType,
) (*int32, error) {
	var ded *int32
	for _, cov := range opts.Options[0].Coverages {
		if cov.CoverageType == targetCov {
			ded = cov.Deductible
			return ded, nil
		}
	}
	return nil, errors.New("Deductible not found")
}

// This test mimics TestAppIndicationOptions, but only selecting one coverage
// instead of both available.
func (s *indicationApisTestSuite) TestAppIndicationOptionsOneCoverage() {
	_, err := s.h.CreateRandomUserAndSession()
	s.Require().Nil(err)
	// Step 1
	appTestWrapper, err := s.h.TestWrappers.AppTestWrapperCreator()
	s.Require().NoError(err)
	appObj, err := appTestWrapper.CreateApplication()
	s.Require().NoError(err)

	// Step 2 and 3
	appObj, err = appTestWrapper.GenerateIndication()
	s.Require().NoError(err)

	// Step 4, 5, & 6
	appObj, err = appTestWrapper.GenerateAndSelectIndicationC(
		test_utils.GenerateAndSelectIndicationParams{Coverages: test_utils.CoverageAL},
	)
	s.Require().Nil(err)
	opts, err := s.h.Api.GetIndicationOptions(appObj.ID)
	s.Require().Nil(err)
	s.Require().NotNil(opts.CurrentDeductibles)
	s.Require().Len(opts.CurrentDeductibles, 1)
	s.Require().NotNil(opts.DeductiblesOptions)
	s.Require().Len(opts.DeductiblesOptions, 1)
}

// This test focuses on the indication form. We first create an application and
// then simulate the 3 steps of the indication flow, where each step adds new
// information. We follow these steps:
// 1. POST to Create application
// 2. PUT to fill operations form
// 3. PUT to fill classes and commodities form
// 4. PUT to fill loss run summary form
func (s *indicationApisTestSuite) TestAppIndicationForm() {
	session, err := s.h.CreateRandomUserAndSession()
	s.Require().Nil(err)
	agencyId := s.h.Api.GetAgencyId()
	s.Require().NotNil(agencyId)
	// Step 1
	appTestWrapper, err := s.h.TestWrappers.AppTestWrapperCreator()
	s.Require().NoError(err)
	appObj, err := appTestWrapper.CreateApplication()
	s.Require().NoError(err)
	appId := appObj.ID
	s.Require().NotNil(appId)
	app, err := s.h.Api.GetApplication(appId)
	s.Require().Nil(err)
	// Step 2
	fileHandleId, err := s.h.CreateFileHandle("TestKey",
		*agencyId, file_enums.FileDestinationGroupQuoting)
	s.Require().Nil(err)
	operationsForm := test_utils.MockOperationsForm(session.UserID, fileHandleId)
	err = s.h.Api.PutIndicationForm(
		appId,
		oapi_app.IndicationForm{
			OperationsForm: operationsForm,
		})
	s.Require().Nil(err)
	app, err = s.h.Api.GetApplication(appId)
	s.Require().Nil(err)
	s.Require().NotNil(app.IndicationForm)
	s.Require().NotNil(app.IndicationForm.OperationsForm)
	opsFormWithDefaultDed, err := test_utils.AddDefaultDeductibles(
		app.IndicationForm.OperationsForm)
	s.Require().Nil(err)
	s.Require().Equal(app.IndicationForm.OperationsForm, opsFormWithDefaultDed)
	s.Require().Nil(app.IndicationForm.ClassesAndCommoditiesForm)
	s.Require().Nil(app.IndicationForm.LossRunSummaryForm)
	// Step 3
	clsAndComForm := test_utils.MockClassesAndCommoditiesForm()
	err = s.h.Api.PutIndicationForm(
		appId,
		oapi_app.IndicationForm{
			OperationsForm:            operationsForm,
			ClassesAndCommoditiesForm: clsAndComForm,
		})
	s.Require().Nil(err)
	app, err = s.h.Api.GetApplication(appId)
	s.Require().Nil(err)
	s.Require().NotNil(app.IndicationForm)
	s.Require().NotNil(app.IndicationForm.OperationsForm)
	s.Require().Equal(app.IndicationForm.OperationsForm, opsFormWithDefaultDed)
	s.Require().NotNil(app.IndicationForm.ClassesAndCommoditiesForm)

	// Setting Label as `nil` so that we can check equality
	// The Request doesn't need the label field but while fetching
	// from DB we populate the label field
	app.IndicationForm.ClassesAndCommoditiesForm.PrimaryCategory.Label = nil
	s.Require().Equal(app.IndicationForm.ClassesAndCommoditiesForm, clsAndComForm)
	s.Require().Nil(app.IndicationForm.LossRunSummaryForm)
	// Step 4
	lossRunSumForm := test_utils.MockLossRunSummaryForm()
	err = s.h.Api.PutIndicationForm(
		appId,
		oapi_app.IndicationForm{
			OperationsForm:            operationsForm,
			ClassesAndCommoditiesForm: clsAndComForm,
			LossRunSummaryForm:        lossRunSumForm,
		})
	s.Require().Nil(err)
	app, err = s.h.Api.GetApplication(appId)
	s.Require().Nil(err)
	s.Require().NotNil(app.IndicationForm.OperationsForm)
	s.Require().Equal(app.IndicationForm.OperationsForm, opsFormWithDefaultDed)
	s.Require().NotNil(app.IndicationForm.ClassesAndCommoditiesForm)

	// Setting Label as `nil` so that we can check equality
	// The Request doesn't need the label field but while fetching
	// from DB we populate the label field
	app.IndicationForm.ClassesAndCommoditiesForm.PrimaryCategory.Label = nil
	s.Require().Equal(app.IndicationForm.ClassesAndCommoditiesForm, clsAndComForm)
	s.Require().NotNil(app.IndicationForm.LossRunSummaryForm)
	s.Require().Equal(app.IndicationForm.LossRunSummaryForm, lossRunSumForm)
	s.Require().NoError(appTestWrapper.Validator.AppState(oapi_app.ApplicationStateInProgress))
}

// TestAppAdditionalInfoForm tests updating additional information form.
// In order to access this actions, we need to pass the application all the
// way through until the options are selected.
func (s *indicationApisTestSuite) TestAppAdditionalInfoForm() {
	_, err := s.h.CreateRandomUserAndSession()
	s.Require().Nil(err)
	// Step 1
	appTestWrapper, err := s.h.TestWrappers.AppTestWrapperCreator()
	s.Require().NoError(err)
	appObj, err := appTestWrapper.CreateApplication()
	s.Require().NoError(err)
	appId := appObj.ID
	s.Require().NotNil(appId)
	appObj, err = appTestWrapper.GenerateAndSelectIndication()
	s.Require().NoError(err)
	err = appTestWrapper.SubmitApplicationForUWReview()
	s.Require().NoError(err)
	// Form is empty
	app, err := s.h.Api.GetApplication(appId)
	s.Require().Nil(err)

	// Now add another field
	updatedForm := *app.AdditionalInfoForm
	updatedForm.Commodities = &[]oapi_app.AdditionalInformationCommodity{
		oapi_app.AddlInfoDoubleOrTripleTrailers,
	}
	err = s.h.Api.PutApplicationAdditionalInfo(appId, updatedForm)
	s.Require().NoError(err)
	app, err = s.h.Api.GetApplication(appId)
	s.Require().Nil(err)
	s.Require().NotNil(app.AdditionalInfoForm)
	s.Require().Len(*(app.AdditionalInfoForm.Commodities), 1)

	// Test excluding coverage metadata info
	updatedForm = *app.AdditionalInfoForm
	updatedForm.CoverageMetadata = nil
	err = s.h.Api.PutApplicationAdditionalInfo(appId, updatedForm)
	s.Require().NoError(err)
	app, err = s.h.Api.GetApplication(appId)
	s.Require().Nil(err)
	s.Require().Nil(app.AdditionalInfoForm.CoverageMetadata)
	// Test updating coverage metadata info
	updatedForm.CoverageMetadata = &oapi_app.AdditionalInfoCoverageMetadata{
		AdditionalIncumbentInfo: oapi_app.AdditionalIncumbentInfo{
			IsALIncumbent: false,
		},
	}
	// test updating target price info
	targetPrices := make([]oapi_app.TargetPrice, 0)
	targetPrices = append(targetPrices, oapi_app.TargetPrice{
		TotalPremium: pointer_utils.Float32(65000),
		CoverageType: oapi_common.CoverageAutoLiability,
	})
	updatedForm.TargetPrices = &targetPrices
	err = s.h.Api.PutApplicationAdditionalInfo(appId, updatedForm)
	s.Require().NoError(err)
	app, err = s.h.Api.GetApplication(appId)
	s.Require().Nil(err)
	s.Require().NotNil(app.AdditionalInfoForm.CoverageMetadata)
	s.Require().False(app.AdditionalInfoForm.CoverageMetadata.AdditionalIncumbentInfo.IsALIncumbent)
	s.Require().NotNil(app.AdditionalInfoForm.TargetPrices)
	s.Require().Len(*app.AdditionalInfoForm.TargetPrices, 1)
	s.Require().Equal(*(*app.AdditionalInfoForm.TargetPrices)[0].TotalPremium, float32(65000))
	s.Require().Equal((*app.AdditionalInfoForm.TargetPrices)[0].CoverageType, oapi_common.CoverageAutoLiability)
}

// TestAppDecline tests the decline API endpoint through out multiple steps
// of the quoting process.
func (s *indicationApisTestSuite) TestAppDecline() {
	_, err := s.h.CreateRandomUserAndSession()
	s.Require().Nil(err)
	// Decline on submitted
	appTestWrapper, err := s.h.TestWrappers.AppTestWrapperCreator()
	s.Require().NoError(err)
	appObj, err := appTestWrapper.CreateApplication()
	s.Require().NoError(err)
	s.Require().NoError(s.h.Api.PostAppDecline(appObj.ID, oapi_app.DeclineAppForm{Description: "decline"}))
	s.Require().NoError(appTestWrapper.Validator.AppState(oapi_app.ApplicationStateDeclined))

	// Decline on indication generated
	appTestWrapper, err = s.h.TestWrappers.AppTestWrapperCreator()
	s.Require().NoError(err)
	appObj, err = appTestWrapper.CreateApplication()
	s.Require().NoError(err)
	appObj, err = appTestWrapper.GenerateIndication()
	s.Require().NoError(err)
	s.Require().NoError(s.h.Api.PostAppDecline(appObj.ID, oapi_app.DeclineAppForm{Description: "decline"}))
	s.Require().NoError(appTestWrapper.Validator.AppState(oapi_app.ApplicationStateDeclined))

	// Decline on indication selected - additional info
	appTestWrapper, err = s.h.TestWrappers.AppTestWrapperCreator()
	s.Require().NoError(err)
	appObj, err = appTestWrapper.CreateApplication()
	s.Require().NoError(err)
	_, err = appTestWrapper.GenerateAndSelectIndication()
	s.Require().NoError(err)
	s.Require().NoError(s.h.Api.PostAppDecline(appObj.ID, oapi_app.DeclineAppForm{Description: "decline"}))
	s.Require().NoError(appTestWrapper.Validator.AppState(oapi_app.ApplicationStateDeclined))

	// Decline on UW review
	appTestWrapper, err = s.h.TestWrappers.AppTestWrapperCreator()
	s.Require().NoError(err)
	appObj, err = appTestWrapper.CreateApplication()
	s.Require().NoError(err)
	_, err = appTestWrapper.GenerateAndSelectIndication()
	s.Require().NoError(err)
	err = appTestWrapper.SubmitApplicationForUWReview()
	s.Require().NoError(err)
	s.Require().NoError(s.h.Api.PostAppDecline(appObj.ID, oapi_app.DeclineAppForm{Description: "decline"}))
	s.Require().NoError(appTestWrapper.Validator.AppState(oapi_app.ApplicationStateDeclined))

	// Decline on UW review (after consent granted)
	appTestWrapper, err = s.h.TestWrappers.AppTestWrapperCreator()
	s.Require().NoError(err)
	appObj, err = appTestWrapper.CreateApplication()
	s.Require().NoError(err)
	_, err = appTestWrapper.GenerateAndSelectIndication()
	s.Require().NoError(err)
	err = appTestWrapper.SubmitApplicationForUWReview()
	s.Require().NoError(err)
	s.Require().NoError(appTestWrapper.Validator.AppState(oapi_app.ApplicationStatePendingELDTelematics))
	s.Require().NoError(appTestWrapper.Validator.StatusTrackerAppSubmitted())
	s.Require().NoError(appTestWrapper.ConnectTSPC(telematics.TSPSamsara, telematics.ConsentKindOAuth, true))
	s.Require().NoError(appTestWrapper.Validator.AppState(oapi_app.ApplicationStateProcessingELDTelematics))
	s.Require().NoError(s.h.Api.PostAppDecline(appObj.ID, oapi_app.DeclineAppForm{Description: "decline"}))
	s.Require().NoError(appTestWrapper.Validator.AppState(oapi_app.ApplicationStateDeclined))
}

// TestPatchAppBasicInfoFields tests updating basic information form.
func (s *indicationApisTestSuite) TestPatchAppBasicInfoFields() {
	err := s.h.LoginDefaultAgent()
	s.Require().NoError(err)
	appTestWrapper, err := s.h.TestWrappers.AppTestWrapperCreator()
	s.Require().NoError(err)
	appObj, err := appTestWrapper.CreateApplication()
	s.Require().NoError(err)
	appId := appObj.ID
	s.Require().NoError(s.testUpdateBasicInfo(appId, appTestWrapper.Validator.ValidateBasicInfoFormUpdate))
	// We generate indication to generate the producer id which was
	// nil before
	appObj, err = appTestWrapper.GenerateIndication()
	s.Require().NoError(err)
	s.Require().NoError(s.testUpdateBasicInfo(appId, appTestWrapper.Validator.ValidateBasicInfoFormUpdate))

	// Check for indication generated state after selecting option
	appObj, err = appTestWrapper.GenerateAndSelectIndication()
	s.Require().NoError(err)
	s.Require().NoError(s.testUpdateBasicInfo(appId, appTestWrapper.Validator.ValidateBasicInfoFormUpdate))

	// Check for uw review
	err = s.h.LoginDefaultAgent()
	s.Require().NoError(err)
	err = appTestWrapper.SubmitApplicationForUWReview()
	s.Require().NoError(err)
	appObj, err = appTestWrapper.GenerateAndSelectIndication()
	s.Require().NoError(err)
	s.Require().NoError(s.testUpdateBasicInfo(appId, appTestWrapper.Validator.ValidateBasicInfoFormUpdate))
}

// We login with the support user and update the created by , producer id
// and agency id for a new agency and user
func (s *indicationApisTestSuite) testUpdateBasicInfo(
	appId string,
	basicInfoCheckFn func(prevApp *application.Application,
		nextAppForm *oapi_app.ApplicationBasicInfo,
		details *oapi_app.ApplicationBasicInfoForm) error,
) error {
	appObj, err := s.h.Metadata.GetApplication(appId)
	if err != nil {
		return errors.Wrapf(err, "failed to get application for id %s", appId)
	}
	originalProdID := appObj.ProducerID
	if appObj.ProducerID == nil {
		originalProdID = pointer_utils.String(appObj.CreatedBy)
	}
	originalCreatedID := pointer_utils.String(appObj.CreatedBy)
	originalAgencyID := pointer_utils.String(appObj.AgencyID.String())
	// Creating a random user to get agency id , producer id to patch
	newSession, _, _, newAgencyID, err := s.h.CreateRandomUserAndSessionC()
	if err != nil {
		return errors.Wrap(err, "unable to create user and session")
	}
	err = s.h.LoginDefaultSupportUser()
	if err != nil {
		return errors.Wrap(err, "unable to login with default support user")
	}
	// Patch with empty company name
	basicInfoForm := s.h.MockBasicInfoFieldsData(
		pointer_utils.String(newAgencyID.String()),
		pointer_utils.String(newSession.UserID.String()),
		pointer_utils.String(newSession.UserID.String()),
	)
	basicInfoForm.CompanyName = nil
	err = s.h.Api.PatchAppBasicInfo(appId, basicInfoForm)
	if err != nil {
		return errors.Wrapf(err, "failed to patch application for id %s "+
			"with empty company name", appId)
	}
	newAppForm, err := s.h.Api.GetAppBasicInfo(appId)
	if err != nil {
		return errors.Wrapf(err, "failed to get application for id %s ", appId)
	}
	err = basicInfoCheckFn(appObj, newAppForm, &basicInfoForm)
	if err != nil {
		return errors.Wrap(err, "failed to validate basic info form "+
			"with empty company name")
	}

	// Patch with Empty Created By
	basicInfoForm = s.h.MockBasicInfoFieldsData(
		pointer_utils.String(newAgencyID.String()),
		pointer_utils.String(newSession.UserID.String()),
		pointer_utils.String(newSession.UserID.String()),
	)
	basicInfoForm.CreatedBy = nil
	appObj, err = s.h.Metadata.GetApplication(appId)
	if err != nil {
		return errors.Wrapf(err, "failed to get application for id %s", appId)
	}
	err = s.h.Api.PatchAppBasicInfo(appId, basicInfoForm)
	if err != nil {
		return errors.Wrapf(err, "failed to patch application id %s with"+
			" empty created by", appId)
	}
	newAppForm, err = s.h.Api.GetAppBasicInfo(appId)
	if err != nil {
		return errors.Wrap(err, "unable to fetch application basic info")
	}
	err = basicInfoCheckFn(appObj, newAppForm, &basicInfoForm)
	if err != nil {
		return errors.Wrap(err, "failed to validate basic info form with "+
			"created by as nil")
	}

	// Patch with empty ProducerID
	basicInfoForm = s.h.MockBasicInfoFieldsData(
		pointer_utils.String(newAgencyID.String()),
		pointer_utils.String(newSession.UserID.String()),
		pointer_utils.String(newSession.UserID.String()),
	)
	basicInfoForm.ProducerID = nil
	appObj, err = s.h.Metadata.GetApplication(appId)
	if err != nil {
		return errors.Wrapf(err, "failed to get application for id %s", appId)
	}
	err = s.h.Api.PatchAppBasicInfo(appId, basicInfoForm)
	if err != nil {
		return errors.Wrapf(err, "failed to patch application for id %s with "+
			"empty producer id", appId)
	}
	newAppForm, err = s.h.Api.GetAppBasicInfo(appId)
	if err != nil {
		return errors.Wrap(err, "unable to fetch application basic info")
	}
	err = basicInfoCheckFn(appObj, newAppForm, &basicInfoForm)
	if err != nil {
		return errors.Wrap(err, "failed to validate basic info form "+
			"with empty producer id")
	}

	// Patch with empty InsuredName
	basicInfoForm = s.h.MockBasicInfoFieldsData(
		pointer_utils.String(newAgencyID.String()),
		pointer_utils.String(newSession.UserID.String()),
		pointer_utils.String(newSession.UserID.String()),
	)
	basicInfoForm.InsuredName = nil
	appObj, err = s.h.Metadata.GetApplication(appId)
	if err != nil {
		return errors.Wrapf(err, "failed to get application for id %s", appId)
	}
	err = s.h.Api.PatchAppBasicInfo(appId, basicInfoForm)
	if err != nil {
		return errors.Wrapf(err, "failed to patch application for id %s "+
			"with empty insured name", appId)
	}
	newAppForm, err = s.h.Api.GetAppBasicInfo(appId)
	if err != nil {
		return errors.Wrap(err, "unable to fetch application basic info")
	}
	err = basicInfoCheckFn(appObj, newAppForm, &basicInfoForm)
	if err != nil {
		return errors.Wrap(err, "failed to validate basic info form")
	}

	// Patch with empty InsuredEmail
	basicInfoForm = s.h.MockBasicInfoFieldsData(
		pointer_utils.String(newAgencyID.String()),
		pointer_utils.String(newSession.UserID.String()),
		pointer_utils.String(newSession.UserID.String()),
	)
	basicInfoForm.InsuredEmail = nil
	appObj, err = s.h.Metadata.GetApplication(appId)
	if err != nil {
		return errors.Wrapf(err, "failed to get application for id %s", appId)
	}
	err = s.h.Api.PatchAppBasicInfo(appId, basicInfoForm)
	if err != nil {
		return errors.Wrapf(err, "failed to patch application for id %s with "+
			"empty insured email", appId)
	}
	newAppForm, err = s.h.Api.GetAppBasicInfo(appId)
	if err != nil {
		return errors.Wrap(err, "unable to fetch application basic info")
	}
	err = basicInfoCheckFn(appObj, newAppForm, &basicInfoForm)
	if err != nil {
		return errors.Wrap(err, "failed to validate basic info form with"+
			" empty insured email")
	}
	err = s.h.Api.PatchAppBasicInfo(appId, oapi_app.ApplicationBasicInfoForm{
		AgencyID:   originalAgencyID,
		CreatedBy:  originalCreatedID,
		ProducerID: originalProdID,
	})
	if err == nil {
		return errors.Wrap(err, "unable to patch back to original ids")
	}
	return nil
}

// TestGLSelectionAndLocationsInput is a temporary test used to test GL
// selection and sending the terminal locations input.
func (s *indicationApisTestSuite) TestGLSelectionAndLocationsInput() {
	appTestWrapper, err := s.h.TestWrappers.AppTestWrapperCreator()
	s.Require().NoError(err)
	appObj, err := appTestWrapper.CreateApplication()
	s.Require().NoError(err)

	operationsForm := test_utils.MockOperationsFormC(
		appTestWrapper.User.ID, pointer_utils.UUID(uuid.New()), test_utils.CoveragesALAndAPDAndGL, nil)
	clsAndComForm := test_utils.MockClassesAndCommoditiesForm()
	lossRunSumForm := test_utils.MockLossRunSummaryFormC(test_utils.CoveragesALAndAPDAndGL)
	s.Require().NoError(s.h.Api.PutIndicationForm(
		appObj.ID,
		oapi_app.IndicationForm{
			OperationsForm:            operationsForm,
			ClassesAndCommoditiesForm: clsAndComForm,
			LossRunSummaryForm:        lossRunSumForm,
		}))
	s.Require().NoError(s.h.Api.PostIndicationSubmit(appObj.ID))
	s.Require().Nil(s.waitForModelRun(appObj.ID))
}

// TestMTCSelectionAndCommoditiesInput is a temporary test used to test MTC
// selection and sending the commodities list input.
func (s *indicationApisTestSuite) TestMTCSelectionAndCommoditiesInput() {
	appTestWrapper, err := s.h.TestWrappers.AppTestWrapperCreator()
	s.Require().NoError(err)
	appObj, err := appTestWrapper.CreateApplicationC(test_utils.CreateAppicationParams{2081158})
	s.Require().NoError(err)

	operationsForm := test_utils.MockOperationsFormC(
		appTestWrapper.User.ID, pointer_utils.UUID(uuid.New()), test_utils.CoveragesALAndMTC, nil)
	clsAndComForm := test_utils.MockClassesAndCommoditiesFormC(test_utils.CoveragesALAndMTC)
	lossRunSumForm := test_utils.MockLossRunSummaryFormC(test_utils.CoveragesALAndMTC)
	s.Require().NoError(s.h.Api.PutIndicationForm(
		appObj.ID,
		oapi_app.IndicationForm{
			OperationsForm:            operationsForm,
			ClassesAndCommoditiesForm: clsAndComForm,
			LossRunSummaryForm:        lossRunSumForm,
		}))
	s.Require().NoError(s.h.Api.PostIndicationSubmit(appObj.ID))
	s.Require().Nil(s.waitForModelRun(appObj.ID))

	app, err := s.h.Metadata.GetApplication(appObj.ID)
	s.Require().Nil(err)

	s.Require().NotNil(app.EquipmentInfo.CommodityDistribution)
	s.Require().Equal(3, len(app.EquipmentInfo.CommodityDistribution.Commodities))
}

// TestMTCIndication tests running indications for MTC.
// Note: only running for IL application for now, but will extend to other
// states once implemented.
func (s *indicationApisTestSuite) TestMTCIndication() {
	appTestWrapper, err := s.h.TestWrappers.AppTestWrapperCreator()
	s.Require().NoError(err)
	_, err = appTestWrapper.CreateApplicationC(test_utils.CreateAppicationParams{DotNumber: 2081158})
	s.Require().NoError(err)
	app, err := appTestWrapper.GenerateIndicationC(
		test_utils.GenerateIndicationParams{Coverages: test_utils.CoveragesALAndMTC},
	)
	s.Require().NoError(err)
	opts, err := s.h.Api.GetIndicationOptions(app.ID)
	s.Require().Nil(err)
	opt, err := opts.GetOptionForTag(app_enums.IndicationOptionTagStandard)
	s.Require().Nil(err)
	mtc, err := opt.GetCoverage(app_enums.CoverageMotorTruckCargo)
	s.Require().Nil(err)
	s.Require().NotNil(mtc.Premium)
	s.Require().NotNil(mtc.PremiumPerHundredMiles)
	s.Require().NotNil(mtc.PremiumPerUnit)
	s.Require().NotNil(mtc.Deductible)
	s.Require().NotNil(mtc.Limit)
}

// TestALAndGLIndication tests running an indication for all coverage
// combinations.
func (s *indicationApisTestSuite) TestIndicationForAllCovsCombinations() {
	appTestWrapper, err := s.h.TestWrappers.AppTestWrapperCreator()
	s.Require().NoError(err)
	_, err = appTestWrapper.CreateApplication()
	s.Require().NoError(err)
	// AL only
	_, err = appTestWrapper.GenerateAndSelectIndicationC(
		test_utils.GenerateAndSelectIndicationParams{
			Coverages: test_utils.CoverageAL,
		},
	)
	s.Require().NoError(err)
	// AL + APD only
	_, err = appTestWrapper.GenerateAndSelectIndicationC(
		test_utils.GenerateAndSelectIndicationParams{
			Coverages: test_utils.CoveragesALAndAPD,
		},
	)
	s.Require().NoError(err)
	// AL + GL only
	_, err = appTestWrapper.GenerateAndSelectIndicationC(
		test_utils.GenerateAndSelectIndicationParams{
			Coverages: test_utils.CoveragesALAndGL,
		},
	)
	s.Require().NoError(err)
	// AL + APD + GL only
	_, err = appTestWrapper.GenerateAndSelectIndicationC(
		test_utils.GenerateAndSelectIndicationParams{
			Coverages: test_utils.CoveragesALAndAPDAndGL,
		},
	)
	s.Require().NoError(err)
}

// TestSmartIndications tests the smart indication functionality, by taking one
// DOT and running an indication with different previous carriers. This enables
// us to check the discounts that are being granted.
func (s *indicationApisTestSuite) TestSmartIndications() {
	err := s.h.LoginDefaultAgent()
	s.Require().NoError(err)
	appTestWrapper, err := s.h.TestWrappers.AppTestWrapperCreator()
	s.Require().NoError(err)

	// Calculate smart indications for quartile 1 indications
	// Get baseline indication with a C-graded carrier
	_, err = appTestWrapper.CreateApplicationC(
		test_utils.CreateAppicationParams{DotNumber: 1},
	)
	s.Require().NoError(err)
	app, err := appTestWrapper.GenerateAndSelectIndication()
	s.Require().NoError(err)
	opts, err := s.h.Api.GetIndicationOptions(app.ID)
	s.Require().Nil(err)
	opt, err := opts.GetOptionForTag(app_enums.IndicationOptionTagStandard)
	s.Require().Nil(err)
	baseline := opt.TotalPremium

	// Get with C-graded carrier
	_, err = appTestWrapper.CreateApplicationC(
		test_utils.CreateAppicationParams{DotNumber: 2},
	)
	s.Require().NoError(err)
	app, err = appTestWrapper.GenerateAndSelectIndication()
	s.Require().NoError(err)
	opts, err = s.h.Api.GetIndicationOptions(app.ID)
	s.Require().Nil(err)
	opt, err = opts.GetOptionForTag(app_enums.IndicationOptionTagStandard)
	s.Require().Nil(err)
	cGraded := opt.TotalPremium

	// Get with B-graded carrier
	_, err = appTestWrapper.CreateApplicationC(
		test_utils.CreateAppicationParams{DotNumber: 3},
	)
	s.Require().NoError(err)
	app, err = appTestWrapper.GenerateAndSelectIndication()
	s.Require().NoError(err)
	opts, err = s.h.Api.GetIndicationOptions(app.ID)
	s.Require().Nil(err)
	opt, err = opts.GetOptionForTag(app_enums.IndicationOptionTagStandard)
	s.Require().Nil(err)
	bGraded := opt.TotalPremium

	// Get with A-graded carrier
	_, err = appTestWrapper.CreateApplicationC(
		test_utils.CreateAppicationParams{DotNumber: 536917},
	)
	s.Require().NoError(err)
	app, err = appTestWrapper.GenerateAndSelectIndication()
	s.Require().NoError(err)
	opts, err = s.h.Api.GetIndicationOptions(app.ID)
	s.Require().Nil(err)
	opt, err = opts.GetOptionForTag(app_enums.IndicationOptionTagStandard)
	s.Require().Nil(err)
	aGraded := opt.TotalPremium

	// Check discounts
	// A-graded carriers should contain 15% discounts (schedule mods) that
	// are equivalent to 14% total discount for quartile 1 indications.
	s.Require().Equal(0.7, math.Round(float64(aGraded)/float64(baseline)*100)/100)
	// B-graded carriers should contain 10% discounts (schedule mods) that
	// are equivalent to 9% total discount for quartile 1 indications.
	s.Require().Equal(0.76, math.Round(float64(bGraded)/float64(baseline)*100)/100)
	// C-graded carriers should not contain any discount
	s.Require().Equal(cGraded, baseline)

	// Manually generate quartile 0 indications by exploding losses and reducing
	// number of vehicles
	covs := test_utils.CoveragesALAndAPDAndGL
	operationsForm := test_utils.MockOperationsFormC(appTestWrapper.User.ID, pointer_utils.UUID(uuid.New()), covs, nil)
	clsAndComForm := test_utils.MockClassesAndCommoditiesForm()
	lossRunSumForm := test_utils.MockLossRunSummaryFormC(covs)
	lossRunSumFormCopy := *lossRunSumForm
	for covIdx := range *lossRunSumForm {
		for yearIdx := range (*lossRunSumForm)[covIdx].LossRunSummary {
			lossRunSumFormCopy[covIdx].LossRunSummary[yearIdx].LossIncurred += 200000
		}
	}
	// Get baseline indication with a C-graded carrier
	app, err = appTestWrapper.CreateApplicationC(
		test_utils.CreateAppicationParams{DotNumber: 1},
	)
	s.Require().NoError(err)
	s.Require().NoError(s.h.Api.PutIndicationForm(
		app.ID,
		oapi_app.IndicationForm{
			OperationsForm:            operationsForm,
			ClassesAndCommoditiesForm: clsAndComForm,
			LossRunSummaryForm:        &lossRunSumFormCopy,
		}),
	)
	s.Require().NoError(s.h.Api.PostIndicationSubmit(app.ID))
	s.Require().NoError(s.waitForModelRun(app.ID))
	opts, err = s.h.Api.GetIndicationOptions(app.ID)
	s.Require().Nil(err)
	opt, err = opts.GetOptionForTag(app_enums.IndicationOptionTagStandard)
	s.Require().Nil(err)
	baseline = opt.TotalPremium
	// Get with B-graded carrier
	app, err = appTestWrapper.CreateApplicationC(
		test_utils.CreateAppicationParams{DotNumber: 3},
	)
	s.Require().NoError(err)
	s.Require().NoError(s.h.Api.PutIndicationForm(
		app.ID,
		oapi_app.IndicationForm{
			OperationsForm:            operationsForm,
			ClassesAndCommoditiesForm: clsAndComForm,
			LossRunSummaryForm:        &lossRunSumFormCopy,
		}),
	)
	s.Require().NoError(s.h.Api.PostIndicationSubmit(app.ID))
	s.Require().NoError(s.waitForModelRun(app.ID))
	opts, err = s.h.Api.GetIndicationOptions(app.ID)
	s.Require().Nil(err)
	opt, err = opts.GetOptionForTag(app_enums.IndicationOptionTagStandard)
	s.Require().Nil(err)
	bGraded = opt.TotalPremium

	// Get with A-graded carrier
	app, err = appTestWrapper.CreateApplicationC(
		test_utils.CreateAppicationParams{DotNumber: 536917},
	)
	s.Require().NoError(err)
	s.Require().NoError(s.h.Api.PutIndicationForm(
		app.ID,
		oapi_app.IndicationForm{
			OperationsForm:            operationsForm,
			ClassesAndCommoditiesForm: clsAndComForm,
			LossRunSummaryForm:        &lossRunSumFormCopy,
		}),
	)
	s.Require().NoError(s.h.Api.PostIndicationSubmit(app.ID))
	s.Require().NoError(s.waitForModelRun(app.ID))
	opts, err = s.h.Api.GetIndicationOptions(app.ID)
	s.Require().Nil(err)
	opt, err = opts.GetOptionForTag(app_enums.IndicationOptionTagStandard)
	s.Require().Nil(err)
	aGraded = opt.TotalPremium
	// Check discounts
	// A-graded carriers should contain 35% discounts (schedule mods) that
	// are equivalent to 32% total discount for quartile 0 indications.
	s.Require().Equal(0.71, math.Round(float64(aGraded)/float64(baseline)*100)/100)
	// B-graded carriers should contain 25% discounts (schedule mods) that
	// are equivalent to 24% total discount for quartile 0 indications.
	s.Require().Equal(0.77, math.Round(float64(bGraded)/float64(baseline)*100)/100)
}

// This test runs the entire flow from creating an application and running indication
// and then adding combined deductibles (both valid & invalid coverages) in the Indication Options
// screen and then removing it, following are the steps:
// 1. POST to create application
// 2. Check correct indication for normal case - (Non-combined coverages)
// 3. Check correct indication for combined coverages case
// 4. Check error when trying to input incorrect combined deductibles
// 5. Check error when trying to input coverages with different deductibles which are combined
// 6. Check if combined deductibles are getting removed successfully in PUT indication
func (s *indicationApisTestSuite) TestAppCombinedDeductibles() {
	session, err := s.h.CreateRandomUserAndSession()
	s.Require().Nil(err)
	agencyId := s.h.Api.GetAgencyId()
	s.Require().NotNil(agencyId)

	// Step 1
	appetiteForm := s.h.MockAppetiteFormC(2081158, test_utils.DefaultEffectiveDate, 30)
	appResp, err := s.h.Api.PostApplication(appetiteForm, *agencyId)
	s.Require().Nil(err)
	appId := appResp.ApplicationID
	s.Require().NotNil(appId)

	// Step 2
	fileHandleId, err := s.h.CreateFileHandle("TestKey",
		*agencyId, file_enums.FileDestinationGroupQuoting)
	s.Require().Nil(err)
	covs := test_utils.CoveragesALAndAPDAndGLAndMTC
	operationsForm := test_utils.MockOperationsFormC(session.UserID, fileHandleId,
		covs, nil)
	clsAndComForm := test_utils.MockClassesAndCommoditiesFormC(covs)
	lossRunSumForm := test_utils.MockLossRunSummaryFormC(covs)
	err = s.h.Api.PutIndicationForm(
		appId,
		oapi_app.IndicationForm{
			OperationsForm:            operationsForm,
			ClassesAndCommoditiesForm: clsAndComForm,
			LossRunSummaryForm:        lossRunSumForm,
		})
	s.Require().Nil(err)
	err = s.h.Api.PostIndicationSubmit(appId)
	s.Require().Nil(err)
	s.Require().Nil(s.waitForModelRun(appId))

	// Step 3
	covs = test_utils.CoveragesALAndAPDAndGLAndMTCWithSameDeductibles
	combinedCoverages := []oapi_common.CombinedCoverages{
		[]oapi_common.CoverageType{
			oapi_common.CoverageMotorTruckCargo,
			oapi_common.CoverageAutoPhysicalDamage,
		},
	}
	operationsForm = test_utils.MockOperationsFormC(session.UserID, fileHandleId,
		covs, &combinedCoverages)
	err = s.h.Api.PutIndicationForm(
		appId,
		oapi_app.IndicationForm{
			OperationsForm:            operationsForm,
			ClassesAndCommoditiesForm: clsAndComForm,
			LossRunSummaryForm:        lossRunSumForm,
		})
	s.Require().Nil(err)
	appObj, err := s.h.Metadata.GetApplication(appId)
	s.Require().Nil(err)
	s.Require().Equal(1, len(appObj.CoverageInfo.CoveragesWithCombinedDeductibles.CombinedCoveragesList))
	err = s.h.Api.PostIndicationSubmit(appId)
	s.Require().Nil(err)
	s.Require().Nil(s.waitForModelRun(appId))

	// Step 4
	covs = test_utils.CoveragesALAndAPDAndGLAndMTCWithSameDeductibles
	combinedCoverages = []oapi_common.CombinedCoverages{
		[]oapi_common.CoverageType{
			oapi_common.CoverageAutoLiability,
			oapi_common.CoverageAutoPhysicalDamage,
		},
	}
	operationsForm = test_utils.MockOperationsFormC(session.UserID, fileHandleId,
		covs, &combinedCoverages)
	err = s.h.Api.PutIndicationForm(
		appId,
		oapi_app.IndicationForm{
			OperationsForm:            operationsForm,
			ClassesAndCommoditiesForm: clsAndComForm,
			LossRunSummaryForm:        lossRunSumForm,
		})
	s.Require().NotNil(err)

	// Step 5
	covs = test_utils.CoveragesALAndAPDAndGLAndMTCWithDifferentDeductibles
	combinedCoverages = []oapi_common.CombinedCoverages{
		[]oapi_common.CoverageType{
			oapi_common.CoverageMotorTruckCargo,
			oapi_common.CoverageAutoPhysicalDamage,
		},
	}
	operationsForm = test_utils.MockOperationsFormC(session.UserID, fileHandleId,
		covs, &combinedCoverages)
	err = s.h.Api.PutIndicationForm(
		appId,
		oapi_app.IndicationForm{
			OperationsForm:            operationsForm,
			ClassesAndCommoditiesForm: clsAndComForm,
			LossRunSummaryForm:        lossRunSumForm,
		})
	s.Require().NotNil(err)

	// Step 6
	operationsForm = test_utils.MockOperationsFormC(session.UserID, fileHandleId,
		test_utils.CoveragesALAndAPDAndMTC, nil)
	err = s.h.Api.PutIndicationForm(
		appId,
		oapi_app.IndicationForm{
			OperationsForm:            operationsForm,
			ClassesAndCommoditiesForm: clsAndComForm,
			LossRunSummaryForm:        lossRunSumForm,
		})
	s.Require().Nil(err)
	appObj, err = s.h.Metadata.GetApplication(appId)
	s.Require().Nil(err)
	s.Require().Equal(
		(*application.CombinedDeductibleCoverages)(nil),
		appObj.CoverageInfo.CoveragesWithCombinedDeductibles,
	)
}

// TestLockingSmartIndications tests locking of smart discount on initial
// indication and verifying that the same discount is applied even if
// the quartile changes
func (s *indicationApisTestSuite) TestLockingSmartIndications() {
	appTestWrapper, err := s.h.TestWrappers.AppTestWrapperCreator()
	s.Require().NoError(err)

	// Get with A-graded carrier
	app, err := appTestWrapper.CreateApplicationC(
		test_utils.CreateAppicationParams{DotNumber: 536917},
	)
	s.Require().NoError(err)

	// Manually generate quartile 0 indications by exploding losses and reducing
	// number of vehicles
	covs := test_utils.CoveragesALAndAPDAndGL
	operationsForm := test_utils.MockOperationsFormC(appTestWrapper.User.ID, pointer_utils.UUID(uuid.New()), covs, nil)
	clsAndComForm := test_utils.MockClassesAndCommoditiesForm()
	lossRunSumForm := test_utils.MockLossRunSummaryFormC(covs)
	lossRunSumFormCopy := *lossRunSumForm
	for covIdx := range *lossRunSumForm {
		for yearIdx := range (*lossRunSumForm)[covIdx].LossRunSummary {
			lossRunSumFormCopy[covIdx].LossRunSummary[yearIdx].LossIncurred += 200000
		}
	}
	s.Require().NoError(s.h.Api.PutIndicationForm(
		app.ID,
		oapi_app.IndicationForm{
			OperationsForm:            operationsForm,
			ClassesAndCommoditiesForm: clsAndComForm,
			LossRunSummaryForm:        &lossRunSumFormCopy,
		}))

	s.Require().NoError(s.h.Api.PostIndicationSubmit(app.ID))
	s.Require().NoError(s.waitForModelRun(app.ID))
	opts, err := s.h.Api.GetIndicationOptions(app.ID)
	s.Require().Nil(err)
	_, err = opts.GetOptionForTag(app_enums.IndicationOptionTagStandard)
	s.Require().Nil(err)

	appObj, err := s.h.Metadata.GetApplication(app.ID)
	s.Require().NoError(err)
	s.Require().Equal(pointer_utils.Float64(35), appObj.ModelPinConfig.RateML.SmartIndicationDiscount)

	// Manually generate quartile 1 indications by using reducing losses
	lossRunSumFormCopy = *lossRunSumForm
	for covIdx := range *lossRunSumForm {
		for yearIdx := range (*lossRunSumForm)[covIdx].LossRunSummary {
			lossRunSumFormCopy[covIdx].LossRunSummary[yearIdx].LossIncurred -= 100000
		}
	}
	s.Require().NoError(s.h.Api.PutIndicationForm(
		app.ID,
		oapi_app.IndicationForm{
			OperationsForm:            operationsForm,
			ClassesAndCommoditiesForm: clsAndComForm,
			LossRunSummaryForm:        &lossRunSumFormCopy,
		}))

	s.Require().NoError(s.h.Api.PostIndicationSubmit(app.ID))
	s.Require().NoError(s.waitForModelRun(app.ID))
	opts, err = s.h.Api.GetIndicationOptions(app.ID)
	s.Require().Nil(err)
	_, err = opts.GetOptionForTag(app_enums.IndicationOptionTagStandard)
	s.Require().Nil(err)

	appObj, err = s.h.Metadata.GetApplication(app.ID)
	s.Require().NoError(err)
	s.Require().Equal(pointer_utils.Float64(35), appObj.ModelPinConfig.RateML.SmartIndicationDiscount)
}

// TODO: add tests to verify that indication options are created with the
// correct coverages depending by state

// TODO: add tests for last step of status tracker
// TODO: add tests to modify app while app is under review

// TestGoldenDataSetPresentStates tests that all states which we are live in
// are present inside the golden dataset which is used for our tests
// We are in the process of adding all the remaining dot in the golden dataset
// hence we are only warning for now. This will be switched to failing tests
// for live tests and warnings to in development states
func (s *indicationApisTestSuite) TestGoldenDatasetPresentStates() {
	ctx := s.h.Ctx
	newGoldenDataSet := golden_dataset.GoldenDotNumbers
	providers := []rtypes.RatemlModelProvider{rtypes.ProviderSentry, rtypes.ProviderSentryMST}
	for _, provider := range providers {
		for i := range models_release.GetActiveStates(ctx, nil, nil, provider) {
			// Iterate over all the states present and check if
			// we have them present in the golden dataset
			if _, ok := newGoldenDataSet[i]; !ok {
				s.T().Errorf("State %s is live but not present in golden dataset", i)
			}
		}
	}
}

// This test focuses on the renewals patch form. We first create an application and
// then simulate the 5 steps of the renewal flow, where each step adds new
// information. We follow these steps:
// 1. we create a renewal application
// 2. PATCH to fill renewal coverages form
// 3. PATCH to fill renewal classes and commodities form
// 4. PATCH to fill loss history summary form
// 5. PATCH to fill additional information form
// 6. PATCH to update sectionCompletionMap
func (s *indicationApisTestSuite) TestAppRenewalForm() {
	quoteTestWrapper, err := s.h.TestWrappers.QuoteTestWrapperCreator()
	s.Require().NoError(err)
	// manually setting the state to bound
	err = s.h.ApplicationDeps.ApplicationWrapper.UpdateApp(s.h.Ctx, quoteTestWrapper.AppId,
		func(a application.Application) (application.Application, error) {
			a.AgencyID = constants.NirvanaDemoAgencyID
			a.State = state_enums.AppStatePolicyCreated
			return a, nil
		})
	s.Require().NoError(err)
	appObj, err := s.h.ApplicationDeps.ApplicationWrapper.GetAppById(s.h.Ctx, quoteTestWrapper.AppId)
	s.Require().NoError(err)
	err = s.h.LoginDefaultSuperUser()
	producerId, err := uuid.Parse(*appObj.ProducerID)
	s.Require().NoError(err)

	// We shouldn't be able to patch non-renewal application
	err = s.h.Api.PatchApplicationApplicationIDRenew(quoteTestWrapper.AppId, oapi_app.PatchRenewalApplicationForm{})
	s.Require().Error(err, "unable to patch renewal fields for non-renewal app")

	// 1. We create a renewal application
	response, _, err := s.h.Api.PostApplicationApplicationIDV2Renew(s.h.Ctx, appObj.ID)
	s.Require().NoError(err)
	renewalAppId := response.ApplicationID
	tests := []struct {
		description  string
		validationFn func() error
		wantErr      bool
	}{
		{
			description: "PATCH renewal coverages",
			validationFn: func() error {
				renewalCoveragesForm := test_utils.MockRenewalCoverageForm(producerId)
				err = s.h.Api.PatchApplicationApplicationIDRenew(renewalAppId, oapi_app.PatchRenewalApplicationForm{
					RenewalCoverageForm: renewalCoveragesForm,
				})
				if err != nil {
					return errors.New("Error while patching renewal coverage form")
				}
				renewalApp, err := s.h.ApplicationDeps.ApplicationWrapper.GetAppById(s.h.Ctx, renewalAppId)
				if err != nil {
					return errors.New("Error while patching renewal coverage form")
				}
				s.Require().NotNil(renewalApp.CoverageInfo)
				s.Require().NotNil(renewalApp.CoverageInfo.Coverages)
				s.Require().True(renewalApp.CoverageInfo.ContainsCoverage(app_enums.CoverageAutoLiability))
				s.Require().NotNil(renewalApp.PackageType)
				s.Require().Equal(*renewalApp.PackageType, app_enums.IndicationOptionTagStandard)
				s.Require().Equal(renewalApp.RenewalMetadata.SectionCompletionMap,
					&application.SectionCompletionMap{
						application.Coverages:             false,
						application.Operations:            false,
						application.ClassesAndCommodities: false,
						application.LossHistory:           false,
						application.AdditionalInformation: false,
					})
				return nil
			},
			wantErr: false,
		},
		{
			description: "PATCH renewal operations",
			validationFn: func() error {
				renewalApp, err := s.h.ApplicationDeps.ApplicationWrapper.GetAppById(s.h.Ctx, renewalAppId)
				if err != nil {
					return errors.New("Error while patching renewal operations form")
				}
				fileHandleId, err := s.h.CreateFileHandle("TestKey",
					renewalApp.AgencyID, file_enums.FileDestinationGroupQuoting)
				s.Require().NoError(err)
				renewalOperationsForm := test_utils.MockRenewalOperationsForm(fileHandleId)
				err = s.h.Api.PatchApplicationApplicationIDRenew(renewalAppId, oapi_app.PatchRenewalApplicationForm{
					RenewalOperationsForm: renewalOperationsForm,
				})
				if err != nil {
					return errors.New("Error while patching renewal operations form")
				}
				renewalApp, err = s.h.ApplicationDeps.ApplicationWrapper.GetAppById(s.h.Ctx, renewalAppId)
				s.Require().NoError(err)
				s.Require().NotNil(renewalApp.CompanyInfo.NumberOfPowerUnits)
				s.Require().Equal(renewalApp.CompanyInfo.NumberOfPowerUnits, renewalOperationsForm.NumberOfPowerUnits)
				s.Require().NotNil(renewalApp.CompanyInfo.ProjectedMileage)
				s.Require().Equal(renewalApp.CompanyInfo.ProjectedMileage, renewalOperationsForm.ProjectedMileage)
				s.Require().NotNil(renewalApp.EquipmentInfo)
				s.Require().NotNil(renewalApp.EquipmentInfo.EquipmentList)
				return nil
			},
			wantErr: false,
		},
		{
			description: "PATCH renewal operations terminal locations",
			validationFn: func() error {
				renewalApp, err := s.h.ApplicationDeps.ApplicationWrapper.GetAppById(s.h.Ctx, renewalAppId)
				if err != nil {
					return errors.New("Error while patching renewal operations form terminal locations")
				}
				fileHandleId, err := s.h.CreateFileHandle("TestKey",
					renewalApp.AgencyID, file_enums.FileDestinationGroupQuoting)
				s.Require().NoError(err)
				renewalOperationsForm := test_utils.MockRenewalOperationsFormC(fileHandleId, test_utils.CoveragesALAndAPDAndGL)
				err = s.h.Api.PatchApplicationApplicationIDRenew(renewalAppId, oapi_app.PatchRenewalApplicationForm{
					RenewalOperationsForm: renewalOperationsForm,
				})
				if err != nil {
					return errors.New("Error while patching renewal operations form terminal locations")
				}
				s.Require().NoError(err)
				renewalApp, err = s.h.ApplicationDeps.ApplicationWrapper.GetAppById(s.h.Ctx, renewalAppId)
				s.Require().NoError(err)
				s.Require().NotNil(renewalApp.CompanyInfo.TerminalLocations)
				s.Require().Equal(len(*renewalApp.CompanyInfo.TerminalLocations), 2)
				return nil
			},
			wantErr: false,
		},
		{
			description: "PATCH renewal classes and commodities",
			validationFn: func() error {
				renewalClsAndCommsForm := test_utils.MockRenewalClsAndCommsForm()
				err = s.h.Api.PatchApplicationApplicationIDRenew(renewalAppId, oapi_app.PatchRenewalApplicationForm{
					RenewalClassesAndCommoditiesForm: renewalClsAndCommsForm,
				})
				if err != nil {
					return errors.New("Error while patching renewal classes and commodities form")
				}
				renewalApp, err := s.h.ApplicationDeps.ApplicationWrapper.GetAppById(s.h.Ctx, renewalAppId)
				if err != nil {
					return errors.New("Error while patching renewal classes and commodities form")
				}
				expectedClass := app_enums.OperatingClassDryVan
				s.Require().NotNil(renewalApp.EquipmentInfo.OperatingClassDistribution)
				s.Require().Equal(len(renewalApp.EquipmentInfo.OperatingClassDistribution), 2)
				s.Require().NotNil(renewalApp.EquipmentInfo.PrimaryOperatingClass)
				s.Require().Equal(*renewalApp.EquipmentInfo.PrimaryOperatingClass, expectedClass)
				s.Require().NotNil(renewalApp.EquipmentInfo.PrimaryCategory)
				s.Require().Nil(renewalApp.EquipmentInfo.CommodityDistribution)
				s.Require().NotNil(renewalApp.AdditionalCommodityInfo)
				s.Require().NotNil(renewalApp.AdditionalCommodityInfo.Commodities)
				s.Require().NotNil(renewalApp.AdditionalCommodityInfo.Comment)
				s.Require().Equal(renewalApp.AdditionalCommodityInfo.Comment.String, "Comment")
				return nil
			},
			wantErr: false,
		},
		{
			description: "PATCH renewal loss history",
			validationFn: func() error {
				renewalLossForm := test_utils.MockRenewalLossForm()
				err = s.h.Api.PatchApplicationApplicationIDRenew(renewalAppId, oapi_app.PatchRenewalApplicationForm{
					RenewalLossForm: renewalLossForm,
				})
				if err != nil {
					return errors.New("Error while patching renewal loss history form")
				}
				renewalApp, err := s.h.ApplicationDeps.ApplicationWrapper.GetAppById(s.h.Ctx, renewalAppId)
				s.Require().NoError(err)
				s.Require().NotNil(renewalApp.LossInfo)
				s.Require().NotNil(renewalApp.LossInfo.LossRunSummary)
				// We only have for AL
				s.Require().Equal(5, len(renewalApp.LossInfo.LossRunSummary[0].Summary))
				return nil
			},
			wantErr: false,
		},
		{
			description: "PATCH renewal additional information",
			validationFn: func() error {
				renewalAdditionalForm := test_utils.MockRenewalAdditionalForm()
				err = s.h.Api.PatchApplicationApplicationIDRenew(renewalAppId, oapi_app.PatchRenewalApplicationForm{
					RenewalAdditionalForm: renewalAdditionalForm,
				})
				s.Require().NoError(err)
				renewalApp, err := s.h.ApplicationDeps.ApplicationWrapper.GetAppById(s.h.Ctx, renewalAppId)
				if err != nil {
					return errors.New("Error while patching renewal additional information form")
				}
				s.Require().NotNil(renewalApp.DriversInfo)
				s.Require().NotNil(renewalApp.AdditionalLossInfo)
				s.Require().NotNil(renewalApp.AdditionalLossInfo.Comment)
				s.Require().Equal(renewalApp.AdditionalLossInfo.Comment.String, "LargeLossComment")
				s.Require().NotNil(renewalApp.AdditionalLossInfo.Files)
				s.Require().Equal(len(renewalApp.AdditionalLossInfo.Files), 1)
				s.Require().NotNil(renewalApp.AdditionalInfoExtraMetadata)
				s.Require().NotNil(renewalApp.AdditionalInfoExtraMetadata.OverallComment)
				s.Require().Equal(renewalApp.AdditionalInfoExtraMetadata.OverallComment.String, "OverallComment")
				s.Require().Equal(renewalApp.AdditionalInfoExtraMetadata.NumOwnerOperatorUnits.Int, 5)
				s.Require().NotNil(renewalApp.AdditionalInfoExtraMetadata.NumOwnerOperatorUnits)
				return nil
			},
			wantErr: false,
		},
		{
			description: "PATCH renewal files form",
			validationFn: func() error {
				renewalFilesForm := test_utils.MockRenewalFilesForm()
				err = s.h.Api.PatchApplicationApplicationIDRenew(renewalAppId, oapi_app.PatchRenewalApplicationForm{
					RenewalFilesForm: renewalFilesForm,
				})
				s.Require().NoError(err)
				renewalApp, err := s.h.ApplicationDeps.ApplicationWrapper.GetAppById(s.h.Ctx, renewalAppId)
				if err != nil {
					return errors.New("Error while patching renewal additional information form")
				}
				s.Require().NotNil(renewalApp.DriversInfo)
				s.Require().NotNil(renewalApp.AdditionalLossInfo)
				s.Require().NotNil(renewalApp.AdditionalLossInfo.Files)
				s.Require().Equal(len(renewalApp.AdditionalLossInfo.Files), 1)
				s.Require().NotNil(renewalApp.EquipmentInfo)
				s.Require().NotNil(renewalApp.EquipmentInfo.EquipmentList)
				s.Require().NotNil(renewalApp.RenewalMetadata.IftaFiles)
				s.Require().Equal(len(*renewalApp.RenewalMetadata.IftaFiles), 1)
				return nil
			},
			wantErr: false,
		},
		{
			description: "PATCH renewal MTC required",
			validationFn: func() error {
				renewalCoveragesForm := test_utils.MockRenewalCoverageFormC(producerId, test_utils.CoveragesALAndMTC, nil)
				err = s.h.Api.PatchApplicationApplicationIDRenew(renewalAppId, oapi_app.PatchRenewalApplicationForm{
					RenewalCoverageForm: renewalCoveragesForm,
				})
				s.Require().NoError(err)
				renewalClsAndCommsForm := test_utils.MockRenewalClsAndCommsFormC(test_utils.CoveragesALAndMTC)
				err = s.h.Api.PatchApplicationApplicationIDRenew(renewalAppId, oapi_app.PatchRenewalApplicationForm{
					RenewalClassesAndCommoditiesForm: renewalClsAndCommsForm,
				})
				s.Require().NoError(err)
				renewalApp, err := s.h.ApplicationDeps.ApplicationWrapper.GetAppById(s.h.Ctx, renewalAppId)
				s.Require().NoError(err)
				s.Require().NotNil(renewalApp.EquipmentInfo.CommodityDistribution)
				s.Require().Equal(len(renewalApp.EquipmentInfo.CommodityDistribution.Commodities), 3)
				s.Require().NotNil(renewalApp.EquipmentInfo.PrimaryCategory)
				return nil
			},
			wantErr: false,
		},
		{
			description: "PATCH renewal form removing MTC",
			validationFn: func() error {
				// remove MTC and check for loss history and commodity distribution
				renewalCoveragesForm := test_utils.MockRenewalCoverageForm(producerId)
				err = s.h.Api.PatchApplicationApplicationIDRenew(renewalAppId, oapi_app.PatchRenewalApplicationForm{
					RenewalCoverageForm: renewalCoveragesForm,
				})
				s.Require().NoError(err)
				renewalApp, err := s.h.ApplicationDeps.ApplicationWrapper.GetAppById(s.h.Ctx, renewalAppId)
				s.Require().NoError(err)
				s.Require().Nil(renewalApp.EquipmentInfo.CommodityDistribution)
				s.Require().Equal(len(renewalApp.LossInfo.LossRunSummary), 1)
				return nil
			},
			wantErr: false,
		},
		{
			description: "PATCH renewal section completion map",
			validationFn: func() error {
				renewalSectionCompletionMap := oapi_app.SectionCompletionMap{
					AdditionalInformation: true,
					ClassesAndCommodities: true,
					Coverages:             false,
					LossHistory:           false,
					Operations:            false,
				}
				err = s.h.Api.PatchApplicationApplicationIDRenew(renewalAppId, oapi_app.PatchRenewalApplicationForm{
					SectionCompletionMap: &renewalSectionCompletionMap,
				})
				if err != nil {
					return errors.New("Error while patching renewal classes and commodities form")
				}
				renewalApp, err := s.h.ApplicationDeps.ApplicationWrapper.GetAppById(s.h.Ctx, renewalAppId)
				if err != nil {
					return errors.New("Error while patching renewal classes and commodities form")
				}
				s.Require().NotNil(renewalApp.RenewalMetadata.SectionCompletionMap)
				s.Require().True((*renewalApp.RenewalMetadata.SectionCompletionMap)[application.ClassesAndCommodities])
				s.Require().True((*renewalApp.RenewalMetadata.SectionCompletionMap)[application.AdditionalInformation])
				return nil
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		s.T().Run(tt.description, func(t *testing.T) {
			err := tt.validationFn()
			if err != nil {
				t.Logf("error: %v", err)
			}
			if (err != nil) != tt.wantErr {
				t.Errorf("TestAppRenewalForm() error = %v, wantErr %v", err, tt.wantErr)
			}
		},
		)
	}
}

// SetupRenewalApplication creates a renewal application from a created application
// and returns its appID.
func (s *indicationApisTestSuite) SetupRenewalApplication(appId string) string {
	// manually setting the state to bound
	err := s.h.ApplicationDeps.ApplicationWrapper.UpdateApp(s.h.Ctx, appId,
		func(a application.Application) (application.Application, error) {
			a.AgencyID = constants.NirvanaDemoAgencyID
			a.State = state_enums.AppStatePolicyCreated
			return a, nil
		})
	s.Require().NoError(err)
	err = s.h.LoginDefaultSuperUser()
	s.Require().NoError(err)
	// We create the renewal application
	createdResp, conflictedResp, err := s.h.Api.PostApplicationApplicationIDV2Renew(s.h.Ctx, appId)
	s.Require().NoError(err)
	if conflictedResp != nil {
		return conflictedResp.ExistingApplicationId
	}
	return createdResp.ApplicationID
}

func (s *indicationApisTestSuite) TestRenewalFormSubmit() {
	err := s.h.LoginDefaultAgent()
	s.Require().NoError(err)
	appTestWrapper, err := s.h.TestWrappers.AppTestWrapperCreator()
	s.Require().NoError(err)
	quoteTestWrapper, err := s.h.TestWrappers.QuoteTestWrapperCreator()
	s.Require().NoError(err)
	appId := s.SetupRenewalApplication(quoteTestWrapper.AppId)
	appObj, err := s.h.ApplicationDeps.ApplicationWrapper.GetAppById(s.h.Ctx, appId)
	s.Require().NoError(err)

	// Add files for submission
	renewalFilesForm := test_utils.MockRenewalFilesForm()
	err = s.h.Api.PatchApplicationApplicationIDRenew(appObj.ID, oapi_app.PatchRenewalApplicationForm{
		RenewalFilesForm: renewalFilesForm,
	})
	s.Require().NoError(err)

	// Submit the renewal application for uw review
	err = appTestWrapper.SubmitRenewalFormAndWaitForJob(appId)
	s.Require().NoError(err)
	appObj, err = s.h.ApplicationDeps.ApplicationWrapper.GetAppById(s.h.Ctx, appId)
	s.Require().NoError(err)

	// Ensure that uw submission id is not nil
	s.Require().NotNil(appObj.UwSubmissionID)

	uwSub, err := s.h.ApplicationDeps.ApplicationWrapper.GetSubmissionById(s.h.Ctx, *appObj.UwSubmissionID)
	s.Require().NoError(err)
	s.Require().NotNil(uwSub.CoverageInfo)
	s.Require().NotNil(uwSub.CoverageInfo.Coverages)

	appReview, err := s.h.ApplicationDeps.ApplicationReviewWrapper.GetLatestPendingReview(s.h.Ctx, appId)
	s.Require().NoError(err)
	s.Require().NotNil(appReview)
}

func (s *indicationApisTestSuite) TestRenewalApplicationRedListFlag() {
	err := s.h.LoginDefaultAgent()
	s.Require().NoError(err)
	_, err = s.h.TestWrappers.AppTestWrapperCreator()
	s.Require().NoError(err)
	quoteTestWrapper, err := s.h.TestWrappers.QuoteTestWrapperCreator()
	s.Require().NoError(err)
	appId := s.SetupRenewalApplication(quoteTestWrapper.AppId)
	err = s.h.Api.PatchRenewalApplicationRedListFlag(
		appId,
		oapi_app.UpdateApplicationRedListForm{
			BelongsToRedList: true,
		},
	)
	s.Require().NoError(err)
	appObj, err := s.h.ApplicationDeps.ApplicationWrapper.GetAppById(s.h.Ctx, appId)
	s.Require().NoError(err)
	s.Require().True(*appObj.RenewalMetadata.BelongsToRedList)
}
