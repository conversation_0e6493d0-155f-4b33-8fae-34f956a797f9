package sg_app_under_review

import (
	"testing"

	"nirvanatech.com/nirvana/infra/fx/testfixtures/lni_fixture"

	"github.com/launchdarkly/go-sdk-common/v3/ldvalue"
	"github.com/stretchr/testify/require"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/api-server/test_utils"
	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/external_data_management/data_processing/vin/testing/testfixtures"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/emailer_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/fmcsa_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	oapi_app "nirvanatech.com/nirvana/openapi-specs/components/application"
	"nirvanatech.com/nirvana/telematics"
	"nirvanatech.com/nirvana/telematics/connections"
)

func TestAppUnderReviewFlowForSG(t *testing.T) {
	var env struct {
		fx.In
		Harness *test_utils.ApiServerHarness
		*emailer_fixture.NoopEmailerFixture
		*fmcsa_fixture.FmcsaFixture
		*lni_fixture.VersionFixture
		*testfixtures.NhtsaToIsoMappingV1Fixture
		*lni_fixture.ActiveOrPendingInsuranceFixture
		FeatureFlagClient *feature_flag_lib.MockClient
	}

	defer testloader.RequireStart(t, &env).RequireStop()
	featureFlagClient := env.FeatureFlagClient
	featureFlagClient.SetValue(feature_flag_lib.FeatureValidateDuplicateApplications, ldvalue.Bool(false))

	harness := env.Harness
	require.NoError(t, harness.CreateDefaultHighestAuthorityUW())

	session, err := harness.CreateRandomUserAndSession()
	require.NoError(t, err)
	require.NotNil(t, session)

	err = harness.CreateDefaultAgency()
	require.NoError(t, err)
	err = harness.CreateDefaultAgent()
	require.NoError(t, err)
	err = harness.CreateDefaultBD()
	require.NoError(t, err)

	// construct test cases
	tsps := []telematics.TSP{
		telematics.TSPClutchELD,
		telematics.TSPCyntrXELDPlus,
	}
	testCases := make(map[telematics.TSP]telematics.TSPInfo)
	for _, tsp := range tsps {
		if _, ok := connections.SupportedSpeedGaugeTSPs[tsp]; !ok {
			t.Errorf("TSP %s value is not present", tsp)
		}
		testCases[tsp] = connections.SupportedSpeedGaugeTSPs[tsp]
	}
	for tsp, tspInfo := range testCases {
		if tsp.IsOneOf(telematics.TSPGeotab, telematics.TSPVerizonConnect) {
			// Geotab & VerizonConnect are no longer supported for SG
			continue
		}
		t.Run(tsp.String(), func(t *testing.T) {
			appTestWrapper, err := harness.TestWrappers.AppTestWrapperCreator()
			require.NoError(t, err)
			_, err = appTestWrapper.CreateApplication()
			require.NoError(t, err)
			_, err = appTestWrapper.GenerateAndSelectIndication()
			require.NoError(t, err)
			require.NoError(t, appTestWrapper.SubmitApplicationForUWReview())
			require.NoError(t, appTestWrapper.Validator.StatusTrackerAppSubmitted())
			require.NoError(t, appTestWrapper.Validator.AppState(oapi_app.ApplicationStatePendingELDTelematics))

			require.NoError(t, appTestWrapper.ConnectTSPC(tsp, tspInfo.ConsentKind, false))
			require.NoError(t, appTestWrapper.Validator.AppState(oapi_app.ApplicationStateUnderReviewForQuote))
			require.NoError(t, appTestWrapper.Validator.StatusTrackerDataProcessed())
		})
	}
}
