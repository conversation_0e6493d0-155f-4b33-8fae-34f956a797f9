package tsp_conn_apis

import (
	"context"
	"fmt"
	"net/url"
	"testing"
	"time"

	"nirvanatech.com/nirvana/infra/fx/testfixtures/lni_fixture"

	"github.com/google/uuid"
	"github.com/samsarahq/go/snapshotter"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"go.uber.org/mock/gomock"

	"nirvanatech.com/nirvana/api-server/handlers/tsp"
	application_deps "nirvanatech.com/nirvana/api-server/interceptors/application/deps"
	"nirvanatech.com/nirvana/api-server/test_utils"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	common_test_utils "nirvanatech.com/nirvana/common-go/test_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/external_data_management/data_processing/vin/testing/testfixtures"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/emailer_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/feature_store_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/fmcsa_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/samsara_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/users_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	oapi_app "nirvanatech.com/nirvana/openapi-specs/components/application"
	oapi_telematics "nirvanatech.com/nirvana/openapi-specs/components/telematics"
	"nirvanatech.com/nirvana/sharing"
	"nirvanatech.com/nirvana/telematics"
	"nirvanatech.com/nirvana/telematics/connections"
	dp_test_utils "nirvanatech.com/nirvana/telematics/data_platform/test_utils"
	samsara_test_utils "nirvanatech.com/nirvana/telematics/integrations/samsara_lib/test_utils"
	"nirvanatech.com/nirvana/underwriting/state_machine"
	uw_mocks "nirvanatech.com/nirvana/underwriting/state_machine/tests/mocks"
)

func TestTSPConnectionTestSuite(t *testing.T) {
	suite.Run(t, new(tspConnectionTestSuite))
}

// mock for app review state machine
func NewMockAppReviewStateMachineWrapper(t testing.TB) *uw_mocks.MockAppReviewWrapper {
	ctrl := gomock.NewController(t)
	return uw_mocks.NewMockAppReviewWrapper(ctrl)
}

type tspConnectionTestSuite struct {
	common_test_utils.StatsHandler
	suite.Suite
	h                                *test_utils.ApiServerHarness
	deps                             application_deps.Deps
	fxapp                            *fxtest.App
	samsaraFixture                   *samsara_fixture.Samsara
	MockAppReviewStateMachineWrapper *uw_mocks.MockAppReviewWrapper
	UsersFixture                     *users_fixture.UsersFixture
}

func (s *tspConnectionTestSuite) TearDownTest() {
	s.fxapp.RequireStop()
}

func (s *tspConnectionTestSuite) SetupTest() {
	var env struct {
		fx.In

		Harness      *test_utils.ApiServerHarness
		UsersFixture *users_fixture.UsersFixture
		*emailer_fixture.NoopEmailerFixture
		*testfixtures.NhtsaToIsoMappingV1Fixture
		*feature_store_fixture.FeatureStoreFixture
		*fmcsa_fixture.FmcsaFixture
		*lni_fixture.VersionFixture
		*samsara_fixture.Samsara
	}

	// setup mock for UW state machine wrapper
	appReviewStateMachineWrapperMock := NewMockAppReviewStateMachineWrapper(s.T())
	mockAppReviewStateMachineOption := fx.Decorate(func() state_machine.AppReviewWrapper {
		return appReviewStateMachineWrapperMock
	})

	s.fxapp = testloader.RequireStart(s.T(), &env, testloader.Use(mockAppReviewStateMachineOption))
	s.MockAppReviewStateMachineWrapper = appReviewStateMachineWrapperMock
	s.h = env.Harness
	s.deps = s.h.ApplicationDeps
	s.samsaraFixture = env.Samsara
	s.UsersFixture = env.UsersFixture
	err := s.h.CreateDefaultBD()
	s.Require().Nil(err)
}

// TODO: refactor to new AppTestWrapper

// TestGetTelematicsProviders tests that the TSPs are being fetched well.
// tsp_handlers.SupportedTSPsREST expose the supported TSPs, so the api call
// should return the same elements.
func (s *tspConnectionTestSuite) TestGetTelematicsProviders() {
	wantDefaultResp, err := tsp.BindTSPInfoMapToRestRecords(connections.DefaultTSPs)
	s.Require().Nil(err)
	type testCase struct {
		name  string
		token string
		want  []oapi_app.TSPRecord
	}
	testCases := []testCase{
		{
			name:  "Default response",
			token: s.createUserAndApp(),
			want:  *wantDefaultResp,
		},
		// TODO: add more cases once TSPConnManager starts supporting non-default scenarios
	}
	for i := range testCases {
		tc := testCases[i]
		s.Run(tc.name, func() {
			tspRecord, err := s.h.Api.GetTelematicsProviders(tc.token)
			s.Require().Nil(err)
			s.Require().ElementsMatch(tspRecord, tc.want)
		})
	}
}

// TestPostTSPOAuthConnsRequest tests the connection initialization request
// for OAuth TSPs:
// 1. Test POST without an existing app
// 2. Test empty POST
func (s *tspConnectionTestSuite) TestPostTSPOAuthConnsRequest() {
	// Case 1: Test POST without an existing app
	{
		connData := s.h.TSPConnManagerHelper.MockConnData(telematics.TSPSamsara, telematics.ConsentKindOAuth)
		conn, err := s.h.Api.PostTSPConnection("", connData)
		s.Require().NotNil(err)
		s.Require().Nil(conn)
	}
	// Case 2: Post empty connection
	appId := s.createUserAndApp()
	conn, err := s.h.Api.PostTSPConnection(appId, oapi_app.TSPConnection{})
	s.Require().NotNil(err)
	s.Require().Nil(conn)
}

// TestPostTSPConnectionUnsupportedConsentKind tests the connection request
// with a different consent kind than the one received in the GetTelematicsProviders
// call. This way we test that the API server rejects any requests with unsupported
// consent kinds.
func (s *tspConnectionTestSuite) TestPostTSPConnectionUnsupportedConsentKind() {
	appId := s.createUserAndApp()
	supportedTSPs, err := s.h.Api.GetTelematicsProviders(appId)
	s.Require().NoError(err)
	for _, tspRecord := range supportedTSPs {
		s.T().Run(fmt.Sprintf("TSP: %s, Supported ConsentKind: %s", tspRecord.Tsp, tspRecord.ConsentKind), func(t *testing.T) {
			tspStr, err := telematics.TSPString(string(tspRecord.Tsp))
			require.NoError(t, err)
			consentKind, err := tsp.BindConsentKindFromRestForTest(tspRecord.ConsentKind)
			require.NoError(t, err)
			// testCases are all consentKinds except the supported one.
			testCases := slice_utils.Filter(telematics.ConsentKindValues(),
				func(kind telematics.ConsentKind) bool {
					return kind != consentKind
				},
			)
			for _, ck := range testCases {
				connData := s.h.TSPConnManagerHelper.MockConnData(tspStr, ck)
				conn, err := s.h.Api.PostTSPConnection(appId, connData)
				// TODO: modify PostTSPConnection to return the response type
				// so we can also verify the status code.
				require.Error(t, err, "Expected error for unsupported consent kind %s", ck.String())
				require.Nil(t, conn)
			}
		})
	}
}

// testPostTSPConnIdempotency verifies the idempotency property of one TSP
// connection.
// TODO: finalize connections in this tests when that's implemented
func (s *tspConnectionTestSuite) testPostTSPConnIdempotency(tspEnum telematics.TSP, consentKind telematics.ConsentKind) {
	// for speed gauge connection is persisted right away
	if consentKind == telematics.ConsentKindSpeedgauge {
		// verify that UW is notified about the handle ID update
		// UW will be notified twice because handle ID is being set twice
		s.MockAppReviewStateMachineWrapper.
			EXPECT().
			TelematicsHandleIdUpdated(gomock.Any(), gomock.Any()).
			Return(nil).
			Times(2)

		// in case of speed gauge, UW should be notified about no historical data as well
		s.MockAppReviewStateMachineWrapper.
			EXPECT().
			NoHistoricalTelematicsData(gomock.Any(), gomock.Any()).
			Return(nil).
			Times(2)
	}

	appId := s.createUserAndApp()
	// Create mock data and POST connection
	tspConn := s.h.TSPConnManagerHelper.MockConnData(tspEnum, consentKind)
	firstConn, err := s.h.Api.PostTSPConnection(appId, tspConn)
	s.Require().Nil(err)
	s.Require().NotNil(firstConn)
	if consentKind == telematics.ConsentKindOAuth {
		s.Require().NotNil(*firstConn.Url)
	}
	// Get the app
	app, err := s.h.Metadata.GetApplication(appId)
	s.Require().Nil(err)
	s.Require().NotNil(app)
	// We can't know the handleId, so we just check that it exists
	firstHandle := app.TSPConnHandleId
	if consentKind == telematics.ConsentKindOAuth {
		s.Require().NotNil(*firstConn.Url)
		s.Require().Nil(firstHandle)
	} else {
		s.Require().NotNil(firstHandle)
	}
	// Try second connection and pass idempotency test
	secondConn, err := s.h.Api.PostTSPConnection(appId, tspConn)
	s.Require().Nil(err)
	s.Require().NotNil(secondConn)
	if consentKind == telematics.ConsentKindOAuth {
		s.Require().NotNil(*secondConn.Url)
	}
	// Get the app
	app, err = s.h.Metadata.GetApplication(appId)
	s.Require().Nil(err)
	s.Require().NotNil(app)
	// Check that handle changed
	if consentKind == telematics.ConsentKindOAuth {
		s.Require().NotNil(*firstConn.Url)
		s.Require().Nil(firstHandle)
	} else {
		secondHandle := app.TSPConnHandleId
		s.Require().NotNil(secondHandle)
		s.Require().NotEqual(firstHandle, secondHandle)
	}
}

// TestPostTSPConnsIdempotency verifies the idempotency property of TSPs
// connections
func (s *tspConnectionTestSuite) TestPostTSPConnsIdempotency() {
	s.testPostTSPConnIdempotency(telematics.TSPSamsara, telematics.ConsentKindOAuth)
	s.testPostTSPConnIdempotency(telematics.TSPAzuga, telematics.ConsentKindSpeedgauge)
}

func (s *tspConnectionTestSuite) Test_PostTSPConnectionCompleteRequest() {
	ctx := context.Background()
	appId := s.createUserAndApp()

	// Create Samsara Connection
	samsaraConsentLink := sharing.NewTelematicsConsentLink(
		uuid.MustParse(appId), s.UsersFixture.Superuser.ID, s.deps.Clock.Now(), nil,
	)
	s.Require().NoError(s.deps.SharingWrapper.InsertShareableLink(ctx, samsaraConsentLink))

	tspConnSamsara := s.h.TSPConnManagerHelper.MockConnData(telematics.TSPSamsara, telematics.ConsentKindOAuth)
	samsaraConn, err := s.h.Api.PostTSPConnection(samsaraConsentLink.ID.String(), tspConnSamsara)
	s.Require().NoError(err)
	s.Require().NotNil(samsaraConn)
	s.Require().NotEmpty(*samsaraConn.Url)
	s.testOAuthUrl(*samsaraConn.Url)

	samsaraConnComplete, err := s.h.TSPConnManagerHelper.MockConnCompleteData(
		*samsaraConn.Url, test_utils.OAuthCallbackScenarioAccepted, telematics.TSPSamsara)
	s.Require().NoError(err)

	ktConsentLink := sharing.NewTelematicsConsentLink(
		uuid.MustParse(appId), s.UsersFixture.Superuser.ID, s.deps.Clock.Now(), nil,
	)
	s.Require().NoError(s.deps.SharingWrapper.InsertShareableLink(ctx, ktConsentLink))

	// Create Go-Motive (aka KeepTruckin) Connection
	ktConnData := s.h.TSPConnManagerHelper.MockConnData(telematics.TSPKeepTruckin, telematics.ConsentKindOAuth)
	ktConn, err := s.h.Api.PostTSPConnection(ktConsentLink.ID.String(), ktConnData)
	s.Require().NoError(err)
	s.Require().NotNil(ktConn)
	s.Require().NotEmpty(*ktConn.Url)
	s.testOAuthUrl(*ktConn.Url)

	ktConnComplete, err := s.h.TSPConnManagerHelper.MockConnCompleteData(
		*ktConn.Url, test_utils.OAuthCallbackScenarioAccepted, telematics.TSPKeepTruckin)
	s.Require().NoError(err)

	samsaraEmptyAuthCodeConn := *samsaraConnComplete.TSPSamsara
	samsaraEmptyAuthCodeConn.AuthCode = pointer_utils.ToPointer("")

	ktEmptyStateConn := *ktConnComplete.TSPKeepTruckin
	ktEmptyStateConn.State = pointer_utils.ToPointer("")

	s.MockAppReviewStateMachineWrapper.
		EXPECT().
		TelematicsHandleIdUpdated(gomock.Any(), gomock.Any()).
		Return(nil).
		Times(2)

	testCases := []struct {
		name            string
		request         oapi_telematics.TSPConnectionCompleteRequest
		shareableLinkId *uuid.UUID
		wantErr         bool
	}{
		{
			name: "TSPSamsara connection successful",
			request: oapi_telematics.TSPConnectionCompleteRequest{
				TSPSamsara: samsaraConnComplete.TSPSamsara,
			},
			shareableLinkId: &samsaraConsentLink.ID,
			wantErr:         false,
		},
		{
			name: "KeepTruckin connection successful",
			request: oapi_telematics.TSPConnectionCompleteRequest{
				TSPKeepTruckin: ktConnComplete.TSPKeepTruckin,
			},
			shareableLinkId: &ktConsentLink.ID,
			wantErr:         false,
		},
		{
			name: "failed due to multiple TSP",
			request: oapi_telematics.TSPConnectionCompleteRequest{
				TSPKeepTruckin: ktConnComplete.TSPKeepTruckin,
				TSPSamsara:     samsaraConnComplete.TSPSamsara,
				ProgramType:    nil,
			},
			shareableLinkId: nil,
			wantErr:         true,
		},
		{
			name: "failed due to empty connections",
			request: oapi_telematics.TSPConnectionCompleteRequest{
				TSPKeepTruckin: nil,
				TSPSamsara:     nil,
				ProgramType:    nil,
			},
			shareableLinkId: nil,
			wantErr:         true,
		},
		{
			name: "failed due to empty auth code",
			request: oapi_telematics.TSPConnectionCompleteRequest{
				TSPKeepTruckin: nil,
				TSPSamsara:     &samsaraEmptyAuthCodeConn,
				ProgramType:    nil,
			},
			shareableLinkId: nil,
			wantErr:         true,
		},
		{
			name: "failed due to empty state",
			request: oapi_telematics.TSPConnectionCompleteRequest{
				TSPKeepTruckin: &ktEmptyStateConn,
				TSPSamsara:     nil,
				ProgramType:    nil,
			},
			shareableLinkId: nil,
			wantErr:         true,
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			response, err := s.h.Api.PostTSPConnectionComplete(tc.request)
			if tc.wantErr {
				s.Error(err)
				return
			}
			s.Require().NoError(err)
			s.NotNil(response)
			s.NotNil(response.HandleID)
			s.Equal(appId, response.ApplicationID)

			if tc.shareableLinkId != nil {
				shareableLink, err := s.deps.SharingWrapper.FetchShareableLink(ctx, *tc.shareableLinkId)
				s.NoError(err)
				s.True(shareableLink.ExpiresAt.Before(time.Now()))
			}
		})
	}
}

// TestTSPOAuthConnScenarios tests multiple scenarios for the connection:
// 1. Connection Accepted
// 2. Connection Rejected
// 3. Invalid state received
func (s *tspConnectionTestSuite) TestTSPOAuthConnScenarios() {
	appId := s.createUserAndApp()
	// Create Samsara mock data and POST connection
	tspConnSamsara := s.h.TSPConnManagerHelper.MockConnData(telematics.TSPSamsara, telematics.ConsentKindOAuth)
	conn, err := s.h.Api.PostTSPConnection(appId, tspConnSamsara)
	s.Require().Nil(err)
	s.Require().NotNil(conn)
	s.Require().NotNil(*conn.Url)
	s.testOAuthUrl(*conn.Url)
	// Case 1
	// verify that UW is notified about the handle ID update
	s.MockAppReviewStateMachineWrapper.
		EXPECT().
		TelematicsHandleIdUpdated(gomock.Any(), gomock.Any()).
		Return(nil).
		Times(1)
	samsaraConn, err := s.h.TSPConnManagerHelper.MockConnCompleteData(
		*conn.Url, test_utils.OAuthCallbackScenarioAccepted, telematics.TSPSamsara)
	s.Require().Nil(err)
	_, err = s.h.Api.PostTSPConnectionComplete(*samsaraConn)
	s.Require().Nil(err)
	// Case 2
	samsaraConn, err = s.h.TSPConnManagerHelper.MockConnCompleteData(
		*conn.Url, test_utils.OAuthCallbackScenarioRejected, telematics.TSPSamsara)
	s.Require().Nil(err)
	_, err = s.h.Api.PostTSPConnectionComplete(*samsaraConn)
	s.Require().NotNil(err)
	// Case 3
	samsaraConn, err = s.h.TSPConnManagerHelper.MockConnCompleteData(
		*conn.Url, test_utils.OAuthCallbackScenarioInvalidState, telematics.TSPSamsara)
	s.Require().Nil(err)
	_, err = s.h.Api.PostTSPConnectionComplete(*samsaraConn)
	s.Require().NotNil(err)
}

// TestTSPConnOAuth tests positive flows for all OAuth TSPs:
func (s *tspConnectionTestSuite) TestTSPConnOAuth() {
	s.postTSPConnOAuth(telematics.TSPSamsara)
	s.postTSPConnOAuth(telematics.TSPKeepTruckin)
}

// postTSPConnOAuth tests positive oauth flows for a given OAuth supported TSP.
// To do that, we perform the following steps of the consent flow:
//  1. Create app & begin connection
//  2. Validate that app has been updated with the correct tsp-handleId
//  3. Finalize the connection
//
// Along the way, we also invoke the GetTelematicsConnectionInfo endpoint a few
// times to validate that it returns the correct status.
func (s *tspConnectionTestSuite) postTSPConnOAuth(tspEnum telematics.TSP) {
	// verify that UW is notified about the handle ID update
	s.MockAppReviewStateMachineWrapper.
		EXPECT().
		TelematicsHandleIdUpdated(gomock.Any(), gomock.Any()).
		Return(nil).
		Times(1)

	// Step 1: Create app & begin connection
	appId := s.createUserAndApp()
	tspConn := s.h.TSPConnManagerHelper.MockConnData(tspEnum, telematics.ConsentKindOAuth)
	conn, err := s.h.Api.PostTSPConnection(appId, tspConn)
	s.Require().Nil(err)
	s.Require().NotNil(conn)
	s.Require().NotNil(*conn.Url)
	s.testOAuthUrl(*conn.Url)
	s.Require().NotNil(conn.HandleID)
	handleId, err := uuid.Parse(*conn.HandleID)
	s.Require().Nil(err)
	// Define a helper function to validate the connection status
	verifyConnStatus := func(status telematics.ConnectionStatus) {
		connInfo, err := s.h.Api.GetTelematicsConnectionInfo(handleId)
		s.Require().Nil(err)
		s.Require().Equal(tsp.BindConnectionStatusToRest(status), connInfo.Status)
	}

	verifyConnStatus(telematics.ConnectionStatusInitiated)
	// Step 2: Finalize the connection
	connCompleteData, err := s.h.TSPConnManagerHelper.MockConnCompleteData(
		*conn.Url, test_utils.OAuthCallbackScenarioAccepted, tspEnum)
	s.Require().Nil(err)
	connComplete, err := s.h.Api.PostTSPConnectionComplete(*connCompleteData)
	s.Require().Nil(err)
	s.Require().NotNil(connComplete)
	s.Require().NotNil(connComplete.ApplicationID)
	s.Require().Equal(connComplete.ApplicationID, appId)

	app, err := s.h.Metadata.GetApplication(appId)
	s.Require().Nil(err)
	s.Require().NotNil(app)
	s.Require().NotNil(app.TSPConnHandleId)
	s.Require().Equal(*app.TSPConnHandleId, handleId.String())
	s.Require().Equal(*app.TSPConnHandleId, *connComplete.HandleID)
	s.Require().NotNil(app.TSPEnum)
	s.Require().Equal(*app.TSPEnum, tspEnum)
	verifyConnStatus(telematics.ConnectionStatusConnected)
}

// TestTelematicsFleetInfo tests the unauthenticated endpoint to retrieve
// basic carrier information from the telematics provider, and compare it
// to the application info.
func (s *tspConnectionTestSuite) TestTelematicsFleetInfo() {
	ctx := context.Background()
	handleID := dp_test_utils.GoldenHandleId(telematics.TSPSamsara)
	s.samsaraFixture.RegisterGoldenDataset(samsara_test_utils.GoldenDatasetOptions.WithNumVehicles(10))
	s.Require().NoError(s.deps.TelematicsDataPlatformClient.SyncEntities(ctx, handleID))

	testcases := []struct {
		description string
		updateFn    application.AppUpdateFn
	}{
		{
			description: "Happy path when telematics data is matching",
			updateFn: func(app application.Application) (application.Application, error) {
				// It's difficult to mock out the telematics VINs, so instead
				// we mock the application VINs to match the samsara VINs.
				app.TSPConnHandleId = pointer_utils.String(handleID.String())
				tspSamsara := telematics.TSPSamsara
				app.TSPEnum = &tspSamsara
				samsaraVins := []string{"SM000000000000001", "SM000000000000002", "SM000000000000003", "SM000000000000004", "SM000000000000005", "SM000000000000006", "SM000000000000007", "SM000000000000008", "SM000000000000009", "SM000000000000010"}
				var equipmentList []application.EquipmentListRecord
				for _, vin := range samsaraVins {
					equipmentList = append(equipmentList, application.EquipmentListRecord{VIN: vin})
				}
				app.EquipmentInfo.EquipmentList.Info = equipmentList
				return app, nil
			},
		},
		{
			description: "Sad path when telematics data is completely mismatched",
			updateFn: func(app application.Application) (application.Application, error) {
				app.TSPConnHandleId = pointer_utils.String(handleID.String())
				tsp := telematics.TSPSamsara
				app.TSPEnum = &tsp
				return app, nil
			},
		},
	}

	snap := snapshotter.New(s.T())
	defer snap.Verify()
	for _, testcase := range testcases {
		s.Run(testcase.description, func() {
			appId := s.createUserAndApp()
			s.Require().NoError(s.h.ApplicationDeps.ApplicationWrapper.UpdateApp(ctx, appId, testcase.updateFn))

			app, err := s.h.Metadata.GetApplication(appId)
			s.Require().Nil(err)
			s.Require().NotNil(app)
			// We can't know the handleId, so we just check that it exists
			s.Require().NotNil(app.TSPConnHandleId)
			s.Require().NotNil(app.TSPEnum)
			s.Require().Equal(handleID.String(), *app.TSPConnHandleId)

			carrierInfo, err := s.h.Api.GetTelematicsFleetInfo(handleID.String())
			s.Require().NoError(err)
			snap.Snapshot(testcase.description, carrierInfo)
			s.Require().NoError(s.h.ApplicationDeps.ApplicationWrapper.UpdateApp(ctx, appId, func(app application.Application) (application.Application, error) {
				// Clear the handleID since we re-use it for the next test.
				app.TSPConnHandleId = nil
				app.TSPEnum = nil
				return app, nil
			}))
			s.Require().NoError(s.h.ApplicationDeps.ApplicationWrapper.ArchiveApp(ctx, appId, nil))
		})
	}
}

// TestPostTSPSpeedgaugeConnsRequest tests the connection initialization request
// for Speedgauge TSPs:
// 1. Test POST without an existing app
// 2. Test empty POST
// 3. Test POST with right info
func (s *tspConnectionTestSuite) TestPostTSPSpeedgaugeConnsRequest() {
	// Case 1
	// Test POST without an existing app
	connData := s.h.TSPConnManagerHelper.MockConnData(telematics.TSPOther, telematics.ConsentKindSpeedgauge)

	conn, err := s.h.Api.PostTSPConnection("", connData)
	s.Require().NotNil(err)
	// Case 2
	appId := s.createUserAndApp()
	// Post empty connection
	emptyConn := connData
	emptyConn.Speedgauge = nil
	conn, err = s.h.Api.PostTSPConnection(appId, emptyConn)
	s.Require().NotNil(err)
	s.Require().Nil(conn)
	// Case 3
	// verify that UW is notified about the handle ID update
	s.MockAppReviewStateMachineWrapper.
		EXPECT().
		TelematicsHandleIdUpdated(gomock.Any(), gomock.Any()).
		Return(nil).
		Times(1)

	// in case of speed gauge, UW should be notified about no historical data as well
	s.MockAppReviewStateMachineWrapper.
		EXPECT().
		NoHistoricalTelematicsData(gomock.Any(), gomock.Any()).
		Return(nil).
		Times(1)
	// Create mock data and POST connection
	tspConn := s.h.TSPConnManagerHelper.MockConnData(telematics.TSPOther, telematics.ConsentKindSpeedgauge)
	conn, err = s.h.Api.PostTSPConnection(appId, tspConn)
	s.Require().Nil(err)
	s.Require().NotNil(conn.HandleID)
	handleId, err := uuid.Parse(*conn.HandleID)
	s.Require().Nil(err)
	// Get the app
	app, err := s.h.Metadata.GetApplication(appId)
	s.Require().Nil(err)
	s.Require().NotNil(app)
	// verify that the handleId is correctly set on the app
	s.Require().NotNil(app.TSPConnHandleId)
	s.Require().Equal(handleId.String(), *app.TSPConnHandleId)
	// also verify (using GetTelematicsConnection) that status is correctly
	// retrieved as "Connected"
	connInfo, err := s.h.Api.GetTelematicsConnectionInfo(handleId)
	s.Require().Nil(err)
	s.Require().Equal(oapi_app.TelematicsConnectionStatusConnected, connInfo.Status)
}

func (s *tspConnectionTestSuite) createUserAndApp() (appId string) {
	// Create user
	session, err := s.h.CreateRandomUserAndSession()
	s.Require().Nil(err)
	agencyId := s.h.Api.GetAgencyId()
	s.Require().NotNil(agencyId)

	// Create underwriter
	s.h.CreateDefaultHighestAuthorityUW()
	// Create application
	appetiteForm := s.h.MockAppetiteForm()
	appResp, err := s.h.Api.PostApplication(appetiteForm, *agencyId)
	s.Require().Nil(err)
	appId = appResp.ApplicationID
	s.Require().NotNil(appId)
	err = s.h.Api.PutIndicationForm(
		appId,
		oapi_app.IndicationForm{
			OperationsForm: test_utils.MockOperationsFormC(session.UserID, nil,
				test_utils.CoveragesALAndAPD, nil),
			ClassesAndCommoditiesForm: test_utils.MockClassesAndCommoditiesForm(),
			LossRunSummaryForm:        test_utils.MockLossRunSummaryFormC(test_utils.CoveragesALAndAPD),
		})
	s.Require().Nil(err)
	return
}

func (s *tspConnectionTestSuite) testOAuthUrl(requestUrl string) {
	parsedURL, err := url.Parse(requestUrl)
	if err != nil {
		return
	}
	params, err := url.ParseQuery(parsedURL.RawQuery)
	if err != nil {
		return
	}
	clientIdStr := params.Get("client_id")
	s.Require().NotEqual(clientIdStr, "")
	redirectUriStr := params.Get("redirect_uri")
	s.Require().NotEqual(redirectUriStr, "")
	stateStr := params.Get("state")
	s.Require().NotEqual(stateStr, "")
	responseTypeStr := params.Get("response_type")
	s.Require().NotEqual(responseTypeStr, "")
}
