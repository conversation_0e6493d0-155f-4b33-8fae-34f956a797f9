load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_test(
    name = "business_auto_apis_test",
    srcs = [
        "authorization_test.go",
        "create_application_test.go",
        "get_application_test.go",
        "get_pricing_test.go",
        "get_producers_test.go",
        "list_applications_kiwiqa_test.go",
        "list_applications_test.go",
        "put_application_test.go",
        "state_transition_test.go",
        "trigger_pricing_test.go",
        "update_application_test.go",
        "update_pre_bind_info_test.go",
    ],
    embed = [":business_auto_apis"],
    deps = [
        "//nirvana/api-server/common",
        "//nirvana/api-server/handlers/business_auto",
        "//nirvana/api-server/interceptors/business_auto/deps",
        "//nirvana/api-server/quoting_jobber",
        "//nirvana/business-auto/enums",
        "//nirvana/business-auto/model",
        "//nirvana/common-go/feature_flag_lib",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/short_id",
        "//nirvana/common-go/test_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/business_auto/application",
        "//nirvana/infra/authz",
        "//nirvana/infra/constants",
        "//nirvana/infra/fx/testfixtures/api_server_fixture",
        "//nirvana/infra/fx/testfixtures/biz_auto_app_fixture",
        "//nirvana/infra/fx/testfixtures/fixture_utils",
        "//nirvana/infra/fx/testfixtures/users_fixture",
        "//nirvana/infra/fx/testloader",
        "//nirvana/jobber/job_utils",
        "//nirvana/nirvanaapp/enums",
        "//nirvana/nirvanaapp/models/application",
        "//nirvana/openapi-specs/api_server_app/business_auto",
        "//nirvana/openapi-specs/components/business-auto",
        "//nirvana/openapi-specs/components/common",
        "//nirvana/openapi-specs/components/nirvana",
        "//nirvana/rating/rtypes",
        "@com_github_google_uuid//:uuid",
        "@com_github_launchdarkly_go_sdk_common_v3//ldvalue",
        "@com_github_oapi_codegen_runtime//types",
        "@com_github_stretchr_testify//suite",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
    ],
)

go_library(
    name = "business_auto_apis",
    srcs = [
        "pricing_job_utils.go",
        "state_utils.go",
    ],
    importpath = "nirvanatech.com/nirvana/api-server/tests/business_auto_apis",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/api-server/quoting_jobber",
        "//nirvana/business-auto/enums",
        "//nirvana/business-auto/model",
        "//nirvana/common-go/short_id",
        "//nirvana/db-api/db_wrappers/business_auto/application",
        "//nirvana/infra/fx/testfixtures/biz_auto_app_fixture",
        "//nirvana/infra/fx/testfixtures/fixture_utils",
        "//nirvana/jobber/job_utils",
        "//nirvana/openapi-specs/api_server_app/business_auto",
        "//nirvana/openapi-specs/components/business-auto",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//require",
    ],
)
