package business_auto_apis

import (
	"context"
	"net/http"
	"testing"
	"time"

	"github.com/google/uuid"
	openapitypes "github.com/oapi-codegen/runtime/types"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"

	ba_enums "nirvanatech.com/nirvana/business-auto/enums"
	ba_models "nirvanatech.com/nirvana/business-auto/model"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/short_id"
	"nirvanatech.com/nirvana/common-go/test_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/business_auto/application"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/api_server_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/biz_auto_app_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/fixture_utils"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/users_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	nirvanaapp_enums "nirvanatech.com/nirvana/nirvanaapp/enums"
	openapi "nirvanatech.com/nirvana/openapi-specs/api_server_app/business_auto"
	businessauto_oapi "nirvanatech.com/nirvana/openapi-specs/components/business-auto"
	common_oapi "nirvanatech.com/nirvana/openapi-specs/components/common"
	nirvana_oapi "nirvanatech.com/nirvana/openapi-specs/components/nirvana"
)

// GetApplicationTestSuite tests the business auto application retrieval API
type GetApplicationTestSuite struct {
	test_utils.StatsHandler
	suite.Suite
	ctx context.Context

	fxapp              *fxtest.App
	appWrapper         application.Wrapper
	businessAutoClient *openapi.Client

	usersFixture      *users_fixture.UsersFixture
	bizAutoAppFixture *biz_auto_app_fixture.BizAutoAppFixture
}

func TestGetApplicationTestSuite(t *testing.T) {
	t.Parallel()
	suite.Run(t, new(GetApplicationTestSuite))
}

func (s *GetApplicationTestSuite) SetupTest() {
	var env struct {
		fx.In
		ApiServer      api_server_fixture.ApiServer
		AppWrapper     application.Wrapper
		UsersFixture   *users_fixture.UsersFixture
		BizAutoFixture *biz_auto_app_fixture.BizAutoAppFixture
	}

	s.ctx = context.Background()
	s.fxapp = testloader.RequireStart(s.T(), &env)

	// Use underwriter for tests that need underwriter permissions (like updating underwriting overrides)
	s.businessAutoClient = env.ApiServer.BusinessAutoClient(&env.UsersFixture.Underwriters[0])

	s.appWrapper = env.AppWrapper
	s.usersFixture = env.UsersFixture
	s.bizAutoAppFixture = env.BizAutoFixture
}

func (s *GetApplicationTestSuite) TearDownTest() {
	s.fxapp.RequireStop()
}

// TestGetApplication_Success tests successful application retrieval with all fields
func (s *GetApplicationTestSuite) TestGetApplication_Success() {
	ctx := context.Background()

	// Create an application first
	createReq := businessauto_oapi.PostBusinessAutoAppRequest{
		EffectiveDate: openapitypes.Date{Time: time.Now()},
		CompanyInfo: businessauto_oapi.CompanyInfo{
			Name:                            "Test Company",
			UsState:                         "OH",
			NoOfPowerUnits:                  pointer_utils.ToPointer(int32(5)),
			NoOfEmployees:                   pointer_utils.ToPointer(int32(10)),
			PerOfEmployeesOperatingOwnAutos: pointer_utils.ToPointer(float32(50.0)),
			PrimaryIndustryClassification:   pointer_utils.ToPointer(businessauto_oapi.PrimaryIndustryClassificationContractorTrucks),
			Address: &nirvana_oapi.Address{
				Street: "123 Main Street",
				City:   "Columbus",
				State:  "OH",
				Zip:    "43215",
			},
			HasWorkCompPolicy: pointer_utils.ToPointer(true),
		},
		VehiclesInfo: &[]businessauto_oapi.VehicleInfo{
			{
				Vin:                              "1HGCM82633A123456",
				Year:                             2020,
				Make:                             "Ford",
				Model:                            "F-150",
				VehicleType:                      businessauto_oapi.VehicleTypeTruck,
				WeightClass:                      businessauto_oapi.Light,
				VehicleUse:                       businessauto_oapi.VehicleUseTowingOperations,
				BusinessUse:                      businessauto_oapi.BusinessUseCommercial,
				StateUsage:                       businessauto_oapi.StateUsageIntrastate,
				RadiusClassification:             businessauto_oapi.RadiusClassificationN0To100,
				PrincipalGaragingLocationZipCode: "12345",
				SpecialtyVehicleType:             businessauto_oapi.SpecialtyVehicleTypeArtisanContractors,
			},
		},
		CoveragesInfo: &businessauto_oapi.CoveragesInfo{
			Coverages: map[string]businessauto_oapi.Coverage{
				"CoverageAutoLiability": {
					CoverageType: common_oapi.CoverageAutoLiability,
					Limit:        pointer_utils.ToPointer(int64(1000000)),
					Deductible:   pointer_utils.ToPointer(int64(0)),
					IsEnabled:    true,
				},
			},
		},
		FilingsInfo: &businessauto_oapi.FilingsInfo{
			HasMultiStateFilings: true,
		},
		ProducerId: s.usersFixture.AgencyProducer.ID,
	}

	// Create the application via API
	createResp, err := s.businessAutoClient.CreateBusinessAutoApplication(ctx, createReq)
	s.Require().NoError(err)
	s.Equal(http.StatusCreated, createResp.StatusCode)

	createAppResp := fixture_utils.ReadResponse[businessauto_oapi.PostBusinessAutoAppResponse](s.T(), createResp)
	s.NotNil(createAppResp)

	appID := createAppResp.Id

	// Get the application through the API
	resp, err := s.businessAutoClient.GetBusinessAutoApplication(ctx, appID)
	s.Require().NoError(err)
	s.Equal(http.StatusOK, resp.StatusCode)

	getAppResp := fixture_utils.ReadResponse[businessauto_oapi.GetBusinessAutoAppResponse](s.T(), resp)
	s.NotNil(getAppResp)

	// Verify common fields
	s.NotEmpty(getAppResp.Id)
	s.NotEmpty(getAppResp.ShortID)
	s.NotEmpty(getAppResp.State)
	s.NotEmpty(getAppResp.EffectiveDate)
	s.NotEmpty(getAppResp.EffectiveDateTo)
	s.NotEmpty(getAppResp.CreatedAt)

	// Verify company info
	s.Require().NotNil(getAppResp.CompanyInfo)
	s.Equal("Test Company", getAppResp.CompanyInfo.Name)
	s.Equal(int32(5), *getAppResp.CompanyInfo.NoOfPowerUnits)
	s.Equal(int32(10), *getAppResp.CompanyInfo.NoOfEmployees)
	s.Equal(float32(50.0), *getAppResp.CompanyInfo.PerOfEmployeesOperatingOwnAutos)
	s.Equal(businessauto_oapi.PrimaryIndustryClassificationContractorTrucks, *getAppResp.CompanyInfo.PrimaryIndustryClassification)

	// Verify hasWorkCompPolicy field (should be true as set in create request)
	s.Require().NotNil(getAppResp.CompanyInfo.HasWorkCompPolicy)
	s.True(*getAppResp.CompanyInfo.HasWorkCompPolicy)

	// Verify address fields
	s.Require().NotNil(getAppResp.CompanyInfo.Address)
	s.Equal("123 Main Street", getAppResp.CompanyInfo.Address.Street)
	s.Equal("Columbus", getAppResp.CompanyInfo.Address.City)
	s.Equal("OH", getAppResp.CompanyInfo.Address.State)
	s.Equal("43215", getAppResp.CompanyInfo.Address.Zip)

	// Verify producer info
	s.NotNil(getAppResp.ProducerInfo.Name)

	// Verify vehicles info
	s.Require().NotEmpty(getAppResp.VehiclesInfo)
	s.Equal(1, len(getAppResp.VehiclesInfo))
	vehicle := getAppResp.VehiclesInfo[0]
	s.Equal("1HGCM82633A123456", vehicle.Vin)
	s.Equal(int32(2020), vehicle.Year)
	s.Equal("Ford", vehicle.Make)
	s.Equal("F-150", vehicle.Model)
	s.Equal(businessauto_oapi.VehicleTypeTruck, vehicle.VehicleType)
	s.Equal(businessauto_oapi.Light, vehicle.WeightClass)
	s.Equal(businessauto_oapi.VehicleUseTowingOperations, vehicle.VehicleUse)
	s.Equal(businessauto_oapi.BusinessUseCommercial, vehicle.BusinessUse)
	s.Equal(businessauto_oapi.StateUsageIntrastate, vehicle.StateUsage)
	s.Equal(businessauto_oapi.RadiusClassificationN0To100, vehicle.RadiusClassification)
	s.Equal("12345", vehicle.PrincipalGaragingLocationZipCode)

	// Verify filings info
	s.Require().NotNil(getAppResp.FilingsInfo)
	s.True(getAppResp.FilingsInfo.HasMultiStateFilings)
	s.False(getAppResp.FilingsInfo.HasSingleStateFilings)
	s.False(getAppResp.FilingsInfo.HasFMCSAFilings)
	s.False(getAppResp.FilingsInfo.HasDOTFilings)
}

// TestGetApplication_WithMultipleVehicles tests application retrieval with multiple vehicles
func (s *GetApplicationTestSuite) TestGetApplication_WithMultipleVehicles() {
	ctx := context.Background()

	// Create an application with multiple vehicles
	createReq := businessauto_oapi.PostBusinessAutoAppRequest{
		EffectiveDate: openapitypes.Date{Time: time.Now()},
		CompanyInfo: businessauto_oapi.CompanyInfo{
			Name:                            "Multi-Vehicle Company",
			UsState:                         "OH",
			NoOfPowerUnits:                  pointer_utils.ToPointer(int32(2)),
			NoOfEmployees:                   pointer_utils.ToPointer(int32(8)),
			PerOfEmployeesOperatingOwnAutos: pointer_utils.ToPointer(float32(40.0)),
			PrimaryIndustryClassification:   pointer_utils.ToPointer(businessauto_oapi.PrimaryIndustryClassificationWholesalersManufacturers),
		},
		VehiclesInfo: &[]businessauto_oapi.VehicleInfo{
			{
				Vin:                              "1HGCM82633A123457",
				Year:                             2021,
				Make:                             "Chevrolet",
				Model:                            "Silverado",
				VehicleType:                      businessauto_oapi.VehicleTypeTruck,
				WeightClass:                      businessauto_oapi.Medium,
				VehicleUse:                       businessauto_oapi.VehicleUseTowingOperations,
				BusinessUse:                      businessauto_oapi.BusinessUseCommercial,
				StateUsage:                       businessauto_oapi.StateUsageInterstate,
				RadiusClassification:             businessauto_oapi.RadiusClassificationN101To300,
				PrincipalGaragingLocationZipCode: "11111",
				SpecialtyVehicleType:             businessauto_oapi.SpecialtyVehicleTypeArtisanContractors,
			},
			{
				Vin:                              "1HGCM82633A123458",
				Year:                             2022,
				Make:                             "Ford",
				Model:                            "Transit",
				VehicleType:                      businessauto_oapi.VehicleTypeTractor,
				WeightClass:                      businessauto_oapi.Light,
				VehicleUse:                       businessauto_oapi.VehicleUseTowingOperations,
				BusinessUse:                      businessauto_oapi.BusinessUseCommercial,
				StateUsage:                       businessauto_oapi.StateUsageIntrastate,
				RadiusClassification:             businessauto_oapi.RadiusClassificationN0To100,
				PrincipalGaragingLocationZipCode: "22222",
				SpecialtyVehicleType:             businessauto_oapi.SpecialtyVehicleTypeArtisanContractors,
			},
		},
		CoveragesInfo: &businessauto_oapi.CoveragesInfo{
			Coverages: map[string]businessauto_oapi.Coverage{
				"CoverageAutoLiability": {
					CoverageType: common_oapi.CoverageAutoLiability,
					Limit:        pointer_utils.ToPointer(int64(1000000)),
					Deductible:   pointer_utils.ToPointer(int64(0)),
					IsEnabled:    true,
				},
			},
		},
		ProducerId: s.usersFixture.AgencyProducer.ID,
	}

	// Create the application via API
	createResp, err := s.businessAutoClient.CreateBusinessAutoApplication(ctx, createReq)
	s.Require().NoError(err)
	s.Equal(http.StatusCreated, createResp.StatusCode)

	createAppResp := fixture_utils.ReadResponse[businessauto_oapi.PostBusinessAutoAppResponse](s.T(), createResp)
	s.NotNil(createAppResp)

	appID := createAppResp.Id

	// Get the application through the API
	resp, err := s.businessAutoClient.GetBusinessAutoApplication(ctx, appID)
	s.Require().NoError(err)
	s.Equal(http.StatusOK, resp.StatusCode)

	getAppResp := fixture_utils.ReadResponse[businessauto_oapi.GetBusinessAutoAppResponse](s.T(), resp)
	s.NotNil(getAppResp)

	// Verify vehicles info
	s.Require().NotEmpty(getAppResp.VehiclesInfo)
	s.Equal(2, len(getAppResp.VehiclesInfo))

	// Verify first vehicle
	vehicle1 := getAppResp.VehiclesInfo[0]
	s.Equal("1HGCM82633A123457", vehicle1.Vin)
	s.Equal(int32(2021), vehicle1.Year)
	s.Equal("Chevrolet", vehicle1.Make)
	s.Equal("Silverado", vehicle1.Model)
	s.Equal(businessauto_oapi.VehicleTypeTruck, vehicle1.VehicleType)
	s.Equal(businessauto_oapi.Medium, vehicle1.WeightClass)

	// Verify second vehicle
	vehicle2 := getAppResp.VehiclesInfo[1]
	s.Equal("1HGCM82633A123458", vehicle2.Vin)
	s.Equal(int32(2022), vehicle2.Year)
	s.Equal("Ford", vehicle2.Make)
	s.Equal("Transit", vehicle2.Model)
	s.Equal(businessauto_oapi.VehicleTypeTractor, vehicle2.VehicleType)
	s.Equal(businessauto_oapi.Light, vehicle2.WeightClass)
}

// TestGetApplication_NotFound tests application retrieval for non-existent application
func (s *GetApplicationTestSuite) TestGetApplication_NotFound() {
	ctx := context.Background()

	// Test getting a non-existent application
	resp, err := s.businessAutoClient.GetBusinessAutoApplication(ctx, uuid.New())
	s.Require().NoError(err)
	s.Equal(http.StatusNotFound, resp.StatusCode)
}

// TestGetApplication_PrimaryCoveragesEnabledStatus tests that primary coverages return correct isEnabled status
func (s *GetApplicationTestSuite) TestGetApplication_PrimaryCoveragesEnabledStatus() {
	ctx := context.Background()

	// Create an application with only AL coverage (no APD)
	createReq := businessauto_oapi.PostBusinessAutoAppRequest{
		EffectiveDate: openapitypes.Date{Time: time.Now()},
		CompanyInfo: businessauto_oapi.CompanyInfo{
			Name:                            "Coverage Test Company",
			UsState:                         "OH",
			NoOfPowerUnits:                  pointer_utils.ToPointer(int32(3)),
			NoOfEmployees:                   pointer_utils.ToPointer(int32(6)),
			PerOfEmployeesOperatingOwnAutos: pointer_utils.ToPointer(float32(30.0)),
			PrimaryIndustryClassification:   pointer_utils.ToPointer(businessauto_oapi.PrimaryIndustryClassificationContractorTrucks),
		},
		VehiclesInfo: &[]businessauto_oapi.VehicleInfo{
			{
				Vin:                              "1HGCM82633A999999",
				Year:                             2019,
				Make:                             "Toyota",
				Model:                            "Tacoma",
				VehicleType:                      businessauto_oapi.VehicleTypeTruck,
				WeightClass:                      businessauto_oapi.Light,
				VehicleUse:                       businessauto_oapi.VehicleUseTowingOperations,
				BusinessUse:                      businessauto_oapi.BusinessUseCommercial,
				StateUsage:                       businessauto_oapi.StateUsageIntrastate,
				RadiusClassification:             businessauto_oapi.RadiusClassificationN0To100,
				PrincipalGaragingLocationZipCode: "54321",
				SpecialtyVehicleType:             businessauto_oapi.SpecialtyVehicleTypeArtisanContractors,
			},
		},
		CoveragesInfo: &businessauto_oapi.CoveragesInfo{
			Coverages: map[string]businessauto_oapi.Coverage{
				"CoverageAutoLiability": {
					CoverageType: common_oapi.CoverageAutoLiability,
					Limit:        pointer_utils.ToPointer(int64(500000)),
					Deductible:   pointer_utils.ToPointer(int64(0)),
					IsEnabled:    true,
				},
				// Note: Not including APD to test isEnabled = false
			},
		},
		ProducerId: s.usersFixture.AgencyProducer.ID,
	}

	// Create the application via API
	createResp, err := s.businessAutoClient.CreateBusinessAutoApplication(ctx, createReq)
	s.Require().NoError(err)
	s.Equal(http.StatusCreated, createResp.StatusCode)

	createAppResp := fixture_utils.ReadResponse[businessauto_oapi.PostBusinessAutoAppResponse](s.T(), createResp)
	s.NotNil(createAppResp)

	appID := createAppResp.Id

	// Get the application through the API
	resp, err := s.businessAutoClient.GetBusinessAutoApplication(ctx, appID)
	s.Require().NoError(err)
	s.Equal(http.StatusOK, resp.StatusCode)

	getAppResp := fixture_utils.ReadResponse[businessauto_oapi.GetBusinessAutoAppResponse](s.T(), resp)
	s.NotNil(getAppResp)

	// Verify coverages info contains all supported primary coverages
	s.Require().NotNil(getAppResp.CoveragesInfo)
	coverages := getAppResp.CoveragesInfo.Coverages

	// Should have 2 supported primary coverages (AL and APD)
	s.Equal(2, len(coverages))

	// Verify AutoLiability is enabled (present in application)
	alCoverage, exists := coverages["CoverageAutoLiability"]
	s.True(exists, "CoverageAutoLiability should be present")
	s.True(alCoverage.IsEnabled, "CoverageAutoLiability should be enabled")
	s.NotNil(alCoverage.Limit, "CoverageAutoLiability should have limit")
	s.Equal(int64(500000), *alCoverage.Limit)
	s.NotNil(alCoverage.Deductible, "CoverageAutoLiability should have deductible")
	s.Equal(int64(0), *alCoverage.Deductible)

	// Verify AutoPhysicalDamage is disabled (not present in application)
	apdCoverage, exists := coverages["CoverageAutoPhysicalDamage"]
	s.True(exists, "CoverageAutoPhysicalDamage should be present in response")
	s.False(apdCoverage.IsEnabled, "CoverageAutoPhysicalDamage should be disabled")
	// No limit/deductible should be set for disabled coverages
	s.Nil(apdCoverage.Limit, "CoverageAutoPhysicalDamage should not have limit when disabled")
	s.Nil(apdCoverage.Deductible, "CoverageAutoPhysicalDamage should not have deductible when disabled")
}

// TestGetApplication_PreservesLimitsAndDeductiblesAfterUnderwritingOverridesUpdate tests that limits and deductibles are preserved when updating underwriting overrides
func (s *GetApplicationTestSuite) TestGetApplication_PreservesLimitsAndDeductiblesAfterUnderwritingOverridesUpdate() {
	ctx := context.Background()

	// Create an application with both AL and APD coverages
	createReq := businessauto_oapi.PostBusinessAutoAppRequest{
		EffectiveDate: openapitypes.Date{Time: time.Now()},
		CompanyInfo: businessauto_oapi.CompanyInfo{
			Name:                            "Test Company",
			UsState:                         "OH",
			NoOfPowerUnits:                  pointer_utils.ToPointer(int32(5)),
			NoOfEmployees:                   pointer_utils.ToPointer(int32(10)),
			PerOfEmployeesOperatingOwnAutos: pointer_utils.ToPointer(float32(50.0)),
			PrimaryIndustryClassification:   pointer_utils.ToPointer(businessauto_oapi.PrimaryIndustryClassificationContractorTrucks),
		},
		VehiclesInfo: &[]businessauto_oapi.VehicleInfo{
			{
				Vin:                              "1HGCM82633A123456",
				Year:                             2020,
				Make:                             "Ford",
				Model:                            "F-150",
				VehicleType:                      businessauto_oapi.VehicleTypeTruck,
				WeightClass:                      businessauto_oapi.Light,
				VehicleUse:                       businessauto_oapi.VehicleUseTowingOperations,
				BusinessUse:                      businessauto_oapi.BusinessUseCommercial,
				StateUsage:                       businessauto_oapi.StateUsageIntrastate,
				RadiusClassification:             businessauto_oapi.RadiusClassificationN0To100,
				PrincipalGaragingLocationZipCode: "12345",
				ApdDeductible:                    pointer_utils.ToPointer(int64(2500)),
				SpecialtyVehicleType:             businessauto_oapi.SpecialtyVehicleTypeArtisanContractors,
			},
		},
		CoveragesInfo: &businessauto_oapi.CoveragesInfo{
			Coverages: map[string]businessauto_oapi.Coverage{
				"CoverageAutoLiability": {
					CoverageType: common_oapi.CoverageAutoLiability,
					Limit:        pointer_utils.ToPointer(int64(1000000)),
					Deductible:   pointer_utils.ToPointer(int64(0)),
					IsEnabled:    true,
				},
				"CoverageAutoPhysicalDamage": {
					CoverageType: common_oapi.CoverageAutoPhysicalDamage,
					Deductible:   pointer_utils.ToPointer(int64(2500)),
					IsEnabled:    true,
				},
			},
		},
		ProducerId: s.usersFixture.AgencyProducer.ID,
	}

	// Create the application via API
	createResp, err := s.businessAutoClient.CreateBusinessAutoApplication(ctx, createReq)
	s.Require().NoError(err)
	s.Equal(http.StatusCreated, createResp.StatusCode)

	createAppResp := fixture_utils.ReadResponse[businessauto_oapi.PostBusinessAutoAppResponse](s.T(), createResp)
	s.NotNil(createAppResp)

	appID := createAppResp.Id

	// Verify initial state - limits and deductibles should be present
	initialResp, err := s.businessAutoClient.GetBusinessAutoApplication(ctx, appID)
	s.Require().NoError(err)
	s.Equal(http.StatusOK, initialResp.StatusCode)

	initialAppResp := fixture_utils.ReadResponse[businessauto_oapi.GetBusinessAutoAppResponse](s.T(), initialResp)
	s.NotNil(initialAppResp)

	// Verify AL coverage has limit and deductible
	alCoverage, exists := initialAppResp.CoveragesInfo.Coverages["CoverageAutoLiability"]
	s.True(exists, "CoverageAutoLiability should be present")
	s.True(alCoverage.IsEnabled, "CoverageAutoLiability should be enabled")
	s.NotNil(alCoverage.Limit, "CoverageAutoLiability should have limit")
	s.Equal(int64(1000000), *alCoverage.Limit)
	s.NotNil(alCoverage.Deductible, "CoverageAutoLiability should have deductible")
	s.Equal(int64(0), *alCoverage.Deductible)

	// Verify APD coverage has deductible (no limit)
	apdCoverage, exists := initialAppResp.CoveragesInfo.Coverages["CoverageAutoPhysicalDamage"]
	s.True(exists, "CoverageAutoPhysicalDamage should be present")
	s.True(apdCoverage.IsEnabled, "CoverageAutoPhysicalDamage should be enabled")
	s.Nil(apdCoverage.Limit, "CoverageAutoPhysicalDamage should not have limit")

	// Update underwriting overrides (this should preserve the existing limits and deductibles)
	updateReq := businessauto_oapi.PatchUnderwritingOverridesRequest{
		AlLossFreeCredit:         pointer_utils.ToPointer(true),
		ApdLossFreeCredit:        pointer_utils.ToPointer(false),
		LossFreeCreditPercentage: pointer_utils.ToPointer(float64(10)),
	}

	updateResp, err := s.businessAutoClient.UpdateBusinessAutoApplicationUnderwritingOverrides(ctx, appID, updateReq)
	s.Require().NoError(err)
	s.Equal(http.StatusOK, updateResp.StatusCode)

	// Verify that limits and deductibles are still present after updating underwriting overrides
	finalResp, err := s.businessAutoClient.GetBusinessAutoApplication(ctx, appID)
	s.Require().NoError(err)
	s.Equal(http.StatusOK, finalResp.StatusCode)

	finalAppResp := fixture_utils.ReadResponse[businessauto_oapi.GetBusinessAutoAppResponse](s.T(), finalResp)
	s.NotNil(finalAppResp)

	// Verify AL coverage still has limit and deductible
	finalAlCoverage, exists := finalAppResp.CoveragesInfo.Coverages["CoverageAutoLiability"]
	s.True(exists, "CoverageAutoLiability should still be present")
	s.True(finalAlCoverage.IsEnabled, "CoverageAutoLiability should still be enabled")
	s.NotNil(finalAlCoverage.Limit, "CoverageAutoLiability should still have limit after underwriting overrides update")
	s.Equal(int64(1000000), *finalAlCoverage.Limit, "CoverageAutoLiability limit should be preserved")
	s.NotNil(finalAlCoverage.Deductible, "CoverageAutoLiability should still have deductible after underwriting overrides update")
	s.Equal(int64(0), *finalAlCoverage.Deductible, "CoverageAutoLiability deductible should be preserved")

	// Verify APD coverage still has deductible (no limit)
	finalApdCoverage, exists := finalAppResp.CoveragesInfo.Coverages["CoverageAutoPhysicalDamage"]
	s.True(exists, "CoverageAutoPhysicalDamage should still be present")
	s.True(finalApdCoverage.IsEnabled, "CoverageAutoPhysicalDamage should still be enabled")
	s.Nil(finalApdCoverage.Limit, "CoverageAutoPhysicalDamage should still not have limit after underwriting overrides update")

	// Verify underwriting overrides were updated correctly
	s.Require().NotNil(finalAppResp.UnderwritingOverrides)
	s.Require().NotNil(finalAppResp.UnderwritingOverrides.AlLossFreeCredit)
	s.True(*finalAppResp.UnderwritingOverrides.AlLossFreeCredit, "AL loss free credit should be enabled")
	s.Require().NotNil(finalAppResp.UnderwritingOverrides.ApdLossFreeCredit)
	s.False(*finalAppResp.UnderwritingOverrides.ApdLossFreeCredit, "APD loss free credit should be disabled")
	s.Require().NotNil(finalAppResp.UnderwritingOverrides.LossFreeCreditPercentage)
	s.Equal(float64(10), *finalAppResp.UnderwritingOverrides.LossFreeCreditPercentage, "Loss free credit percentage should be set to 10")
}

// TestGetApplication_VehicleDeductibles tests retrieving application with vehicle-level deductibles
func (s *GetApplicationTestSuite) TestGetApplication_VehicleDeductibles() {
	ctx := context.Background()

	// Create an app with multiple vehicles and vehicle-specific deductibles
	app := *s.bizAutoAppFixture.MinimalApp
	app.ID = uuid.New()
	app.ShortID = short_id.ShortID("GETTEST1")

	// Add multiple vehicles with different VINs
	vehicleDeductibleApp := &[]ba_models.VehicleInfo{
		{
			VIN:                              "1HGBH41JXMN109186",
			Year:                             2020,
			Make:                             "Ford",
			Model:                            "F-150",
			VehicleType:                      nirvanaapp_enums.VehicleTypeTruck,
			WeightClass:                      ba_enums.WeightClassLight,
			VehicleUse:                       ba_enums.VehicleUseTowingOperations,
			BusinessUse:                      ba_enums.BusinessUseCommercial,
			StateUsage:                       ba_enums.StateUsageIntrastate,
			RadiusClassification:             ba_enums.RadiusClassification0To100,
			PrincipalGaragingLocationZipCode: "43322",
			APDDeductible:                    pointer_utils.ToPointer(int64(500)),
			SpecialtyVehicleType:             ba_enums.SpecialtyVehicleTypeArtisanContractors,
		},
		{
			VIN:                              "2HGCM82633A654321",
			Year:                             2021,
			Make:                             "Chevrolet",
			Model:                            "Silverado",
			VehicleType:                      nirvanaapp_enums.VehicleTypeTruck,
			WeightClass:                      ba_enums.WeightClassMedium,
			VehicleUse:                       ba_enums.VehicleUseTowingOperations,
			BusinessUse:                      ba_enums.BusinessUseCommercial,
			StateUsage:                       ba_enums.StateUsageIntrastate,
			RadiusClassification:             ba_enums.RadiusClassification0To100,
			PrincipalGaragingLocationZipCode: "43322",
			APDDeductible:                    pointer_utils.ToPointer(int64(1000)),
			SpecialtyVehicleType:             ba_enums.SpecialtyVehicleTypeArtisanContractors,
		},
	}
	app.VehiclesInfo = vehicleDeductibleApp

	err := s.appWrapper.Insert(ctx, &app)
	s.Require().NoError(err)

	appID := app.ID

	// First, set up vehicle deductibles via underwriting overrides
	req := businessauto_oapi.PatchUnderwritingOverridesRequest{
		AncillaryCoverages: &[]businessauto_oapi.AncillaryCoverage{
			{
				Coverage:           common_oapi.CoverageUninsuredMotoristBodilyInjury,
				IsEnabled:          true,
				SelectedLimit:      pointer_utils.ToPointer(int64(100000)),
				SelectedDeductible: pointer_utils.ToPointer(int64(0)),
			},
			{
				Coverage:           common_oapi.CoverageUninsuredMotoristPropertyDamage,
				IsEnabled:          true,
				SelectedLimit:      pointer_utils.ToPointer(int64(100000)),
				SelectedDeductible: pointer_utils.ToPointer(int64(2500)),
				VehicleDeductibles: &[]businessauto_oapi.VehicleDeductible{
					{
						Vin:                "1HGBH41JXMN109186",
						SelectedDeductible: int64(1000),
					},
					{
						Vin:                "2HGCM82633A654321",
						SelectedDeductible: int64(2000),
					},
				},
			},
		},
	}

	// Set up the underwriting overrides
	updateResp, err := s.businessAutoClient.UpdateBusinessAutoApplicationUnderwritingOverrides(ctx, appID, req)
	s.Require().NoError(err)
	s.Equal(http.StatusOK, updateResp.StatusCode)

	// Now get the application and verify vehicle deductibles are returned
	getResp, err := s.businessAutoClient.GetBusinessAutoApplication(ctx, appID)
	s.Require().NoError(err)
	s.Equal(http.StatusOK, getResp.StatusCode)

	appResp := fixture_utils.ReadResponse[businessauto_oapi.GetBusinessAutoAppResponse](s.T(), getResp)
	s.Require().NotNil(appResp)

	// Verify the basic application structure
	s.Equal(appID, appResp.Id)
	s.Require().NotNil(appResp.VehiclesInfo)
	s.Len(appResp.VehiclesInfo, 2)

	// Verify underwriting overrides with ancillary coverages
	s.Require().NotNil(appResp.UnderwritingOverrides)
	s.Require().NotNil(appResp.UnderwritingOverrides.AncillaryCoverages)

	ancillaryCoverages := *appResp.UnderwritingOverrides.AncillaryCoverages
	s.Require().GreaterOrEqual(len(ancillaryCoverages), 1)

	// Find the Uninsured Motorist Property Damage coverage and verify vehicle deductibles
	var umpdCoverage *businessauto_oapi.AncillaryCoverage
	for _, coverage := range ancillaryCoverages {
		if coverage.Coverage == common_oapi.CoverageUninsuredMotoristPropertyDamage {
			umpdCoverage = &coverage
			break
		}
	}

	s.Require().NotNil(umpdCoverage, "Uninsured Motorist Property Damage coverage should be present")
	s.True(umpdCoverage.IsEnabled)
	s.Require().NotNil(umpdCoverage.VehicleDeductibles)

	vehicleDeductibles := *umpdCoverage.VehicleDeductibles
	s.Require().Len(vehicleDeductibles, 2)

	// Create a map for easier verification
	vehicleDeductibleMap := make(map[string]int64)
	for _, vd := range vehicleDeductibles {
		vehicleDeductibleMap[vd.Vin] = vd.SelectedDeductible
	}

	s.Equal(int64(1000), vehicleDeductibleMap["1HGBH41JXMN109186"])
	s.Equal(int64(2000), vehicleDeductibleMap["2HGCM82633A654321"])
}

// TestGetApplication_VehicleDeductiblesWithAPDDeductibles tests retrieving application with both APD and ancillary coverage vehicle deductibles
func (s *GetApplicationTestSuite) TestGetApplication_VehicleDeductiblesWithAPDDeductibles() {
	ctx := context.Background()

	// Create an app with APD deductibles on vehicles
	app := *s.bizAutoAppFixture.MinimalApp
	app.ID = uuid.New()
	app.ShortID = short_id.ShortID("GETTEST2")

	// Add vehicle with APD deductible
	vehicleDeductibleApp := &[]ba_models.VehicleInfo{
		{
			VIN:                              "1HGBH41JXMN109186",
			Year:                             2020,
			Make:                             "Ford",
			Model:                            "F-150",
			VehicleType:                      nirvanaapp_enums.VehicleTypeTruck,
			WeightClass:                      ba_enums.WeightClassLight,
			VehicleUse:                       ba_enums.VehicleUseTowingOperations,
			BusinessUse:                      ba_enums.BusinessUseCommercial,
			StateUsage:                       ba_enums.StateUsageIntrastate,
			RadiusClassification:             ba_enums.RadiusClassification0To100,
			PrincipalGaragingLocationZipCode: "43322",
			APDDeductible:                    pointer_utils.ToPointer(int64(500)),
			SpecialtyVehicleType:             ba_enums.SpecialtyVehicleTypeArtisanContractors,
		},
	}
	app.VehiclesInfo = vehicleDeductibleApp

	err := s.appWrapper.Insert(ctx, &app)
	s.Require().NoError(err)

	appID := app.ID

	// Set up ancillary coverage with vehicle deductibles
	req := businessauto_oapi.PatchUnderwritingOverridesRequest{
		AncillaryCoverages: &[]businessauto_oapi.AncillaryCoverage{
			{
				Coverage:           common_oapi.CoverageUninsuredMotoristBodilyInjury,
				IsEnabled:          true,
				SelectedLimit:      pointer_utils.ToPointer(int64(100000)),
				SelectedDeductible: pointer_utils.ToPointer(int64(0)),
			},
			{
				Coverage:           common_oapi.CoverageUninsuredMotoristPropertyDamage,
				IsEnabled:          true,
				SelectedLimit:      pointer_utils.ToPointer(int64(100000)),
				SelectedDeductible: pointer_utils.ToPointer(int64(2500)),
				VehicleDeductibles: &[]businessauto_oapi.VehicleDeductible{
					{
						Vin:                "1HGBH41JXMN109186",
						SelectedDeductible: int64(1500),
					},
				},
			},
		},
	}

	// Set up the underwriting overrides
	updateResp, err := s.businessAutoClient.UpdateBusinessAutoApplicationUnderwritingOverrides(ctx, appID, req)
	s.Require().NoError(err)
	s.Equal(http.StatusOK, updateResp.StatusCode)

	// Now get the application and verify both APD and ancillary vehicle deductibles are returned
	getResp, err := s.businessAutoClient.GetBusinessAutoApplication(ctx, appID)
	s.Require().NoError(err)
	s.Equal(http.StatusOK, getResp.StatusCode)

	appResp := fixture_utils.ReadResponse[businessauto_oapi.GetBusinessAutoAppResponse](s.T(), getResp)
	s.Require().NotNil(appResp)

	// Verify vehicle APD deductible is present
	s.Require().NotNil(appResp.VehiclesInfo)
	s.Len(appResp.VehiclesInfo, 1)
	vehicle := appResp.VehiclesInfo[0]
	s.Equal("1HGBH41JXMN109186", vehicle.Vin)
	s.Require().NotNil(vehicle.ApdDeductible)
	s.Equal(int64(500), *vehicle.ApdDeductible)

	// Verify ancillary coverage vehicle deductible is present
	s.Require().NotNil(appResp.UnderwritingOverrides)
	s.Require().NotNil(appResp.UnderwritingOverrides.AncillaryCoverages)

	ancillaryCoverages := *appResp.UnderwritingOverrides.AncillaryCoverages
	var umpdCoverage *businessauto_oapi.AncillaryCoverage
	for _, coverage := range ancillaryCoverages {
		if coverage.Coverage == common_oapi.CoverageUninsuredMotoristPropertyDamage {
			umpdCoverage = &coverage
			break
		}
	}

	s.Require().NotNil(umpdCoverage, "Uninsured Motorist Property Damage coverage should be present")
	s.Require().NotNil(umpdCoverage.VehicleDeductibles)
	vehicleDeductibles := *umpdCoverage.VehicleDeductibles
	s.Require().Len(vehicleDeductibles, 1)
	s.Equal("1HGBH41JXMN109186", vehicleDeductibles[0].Vin)
	s.Equal(int64(1500), vehicleDeductibles[0].SelectedDeductible)
}

// TestGetApplication_MultipleAncillaryCoveragesWithVehicleDeductibles tests retrieving application with multiple ancillary coverages having vehicle deductibles
func (s *GetApplicationTestSuite) TestGetApplication_MultipleAncillaryCoveragesWithVehicleDeductibles() {
	ctx := context.Background()

	// Create an app with multiple ancillary coverages having vehicle deductibles
	app := *s.bizAutoAppFixture.MinimalApp
	app.ID = uuid.New()
	app.ShortID = short_id.ShortID("GETTEST3")
	err := s.appWrapper.Insert(ctx, &app)
	s.Require().NoError(err)

	appID := app.ID

	// Set up ancillary coverage with vehicle deductibles (only UMPD since GL/MTC is not supported in business auto)
	req := businessauto_oapi.PatchUnderwritingOverridesRequest{
		AncillaryCoverages: &[]businessauto_oapi.AncillaryCoverage{
			{
				Coverage:           common_oapi.CoverageUninsuredMotoristBodilyInjury,
				IsEnabled:          true,
				SelectedLimit:      pointer_utils.ToPointer(int64(100000)),
				SelectedDeductible: pointer_utils.ToPointer(int64(0)),
			},
			{
				Coverage:           common_oapi.CoverageUninsuredMotoristPropertyDamage,
				IsEnabled:          true,
				SelectedLimit:      pointer_utils.ToPointer(int64(100000)),
				SelectedDeductible: pointer_utils.ToPointer(int64(2500)),
				VehicleDeductibles: &[]businessauto_oapi.VehicleDeductible{
					{
						Vin:                "1HGBH41JXMN109186",
						SelectedDeductible: int64(1500),
					},
				},
			},
		},
	}

	// Set up the underwriting overrides
	updateResp, err := s.businessAutoClient.UpdateBusinessAutoApplicationUnderwritingOverrides(ctx, appID, req)
	s.Require().NoError(err)
	s.Equal(http.StatusOK, updateResp.StatusCode)

	// Now get the application and verify ancillary coverage vehicle deductibles are returned
	getResp, err := s.businessAutoClient.GetBusinessAutoApplication(ctx, appID)
	s.Require().NoError(err)
	s.Equal(http.StatusOK, getResp.StatusCode)

	appResp := fixture_utils.ReadResponse[businessauto_oapi.GetBusinessAutoAppResponse](s.T(), getResp)
	s.Require().NotNil(appResp)
	s.Require().NotNil(appResp.UnderwritingOverrides)
	s.Require().NotNil(appResp.UnderwritingOverrides.AncillaryCoverages)

	ancillaryCoverages := *appResp.UnderwritingOverrides.AncillaryCoverages
	s.Require().GreaterOrEqual(len(ancillaryCoverages), 1)

	// Find the Uninsured Motorist Property Damage coverage and verify vehicle deductibles
	var umpdCoverage *businessauto_oapi.AncillaryCoverage
	for _, coverage := range ancillaryCoverages {
		if coverage.Coverage == common_oapi.CoverageUninsuredMotoristPropertyDamage && coverage.IsEnabled {
			umpdCoverage = &coverage
			break
		}
	}

	s.Require().NotNil(umpdCoverage, "Uninsured Motorist Property Damage coverage should be present")
	s.True(umpdCoverage.IsEnabled)
	s.Require().NotNil(umpdCoverage.VehicleDeductibles)

	vehicleDeductibles := *umpdCoverage.VehicleDeductibles
	s.Require().Len(vehicleDeductibles, 1)
	s.Equal("1HGBH41JXMN109186", vehicleDeductibles[0].Vin)
	s.Equal(int64(1500), vehicleDeductibles[0].SelectedDeductible)
}

// TestGetApplication_UMPDVehicleDeductibles tests retrieving application with UMPD vehicle deductibles
func (s *GetApplicationTestSuite) TestGetApplication_UMPDVehicleDeductibles() {
	ctx := context.Background()

	// Create an app with ancillary coverage and vehicle deductibles
	app := *s.bizAutoAppFixture.MinimalApp
	app.ID = uuid.New()
	app.ShortID = short_id.ShortID("GETTEST4")
	err := s.appWrapper.Insert(ctx, &app)
	s.Require().NoError(err)

	appID := app.ID

	// Set up ancillary coverage with vehicle deductibles (UMPD is vehicle-level and requires vehicle deductibles)
	req := businessauto_oapi.PatchUnderwritingOverridesRequest{
		AncillaryCoverages: &[]businessauto_oapi.AncillaryCoverage{
			{
				Coverage:           common_oapi.CoverageUninsuredMotoristBodilyInjury,
				IsEnabled:          true,
				SelectedLimit:      pointer_utils.ToPointer(int64(100000)),
				SelectedDeductible: pointer_utils.ToPointer(int64(0)),
			},
			{
				Coverage:           common_oapi.CoverageUninsuredMotoristPropertyDamage,
				IsEnabled:          true,
				SelectedLimit:      pointer_utils.ToPointer(int64(100000)),
				SelectedDeductible: pointer_utils.ToPointer(int64(2500)),
				// No SelectedDeductible since this is vehicle-level coverage
				VehicleDeductibles: &[]businessauto_oapi.VehicleDeductible{
					{
						Vin:                "1HGBH41JXMN109186",
						SelectedDeductible: int64(1500),
					},
				},
			},
		},
	}

	// Set up the underwriting overrides
	updateResp, err := s.businessAutoClient.UpdateBusinessAutoApplicationUnderwritingOverrides(ctx, appID, req)
	s.Require().NoError(err)
	s.Equal(http.StatusOK, updateResp.StatusCode)

	// Now get the application and verify vehicle deductibles are returned
	getResp, err := s.businessAutoClient.GetBusinessAutoApplication(ctx, appID)
	s.Require().NoError(err)
	s.Equal(http.StatusOK, getResp.StatusCode)

	appResp := fixture_utils.ReadResponse[businessauto_oapi.GetBusinessAutoAppResponse](s.T(), getResp)
	s.Require().NotNil(appResp)
	s.Require().NotNil(appResp.UnderwritingOverrides)
	s.Require().NotNil(appResp.UnderwritingOverrides.AncillaryCoverages)

	ancillaryCoverages := *appResp.UnderwritingOverrides.AncillaryCoverages
	var umpdCoverage *businessauto_oapi.AncillaryCoverage
	for _, coverage := range ancillaryCoverages {
		if coverage.Coverage == common_oapi.CoverageUninsuredMotoristPropertyDamage {
			umpdCoverage = &coverage
			break
		}
	}

	s.Require().NotNil(umpdCoverage, "Uninsured Motorist Property Damage coverage should be present")
	s.True(umpdCoverage.IsEnabled)
	s.Require().NotNil(umpdCoverage.SelectedLimit)
	s.Equal(int64(100000), *umpdCoverage.SelectedLimit)
	// Vehicle-level coverages should not have a general SelectedDeductible
	s.Nil(umpdCoverage.SelectedDeductible, "Vehicle-level coverage should not have general deductible")

	// Verify vehicle deductibles are present
	s.Require().NotNil(umpdCoverage.VehicleDeductibles)
	vehicleDeductibles := *umpdCoverage.VehicleDeductibles
	s.Require().Len(vehicleDeductibles, 1)
	s.Equal("1HGBH41JXMN109186", vehicleDeductibles[0].Vin)
	s.Equal(int64(1500), vehicleDeductibles[0].SelectedDeductible)
}

// TestGetApplication_PA_WorkLossBenefits_OnlyAggregateLimit tests that for PA WorkLossBenefits coverage,
// only the aggregate limit is returned in the API response even when both aggregate and monthly limits exist internally
func (s *GetApplicationTestSuite) TestGetApplication_PA_WorkLossBenefits_OnlyAggregateLimit() {
	ctx := context.Background()

	// Create an application using the fixture approach (like other tests)
	app := *s.bizAutoAppFixture.MinimalApp
	app.ID = uuid.New()
	app.ShortID = short_id.ShortID("PAWORKTEST")

	// Set the state to PA (Pennsylvania)
	app.CompanyInfo.USState = us_states.PA

	// Ensure we have AutoLiability primary coverage (required for WorkLossBenefits)
	if app.CoveragesInfo == nil {
		app.CoveragesInfo = &ba_models.CoveragesInfo{}
	}

	// Add AutoLiability primary coverage if not present
	hasAutoLiability := false
	for _, primaryCov := range app.CoveragesInfo.PrimaryCoverages {
		if primaryCov.ID == app_enums.CoverageAutoLiability {
			hasAutoLiability = true
			break
		}
	}

	if !hasAutoLiability {
		app.CoveragesInfo.PrimaryCoverages = append(app.CoveragesInfo.PrimaryCoverages, ba_models.PrimaryCoverage{
			ID: app_enums.CoverageAutoLiability,
			SubCoverageIDs: []app_enums.Coverage{
				app_enums.CoverageBodilyInjury,
				app_enums.CoveragePropertyDamage,
			},
		})
	}

	// Insert the application into the database
	err := s.appWrapper.Insert(ctx, &app)
	s.Require().NoError(err)

	appID := app.ID

	// Add WorkLossBenefits coverage with an aggregate limit that maps to both aggregate and monthly limits internally
	// Using 25,000 aggregate which should map to 1,500 monthly based on our mapping
	updateReq := businessauto_oapi.PatchUnderwritingOverridesRequest{
		AncillaryCoverages: &[]businessauto_oapi.AncillaryCoverage{
			{
				Coverage:      common_oapi.CoverageWorkLossBenefits,
				IsEnabled:     true,
				SelectedLimit: pointer_utils.ToPointer(int64(25000)), // This should create both aggregate (25000) and monthly (1500) limits internally
			},
		},
	}

	// Update the application with WorkLossBenefits coverage
	updateResp, err := s.businessAutoClient.UpdateBusinessAutoApplicationUnderwritingOverrides(ctx, appID, updateReq)
	s.Require().NoError(err)
	s.Equal(http.StatusOK, updateResp.StatusCode)

	// Get the application through the API
	getResp, err := s.businessAutoClient.GetBusinessAutoApplication(ctx, appID)
	s.Require().NoError(err)
	s.Equal(http.StatusOK, getResp.StatusCode)

	getAppResp := fixture_utils.ReadResponse[businessauto_oapi.GetBusinessAutoAppResponse](s.T(), getResp)
	s.NotNil(getAppResp)

	// Verify that the application is in PA - fix type comparison
	s.Require().NotNil(getAppResp.CompanyInfo)
	s.Equal("PA", string(getAppResp.CompanyInfo.UsState))

	// Verify that WorkLossBenefits coverage is present in ancillary coverages
	s.Require().NotNil(getAppResp.UnderwritingOverrides)
	s.Require().NotNil(getAppResp.UnderwritingOverrides.AncillaryCoverages)

	ancillaryCoverages := *getAppResp.UnderwritingOverrides.AncillaryCoverages
	s.Require().GreaterOrEqual(len(ancillaryCoverages), 1)

	// Find the WorkLossBenefits coverage
	var workLossBenefitsCoverage *businessauto_oapi.AncillaryCoverage
	for _, coverage := range ancillaryCoverages {
		if coverage.Coverage == common_oapi.CoverageWorkLossBenefits {
			workLossBenefitsCoverage = &coverage
			break
		}
	}

	s.Require().NotNil(workLossBenefitsCoverage, "WorkLossBenefits coverage should be present")
	s.True(workLossBenefitsCoverage.IsEnabled, "WorkLossBenefits coverage should be enabled")

	// Verify that only the aggregate limit is returned (25,000), not the monthly limit (1,500)
	s.Require().NotNil(workLossBenefitsCoverage.SelectedLimit, "WorkLossBenefits should have a selected limit")
	s.Equal(int64(25000), *workLossBenefitsCoverage.SelectedLimit, "Should return the aggregate limit (25,000), not the monthly limit (1,500)")

	// Verify that this is indeed the aggregate limit by checking it matches what the user selected
	// The monthly limit (1,500) should not be exposed in the API response
	// Note: The internal system should have created both limits, but the API should only show the aggregate one
}
