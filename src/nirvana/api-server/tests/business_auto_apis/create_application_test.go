package business_auto_apis

import (
	"context"
	"net/http"
	"testing"
	"time"

	"github.com/google/uuid"
	openapitypes "github.com/oapi-codegen/runtime/types"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/test_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	"nirvanatech.com/nirvana/db-api/db_wrappers/business_auto/application"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/api_server_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/biz_auto_app_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/fixture_utils"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/users_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	openapi "nirvanatech.com/nirvana/openapi-specs/api_server_app/business_auto"
	businessauto_oapi "nirvanatech.com/nirvana/openapi-specs/components/business-auto"
	"nirvanatech.com/nirvana/openapi-specs/components/common"
	"nirvanatech.com/nirvana/rating/rtypes"
)

// CreateApplicationTestSuite tests the business auto application creation API
type CreateApplicationTestSuite struct {
	test_utils.StatsHandler
	suite.Suite
	ctx context.Context

	fxapp              *fxtest.App
	appWrapper         application.Wrapper
	businessAutoClient *openapi.Client

	usersFixture      *users_fixture.UsersFixture
	bizAutoAppFixture *biz_auto_app_fixture.BizAutoAppFixture
}

func TestCreateApplicationTestSuite(t *testing.T) {
	t.Parallel()
	suite.Run(t, new(CreateApplicationTestSuite))
}

func (s *CreateApplicationTestSuite) SetupTest() {
	var env struct {
		fx.In
		ApiServer      api_server_fixture.ApiServer
		AppWrapper     application.Wrapper
		UsersFixture   *users_fixture.UsersFixture
		BizAutoFixture *biz_auto_app_fixture.BizAutoAppFixture
	}

	s.ctx = context.Background()
	s.fxapp = testloader.RequireStart(s.T(), &env)

	// Use the business auto contractor user for tests that need operator permissions
	s.businessAutoClient = env.ApiServer.BusinessAutoClient(&env.UsersFixture.BusinessAutoContractor)

	s.appWrapper = env.AppWrapper
	s.usersFixture = env.UsersFixture
	s.bizAutoAppFixture = env.BizAutoFixture
}

func (s *CreateApplicationTestSuite) TearDownTest() {
	s.fxapp.RequireStop()
}

// TestCreateApplication_Success tests successful application creation with all required fields
func (s *CreateApplicationTestSuite) TestCreateApplication_Success() {
	ctx := context.Background()

	// Create request body with all required fields
	req := businessauto_oapi.PostBusinessAutoAppRequest{
		CompanyInfo: businessauto_oapi.CompanyInfo{
			Name:                            "Test Company",
			NoOfPowerUnits:                  pointer_utils.ToPointer(int32(5)),
			NoOfEmployees:                   pointer_utils.ToPointer(int32(10)),
			PerOfEmployeesOperatingOwnAutos: pointer_utils.ToPointer(float32(50.0)),
			PrimaryIndustryClassification:   pointer_utils.ToPointer(businessauto_oapi.PrimaryIndustryClassificationContractorTrucks),
			HasWorkCompPolicy:               pointer_utils.ToPointer(false), // include hasWorkCompPolicy field
			UsState:                         "OH",
		},
		VehiclesInfo: &[]businessauto_oapi.VehicleInfo{
			{
				Vin:                              "1HGCM82633A123456",
				VehicleType:                      businessauto_oapi.VehicleTypeTruck,
				WeightClass:                      businessauto_oapi.Light,
				VehicleUse:                       businessauto_oapi.VehicleUseTowingOperations,
				BusinessUse:                      businessauto_oapi.BusinessUseCommercial,
				StateUsage:                       businessauto_oapi.StateUsageIntrastate,
				RadiusClassification:             businessauto_oapi.RadiusClassificationN0To100,
				PrincipalGaragingLocationZipCode: "12345",
				SpecialtyVehicleType:             businessauto_oapi.SpecialtyVehicleTypeArtisanContractors,
			},
		},
		CoveragesInfo: &businessauto_oapi.CoveragesInfo{
			Coverages: map[string]businessauto_oapi.Coverage{
				"CoverageAutoLiability": {
					CoverageType: common.CoverageAutoLiability,
					Limit:        pointer_utils.ToPointer(int64(1000000)),
					Deductible:   pointer_utils.ToPointer(int64(0)),
				},
			},
		},
		FilingsInfo: &businessauto_oapi.FilingsInfo{
			HasMultiStateFilings: true,
		},
		EffectiveDate: openapitypes.Date{
			Time: time.Now(),
		},
		ProducerId: s.usersFixture.AgencyProducer.ID,
	}

	// Make the API call
	resp, err := s.businessAutoClient.CreateBusinessAutoApplication(ctx, req)
	s.Require().NoError(err)
	s.Equal(http.StatusCreated, resp.StatusCode)

	// Parse and verify the response contains the created application
	s.NotNil(resp)

	// Parse the response body
	createAppResp := fixture_utils.ReadResponse[businessauto_oapi.PostBusinessAutoAppResponse](s.T(), resp)
	s.Require().NotNil(createAppResp)

	// Verify ID is set and is a valid UUID
	s.NotEqual(uuid.Nil, createAppResp.Id)

	// Fetch the created application to verify RateML config
	app, err := s.appWrapper.GetByID(ctx, createAppResp.Id)
	s.Require().NoError(err)
	s.NotNil(app.ModelPinConfig)
	s.NotNil(app.ModelPinConfig.RateML)
	s.Equal(rtypes.ProviderNico, app.ModelPinConfig.RateML.Provider)
	s.Equal(rtypes.Version000, app.ModelPinConfig.RateML.Version)
	s.Equal(us_states.OH, app.ModelPinConfig.RateML.USState)
}

// TestCreateApplication_MinimalFields tests application creation with only required fields
func (s *CreateApplicationTestSuite) TestCreateApplication_MinimalFields() {
	ctx := context.Background()

	// Create request body with minimal required fields
	req := businessauto_oapi.PostBusinessAutoAppRequest{
		CompanyInfo: businessauto_oapi.CompanyInfo{
			Name:                            "Minimal Test Company",
			UsState:                         "OH",
			NoOfPowerUnits:                  pointer_utils.ToPointer(int32(1)),
			NoOfEmployees:                   pointer_utils.ToPointer(int32(1)),
			PerOfEmployeesOperatingOwnAutos: pointer_utils.ToPointer(float32(0.0)),
		},
		VehiclesInfo: &[]businessauto_oapi.VehicleInfo{
			{
				Vin:                              "1HGCM82633A123457",
				Year:                             2020,
				Make:                             "Toyota",
				Model:                            "Camry",
				VehicleType:                      businessauto_oapi.VehicleTypeTruck,
				WeightClass:                      businessauto_oapi.Light,
				VehicleUse:                       businessauto_oapi.VehicleUseTowingOperations,
				BusinessUse:                      businessauto_oapi.BusinessUseCommercial,
				StateUsage:                       businessauto_oapi.StateUsageIntrastate,
				RadiusClassification:             businessauto_oapi.RadiusClassificationN0To100,
				PrincipalGaragingLocationZipCode: "54321",
				SpecialtyVehicleType:             businessauto_oapi.SpecialtyVehicleTypeArtisanContractors,
			},
		},
		CoveragesInfo: &businessauto_oapi.CoveragesInfo{
			Coverages: map[string]businessauto_oapi.Coverage{
				"CoverageAutoLiability": {
					Limit:      pointer_utils.ToPointer(int64(500000)),
					Deductible: pointer_utils.ToPointer(int64(0)),
				},
			},
		},
		EffectiveDate: openapitypes.Date{
			Time: time.Now(),
		},
		ProducerId: s.usersFixture.AgencyProducer.ID,
	}

	// Make the API call
	resp, err := s.businessAutoClient.CreateBusinessAutoApplication(ctx, req)
	s.Require().NoError(err)
	s.Equal(http.StatusCreated, resp.StatusCode)

	// Parse and verify the response contains the created application
	s.NotNil(resp)

	// Parse the response body
	createAppResp := fixture_utils.ReadResponse[businessauto_oapi.PostBusinessAutoAppResponse](s.T(), resp)
	s.Require().NotNil(createAppResp)

	// Verify ID is set and is a valid UUID
	s.NotEqual(uuid.Nil, createAppResp.Id)
}

// TestCreateApplication_WithMultipleVehicles tests application creation with multiple vehicles
func (s *CreateApplicationTestSuite) TestCreateApplication_WithMultipleVehicles() {
	ctx := context.Background()

	// Create request body with multiple vehicles
	req := businessauto_oapi.PostBusinessAutoAppRequest{
		CompanyInfo: businessauto_oapi.CompanyInfo{
			Name:                            "Multi-Vehicle Company",
			NoOfPowerUnits:                  pointer_utils.ToPointer(int32(3)),
			NoOfEmployees:                   pointer_utils.ToPointer(int32(15)),
			PerOfEmployeesOperatingOwnAutos: pointer_utils.ToPointer(float32(60.0)),
			PrimaryIndustryClassification:   pointer_utils.ToPointer(businessauto_oapi.PrimaryIndustryClassificationContractorTrucks),
			UsState:                         "OH",
		},
		VehiclesInfo: &[]businessauto_oapi.VehicleInfo{
			{
				Vin:                              "1HGCM82633A123458",
				Year:                             2021,
				Make:                             "Chevrolet",
				Model:                            "Silverado",
				VehicleType:                      businessauto_oapi.VehicleTypeTruck,
				WeightClass:                      businessauto_oapi.Medium,
				VehicleUse:                       businessauto_oapi.VehicleUseTowingOperations,
				BusinessUse:                      businessauto_oapi.BusinessUseCommercial,
				StateUsage:                       businessauto_oapi.StateUsageInterstate,
				RadiusClassification:             businessauto_oapi.RadiusClassificationN101To300,
				PrincipalGaragingLocationZipCode: "11111",
				ApdDeductible:                    pointer_utils.Int64(int64(500)),
				SpecialtyVehicleType:             businessauto_oapi.SpecialtyVehicleTypeArtisanContractors,
			},
			{
				Vin:                              "1HGCM82633A123459",
				Year:                             2022,
				Make:                             "Ford",
				Model:                            "Transit",
				VehicleType:                      businessauto_oapi.VehicleTypeTrailer,
				WeightClass:                      businessauto_oapi.Light,
				VehicleUse:                       businessauto_oapi.VehicleUseTowingOperations,
				BusinessUse:                      businessauto_oapi.BusinessUseCommercial,
				StateUsage:                       businessauto_oapi.StateUsageIntrastate,
				RadiusClassification:             businessauto_oapi.RadiusClassificationN0To100,
				PrincipalGaragingLocationZipCode: "22222",
				ApdDeductible:                    pointer_utils.Int64(int64(500)),
				SpecialtyVehicleType:             businessauto_oapi.SpecialtyVehicleTypeArtisanContractors,
			},
		},
		CoveragesInfo: &businessauto_oapi.CoveragesInfo{
			Coverages: map[string]businessauto_oapi.Coverage{
				"CoverageAutoLiability": {
					Limit:      pointer_utils.ToPointer(int64(1000000)),
					Deductible: pointer_utils.ToPointer(int64(0)),
				},
				"CoverageAutoPhysicalDamage": {},
			},
		},
		FilingsInfo: &businessauto_oapi.FilingsInfo{
			HasMultiStateFilings:  true,
			HasSingleStateFilings: true,
		},
		EffectiveDate: openapitypes.Date{
			Time: time.Now(),
		},
		ProducerId: s.usersFixture.AgencyProducer.ID,
	}

	// Make the API call
	resp, err := s.businessAutoClient.CreateBusinessAutoApplication(ctx, req)
	s.Require().NoError(err)
	s.Equal(http.StatusCreated, resp.StatusCode)

	// Parse and verify the response contains the created application
	s.NotNil(resp)

	// Parse the response body
	createAppResp := fixture_utils.ReadResponse[businessauto_oapi.PostBusinessAutoAppResponse](s.T(), resp)
	s.Require().NotNil(createAppResp)

	// Verify ID is set and is a valid UUID
	s.NotEqual(uuid.Nil, createAppResp.Id)
}

// TestCreateApplication_DuplicateVINs tests that duplicate VINs are rejected
func (s *CreateApplicationTestSuite) TestCreateApplication_DuplicateVINs() {
	ctx := context.Background()

	// Create request body with duplicate VINs
	req := businessauto_oapi.PostBusinessAutoAppRequest{
		CompanyInfo: businessauto_oapi.CompanyInfo{
			Name:                            "Duplicate VIN Test Company",
			NoOfPowerUnits:                  pointer_utils.ToPointer(int32(2)),
			NoOfEmployees:                   pointer_utils.ToPointer(int32(5)),
			PerOfEmployeesOperatingOwnAutos: pointer_utils.ToPointer(float32(30.0)),
			PrimaryIndustryClassification:   pointer_utils.ToPointer(businessauto_oapi.PrimaryIndustryClassificationContractorTrucks),
			UsState:                         "OH",
		},
		VehiclesInfo: &[]businessauto_oapi.VehicleInfo{
			{
				Vin:                              "1HGCM82633A123456",
				Year:                             2021,
				Make:                             "Chevrolet",
				Model:                            "Silverado",
				VehicleType:                      businessauto_oapi.VehicleTypeTruck,
				WeightClass:                      businessauto_oapi.Medium,
				VehicleUse:                       businessauto_oapi.VehicleUseTowingOperations,
				BusinessUse:                      businessauto_oapi.BusinessUseCommercial,
				StateUsage:                       businessauto_oapi.StateUsageInterstate,
				RadiusClassification:             businessauto_oapi.RadiusClassificationN0To100,
				PrincipalGaragingLocationZipCode: "12345",
				ApdDeductible:                    pointer_utils.ToPointer(int64(500)),
				SpecialtyVehicleType:             businessauto_oapi.SpecialtyVehicleTypeArtisanContractors,
			},
			{
				Vin:                              "1HGCM82633A123456", // Duplicate VIN
				Year:                             2022,
				Make:                             "Ford",
				Model:                            "F-150",
				VehicleType:                      businessauto_oapi.VehicleTypeTruck,
				WeightClass:                      businessauto_oapi.Light,
				VehicleUse:                       businessauto_oapi.VehicleUseTowingOperations,
				BusinessUse:                      businessauto_oapi.BusinessUseCommercial,
				StateUsage:                       businessauto_oapi.StateUsageIntrastate,
				RadiusClassification:             businessauto_oapi.RadiusClassificationN0To100,
				PrincipalGaragingLocationZipCode: "54321",
				ApdDeductible:                    pointer_utils.ToPointer(int64(500)),
				SpecialtyVehicleType:             businessauto_oapi.SpecialtyVehicleTypeArtisanContractors,
			},
		},
		CoveragesInfo: &businessauto_oapi.CoveragesInfo{
			Coverages: map[string]businessauto_oapi.Coverage{
				"CoverageAutoLiability": {
					Limit:      pointer_utils.ToPointer(int64(1000000)),
					Deductible: pointer_utils.ToPointer(int64(0)),
				},
			},
		},
		EffectiveDate: openapitypes.Date{
			Time: time.Now(),
		},
		ProducerId: s.usersFixture.AgencyProducer.ID,
	}

	// Make the API call - should fail due to duplicate VINs
	resp, err := s.businessAutoClient.CreateBusinessAutoApplication(ctx, req)
	s.Require().NoError(err)
	s.Equal(http.StatusBadRequest, resp.StatusCode)

	// Verify the error response contains the expected message
	s.NotNil(resp)
}

// TestCreateApplication_EmptyVINs tests that empty VINs are allowed (not treated as duplicates)
func (s *CreateApplicationTestSuite) TestCreateApplication_EmptyVINs() {
	ctx := context.Background()

	// Create request body with empty VINs (should be allowed)
	req := businessauto_oapi.PostBusinessAutoAppRequest{
		CompanyInfo: businessauto_oapi.CompanyInfo{
			Name:                            "Empty VIN Test Company",
			NoOfPowerUnits:                  pointer_utils.ToPointer(int32(2)),
			NoOfEmployees:                   pointer_utils.ToPointer(int32(5)),
			PerOfEmployeesOperatingOwnAutos: pointer_utils.ToPointer(float32(30.0)),
			PrimaryIndustryClassification:   pointer_utils.ToPointer(businessauto_oapi.PrimaryIndustryClassificationContractorTrucks),
			UsState:                         "OH",
		},
		VehiclesInfo: &[]businessauto_oapi.VehicleInfo{
			{
				Vin:                              "", // Empty VIN
				Year:                             2021,
				Make:                             "Chevrolet",
				Model:                            "Silverado",
				VehicleType:                      businessauto_oapi.VehicleTypeTruck,
				WeightClass:                      businessauto_oapi.Medium,
				VehicleUse:                       businessauto_oapi.VehicleUseTowingOperations,
				BusinessUse:                      businessauto_oapi.BusinessUseCommercial,
				StateUsage:                       businessauto_oapi.StateUsageInterstate,
				RadiusClassification:             businessauto_oapi.RadiusClassificationN0To100,
				PrincipalGaragingLocationZipCode: "12345",
				ApdDeductible:                    pointer_utils.Int64(int64(500)),
				SpecialtyVehicleType:             businessauto_oapi.SpecialtyVehicleTypeArtisanContractors,
			},
			{
				Vin:                              "", // Another empty VIN (should be allowed)
				Year:                             2022,
				Make:                             "Ford",
				Model:                            "F-150",
				VehicleType:                      businessauto_oapi.VehicleTypeTruck,
				WeightClass:                      businessauto_oapi.Light,
				VehicleUse:                       businessauto_oapi.VehicleUseTowingOperations,
				BusinessUse:                      businessauto_oapi.BusinessUseCommercial,
				StateUsage:                       businessauto_oapi.StateUsageIntrastate,
				RadiusClassification:             businessauto_oapi.RadiusClassificationN0To100,
				PrincipalGaragingLocationZipCode: "54321",
				ApdDeductible:                    pointer_utils.Int64(int64(500)),
				SpecialtyVehicleType:             businessauto_oapi.SpecialtyVehicleTypeArtisanContractors,
			},
		},
		CoveragesInfo: &businessauto_oapi.CoveragesInfo{
			Coverages: map[string]businessauto_oapi.Coverage{
				"CoverageAutoLiability": {
					Limit:      pointer_utils.ToPointer(int64(1000000)),
					Deductible: pointer_utils.ToPointer(int64(0)),
				},
			},
		},
		EffectiveDate: openapitypes.Date{
			Time: time.Now(),
		},
		ProducerId: s.usersFixture.AgencyProducer.ID,
	}

	// Make the API call - should succeed since empty VINs are not duplicates
	resp, err := s.businessAutoClient.CreateBusinessAutoApplication(ctx, req)
	s.Require().NoError(err)
	s.Equal(http.StatusCreated, resp.StatusCode)

	// Parse and verify the response contains the created application
	s.NotNil(resp)

	// Parse the response body
	createAppResp := fixture_utils.ReadResponse[businessauto_oapi.PostBusinessAutoAppResponse](s.T(), resp)
	s.Require().NotNil(createAppResp)

	// Verify ID is set and is a valid UUID
	s.NotEqual(uuid.Nil, createAppResp.Id)
}

// TestCreateApplication_NonOhioState tests that non-Ohio states get nil ModelPinConfig
func (s *CreateApplicationTestSuite) TestCreateApplication_NonOhioState() {
	ctx := context.Background()

	// Create request body with CA state
	req := businessauto_oapi.PostBusinessAutoAppRequest{
		CompanyInfo: businessauto_oapi.CompanyInfo{
			Name:                            "California Company",
			NoOfPowerUnits:                  pointer_utils.ToPointer(int32(5)),
			NoOfEmployees:                   pointer_utils.ToPointer(int32(10)),
			PerOfEmployeesOperatingOwnAutos: pointer_utils.ToPointer(float32(50.0)),
			PrimaryIndustryClassification:   pointer_utils.ToPointer(businessauto_oapi.PrimaryIndustryClassificationContractorTrucks),
			UsState:                         "CA",
		},
		VehiclesInfo: &[]businessauto_oapi.VehicleInfo{
			{
				Vin:                              "1HGCM82633A123456",
				VehicleType:                      businessauto_oapi.VehicleTypeTruck,
				WeightClass:                      businessauto_oapi.Light,
				VehicleUse:                       businessauto_oapi.VehicleUseTowingOperations,
				BusinessUse:                      businessauto_oapi.BusinessUseCommercial,
				StateUsage:                       businessauto_oapi.StateUsageIntrastate,
				RadiusClassification:             businessauto_oapi.RadiusClassificationN0To100,
				PrincipalGaragingLocationZipCode: "94105",
				SpecialtyVehicleType:             businessauto_oapi.SpecialtyVehicleTypeArtisanContractors,
			},
		},
		CoveragesInfo: &businessauto_oapi.CoveragesInfo{
			Coverages: map[string]businessauto_oapi.Coverage{
				"CoverageAutoLiability": {
					CoverageType: common.CoverageAutoLiability,
					Limit:        pointer_utils.ToPointer(int64(1000000)),
					Deductible:   pointer_utils.ToPointer(int64(0)),
				},
			},
		},
		EffectiveDate: openapitypes.Date{
			Time: time.Now(),
		},
		ProducerId: s.usersFixture.AgencyProducer.ID,
	}

	// Make the API call
	resp, err := s.businessAutoClient.CreateBusinessAutoApplication(ctx, req)
	s.Require().NoError(err)
	s.Equal(http.StatusCreated, resp.StatusCode)

	// Parse the response body
	createAppResp := fixture_utils.ReadResponse[businessauto_oapi.PostBusinessAutoAppResponse](s.T(), resp)
	s.Require().NotNil(createAppResp)

	// Fetch the created application to verify RateML config
	app, err := s.appWrapper.GetByID(ctx, createAppResp.Id)
	s.Require().NoError(err)
	s.Nil(app.ModelPinConfig) // Non-OH state should have nil config
}

// TestCreateApplication_WorkCompPolicy tests hasWorkCompPolicy field functionality
func (s *CreateApplicationTestSuite) TestCreateApplication_WorkCompPolicy() {
	ctx := context.Background()

	// Test hasWorkCompPolicy = true
	req := businessauto_oapi.PostBusinessAutoAppRequest{
		CompanyInfo: businessauto_oapi.CompanyInfo{
			Name:              "Test Company With Work Comp",
			UsState:           "OH",
			HasWorkCompPolicy: pointer_utils.ToPointer(true),
			NoOfPowerUnits:    pointer_utils.ToPointer(int32(3)),
		},
		VehiclesInfo: &[]businessauto_oapi.VehicleInfo{
			{
				Vin:                              "1HGCM82633A789123",
				Year:                             2020,
				Make:                             "Ford",
				Model:                            "F-150",
				VehicleType:                      businessauto_oapi.VehicleTypeTruck,
				WeightClass:                      businessauto_oapi.Light,
				VehicleUse:                       businessauto_oapi.VehicleUseTowingOperations,
				BusinessUse:                      businessauto_oapi.BusinessUseCommercial,
				StateUsage:                       businessauto_oapi.StateUsageIntrastate,
				RadiusClassification:             businessauto_oapi.RadiusClassificationN0To100,
				PrincipalGaragingLocationZipCode: "12345",
				SpecialtyVehicleType:             businessauto_oapi.SpecialtyVehicleTypeArtisanContractors,
			},
		},
		CoveragesInfo: &businessauto_oapi.CoveragesInfo{
			Coverages: map[string]businessauto_oapi.Coverage{
				"CoverageAutoLiability": {
					CoverageType: common.CoverageAutoLiability,
					Limit:        pointer_utils.ToPointer(int64(1000000)),
					Deductible:   pointer_utils.ToPointer(int64(0)),
					IsEnabled:    true,
				},
			},
		},
		FilingsInfo: &businessauto_oapi.FilingsInfo{
			HasMultiStateFilings: true,
		},
		EffectiveDate: openapitypes.Date{
			Time: time.Now(),
		},
		ProducerId: s.usersFixture.AgencyProducer.ID,
	}

	// Create and verify hasWorkCompPolicy = true
	resp, err := s.businessAutoClient.CreateBusinessAutoApplication(ctx, req)
	s.Require().NoError(err)
	s.Equal(http.StatusCreated, resp.StatusCode)

	createAppResp := fixture_utils.ReadResponse[businessauto_oapi.PostBusinessAutoAppResponse](s.T(), resp)
	s.Require().NotNil(createAppResp)

	// Verify field persists correctly via GET
	getResp, err := s.businessAutoClient.GetBusinessAutoApplication(ctx, createAppResp.Id)
	s.Require().NoError(err)
	getAppResp := fixture_utils.ReadResponse[businessauto_oapi.GetBusinessAutoAppResponse](s.T(), getResp)
	s.Require().NotNil(getAppResp.CompanyInfo.HasWorkCompPolicy)
	s.True(*getAppResp.CompanyInfo.HasWorkCompPolicy)

	// Test hasWorkCompPolicy = false with different VIN
	req.CompanyInfo.HasWorkCompPolicy = pointer_utils.ToPointer(false)
	req.VehiclesInfo = &[]businessauto_oapi.VehicleInfo{
		{
			Vin:                              "1HGCM82633A789124", // Different VIN
			Year:                             2020,
			Make:                             "Honda",
			Model:                            "Ridgeline",
			VehicleType:                      businessauto_oapi.VehicleTypeTruck,
			WeightClass:                      businessauto_oapi.Light,
			VehicleUse:                       businessauto_oapi.VehicleUseTowingOperations,
			BusinessUse:                      businessauto_oapi.BusinessUseCommercial,
			StateUsage:                       businessauto_oapi.StateUsageIntrastate,
			RadiusClassification:             businessauto_oapi.RadiusClassificationN0To100,
			PrincipalGaragingLocationZipCode: "12345",
			SpecialtyVehicleType:             businessauto_oapi.SpecialtyVehicleTypeArtisanContractors,
		},
	}

	resp2, err := s.businessAutoClient.CreateBusinessAutoApplication(ctx, req)
	s.Require().NoError(err)
	createAppResp2 := fixture_utils.ReadResponse[businessauto_oapi.PostBusinessAutoAppResponse](s.T(), resp2)

	getResp2, err := s.businessAutoClient.GetBusinessAutoApplication(ctx, createAppResp2.Id)
	s.Require().NoError(err)
	getAppResp2 := fixture_utils.ReadResponse[businessauto_oapi.GetBusinessAutoAppResponse](s.T(), getResp2)
	s.Require().NotNil(getAppResp2.CompanyInfo.HasWorkCompPolicy)
	s.False(*getAppResp2.CompanyInfo.HasWorkCompPolicy)
}
