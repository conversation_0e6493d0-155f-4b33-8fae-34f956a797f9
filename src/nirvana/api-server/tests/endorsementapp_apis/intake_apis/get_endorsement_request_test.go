package intake_apis

import (
	"context"
	"net/http"
	"testing"

	"github.com/google/uuid"
	openapi_types "github.com/oapi-codegen/runtime/types"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"go.uber.org/mock/gomock"

	helper "nirvanatech.com/nirvana/api-server/tests/endorsementapp_apis"
	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	slices_util "nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/test_utils"
	admittedApplicationBuilder "nirvanatech.com/nirvana/common-go/test_utils/builders/admitted_app"
	mockBuilder "nirvanatech.com/nirvana/common-go/test_utils/builders/endorsementapp/endorsement-request"
	"nirvanatech.com/nirvana/common-go/time_utils"
	endorsementrequest "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-request"
	nf_common "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/api_server_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/fixture_utils"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/users_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	ibservice "nirvanatech.com/nirvana/insurance-bundle/service"
	endorsementappoapi "nirvanatech.com/nirvana/openapi-specs/api_server_app/endorsementapp"
	"nirvanatech.com/nirvana/openapi-specs/components/common"
	intakeoapi "nirvanatech.com/nirvana/openapi-specs/components/endorsementapp/intake"
)

func TestGetEndorsementRequestTestSuite(t *testing.T) {
	t.Parallel()
	suite.Run(t, new(getEndorsementRequestTestSuite))
}

type getEndorsementRequestTestSuite struct {
	test_utils.StatsHandler
	suite.Suite
	ctx context.Context

	fxapp                     *fxtest.App
	mockAdmittedWrapper       *admitted_app.MockAdmittedAppWrapper
	endorsementAPIClient      *endorsementappoapi.Client
	endorsementRequestWrapper endorsementrequest.Wrapper
	insuranceBundleManager    ibservice.InsuranceBundleManagerClient
	featureFlagClient         feature_flag_lib.Client

	usersFixture *users_fixture.UsersFixture
}

func (e *getEndorsementRequestTestSuite) SetupTest() {
	var env struct {
		fx.In
		ApiServer                 api_server_fixture.ApiServer
		EndorsementRequestWrapper endorsementrequest.Wrapper
		InsuranceBundleManager    ibservice.InsuranceBundleManagerClient
		FeatureFlagClient         feature_flag_lib.Client
		UsersFixture              *users_fixture.UsersFixture
	}

	e.ctx = context.Background()
	ctrl, ctx := gomock.WithContext(e.ctx, e.T())
	e.ctx = ctx
	e.mockAdmittedWrapper = admitted_app.NewMockAdmittedAppWrapper(ctrl)

	newAdmittedAppWrapper := func() nf_common.Wrapper[*admitted_app.AdmittedApp] {
		return e.mockAdmittedWrapper
	}

	e.fxapp = testloader.RequireStart(
		e.T(),
		&env,
		testloader.Use(fx.Decorate(newAdmittedAppWrapper)),
	)
	e.endorsementAPIClient = env.ApiServer.EndorsementAppClient(&env.UsersFixture.Superuser)
	e.endorsementRequestWrapper = env.EndorsementRequestWrapper
	e.insuranceBundleManager = env.InsuranceBundleManager
	e.featureFlagClient = env.FeatureFlagClient
	e.usersFixture = env.UsersFixture
}

func (e *getEndorsementRequestTestSuite) TearDownTest() {
	e.fxapp.RequireStop()
}

func (e *getEndorsementRequestTestSuite) TestGetEndorsementRequest() {
	ctx := context.Background()

	baseIDStr, err := helper.InsertIB(ctx, e.insuranceBundleManager)
	e.Require().NoError(err)

	baseID, err := uuid.Parse(baseIDStr)
	e.Require().NoError(err)

	uw := e.usersFixture.Underwriters[0].ID
	e.mockAdmittedWrapper.
		EXPECT().
		GetAppById(gomock.Any(), gomock.Any()).
		Return(admittedApplicationBuilder.New().WithUnderwriterID(uw).Build(), nil).
		AnyTimes()

	changes := []*mockBuilder.ChangeDataWithDescription{
		mockBuilder.NFAddDriverChangeDataWithDescription,
		mockBuilder.NFRemoveDriverChangeDataWithDescription,
		mockBuilder.NFUpdateDriverChangeDataWithDescription,
		mockBuilder.NFAddVehicleChangeDataWithDescription,
	}
	request, err := helper.InsertEndorsementRequest(
		ctx,
		e.endorsementRequestWrapper,
		baseID,
		changes...,
	)
	e.Require().NoError(err)

	resp, err := e.endorsementAPIClient.GetEndorsementRequest(ctx, uuid.NewString(), request.ID.String())
	e.Require().NoError(err)
	e.Require().Equal(http.StatusOK, resp.StatusCode)

	gotResponse := fixture_utils.ReadResponse[intakeoapi.GetEndorsementRequestResponse](e.T(), resp)
	e.NotNil(gotResponse)

	// Verify effective dates
	e.verifyEffectiveDates(gotResponse.EffectiveDates)

	// Verify state
	e.Equal(intakeoapi.EndorsementRequestStateCreated, gotResponse.State)

	// Verify Changes
	e.NotNil(gotResponse.Changes)
	e.Equal(intakeoapi.Drivers, ((*gotResponse.Changes)[0]).ChangeType)
	e.Equal(intakeoapi.Equipments, ((*gotResponse.Changes)[1]).ChangeType)
	e.True(slices_util.EqualSorted([]string{
		"Added Emily Johnson",
		"Removed Ihor Fedyushnko",
		"Updated Ihor Peleh",
	}, (*gotResponse.Changes)[0].Descriptions))
	e.True(slices_util.EqualSorted([]string{
		"Added 1HGBH41JXMN109186",
	}, (*gotResponse.Changes)[1].Descriptions))

	// Verify ProvisionalEndorsementNumber
	e.Equal("1.1", gotResponse.ProvisionalEndorsementNumber)

	// Verify Default Dates
	e.Equal(time_utils.NewDate(2024, 11, 1).ToTime(), *gotResponse.DefaultEffectiveDate)
	e.Equal(time_utils.NewDate(2025, 12, 1).ToTime(), *gotResponse.DefaultExpirationDate)

	// Verify UnderwriterInfo
	e.Equal("Amanda", gotResponse.UnderwriterInfo.FirstName)
	e.Equal("Hensel", gotResponse.UnderwriterInfo.LastName)

	// Verify Insured
	e.Equal(int64(123456), gotResponse.Insured.DOTNumber)
	e.Equal("Nirvana Trucking Solutions", gotResponse.Insured.Name)
}

func (e *createEndorsementRequestTestSuite) TestCreateEndorsementRequest_ProvisionalEndorsementNumber() {
	ctx := context.Background()

	// Insert insurance bundle
	baseIDStr, err := helper.InsertIB(ctx, e.insuranceBundleManager)
	e.Require().NoError(err)
	e.Require().NotEmpty(baseIDStr)

	// Get bundle external ID
	bundle, err := e.insuranceBundleManager.GetInsuranceBundle(ctx, &ibservice.GetInsuranceBundleRequest{
		PrimaryFilter: &ibservice.GetInsuranceBundleRequest_PrimaryFilter{
			Identifier: &ibservice.GetInsuranceBundleRequest_PrimaryFilter_InternalId{InternalId: baseIDStr},
		},
	})
	e.Require().NoError(err)
	bundleExternalID := bundle.InsuranceBundle.ExternalId

	// Mock the admitted wrapper
	e.mockAdmittedWrapper.
		EXPECT().
		GetAppById(gomock.Any(), gomock.Any()).
		Return(admittedApplicationBuilder.New().WithProducerID(uuid.New()).WithMarketerId(uuid.New()).Build(), nil).
		AnyTimes()

	// Create the first endorsement request
	firstReq := intakeoapi.CreateEndorsementRequestBody{
		ProgramType: common.ProgramTypeNonFleetAdmitted,
	}

	firstResp, err := e.endorsementAPIClient.CreateEndorsementRequest(ctx, bundleExternalID, firstReq)
	e.Require().NoError(err)
	e.Require().Equal(http.StatusCreated, firstResp.StatusCode)

	firstGotResponse := fixture_utils.ReadResponse[intakeoapi.CreateEndorsementResponse](e.T(), firstResp)

	// Get the created request to verify provisional number
	firstCreatedRequest, err := e.endorsementRequestWrapper.GetByID(
		ctx,
		uuid.MustParse(firstGotResponse.EndorsementRequestID),
	)
	e.Require().NoError(err)
	e.Equal("1.1", firstCreatedRequest.ProvisionalEndorsementNumber)

	// Create a second endorsement request
	secondReq := intakeoapi.CreateEndorsementRequestBody{
		ProgramType: common.ProgramTypeNonFleetAdmitted,
	}

	secondResp, err := e.endorsementAPIClient.CreateEndorsementRequest(ctx, bundleExternalID, secondReq)
	e.Require().NoError(err)
	e.Require().Equal(http.StatusCreated, secondResp.StatusCode)

	secondGotResponse := fixture_utils.ReadResponse[intakeoapi.CreateEndorsementResponse](e.T(), secondResp)

	// Get the second created request to verify provisional number
	secondCreatedRequest, err := e.endorsementRequestWrapper.GetByID(
		ctx,
		uuid.MustParse(secondGotResponse.EndorsementRequestID),
	)
	e.Require().NoError(err)
	e.Equal("1.2", secondCreatedRequest.ProvisionalEndorsementNumber)
}

func (e *getEndorsementRequestTestSuite) verifyEffectiveDates(
	effectiveDates []intakeoapi.EffectiveDateEntry,
) {
	e.Len(effectiveDates, 4)
	effectiveDate := openapi_types.Date{Time: time_utils.NewDate(2024, 12, 1).ToTime()}
	expirationDate := openapi_types.Date{Time: time_utils.NewDate(2025, 12, 1).ToTime()}
	e.Equal(effectiveDate, effectiveDates[0].EffectiveDate)
	e.Equal(expirationDate, effectiveDates[0].ExpirationDate)
	e.Equal(effectiveDate, effectiveDates[1].EffectiveDate)
	e.Equal(expirationDate, effectiveDates[1].ExpirationDate)
	e.Equal(effectiveDate, effectiveDates[2].EffectiveDate)
	e.Equal(expirationDate, effectiveDates[2].ExpirationDate)
}
