load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "endorsementapp_apis",
    srcs = ["helpers.go"],
    importpath = "nirvanatech.com/nirvana/api-server/tests/endorsementapp_apis",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/proto",
        "//nirvana/common-go/test_utils/builders/admitted_submission",
        "//nirvana/common-go/test_utils/builders/endorsementapp/endorsement-request",
        "//nirvana/common-go/test_utils/builders/endorsementapp/endorsement-review",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-request",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-review",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/policy",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/infra/constants",
        "//nirvana/insurance-bundle/model",
        "//nirvana/insurance-bundle/service",
        "//nirvana/insurance-core/proto",
        "//nirvana/insured/model",
        "//nirvana/policy/enums",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@org_uber_go_mock//gomock",
    ],
)
