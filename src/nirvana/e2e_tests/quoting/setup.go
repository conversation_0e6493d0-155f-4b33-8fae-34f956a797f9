package tests

import (
	"context"
	"net/http"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	openapi_types "github.com/oapi-codegen/runtime/types"

	"nirvanatech.com/nirvana/openapi-specs/api_server_app/fleet_app"
	"nirvanatech.com/nirvana/openapi-specs/api_server_auth"
)

type testContext struct {
	ctx        context.Context
	appClient  *fleet_app.ClientWithResponses
	authClient *api_server_auth.ClientWithResponses
	// auth
	sessionId string
	userId    uuid.UUID
}

func setupTest(
	ctx context.Context, config testConfig,
) (testContext, error) {
	var testCtx testContext
	testCtx.ctx = ctx

	// Init auth client
	authClient, err := api_server_auth.NewClientWithResponses(config.ServerUrl)
	if err != nil {
		return testCtx, err
	}

	// Login user
	switch {
	case config.UserConfig.NewUser != nil &&
		config.UserConfig.ExistingUser != nil:
		return testCtx, errors.New(
			"Both new user spec and existing user spec can't be defined")
	case config.UserConfig.NewUser != nil:
		return testCtx, errors.New("New user creation unimplemented!")
	case config.UserConfig.ExistingUser != nil:
		sessionId, err := loginUser(
			ctx, authClient,
			config.UserConfig.ExistingUser.Email,
			config.UserConfig.ExistingUser.Password,
		)
		if err != nil {
			return testCtx, err
		}
		testCtx.sessionId = sessionId
	default:
		return testCtx, errors.Errorf(
			"Unexpected user config = %+v", config.UserConfig)
	}

	// Now that we're logged in, create auth client with header
	authClient, err = api_server_auth.NewClientWithResponses(
		config.ServerUrl,
		// Set auth header
		api_server_auth.WithRequestEditorFn(
			func(ctx context.Context, req *http.Request) error {
				req.Header.Set("JSESSIONID", testCtx.sessionId)
				return nil
			}))
	if err != nil {
		return testCtx, err
	}
	testCtx.authClient = authClient

	// Get self ID
	meResponse, err := testCtx.authClient.GetMeWithResponse(ctx)
	if err != nil {
		return testCtx, err
	}
	if meResponse.JSON200 == nil {
		return testCtx, errors.New("Failed to get /me.")
	}
	testCtx.userId, err = uuid.Parse(meResponse.JSON200.Id)
	if err != nil {
		return testCtx, errors.Wrap(err, "unable to parse user id")
	}

	// Init app client
	appClient, err := fleet_app.NewClientWithResponses(
		config.ServerUrl,
		// Set auth header
		fleet_app.WithRequestEditorFn(
			func(ctx context.Context, req *http.Request) error {
				req.Header.Set("JSESSIONID", testCtx.sessionId)
				return nil
			}))
	if err != nil {
		return testCtx, err
	}
	// Check health endpoint
	response, err := appClient.GetHealthWithResponse(ctx)
	if err != nil {
		return testCtx, err
	}
	if response.JSON200 == nil {
		return testCtx, errors.Errorf(
			"Server not healthy. Status = %s", response.Status())
	}
	testCtx.appClient = appClient

	return testCtx, nil
}

// loginUser logs in a user and returns the session id
func loginUser(
	ctx context.Context,
	authClient *api_server_auth.ClientWithResponses,
	email, password string,
) (string, error) {
	resp, err := authClient.PostAuthLoginWithResponse(
		ctx,
		api_server_auth.PostAuthLoginJSONRequestBody{
			Email:    openapi_types.Email(email),
			Password: password,
		})
	if err != nil {
		return "", err
	}
	if resp.JSON200 == nil {
		return "", errors.Errorf(
			"Failed to login. Status = %s. Body = %s",
			resp.Status(), string(resp.Body))
	}
	return resp.JSON200.SessionId, nil
}
