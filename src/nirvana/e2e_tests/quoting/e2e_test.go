package tests

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/suite"

	"nirvanatech.com/nirvana/api-server/test_utils"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/openapi-specs/components/application"
)

func TestQuotingE2ETestSuite(t *testing.T) {
	suite.Run(t, new(quotingE2ETestSuite))
}

type quotingE2ETestSuite struct {
	suite.Suite
	h *testHarness
}

func (s *quotingE2ETestSuite) SetupTest() {
	ctx := context.TODO()
	config, err := getTestConfig()
	if err != nil {
		log.Sugared.Fatalf("Failed to load config. Error = %v", err)
	}
	testCtx, err := setupTest(ctx, config)
	if err != nil {
		log.Sugared.Fatalf("Failed to setup test. Error = %v", err)
	}
	s.h = &testHarness{
		testContext: testCtx,
		Suite:       &s.Suite,
	}
}

// Verify E2E flow until indication options generation
func (s *quotingE2ETestSuite) TestIndicationOptionsGeneration() {
	// TODO: Move to config
	dotNumber := int64(536917)
	effDate, err := time.Parse("2006-01-02", "2021-04-01")
	s.Require().Nil(err)

	// 1. Use prefill APIs to get DOT number based information
	prefillResp := s.h.GetDotNumberPrefill(dotNumber)
	s.Require().NotEmpty(prefillResp.Name)

	// 2. Create application and verify that appetite check passes
	applicationId := s.h.PostCreateApplication(
		dotNumber, prefillResp.Name, effDate)
	log.Sugared.Infof("Appetite check passed. Application id is %s", applicationId)

	app := s.h.GetApplication(applicationId)

	s.Require().Nil(app.IndicationForm)
	s.Require().Nil(app.AdditionalInfoForm)
	s.Require().Nil(app.SelectedIndication)

	s.Require().Equal(
		application.ApplicationStateInProgress,
		app.Summary.State)
	s.Require().Equal(dotNumber, app.Summary.DotNumber)
	s.Require().NotEmpty(app.Summary.ShortID)
	s.Require().Equal(app.Summary.EffectiveDate.Time, effDate)

	// 3. Update indication form
	indicationForm := application.IndicationForm{}
	indicationForm.OperationsForm = test_utils.MockOperationsForm(s.h.userId, nil)
	indicationForm.ClassesAndCommoditiesForm = test_utils.MockClassesAndCommoditiesForm()

	// 3.1 Update classes and commodity form
	s.h.PutUpdateIndicationForm(applicationId, indicationForm)

	app = s.h.GetApplication(applicationId)

	s.Require().NotNil(app.IndicationForm)
	s.Require().NotNil(app.IndicationForm.ClassesAndCommoditiesForm)
	s.Require().NotNil(app.IndicationForm.OperationsForm)
	s.Require().Nil(app.IndicationForm.LossRunSummaryForm)

	// 3.2 Update loss run form
	indicationFormWithLossRun := *app.IndicationForm
	indicationFormWithLossRun.LossRunSummaryForm = test_utils.MockLossRunSummaryForm()
	s.h.PutUpdateIndicationForm(applicationId, indicationFormWithLossRun)

	app = s.h.GetApplication(applicationId)

	s.Require().NotNil(app.IndicationForm)
	s.Require().NotNil(app.IndicationForm.ClassesAndCommoditiesForm)
	s.Require().NotNil(app.IndicationForm.OperationsForm)
	s.Require().NotNil(app.IndicationForm.LossRunSummaryForm)

	// 4. Submit indication form
	s.h.PostSubmitIndicationForm(applicationId)

	// 5. Wait for indication options to be generated
	err = s.h.WaitForIndicationGeneration(applicationId)
	s.Require().Nil(err)

	app = s.h.GetApplication(applicationId)
	s.Require().Equal(
		application.ApplicationStateIndicationGenerated,
		app.Summary.State)

	// 6. Verify options
	s.h.GetIndicationOptions(applicationId)
	log.Sugared.Infof("App is %v", app)
}
