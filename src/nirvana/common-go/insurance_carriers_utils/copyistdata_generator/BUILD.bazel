load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "copyistdata_generator_lib",
    srcs = ["main.go"],
    importpath = "nirvanatech.com/nirvana/common-go/insurance_carriers_utils/copyistdata_generator",
    visibility = ["//visibility:private"],
    deps = [
        "//nirvana/common-go/aws_utils",
        "//nirvana/common-go/gworkspace_utils/gsheets_utils",
        "//nirvana/common-go/log",
    ],
)

go_binary(
    name = "copyistdata_generator",
    embed = [":copyistdata_generator_lib"],
    visibility = ["//visibility:public"],
)
