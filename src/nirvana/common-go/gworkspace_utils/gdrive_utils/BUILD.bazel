load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "gdrive_utils",
    srcs = ["client.go"],
    importpath = "nirvanatech.com/nirvana/common-go/gworkspace_utils/gdrive_utils",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/gworkspace_utils/internal/http_client",
        "@org_golang_google_api//drive/v3:drive",
        "@org_golang_google_api//option",
    ],
)
