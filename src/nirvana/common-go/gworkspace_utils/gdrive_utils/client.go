package gdrive_utils

import (
	"context"

	"nirvanatech.com/nirvana/common-go/gworkspace_utils/internal/http_client"

	"google.golang.org/api/drive/v3"
	"google.golang.org/api/option"
)

type ClientCredentials http_client.Credentials

func NewClient(ctx context.Context, credentials ClientCredentials, oauthScopes []string) (*drive.Service, error) {
	httpClient := http_client.New(ctx, http_client.Credentials(credentials), oauthScopes)

	return drive.NewService(ctx, option.WithHTTPClient(httpClient))
}
