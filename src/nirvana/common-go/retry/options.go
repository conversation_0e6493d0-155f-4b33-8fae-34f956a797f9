package retry

import (
	"context"
	"time"

	"github.com/benbjohnson/clock"
)

type OnRetryFunc func(error)

type onRetryDo struct {
	errors    []error
	setupFunc OnRetryFunc
}

type Config struct {
	backOff     BackOff
	maxAttempts uint
	onRetries   []onRetryDo
	ctx         context.Context
	clk         clock.Clock
}

func newDefaultConfig() Config {
	return Config{
		backOff:     ConstantBackOff(time.Second),
		maxAttempts: 3,
		onRetries:   nil,
		ctx:         context.Background(),
		clk:         clock.New(), // Use real clock by default
	}
}

type Option func(*Config)

// WithBackOff option can be used to set a custom BackOff (default = constant 1s)
func WithBackOff(b BackOff) Option {
	return func(config *Config) {
		config.backOff = b
	}
}

// WithMaxAttempts option can be used to set limit on max attempts (default = 3).
// Setting to 0 will retry until the retried function succeeds.
func WithMaxAttempts(n uint) Option {
	return func(config *Config) {
		config.maxAttempts = n
	}
}

// OnRetryDo option can be used to run a closure before the function is retried.
// The closure is only run if the error encountered is in the given set of errors.
// This option may be used multiple times to configure multiple closures for separate set of errors.
func OnRetryDo(setupFunc OnRetryFunc, errs ...error) Option {
	return func(config *Config) {
		config.onRetries = append(config.onRetries, onRetryDo{
			errors:    errs,
			setupFunc: setupFunc,
		})
	}
}

// WithTimer option can be used to set a custom timer for delays (default = real clock).
// This is particularly useful in tests with mock clocks that implement the Timer interface.
func WithTimer(clk clock.Clock) Option {
	return func(config *Config) {
		config.clk = clk
	}
}
