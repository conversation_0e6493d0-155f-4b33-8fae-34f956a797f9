load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "retry",
    srcs = [
        "backoff.go",
        "options.go",
        "retry.go",
        "utils.go",
    ],
    importpath = "nirvanatech.com/nirvana/common-go/retry",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_avast_retry_go_v4//:retry-go",
        "@com_github_<PERSON><PERSON><PERSON><PERSON><PERSON>_clock//:clock",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
    ],
)

go_test(
    name = "retry_test",
    srcs = [
        "retry_test.go",
        "utils_test.go",
    ],
    embed = [":retry"],
    deps = [
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_go_cmp//cmp",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//assert",
    ],
)
