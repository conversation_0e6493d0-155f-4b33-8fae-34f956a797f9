package retry

import (
	"context"
	"time"

	"github.com/avast/retry-go/v4"
	"github.com/cockroachdb/errors"
)

var NonRetriableError = errors.NewWithDepth(0, "Can not retry again")

// Do will retry the retryableFunc function until either
//  1. retryableFunc returns no error
//  2. retryableFunc returns an error tainted with NonRetriableError
//  3. number of attempts has exceeded max limit (set by Option WithMaxAttempts(...))
//
// Return value will be nil if retryableFunc succeeds after zero or more retries,
// otherwise only the error on *last* retry attempt is returned (with stack trace)
//
// The Option OnRetryDo(...) can be used to run a closure before retrying again.
// OnRetryDo(...) option may be specified multiple times to run different closures
// for different sets of errors.
//
// The context passed as ctx is respected when waiting between retries.
//
// The Option WithBackoff(...) can be used to customize the backoff function used
// while waiting for between retries. See <PERSON><PERSON>ff doc to understand what backoff
// functions are available ready-made.
func Do(
	ctx context.Context,
	retryableFunc retry.RetryableFunc,
	opts ...Option,
) error {
	cfg := newDefaultConfig()
	for _, opt := range opts {
		opt(&cfg)
	}

	var libOpts []retry.Option
	libOpts = append(libOpts, retry.Attempts(cfg.maxAttempts))
	libOpts = append(libOpts, retry.Context(ctx))
	// retry-go library needs to explicitly configured to output only last error
	libOpts = append(libOpts, retry.LastErrorOnly(true))
	// retry-go library itself also has some backoff functions,
	// but since their interface is ugly we override it with our own backoff
	libOpts = append(libOpts, retry.DelayType(
		func(n uint, err error, _ *retry.Config) time.Duration {
			return cfg.backOff(n, err)
		},
	))
	libOpts = append(libOpts, retry.WithTimer(cfg.clk))
	// retry-go library does not use cockroachdb/errors to check error type
	// so we override the RetryIf method to do it ourselves
	libOpts = append(libOpts, retry.RetryIf(
		func(err error) bool {
			return !errors.Is(err, NonRetriableError)
		},
	))
	libOpts = append(libOpts, retry.OnRetry(
		func(_ uint, err error) {
			for _, onRetry := range cfg.onRetries {
				if errors.IsAny(err, onRetry.errors...) {
					onRetry.setupFunc(err)
					break
				}
			}
		},
	))
	// we capture stack strace at this point also with WithStack
	// if error is nil, WithStack becomes no-op
	return errors.WithStack(retry.Do(retryableFunc, libOpts...))
}
