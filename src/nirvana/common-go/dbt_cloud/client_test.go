//go:build manual

package dbt_cloud

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/require"

	"nirvanatech.com/nirvana/common-go/aws_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
)

const accountId = "108988"

func TestClient_GetJobRunStatus(t *testing.T) {
	a := require.New(t)
	secretsHelper, err := aws_utils.DeprecatedNewSecretsHelper()
	a.NoError(err)
	secrets, err := secretsHelper.GetJsonSecret(context.TODO(), "dbt-cloud-secrets")
	a.NoError(err)
	apiKey, ok := secrets["dbt_cloud_api_key"]
	a.True(ok)
	c := NewClient(apiKey, accountId, "test kaavee/dbt_modifs")
	type args struct {
		ctx   context.Context
		runID int
	}
	tests := []struct {
		name    string
		args    args
		want    JobRunStatus
		wantErr bool
	}{
		{
			name: "successful job",
			args: args{
				ctx:   context.Background(),
				runID: ********,
			},
			want:    JobRunStatusSuccess,
			wantErr: false,
		},
		{
			name: "cancelled job",
			args: args{
				ctx:   context.Background(),
				runID: ********,
			},
			want:    JobRunStatusCancelled,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := c.GetJobRunStatus(tt.args.ctx, tt.args.runID)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetJobRunStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetJobRunStatus() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestClient_ListJobs(t *testing.T) {
	c := NewClient("e2ef05c53bb81bfd92bccbb8cccf6b384c51873b", accountId, "test kaavee/dbt_modifs")
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		args    args
		want    []JobItem
		wantErr bool
	}{
		{
			name: "subset jobs",
			args: args{
				ctx: context.Background(),
			},
			want: []JobItem{
				{
					Name: "DBT Prod",
					Id:   135631,
				},
				{
					Name: "test kaavee/dbt_modifs",
					Id:   135643,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := require.New(t)
			got, err := c.ListJobs(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("ListJobs() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			fmt.Println(got)
			for _, elem := range tt.want {
				a.True(slice_utils.Contains(got, elem), "job %v not present in job list", elem)
			}
		})
	}
}
