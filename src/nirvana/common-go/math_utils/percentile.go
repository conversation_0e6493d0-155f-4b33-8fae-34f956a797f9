package math_utils

import (
	"math"
	"sort"

	"github.com/cockroachdb/errors"
)

// Percentile calculates the percentile value for a given slice of comparable numeric types,
// including both integers and floats. This function performs linear interpolation for
// non-integer indices, allowing fractional percentiles.
// The percentile parameter is expected to be in the range [0, 100].
// It is crucial for the input slice to be sorted in ascending order before calling this function.
// The result may not be a member of the original slice due to linear interpolation.
func Percentile[T Number](data []T, percentile float64) (T, error) {
	if len(data) == 0 {
		return 0, errors.New("empty slice")
	}
	if percentile < 0 || percentile > 100 {
		return 0, errors.New("percentile must be in the range [0, 100]")
	}
	// Check if the slice is already sorted in ascending order.
	// If not, return an error.
	if !sort.SliceIsSorted(data, func(i, j int) bool {
		return data[i] < data[j]
	}) {
		return 0, errors.New("slice is not sorted")
	}
	n := len(data)
	// Create a copy of the slice.
	sliceCopy := make([]T, len(data))
	copy(sliceCopy, data)

	// Ensure the percentile is in the valid range [0, 100].
	if percentile < 0 || percentile > 100 {
		return 0, errors.New("percentile must be in the range [0, 100]")
	}

	// Convert percentile to the [0, 1] range.
	normalizedPercentile := percentile / 100.0

	// Calculate the fractional index for the given percentile.
	fractionalIndex := normalizedPercentile * float64(len(sliceCopy)-1)

	floatIndex, delta := math.Modf(fractionalIndex)
	idx := int(floatIndex) // guaranteed to be in [0, n-1] range
	if idx == n-1 {
		return data[idx], nil
	}
	// idx + 1 will not overflow
	return data[idx] + T(delta)*(data[idx+1]-data[idx]), nil
}
