package risk_score_utils

import (
	"time"
)

const (
	riskScoreVersionV3 = "v3"
	riskScoreVersionV4 = "v4"
	riskScoreVersionV5 = "v5"
	ThreeMonthsPeriod  = "3M"
	SixMonthsPeriod    = "6M"
	TwelveMonthsPeriod = "12M"
)

type RiskScore struct {
	RiskScoreType    string
	RiskScoreVersion string
	UWRubricVersion  string
	RiskScoreTrend   []RiskScoreItem
	UWRubric         []UWRubricRow
}

type RiskScoreItem struct {
	Score                  *float32
	WindowEnd              time.Time
	WindowStart            time.Time
	Timestamp              time.Time
	VinCount               *float32
	WindowType             WindowType
	MarketCategory         *string
	ExposeScoreTrend       *bool
	ExposeScoreTrendReason []ExposeScoreTrendReason // multiple reasons can be present
	IsConfidentScore       *bool
	LowConfidentReason     *LowConfidenceReason
}

type UWRubricRow struct {
	ScoreStart     int64
	ScoreEnd       int64
	MarketCategory string
	Decile         float32
	Discount       *float32
	IsMarket       bool
}

//go:generate go run github.com/dmarkham/enumer -type=WindowType -json -trimprefix=WindowType
type WindowType int

const (
	WindowType12M WindowType = iota + 1
	WindowType3M
	WindowType6M
)

//go:generate go run github.com/dmarkham/enumer -type=ExposeScoreTrendReason -json -trimprefix=ExposeScoreTrendReason
type ExposeScoreTrendReason int

const (
	ExposeScoreTrendReasonShortHaul ExposeScoreTrendReason = iota + 1
)

//go:generate go run github.com/dmarkham/enumer -type=LowConfidenceReason -json -trimprefix=LowConfidenceReason
type LowConfidenceReason int

const (
	LowConfidenceReasonShortHaul LowConfidenceReason = iota + 1
)
