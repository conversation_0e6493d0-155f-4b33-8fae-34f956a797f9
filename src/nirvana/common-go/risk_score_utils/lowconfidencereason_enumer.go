// Code generated by "enumer -type=LowConfidenceReason -json -trimprefix=LowConfidenceReason"; DO NOT EDIT.

package risk_score_utils

import (
	"encoding/json"
	"fmt"
	"strings"
)

const _LowConfidenceReasonName = "ShortHaul"

var _LowConfidenceReasonIndex = [...]uint8{0, 9}

const _LowConfidenceReasonLowerName = "shorthaul"

func (i LowConfidenceReason) String() string {
	i -= 1
	if i < 0 || i >= LowConfidenceReason(len(_LowConfidenceReasonIndex)-1) {
		return fmt.Sprintf("LowConfidenceReason(%d)", i+1)
	}
	return _LowConfidenceReasonName[_LowConfidenceReasonIndex[i]:_LowConfidenceReasonIndex[i+1]]
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _LowConfidenceReasonNoOp() {
	var x [1]struct{}
	_ = x[LowConfidenceReasonShortHaul-(1)]
}

var _LowConfidenceReasonValues = []LowConfidenceReason{LowConfidenceReasonShortHaul}

var _LowConfidenceReasonNameToValueMap = map[string]LowConfidenceReason{
	_LowConfidenceReasonName[0:9]:      LowConfidenceReasonShortHaul,
	_LowConfidenceReasonLowerName[0:9]: LowConfidenceReasonShortHaul,
}

var _LowConfidenceReasonNames = []string{
	_LowConfidenceReasonName[0:9],
}

// LowConfidenceReasonString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func LowConfidenceReasonString(s string) (LowConfidenceReason, error) {
	if val, ok := _LowConfidenceReasonNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _LowConfidenceReasonNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to LowConfidenceReason values", s)
}

// LowConfidenceReasonValues returns all values of the enum
func LowConfidenceReasonValues() []LowConfidenceReason {
	return _LowConfidenceReasonValues
}

// LowConfidenceReasonStrings returns a slice of all String values of the enum
func LowConfidenceReasonStrings() []string {
	strs := make([]string, len(_LowConfidenceReasonNames))
	copy(strs, _LowConfidenceReasonNames)
	return strs
}

// IsALowConfidenceReason returns "true" if the value is listed in the enum definition. "false" otherwise
func (i LowConfidenceReason) IsALowConfidenceReason() bool {
	for _, v := range _LowConfidenceReasonValues {
		if i == v {
			return true
		}
	}
	return false
}

// MarshalJSON implements the json.Marshaler interface for LowConfidenceReason
func (i LowConfidenceReason) MarshalJSON() ([]byte, error) {
	return json.Marshal(i.String())
}

// UnmarshalJSON implements the json.Unmarshaler interface for LowConfidenceReason
func (i *LowConfidenceReason) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return fmt.Errorf("LowConfidenceReason should be a string, got %s", data)
	}

	var err error
	*i, err = LowConfidenceReasonString(s)
	return err
}
