package wrapper

import (
	"context"
	"strconv"
	"strings"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	policyenums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/external_client/salesforce/client/enums"
	clearance_enums "nirvanatech.com/nirvana/quoting/clearance/enums"
)

type ProdSalesforceImpl struct {
	deps Deps
}

func (q ProdSalesforceImpl) UpdateOpportunityForApplicationRolledBack(ctx context.Context, args SalesforceEventUpdateApplicationArgs) error {
	// validations for application stage
	opportunityObject, err := getSalesforceOpportunityFieldsWithApplicationId(ctx, q.deps, args.ApplicationID, opportunitySalesforceObjectFieldStage)
	if err != nil {
		return errors.Wrapf(err, "failed to get salesforce opportunity object for the application id %s", args.ApplicationID)
	}
	log.Info(ctx, "Rolling back opportunity for application", log.String("Application ID", args.ApplicationID),
		log.String("Previous State", args.PreviousState.String()),
		log.Any("Opportunity Stage", opportunityObject[opportunitySalesforceObjectFieldStage]))

	//nolint:exhaustive
	switch args.PreviousState {
	case uw.ApplicationReviewStateClosed:
		if opportunityObject[opportunitySalesforceObjectFieldStage] != OpportunityStageCloseLost.Name() {
			return errors.Newf("opportunity is not in the correct stage to be rolled back for application id %s stage %s",
				args.ApplicationID, opportunityObject[opportunitySalesforceObjectFieldStage])
		}
	case uw.ApplicationReviewStateApproved:
		if opportunityObject[opportunitySalesforceObjectFieldStage] != OpportunityStageInitialUWReview.Name() {
			return errors.Newf("opportunity is not in the correct stage to be rolled back for application id %s stage %s",
				args.ApplicationID, opportunityObject[opportunitySalesforceObjectFieldStage])
		}
	case uw.ApplicationReviewStateDeclined:
		if opportunityObject[opportunitySalesforceObjectFieldStage] != OpportunityStageDeclined.Name() {
			return errors.Newf("opportunity is not in the correct stage to be rolled back for application id %s stage %s",
				args.ApplicationID, opportunityObject[opportunitySalesforceObjectFieldStage])
		}
	default:
		return errors.Newf("unsupported application review state %s", args.PreviousState)
	}

	opportunityFields := map[string]interface{}{
		opportunitySalesforceObjectFieldStage: OpportunityStageInitialUWReview.Name(),
	}
	opportunityFields, err = generateArgumentsForUpdateOpportunityForApplicationRolledBack(
		args.PreviousState,
		opportunityFields)
	if err != nil {
		return errors.Wrapf(err, "failed to generate arguments for update opportunity for application rolled back")
	}
	return updateSalesforceOpportunityWithApplicationId(ctx, q.deps, args.ApplicationID, opportunityFields)
}

func NewProdSalesforceWrapperImpl(deps Deps) SalesforceWrapper {
	return ProdSalesforceImpl{
		deps: deps,
	}
}

func (q ProdSalesforceImpl) CreateOpportunity(
	ctx context.Context, args SalesforceEventApplicationCreatedArgs,
) (*string, error) {
	// Checking if there already exists an opportunity for the given application id
	if sfOpportunity, err := getSalesforceOpportunityWithApplicationId(ctx, q.deps, args.ApplicationID); err != nil {
		return nil, errors.Wrapf(
			err, "error in retrieving duplicate opportunities for application:%v", args.ApplicationID,
		)
	} else if sfOpportunity != nil {
		return nil, errors.Newf(
			"found existing opportunity in salesforce for the application: %v with id: %v",
			args.ApplicationID, sfOpportunity,
		)
	}

	// fields that are needed to create the opportunity object
	opportunityFields := map[string]interface{}{
		// Fields to be set in the object, the keys are the name of the fields in salesforce side
		SalesforceObjectFieldApplicationId:              args.ApplicationID,
		opportunitySalesforceObjectFieldCloseDate:       args.EffectiveDate,
		opportunitySalesforceObjectFieldDot:             args.DotNumber,
		opportunitySalesforceObjectFieldDesiredDate:     args.EffectiveDate,
		opportunitySalesforceObjectFieldRecordType:      newOpportunityRecordTypeId,
		opportunitySalesforceObjectFieldStage:           OpportunityStageIncompleteSubmission.Name(),
		opportunitySalesforceObjectFieldState:           args.State,
		opportunitySalesforceObjectFieldType:            fleetOppType,
		opportunitySalesforceObjectFieldClearanceStatus: args.ClearanceStatus.String(),
		opportunitySalesforceObjectFieldFronter:         args.Fronter.String(),
	}

	if args.ClearanceStatus == clearance_enums.ApplicationRequiresClearance {
		opportunityFields[opportunitySalesforceObjectFieldStage] = OpportunityStageDeclined.Name()
		opportunityFields[opportunitySalesforceObjectFieldDeclineReason] = clearanceDuplicate
		opportunityFields[opportunitySalesforceObjectFieldDeclineSubReason] = clearanceDuplicate
	}

	if args.IsNonAdmitted {
		opportunityFields[opportunitySalesforceObjectFieldType] = fleetNonAdmittedOppType
	}

	// checking if an agency is present in salesforce for the given agency id
	sfAgencyIdPointer, sfOwnerIdPointer, err := getSalesforceAgencyWithId(ctx, q.deps, args.AgencyId)
	if err != nil {
		return nil, errors.Wrapf(
			err, "failed to fetch agency records for the application: %v, with agency id:%v",
			args.ApplicationID, args.AgencyId,
		)
	}

	// updating the fields with the ids of the object on the salesforce side
	if sfAgencyIdPointer != nil {
		opportunityFields[opportunitySalesforceObjectFieldAgency] = *sfAgencyIdPointer
		opportunityFields[opportunitySalesforceObjectFieldOwner] = *sfOwnerIdPointer
	}

	// checking if a producer is present in salesforce with the given producer id or email
	sfProducerIdPointer, err := getSalesforceProducerWithIdOrEmail(ctx, q.deps, args.ProducerEmail, args.ProducerID)
	if err != nil {
		return nil, errors.Wrapf(
			err, "failed to get salesforce producer for the application: %v, with producer id:%v or email:%v",
			args.ApplicationID, args.ProducerID, args.ProducerEmail,
		)
	}

	if sfProducerIdPointer != nil {
		opportunityFields[opportunitySalesforceObjectFieldAgent] = *sfProducerIdPointer
	}

	// checking if an agent is present in salesforce with the given agent id or email
	sfAgentIdPointer, sfAgencyOfficeIdPointer, err := getSalesforceAgentWithIdOrEmail(ctx, q.deps, args.AgentID, args.AgentEmail)
	if err != nil {
		return nil, errors.Wrapf(
			err, "failed to get salesforce agent for the application:%v , with agent id:%v or email:%v",
			args.ApplicationID, args.AgentID, args.AgentEmail,
		)
	}

	if sfAgentIdPointer != nil {
		opportunityFields[opportunitySalesforceObjectFieldMarketer] = *sfAgentIdPointer
	}

	if sfAgencyOfficeIdPointer != nil {
		_, sfOwnerIdPointer, err := getSalesforceAgencyWithSalesforceId(ctx, q.deps, *sfAgencyOfficeIdPointer)
		if err != nil {
			return nil, errors.Wrapf(
				err, "failed to get salesforce agency office for the application:%v, with salesforce id: %v",
				args.ApplicationID, *sfAgencyOfficeIdPointer,
			)
		}

		if sfOwnerIdPointer != nil {
			opportunityFields[opportunitySalesforceObjectFieldOwner] = *sfOwnerIdPointer
		}
	}

	salesforceUWIdPointer, err := getSalesforceUWWithName(ctx, q.deps, args.Underwriter)
	if err != nil {
		return nil, errors.Wrapf(
			err, "failed to get salesforce underwriter for the given application: %v, with underwriter name: %v",
			args.ApplicationID, args.Underwriter,
		)
	}

	if salesforceUWIdPointer != nil {
		opportunityFields[opportunitySalesforceObjectFieldUnderwriter] = *salesforceUWIdPointer
	}

	if args.IsRenewal {
		_, renewalOpportunityPointer, err := getSalesforceOriginalOpportunityWithId(ctx, q.deps, args.RenewalOriginalApplicationId)
		if err != nil {
			return nil, errors.Wrapf(
				err, "failed to fetch original opportunity records for the given renewal application: %v",
				args.ApplicationID,
			)
		}

		if renewalOpportunityPointer != nil {
			renewalOpportunityIdPointer, err := getSalesforceRenewalOpportunityWithId(ctx, q.deps, *renewalOpportunityPointer)
			if err != nil {
				return nil, errors.Wrapf(
					err, "failed to fetch renewal opportunity records of id: %v for application: %v",
					*renewalOpportunityPointer, args.ApplicationID,
				)
			}

			if renewalOpportunityIdPointer != nil {
				updateFields := map[string]interface{}{
					renewalOpportunitySalesforceFieldApplicationID: args.ApplicationID,
					opportunitySalesforceObjectFieldCloseDate:      args.EffectiveDate,
					opportunitySalesforceObjectFieldDesiredDate:    args.EffectiveDate,
					opportunitySalesforceObjectFieldStage:          OpportunityStageIndicationGenerated.Name(),
					opportunitySalesforceObjectFieldType:           fleetOppType,
					opportunitySalesforceObjectFieldFronter:        args.Fronter.String(),
				}

				err := q.deps.SalesforceClient.UpdateSalesforceObject(
					ctx, enums.Opportunity, *renewalOpportunityIdPointer, updateFields,
				)
				if err != nil {
					return nil, errors.Wrapf(
						err, "failed to update salesforce opportunity: %v for renewal application: %v",
						*renewalOpportunityIdPointer, args.ApplicationID,
					)
				}

				log.Info(
					ctx, "updated opportunity record in salesforce for renewal application",
					log.String("Application Id", args.ApplicationID),
					log.String("Salesforce Opportunity Id", *renewalOpportunityIdPointer),
				)
			}

			return renewalOpportunityIdPointer, nil

		}

	} else {
		opportunityName, err := buildSalesforceOpportunityName(policyenums.ProgramTypeFleet, args.CompanyName, args.EffectiveDate)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to get salesforce opportunity name %s", args.CompanyName)
		}

		opportunityFields[salesforceObjectFieldName] = opportunityName

		salesforceAccountIdPointer, err := getSalesforceAccountWithDotNumber(ctx, q.deps, args.DotNumber)
		if err != nil {
			return nil, errors.Wrapf(
				err, "failed to fetch account records for the given application: %v", args.ApplicationID,
			)
		}

		if args.ActiveOrPendingInsuranceInsuranceCarrier != nil {
			_, salesforceCarrierMappingAccount, err := getSalesforceCarrierMappingWithName(ctx, q.deps, *args.ActiveOrPendingInsuranceInsuranceCarrier)
			if err != nil {
				return nil, errors.Wrapf(
					err, "failed to fetch account records for the given application: %v", args.ApplicationID,
				)
			}

			if salesforceCarrierMappingAccount != nil {
				opportunityFields[opportunitySalesforceObjectFieldCurrentCarrier] = *args.ActiveOrPendingInsuranceInsuranceCarrier
				opportunityFields[opportunitySalesforceObjectFieldCurrentCarrierId] = *salesforceCarrierMappingAccount
			}
		}

		var opportunityId *string
		if salesforceAccountIdPointer != nil {
			opportunityFields[opportunitySalesforceObjectFieldAccount] = *salesforceAccountIdPointer

			opportunityIdTemp, err := createNewOpportunityForExistingAccount(
				ctx, q.deps, opportunityFields,
			)
			if err != nil {
				return nil, errors.Wrapf(
					err, "failed to create salesforce opportunity for the application:%v",
					args.ApplicationID,
				)
			}
			opportunityId = opportunityIdTemp

		} else {
			opportunityIdTemp, err := createNewAccountAndOpportunity(
				ctx, q.deps, opportunityFields, args.CompanyName, args.ApplicationID, args.DotNumber,
			)
			if err != nil {
				return nil, errors.Wrapf(
					err, "failed to create salesforce account and opportunity for the application:%v",
					args.ApplicationID,
				)
			}
			opportunityId = opportunityIdTemp
		}
		return opportunityId, nil
	}
	// nolint:nilnil
	return nil, nil
}

func (q ProdSalesforceImpl) UpsertEndorsementOpportunity(ctx context.Context, args SalesforceEventEndorsementCreatedArgs) error {
	if args.ProgramType == policyenums.ProgramTypeNonFleetAdmitted && !args.IsEndorsementV2 {
		existingOpportunity, err := q.updateExistingOpportunity(ctx, args)
		if err != nil {
			return errors.Wrap(err, "failed to get existing expansion opportunity")
		}
		if existingOpportunity {
			return nil
		}
	}

	return q.createNewExpansionOpportunity(ctx, args)
}

func (q ProdSalesforceImpl) updateExistingOpportunity(ctx context.Context, args SalesforceEventEndorsementCreatedArgs) (bool, error) {
	existingOpportunity, err := getExistingExpansionOpportunity(ctx, q.deps, strconv.Itoa(int(args.DotNumber)), args.ChangeTypes, args.EffectiveDate)
	if err != nil {
		return false, errors.Wrapf(err, "failed to get existing expansion opportunity for application id %s", args.ApplicationID)
	}

	if existingOpportunity == nil {
		return false, nil
	}

	existingOpportunityId, err := parseStringField(existingOpportunity, salesforceObjectFieldSalesforceId)
	if err != nil {
		return false, errors.Wrap(err, "failed to parse existing opportunity ID")
	}

	transformedUWNotes, err := q.buildTransformedUWNotes(ctx, existingOpportunityId, args)
	if err != nil {
		return false, err
	}

	existingOpportunityFields := q.buildExistingEndorsementOpportunityFields(args, transformedUWNotes)

	if err := updateSalesforceObjectWithId(ctx, q.deps, enums.Opportunity, *existingOpportunityId, existingOpportunityFields); err != nil {
		return false, errors.Wrapf(err, "failed to update expansion opportunity for application id %s", args.ApplicationID)
	}

	return true, nil
}

func (q ProdSalesforceImpl) buildTransformedUWNotes(ctx context.Context, existingOpportunityId *string, args SalesforceEventEndorsementCreatedArgs) (string, error) {
	uwNotes, err := getSalesforceRecordFields(ctx, q.deps, enums.Opportunity, salesforceObjectFieldSalesforceId, *existingOpportunityId, opportunitySalesforceObjectFieldUWNotes)
	if err != nil {
		return "", errors.Wrap(err, "failed to get uw notes for expansion opportunity")
	}

	notes, err := parseStringField(uwNotes, opportunitySalesforceObjectFieldUWNotes)
	if err != nil {
		log.Warn(ctx, "UW notes are not available in Salesforce", log.AppID(args.ApplicationID))
	}

	if notes != nil && len(*notes) != 0 {
		return strings.Join([]string{*notes, args.UWNotes}, ","), nil
	}

	return args.UWNotes, nil
}

func (q ProdSalesforceImpl) buildExistingEndorsementOpportunityFields(args SalesforceEventEndorsementCreatedArgs, uwNotes string) map[string]any {
	return map[string]any{
		opportunitySalesforceObjectFieldTotalAmount: args.Amount,
		opportunitySalesforceObjectFieldUWNotes:     uwNotes,
	}
}

func (q ProdSalesforceImpl) createNewExpansionOpportunity(ctx context.Context, args SalesforceEventEndorsementCreatedArgs) error {
	fields := []string{
		opportunitySalesforceObjectFieldState,
		opportunitySalesforceObjectFieldAgency,
		opportunitySalesforceObjectFieldAgent,
		opportunitySalesforceObjectFieldAccount,
		opportunitySalesforceObjectFieldAgencyOffice,
	}

	originalOpportunity, err := q.getOriginalOpportunity(ctx, args.ApplicationID, fields)
	if err != nil {
		return errors.Wrapf(err, "failed to get original opportunity for application %s", args.ApplicationID)
	}

	if len(originalOpportunity) == 0 {
		return errors.Newf("original opportunity does not exist for the application %s", args.ApplicationID)
	}

	ownerID, err := q.getOwnerID(ctx, args, originalOpportunity)
	if err != nil {
		log.Warn(ctx, "failed to get original opportunity owner", log.Err(err), log.AppID(args.ApplicationID))
	}

	expansionOpportunityFields := q.buildNewOpportunityFields(args, originalOpportunity, fields)
	opportunityName, err := buildSalesforceEndorseOpportunityName(args.ProgramType, args.Coverage, args.CompanyName, args.EffectiveDate)
	if err != nil {
		return errors.Wrapf(err, "failed to build endorsement opportunity name %s", args.CompanyName)
	}

	salesforceUWID, err := getSalesforceUWWithEmail(ctx, q.deps, args.Underwriter)
	if err != nil {
		return errors.Wrapf(err, "failed to get Salesforce UW Id %s", args.Underwriter)
	}
	if salesforceUWID != nil {
		expansionOpportunityFields[opportunitySalesforceObjectFieldUnderwriter] = *salesforceUWID
	}

	expansionOpportunityFields[salesforceObjectFieldName] = opportunityName
	if ownerID != nil {
		expansionOpportunityFields[opportunitySalesforceObjectFieldOwner] = ownerID
	}

	_, err = createNewOpportunityForExistingAccount(ctx, q.deps, expansionOpportunityFields)
	return err
}

func (q ProdSalesforceImpl) getOriginalOpportunity(ctx context.Context, applicationID string, fields []string) (map[string]any, error) {
	originalOpportunity, err := getSalesforceOpportunityFieldsWithApplicationId(ctx, q.deps, applicationID, fields...)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get original opportunity for application id %s", applicationID)
	}

	return originalOpportunity, nil
}

func (q ProdSalesforceImpl) getOwnerID(ctx context.Context, args SalesforceEventEndorsementCreatedArgs, originalOpportunity map[string]any) (*string, error) {
	agencyOfficeID, err := parseStringField(originalOpportunity, opportunitySalesforceObjectFieldAgencyOffice)
	if err != nil {
		log.Warn(ctx, "failed to get agency office id from original opportunity", log.AppID(args.ApplicationID))
		return nil, err
	}

	if agencyOfficeID == nil {
		return nil, errors.Wrap(err, "owner id is missing in salesforce account")
	}

	agencyAccount, err := getSalesforceRecordFields(ctx, q.deps, enums.Account, salesforceObjectFieldSalesforceId, *agencyOfficeID, opportunitySalesforceObjectFieldOwner, opportunitySalesforceObjectFieldNFAccountOwner)
	if err != nil {
		log.Warn(ctx, "failed to get owner from agency account", log.AppID(args.ApplicationID))
		return nil, errors.Wrap(err, "failed to get owner from agency account")
	}

	// nolint:exhaustive
	switch args.ProgramType {
	case policyenums.ProgramTypeFleet:
		return parseStringField(agencyAccount, opportunitySalesforceObjectFieldOwner)
	case policyenums.ProgramTypeNonFleetAdmitted:
		ownerID, err := parseStringField(agencyAccount, opportunitySalesforceObjectFieldNFAccountOwner)
		if ownerID == nil {
			admittedOwner := admittedOwnerId
			return &admittedOwner, nil
		}
		return ownerID, err
	}

	return nil, errors.New("failed to get owner id")
}

func (q ProdSalesforceImpl) buildNewOpportunityFields(args SalesforceEventEndorsementCreatedArgs, originalOpportunity map[string]any, existingsFields []string) map[string]any {
	expansionOpportunityFields := getExpansionOpportunityCommonFields(args)
	addEndorsementProgramSpecificFields(args, expansionOpportunityFields)
	populateFieldsFromOriginalObject(existingsFields, originalOpportunity, expansionOpportunityFields)

	expansionOpportunityFields[opportunitySalesforceObjectFieldParentOpportunity] = originalOpportunity[salesforceObjectFieldSalesforceId]
	if args.EndorsementReviewId != nil {
		expansionOpportunityFields[opportunitySalesforceObjectFieldEndorsementReviewId] = *args.EndorsementReviewId
	}
	return expansionOpportunityFields
}

func (q ProdSalesforceImpl) CreateQuote(ctx context.Context, args SalesforceEventQuoteCreatedArgs) (*string, error) {
	opportunityObjectId, err := getSalesforceOpportunityWithApplicationId(ctx, q.deps, args.ApplicationID)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get opportunity object id from salesforce for application id %s", args.ApplicationID)
	}

	quoteFields := map[string]interface{}{
		SalesforceObjectFieldApplicationId: args.ApplicationID,
		billingName:                        args.CompanyName,
		salesforceObjectFieldName: concatSalesforceObjectNameWithExtension(
			args.CompanyName, quoteNameExtension),
		pricebook:    newQuoteRecordTypeId,
		quoteId:      args.FormCompilationId,
		shippingName: args.CompanyName,
		statusField:  statusPresented,
	}

	if opportunityObjectId == nil {
		return nil, errors.Newf("failed to get salesforce opportunity id for the application id %s", args.ApplicationID)
	}

	quoteFields[opportunityId] = *opportunityObjectId

	quoteID, err := createNewQuote(ctx, q.deps, quoteFields)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to create quote on salesforce for application id %s", args.ApplicationID)
	}
	return quoteID, nil
}

func (q ProdSalesforceImpl) CreateQuoteLineItems(ctx context.Context, args SalesforceEventCreateQuoteLineItemsArgs) error {
	quoteLineItems := addQuoteLineItemsFields(args.QuoteId, args.CoveragePremiums)

	err := createNewQuoteLineItems(ctx, q.deps, args.ApplicationId, quoteLineItems)
	if err != nil {
		return errors.Wrapf(err, "failed to create quote line items on salesforce for application id %s", args.ApplicationId)
	}

	return nil
}

func (q ProdSalesforceImpl) CreateCampaign(ctx context.Context, args SalesforceEventCreateCampaignArgs) (*string, error) {
	campaignFields := map[string]interface{}{
		campaignObjectDescription:  descriptionValue,
		campaignObjectIsActive:     salesforceValueTrue,
		salesforceObjectFieldName:  args.CampaignName,
		campaignObjectRecordTypeId: campaignRecordTypeId,
		statusField:                campaignStatus,
		campaignObjectType:         campaignType,
	}

	return createNewCampaign(ctx, q.deps, campaignFields)
}

func (q ProdSalesforceImpl) CreateCampaignMember(ctx context.Context, args SalesforceEventCreateCampaignMemberArgs) (*string, error) {
	campaignMemberFields := map[string]interface{}{
		campaignId:  args.SalesforceCampaignId,
		source:      args.Source,
		statusField: responded,
		utmAdGroup:  args.UTMTags.Adgroup,
		utmCampaign: args.UTMTags.Campaign,
		utmKeyword:  args.UTMTags.Keyword,
		utmMedium:   args.UTMTags.Medium,
		utmSource:   args.UTMTags.Source,
	}

	if args.SalesforceContactId != nil {
		campaignMemberFields[contactId] = *args.SalesforceContactId
		return createNewCampaignMember(ctx, q.deps, campaignMemberFields)
	}

	campaignMemberFields[leadId] = *args.SalesforceLeadId
	return createNewCampaignMember(ctx, q.deps, campaignMemberFields)
}

func (q ProdSalesforceImpl) CreateLead(ctx context.Context, args SalesforceEventCreateLeadArgs) (*string, error) {
	leadFields := map[string]interface{}{
		leadCompany:                args.Company,
		salesforceObjectFieldEmail: args.Email,
		leadLastName:               args.Email,
	}

	return createNewLead(ctx, q.deps, leadFields)
}

func (q ProdSalesforceImpl) UpdateOpportunityForApplicationDeclined(ctx context.Context, args SalesforceEventUpdateApplicationArgs) error {
	opportunityFields := map[string]interface{}{
		opportunitySalesforceObjectFieldDeclinedRules:    args.DeclinedRules,
		opportunitySalesforceObjectFieldStage:            OpportunityStageDeclined.Name(),
		opportunitySalesforceObjectFieldDeclineReason:    args.DeclineReason,
		opportunitySalesforceObjectFieldDeclineSubReason: args.DeclineSubReason,
	}
	return updateSalesforceOpportunityWithApplicationId(ctx, q.deps, args.ApplicationID, opportunityFields)
}

func (q ProdSalesforceImpl) UpdateOpportunityForApplicationFlagged(ctx context.Context, args SalesforceEventUpdateApplicationArgs) error {
	opportunityFields := map[string]interface{}{
		opportunitySalesforceObjectFieldFlags: args.Flags,
	}
	return updateSalesforceOpportunityWithApplicationId(ctx, q.deps, args.ApplicationID, opportunityFields)
}

func (q ProdSalesforceImpl) UpdateOpportunityForIndicationGenerated(ctx context.Context, args SalesforceEventUpdateApplicationArgs) error {
	opportunityFields := map[string]interface{}{
		opportunitySalesforceObjectFieldIndicationAmount: args.IndicationOptionAmount,
		opportunitySalesforceObjectFieldPowerUnits:       args.NumberOfPowerUnits,
		opportunitySalesforceObjectFieldStage:            OpportunityStageIndicationGenerated.Name(),
	}

	opportunityObject, err := getSalesforceOpportunityFieldsWithApplicationId(ctx, q.deps, args.ApplicationID, opportunitySalesforceObjectFieldIndicationDate)
	if err != nil {
		return errors.Wrapf(err, "failed to get salesforce opportunity object for the application id %s", args.ApplicationID)
	}

	if _, ok := opportunityObject[opportunitySalesforceObjectFieldIndicationDate]; !ok {
		opportunityFields[opportunitySalesforceObjectFieldIndicationDate] = args.CreatedDate
	}

	return updateSalesforceOpportunityWithApplicationId(ctx, q.deps, args.ApplicationID, opportunityFields)
}

func (q ProdSalesforceImpl) UpdateOpportunityForTSPConnectionStarted(ctx context.Context, args SalesforceEventUpdateApplicationArgs) error {
	opportunityObject, err := getSalesforceOpportunityFieldsWithApplicationId(ctx, q.deps, args.ApplicationID, opportunitySalesforceObjectFieldTSPConnectionStarted)
	if err != nil {
		return errors.Wrapf(err, "failed to get salesforce opportunity object for the application id %s", args.ApplicationID)
	}

	if _, ok := opportunityObject[opportunitySalesforceObjectFieldTSPConnectionStarted]; !ok {
		// update the opportunity field if the field is not updated before
		return updateSalesforceOpportunityWithApplicationId(ctx, q.deps, args.ApplicationID, map[string]interface{}{
			opportunitySalesforceObjectFieldTSPConnectionStarted: args.CreatedDate,
		})
	}

	return nil
}

func (q ProdSalesforceImpl) UpdateOpportunityForTSPLinkOpened(ctx context.Context, args SalesforceEventUpdateApplicationArgs) error {
	opportunityObject, err := getSalesforceOpportunityFieldsWithApplicationId(ctx, q.deps, args.ApplicationID, opportunitySalesforceObjectFieldTSPLinkOpened)
	if err != nil {
		return errors.Wrapf(err, "failed to get salesforce opportunity object for the application id %s", args.ApplicationID)
	}

	if _, ok := opportunityObject[opportunitySalesforceObjectFieldTSPLinkOpened]; !ok {
		// update the opportunity field if the field is not updated before
		return updateSalesforceOpportunityWithApplicationId(ctx, q.deps, args.ApplicationID, map[string]interface{}{
			opportunitySalesforceObjectFieldTSPLinkOpened: args.CreatedDate,
		})
	}

	return nil
}

func (q ProdSalesforceImpl) UpdateOpportunityForTSPLinkGenerated(ctx context.Context, args SalesforceEventUpdateApplicationArgs) error {
	opportunityObject, err := getSalesforceOpportunityFieldsWithApplicationId(ctx, q.deps, args.ApplicationID, opportunitySalesforceObjectFieldTSPLinkGenerated)
	if err != nil {
		return errors.Wrapf(err, "failed to get salesforce opportunity object for the application id %s", args.ApplicationID)
	}

	if _, ok := opportunityObject[opportunitySalesforceObjectFieldTSPLinkGenerated]; !ok {
		// update the opportunity field if the field is not updated before
		return updateSalesforceOpportunityWithApplicationId(ctx, q.deps, args.ApplicationID, map[string]interface{}{
			opportunitySalesforceObjectFieldTSPLinkGenerated: args.CreatedDate,
		})
	}

	return nil
}

func (q ProdSalesforceImpl) UpdateOpportunityForTSPConnectionCompleted(ctx context.Context, args SalesforceEventUpdateApplicationArgs) error {
	opportunityObject, err := getSalesforceOpportunityFieldsWithApplicationId(ctx, q.deps, args.ApplicationID, opportunitySalesforceObjectFieldTSPConnectionCompleted, opportunitySalesforceObjectFieldTSPELDProvider)
	if err != nil {
		return errors.Wrapf(err, "failed to get salesforce opportunity object for the application id %s", args.ApplicationID)
	}

	opportunityFields := make(map[string]interface{})

	if _, ok := opportunityObject[opportunitySalesforceObjectFieldTSPELDProvider]; !ok {
		salesforceAccountObjectId, err := getSalesforceTSPAccountWithName(ctx, q.deps, args.TSPProvider)
		if err != nil {
			return errors.Wrapf(err, "failed to get salesforce account object id for the application id %s", args.ApplicationID)
		}

		if salesforceAccountObjectId != nil {
			opportunityFields[opportunitySalesforceObjectFieldTSPELDProvider] = *salesforceAccountObjectId
		}
	}

	if _, ok := opportunityObject[opportunitySalesforceObjectFieldTSPConnectionCompleted]; !ok {
		opportunityFields[opportunitySalesforceObjectFieldTSPConnectionCompleted] = args.CreatedDate
	}

	return updateSalesforceOpportunityWithApplicationId(ctx, q.deps, args.ApplicationID, opportunityFields)
}

func (q ProdSalesforceImpl) UpdateOpportunityForApplicationSubmission(ctx context.Context, args SalesforceEventUpdateApplicationArgs) error {
	opportunityObject, err := getSalesforceOpportunityFieldsWithApplicationId(ctx, q.deps, args.ApplicationID, opportunitySalesforceObjectFieldStage)
	if err != nil {
		return errors.Wrapf(err, "failed to get salesforce opportunity object for the application id %s", args.ApplicationID)
	}

	opportunityFields := map[string]interface{}{
		opportunitySalesforceObjectFieldIncumbent: getALIncumbentFieldValue(args.IsALIncumbent),
	}

	if opportunityObject[opportunitySalesforceObjectFieldStage] == OpportunityStageIncompleteSubmission.Name() ||
		opportunityObject[opportunitySalesforceObjectFieldStage] == OpportunityStageIndicationGenerated.Name() {
		opportunityFields[opportunitySalesforceObjectFieldStage] = OpportunityStageInitialUWReview.Name()
	}

	return updateSalesforceOpportunityWithApplicationId(ctx, q.deps, args.ApplicationID, opportunityFields)
}

func (q ProdSalesforceImpl) UpdateOpportunityDesiredEffectiveDate(ctx context.Context, args SalesforceEventUpdateApplicationArgs) error {
	opportunityFields := map[string]interface{}{
		opportunitySalesforceObjectFieldDesiredDate: args.EffectiveDate,
	}

	return updateSalesforceOpportunityWithApplicationId(ctx, q.deps, args.ApplicationID, opportunityFields)
}

func (q ProdSalesforceImpl) UpdateOpportunityForApplicationReviewApproved(ctx context.Context, args SalesforceEventUpdateApplicationArgs) error {
	opportunityFields := map[string]interface{}{
		opportunitySalesforceObjectFieldPowerUnits: args.NumberOfPowerUnits,
		opportunitySalesforceObjectFieldUWGrade:    args.AccountGrade,
		opportunitySalesforceObjectFieldUWNotes:    args.UnderwriterNotes,
	}

	return updateSalesforceOpportunityWithApplicationId(ctx, q.deps, args.ApplicationID, opportunityFields)
}

func (q ProdSalesforceImpl) UpdateOpportunityForApplicationReviewAssigned(ctx context.Context, args SalesforceEventUpdateApplicationArgs) error {
	salesforceUserId, err := getSalesforceUWWithEmail(ctx, q.deps, args.UnderwriterEmail)
	if err != nil {
		return errors.Wrapf(err, "failed to get salesforce user object for the application id %s", args.ApplicationID)
	}

	if salesforceUserId != nil {
		return updateSalesforceOpportunityWithApplicationId(ctx, q.deps, args.ApplicationID, map[string]interface{}{
			opportunitySalesforceObjectFieldUnderwriter: *salesforceUserId,
		})
	}

	return nil
}

func (q ProdSalesforceImpl) UpdateOpportunityForApplicationReviewDeclined(ctx context.Context, args SalesforceEventUpdateApplicationArgs) error {
	opportunityFields := map[string]interface{}{
		opportunitySalesforceObjectFieldDeclineReason:       args.DeclineReason,
		opportunitySalesforceObjectFieldDeclineReasonDetail: args.DeclineReasonComment,
		opportunitySalesforceObjectFieldDeclineSubReason:    args.DeclineSubReason,
		opportunitySalesforceObjectFieldStage:               OpportunityStageDeclined.Name(),
	}

	return updateSalesforceOpportunityWithApplicationId(ctx, q.deps, args.ApplicationID, opportunityFields)
}

func (q ProdSalesforceImpl) UpdateOpportunityForApplicationClosed(ctx context.Context, args SalesforceEventUpdateApplicationArgs) error {
	opportunityFields := map[string]interface{}{
		opportunitySalesforceObjectFieldStage: OpportunityStageCloseLost.Name(),
	}

	opportunityObject, err := getSalesforceOpportunityFieldsWithApplicationId(ctx, q.deps, args.ApplicationID,
		opportunitySalesforceObjectFieldLossReasonComment,
		opportunitySalesforceObjectFieldLossReason,
		opportunitySalesforceObjectFieldLossSubReason,
		opportunitySalesforceObjectFieldDeclineReason,
	)
	if err != nil {
		return errors.Wrapf(err, "failed to get salesforce opportunity object for the application id %s", args.ApplicationID)
	}

	if _, ok := opportunityObject[opportunitySalesforceObjectFieldDeclineReason]; ok {
		return nil
	}

	// If more fields are added here then make sure to set them as nil as part of the
	// generateArgumentsForUpdateOpportunityForApplicationRolledBack function
	if _, ok := opportunityObject[opportunitySalesforceObjectFieldLossReasonComment]; !ok {
		opportunityFields[opportunitySalesforceObjectFieldLossReasonComment] = args.CloseReasonComment
	}

	if _, ok := opportunityObject[opportunitySalesforceObjectFieldLossReason]; !ok {
		opportunityFields[opportunitySalesforceObjectFieldLossReason] = args.CloseReason
	}

	if _, ok := opportunityObject[opportunitySalesforceObjectFieldLossSubReason]; !ok {
		opportunityFields[opportunitySalesforceObjectFieldLossSubReason] = args.CloseSubReason
	}

	return updateSalesforceOpportunityWithApplicationId(ctx, q.deps, args.ApplicationID, opportunityFields)
}

func (q ProdSalesforceImpl) UpdateOpportunityForFormsSignaturePacketReleased(ctx context.Context, args SalesforceEventUpdateApplicationArgs) error {
	opportunityFields := map[string]interface{}{
		opportunitySalesforceObjectFieldStage:       OpportunityStageQuoted.Name(),
		opportunitySalesforceObjectFieldTotalAmount: args.TotalPremium,
		opportunitySalesforceObjectFieldQuoted:      salesforceValueTrue,
	}

	addPremiumsToOpportunity(opportunityFields, args.CoveragePremiums)

	err := updateSalesforceOpportunityWithApplicationId(ctx, q.deps, args.ApplicationID, opportunityFields)
	if err != nil {
		return errors.Wrapf(err, "failed to update salesforce opportunity for application id %s and event %v", args.ApplicationID, args.EventName)
	}

	quoteID, err := q.CreateQuote(ctx, SalesforceEventQuoteCreatedArgs{
		ApplicationID:     args.ApplicationID,
		CompanyName:       args.CompanyName,
		FormCompilationId: args.FormCompilationId,
	})
	if err != nil {
		return errors.Wrapf(err, "failed for create quote on salesforce for application id %s and event %v", args.ApplicationID, args.EventName)
	}

	return q.CreateQuoteLineItems(ctx, SalesforceEventCreateQuoteLineItemsArgs{
		ApplicationId:    args.ApplicationID,
		QuoteId:          *quoteID,
		CoveragePremiums: args.CoveragePremiums,
	})
}

func (q ProdSalesforceImpl) UpdateOpportunityForFormsSignaturePacketBound(ctx context.Context, args SalesforceEventUpdateApplicationArgs) error {
	opportunityFields := map[string]interface{}{
		opportunitySalesforceObjectFieldStage:              OpportunityStageBound.Name(),
		opportunitySalesforceObjectFieldTotalAmount:        args.TotalPremium,
		opportunitySalesforceObjectFieldCloseDate:          args.CloseDate,
		opportunitySalesforceObjectFieldWinReason:          args.WinReason,
		opportunitySalesforceObjectFieldPrimaryWinReason:   args.PrimaryWinReason,
		opportunitySalesforceObjectFieldSecondaryWinReason: args.SecondaryWinReason,
		opportunitySalesforceObjectFieldFronter:            args.Fronter.String(),
	}

	addPremiumsToOpportunity(opportunityFields, args.CoveragePremiums)
	addBoundPremiumsToOpportunity(opportunityFields, args.CoverageSummary)

	if args.NumberOfCameras != nil {
		opportunityFields[opportunitySalesforceObjectFieldNumberOfCameras] = args.NumberOfCameras
	}

	if args.BillingInfo != nil {
		opportunityFields[opportunitySalesforceObjectFieldDepositAmount] = args.BillingInfo.DepositAmount
		opportunityFields[opportunitySalesforceObjectFieldDepositPaymentMethod] = args.BillingInfo.DepositPaymentMethod
		opportunityFields[opportunitySalesforceObjectFieldPaymentMethod] = args.BillingInfo.PaymentMethod
		opportunityFields[opportunitySalesforceObjectFieldCommissionRate] = args.BillingInfo.CommissionRate
		opportunityFields[opportunitySalesforceObjectFieldPaidInFull] = getPaidInFullFieldValue(args.BillingInfo.PaymentMethod)
	}

	err := updateSalesforceOpportunityWithApplicationId(ctx, q.deps, args.ApplicationID, opportunityFields)
	if err != nil {
		return errors.Wrapf(err, "failed to update salesforce opportunity for application id %s and event %v", args.ApplicationID, args.EventName)
	}

	return q.UpdateQuote(ctx, SalesforceEventQuoteCreatedArgs{
		FormCompilationId: args.FormCompilationId,
	})
}

func (q ProdSalesforceImpl) UpdateQuote(ctx context.Context, args SalesforceEventQuoteCreatedArgs) error {
	quoteID, err := getSalesforceQuoteWithFormCompId(ctx, q.deps, args.FormCompilationId)
	if err != nil {
		return errors.Wrapf(err, "failed to get salesforce quote object id for the form compilation id %s", args.FormCompilationId)
	}

	err = updateSalesforceObjectWithId(ctx, q.deps, enums.Quote, *quoteID, map[string]interface{}{
		statusField: statusAccepted,
	})
	if err != nil {
		return errors.Wrapf(err, "failed to update quote object for form compilation %s", args.FormCompilationId)
	}

	return nil
}

func (q ProdSalesforceImpl) UpdateOpportunityForPolicyCancelled(ctx context.Context, args SalesforceEventUpdateApplicationArgs) error {
	opportunityFields := map[string]interface{}{
		opportunitySalesforceObjectFieldCancellationEffectiveDate: args.CancellationEffectiveDate,
		opportunitySalesforceObjectFieldCancellationTime:          args.CancellationFilingDate,
		opportunitySalesforceObjectFieldCancellationReason:        args.CancellationReason,
	}

	return updateSalesforceOpportunityWithApplicationId(ctx, q.deps, args.ApplicationID, opportunityFields)
}

func (q ProdSalesforceImpl) UpdateOpportunityForPolicyReinstated(ctx context.Context, args SalesforceEventUpdateApplicationArgs) error {
	opportunityFields := map[string]interface{}{
		opportunitySalesforceObjectFieldCancellationEffectiveDate: nil,
		opportunitySalesforceObjectFieldCancellationTime:          nil,
		opportunitySalesforceObjectFieldCancellationReason:        nil,
		opportunitySalesforceObjectFieldReinstateDate:             args.ReistatementDate,
	}

	return updateSalesforceOpportunityWithApplicationId(ctx, q.deps, args.ApplicationID, opportunityFields)
}

func (q ProdSalesforceImpl) CreateCampaignAndLead(ctx context.Context, args SalesforceEventUpsertCampaignAndLeadArgs) error {
	campaignID, err := getSalesforceCampaignWithName(ctx, q.deps, args.CampaignName)
	if err != nil {
		return errors.Wrapf(err, "failed to get salesforce campaign object %s", args.CampaignName)
	}

	if campaignID != nil {
		campaignMemberID, err := getSalesforceCampaignMemberWithEmailAndCampaignId(ctx, q.deps, args.Email, *campaignID)
		if err != nil {
			return errors.Wrapf(err, "failed to get salesforce campaign member object %s", args.Email)
		}

		if campaignMemberID != nil {
			campaignMemberFields := map[string]interface{}{
				statusField: responded,
			}
			return updateSalesforceCampaignMemberWithId(ctx, q.deps, *campaignMemberID, campaignMemberFields)
		}
	} else {
		campaignID, err = q.CreateCampaign(ctx, SalesforceEventCreateCampaignArgs{CampaignName: args.CampaignName})
		if err != nil {
			return errors.Wrapf(err, "failed to create campaign object in salesforce for campaign %s", args.CampaignName)
		}
	}

	contactID, err := getSalesforceContactWithEmail(ctx, q.deps, args.Email)
	if err != nil {
		return errors.Wrapf(err, "failed to get salesforce contact object %s", args.Email)
	}

	campaignMemberArgs := SalesforceEventCreateCampaignMemberArgs{
		SalesforceCampaignId: *campaignID,
		Source:               args.Source,
		UTMTags:              args.UTMTags,
	}

	if contactID != nil {
		campaignMemberArgs.SalesforceContactId = contactID
		_, err = q.CreateCampaignMember(ctx, campaignMemberArgs)
		if err != nil {
			return errors.Wrap(err, "failed to create campaign member object in salesforce")
		}

		return nil
	}

	leadID, err := getSalesforceLeadWithEmail(ctx, q.deps, args.Email)
	if err != nil {
		return errors.Wrapf(err, "failed to get salesforce lead object %s", args.Email)
	}

	if leadID == nil {
		leadID, err = q.CreateLead(ctx, SalesforceEventCreateLeadArgs{
			Email:   args.Email,
			Company: getSalesforceCompanyFromEmail(args.Email),
		})
		if err != nil {
			return errors.Wrap(err, "failed to create lead object in salesforce")
		}
	}

	campaignMemberArgs.SalesforceLeadId = leadID
	_, err = q.CreateCampaignMember(ctx, campaignMemberArgs)
	if err != nil {
		return errors.Wrap(err, "failed to create campaign member object in salesforce")
	}

	return nil
}

func (q ProdSalesforceImpl) UpdateOpportunityForApplicationClearance(ctx context.Context, args SalesforceEventUpdateApplicationArgs) error {
	opportunityFields := map[string]interface{}{
		opportunitySalesforceObjectFieldClearanceStatus: args.ClearanceStatus.String(),
	}

	if args.ClearanceStatus == clearance_enums.ApplicationRequiresClearance {
		opportunityFields[opportunitySalesforceObjectFieldStage] = OpportunityStageDeclined.Name()
		opportunityFields[opportunitySalesforceObjectFieldDeclineReason] = clearanceDuplicate
		opportunityFields[opportunitySalesforceObjectFieldDeclineSubReason] = clearanceDuplicate
	}

	if args.RemoveClearance {
		opportunityFields[opportunitySalesforceObjectFieldStage] = OpportunityStageInitialUWReview.Name()
		opportunityFields[opportunitySalesforceObjectFieldDeclineReason] = " "
		opportunityFields[opportunitySalesforceObjectFieldDeclineSubReason] = " "
	}

	return updateSalesforceOpportunityWithApplicationId(ctx, q.deps, args.ApplicationID, opportunityFields)
}

func (q ProdSalesforceImpl) UpdateOpportunityForWinningCarrier(ctx context.Context, args SalesforceEventUpdateApplicationArgs) error {
	opportunityFields := map[string]interface{}{
		OpportunitySalesforceObjectFieldWinningCarrier: args.WinningCarrier,
	}

	return updateSalesforceOpportunityWithApplicationId(ctx, q.deps, args.ApplicationID, opportunityFields)
}

func (q ProdSalesforceImpl) UpdateOpportunityForRecommendedAction(ctx context.Context, args SalesforceEventUpdateApplicationArgs) error {
	if args.RecommendedAction == nil {
		return errors.New("recommended action is nil")
	}

	opportunityFields := map[string]interface{}{
		opportunitySalesforceObjectFieldRecommendedAction: args.RecommendedAction.String(),
	}

	opportunityObject, err := getSalesforceOpportunityFieldsWithApplicationId(ctx, q.deps, args.ApplicationID, opportunitySalesforceObjectFieldRecommendedAction)
	if err != nil {
		return errors.Wrapf(err, "failed to get salesforce opportunity object for the application id %s", args.ApplicationID)
	}

	sfdcRecommendedAction, err := parseStringField(opportunityObject, opportunitySalesforceObjectFieldRecommendedAction)
	if err != nil {
		log.Warn(ctx, "Failed to parse recommended action field from sfdc", log.Err(err))
	}

	if sfdcRecommendedAction != nil && *sfdcRecommendedAction == args.RecommendedAction.String() {
		// we don't want to update if we have same recommended action
		log.Warn(ctx, "Skipping call to update recommended action",
			log.String("recommendedAction", args.RecommendedAction.String()),
			log.String("sfdcRecommendedAction", *sfdcRecommendedAction),
		)
		return nil
	}

	return updateSalesforceOpportunityWithApplicationId(ctx, q.deps, args.ApplicationID, opportunityFields)
}

func (q ProdSalesforceImpl) UpdateOpportunityForUWPriceRun(ctx context.Context, args SalesforceEventUpdateApplicationArgs) error {
	opportunityFields := map[string]interface{}{
		opportunitySalesforceObjectFieldUWRanPrice: args.CreatedDate,
	}

	opportunityObject, err := getSalesforceOpportunityFieldsWithApplicationId(ctx, q.deps, args.ApplicationID, opportunitySalesforceObjectFieldUWRanPrice)
	if err != nil {
		return errors.Wrapf(err, "failed to get salesforce opportunity object for the application id %s", args.ApplicationID)
	}

	if _, ok := opportunityObject[opportunitySalesforceObjectFieldUWRanPrice]; ok {
		// don't update since we only want to capture first time rml runs for analytics
		log.Warn(ctx, "Skipping call to update as time stamp is already captured", log.Err(err))
		return nil
	}

	return updateSalesforceOpportunityWithApplicationId(ctx, q.deps, args.ApplicationID, opportunityFields)
}

func (q ProdSalesforceImpl) GetOpportunityFieldsForApplicationId(
	ctx context.Context,
	applicationId string,
	getFields ...string,
) (map[string]interface{}, error) {
	return getSalesforceOpportunityFieldsWithApplicationId(ctx, q.deps, applicationId, getFields...)
}

func (q ProdSalesforceImpl) CreateContact(ctx context.Context, args SalesforceEventContactArgs) error {
	contactFields := map[string]interface{}{
		salesforceObjectFieldEmail: args.Email,
		// salesforce has a validation for last name when we create a contact
		// since we don't have this information when we invite user we fill the first name as INVITE
		// and  last name with PENDING and later when user signs up last name will be updated
		salesforceObjectFieldFirstName: "INVITE",
		salesforceObjectFieldLastName:  "PENDING",
		userCreationType:               newUserType,
		agentSalesforceObjectFieldId:   args.UserId,
		credentialSharedDate:           q.deps.Clock.Now().Format(time_utils.ISOLayout),
		utmCampaign:                    agentsUserInvite,
	}

	if len(args.ProgramTypeAccess) != 0 {
		now := q.deps.Clock.Now().Format(time_utils.ISOLayout)

		hasFleet := slice_utils.Contains(args.ProgramTypeAccess, policyenums.ProgramTypeFleet)
		hasNonFleet := slice_utils.Contains(args.ProgramTypeAccess, policyenums.ProgramTypeNonFleetAdmitted)

		switch {
		case hasFleet && hasNonFleet:
			contactFields[combinedOnboardingDate] = now
		case hasFleet:
			contactFields[fleetOnboardingDate] = now
		case hasNonFleet:
			contactFields[nonFleetOnboardingDate] = now
		default:
			contactFields[combinedOnboardingDate] = now
		}

		contactFields[userAccess] = getProgramTypeAccess(args.ProgramTypeAccess)
	}

	if len(args.SFDCAgencyRoles) != 0 {
		agencyRoles, err := getSalesforceAgencyRoles(args.SFDCAgencyRoles)
		if err != nil {
			log.Error(ctx, "Failed to get sfdc agency roles for agent", log.String("email", args.Email))
		}
		contactFields[agencyRole] = *agencyRoles
	}

	sfAgencyIdPointer, sfOwnerIdPointer, err := getSalesforceAgencyWithId(ctx, q.deps, args.AgencyId)
	if err != nil {
		return errors.Wrapf(
			err, "failed to fetch agency records for the agency id:%v",
			args.AgencyId,
		)
	}

	if sfAgencyIdPointer != nil {
		contactFields[opportunitySalesforceObjectFieldAccount] = *sfAgencyIdPointer
		contactFields[opportunitySalesforceObjectFieldOwner] = *sfOwnerIdPointer
	}

	if _, err = createSalesforceObject(ctx, q.deps, enums.Contact, contactFields); err != nil {
		return errors.Wrapf(err, "failed to create contact object in salesforce for %s", args.Email)
	}

	log.Info(ctx, "created contact object in Salesforce", log.String("email", args.Email))
	return nil
}

func (q ProdSalesforceImpl) UpdateContact(ctx context.Context, args SalesforceEventContactArgs) error {
	contactID, err := getSalesforceRecordID(ctx, q.deps, enums.Contact, salesforceObjectFieldEmail, args.Email)
	if err != nil {
		return errors.Wrapf(err, "failed to get salesforce contact object for email %s", args.Email)
	}

	if contactID == nil {
		return errors.Wrapf(err, "no contact record found for email %s", args.Email)
	}

	contactFields := map[string]interface{}{
		profileCreationDate: q.deps.Clock.Now().Format(time_utils.ISOLayout),
	}

	if len(args.FirstName) != 0 {
		contactFields[salesforceObjectFieldFirstName] = args.FirstName
		contactFields[salesforceObjectFieldLastName] = args.LastName
		contactFields[firstLoginDate] = q.deps.Clock.Now().Format(time_utils.ISOLayout)
	}

	if len(args.Title) != 0 {
		contactFields[title] = args.Title
		contactFields[department] = args.Department
		contactFields[phone] = args.PhoneNumber
	}

	if len(args.SFDCAgencyRoles) != 0 {
		agencyRoles, err := getSalesforceAgencyRoles(args.SFDCAgencyRoles)
		if err != nil {
			log.Error(ctx, "Failed to get sfdc agency roles for agent", log.String("email", args.Email))
		}
		contactFields[agencyRole] = *agencyRoles
	}

	if err = updateSalesforceObjectWithId(ctx, q.deps, enums.Contact, *contactID, contactFields); err != nil {
		return errors.Wrapf(err, "failed to update contact in salesforce for email %s", args.Email)
	}

	log.Info(ctx, "updated contact object in Salesforce", log.String("email", args.Email))
	return nil
}

func (q ProdSalesforceImpl) UpdateOpportunityForBillingInfo(ctx context.Context, args SalesforceEventUpdateBillingInfoArgs) error {
	opportunityFields := make(map[string]any)

	if args.PaymentMethod != nil {
		paymentMethod := *args.PaymentMethod

		opportunityFields[opportunitySalesforceObjectFieldPaymentMethod] = paymentMethod
		opportunityFields[opportunitySalesforceObjectFieldPaidInFull] = getPaidInFullFieldValue(paymentMethod)
	}

	if args.CountOfDepositInstallments != nil {
		opportunityFields[OpportunitySalesforceObjectFieldCountOfDepositInstallments] = *args.CountOfDepositInstallments
	}

	if args.ToolkitBound != nil {
		opportunityFields[OpportunitySalesforceObjectFieldToolkitBound] = args.ToolkitBound
	}

	if args.ToolkitOffered != nil {
		opportunityFields[OpportunitySalesforceObjectFieldToolkitOffered] = args.ToolkitOffered
	}

	if len(opportunityFields) == 0 { // no fields to update
		return nil
	}

	return updateSalesforceOpportunityWithApplicationId(ctx, q.deps, args.ApplicationID, opportunityFields)
}
