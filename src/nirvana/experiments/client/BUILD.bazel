load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "client",
    srcs = [
        "client_mock.go",
        "fx.go",
        "impl.go",
        "interfaces.go",
        "wrapper_mock.go",
    ],
    importpath = "nirvanatech.com/nirvana/experiments/client",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/slice_utils",
        "//nirvana/db-api/db_models/experiments",
        "//nirvana/experiments/builders",
        "//nirvana/experiments/db",
        "//nirvana/experiments/enums",
        "//nirvana/experiments/models",
        "//nirvana/infra/fx/fxregistry",
        "@com_github_benb<PERSON><PERSON><PERSON>_clock//:clock",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_volatiletech_sqlboiler_v4//queries/qm",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_mock//gomock",
    ],
)

go_test(
    name = "client_test",
    srcs = ["impl_test.go"],
    embed = [":client"],
    deps = [
        "//nirvana/db-api/db_models/experiments",
        "//nirvana/experiments/builders",
        "//nirvana/experiments/enums",
        "//nirvana/experiments/models",
        "//nirvana/infra/fx/testloader",
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//suite",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
        "@org_uber_go_mock//gomock",
    ],
)
