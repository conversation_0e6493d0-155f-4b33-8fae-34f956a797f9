package client

import (
	"context"
	"database/sql"

	"github.com/benb<PERSON><PERSON>son/clock"
	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/db-api/db_models/experiments"
	"nirvanatech.com/nirvana/experiments/builders"
	"nirvanatech.com/nirvana/experiments/enums"
	"nirvanatech.com/nirvana/experiments/models"
)

var ErrApplicabilityNotFound = errors.New("experiment applicability not found")

type Deps struct {
	fx.In

	ExperimentsWrapper ExperimentsWrapper
	Clock              clock.Clock
}
type Impl struct {
	deps Deps
}

func newClientImpl(deps Deps) ExperimentsClient {
	return &Impl{deps: deps}
}

func (c *Impl) GetExperimentsByIDs(
	ctx context.Context,
	experimentIds []uuid.UUID,
) ([]*models.Experiment, error) {
	experimentIDsStr := slice_utils.Map(experimentIds, func(id uuid.UUID) string {
		return id.String()
	})
	qmMods := []qm.QueryMod{
		experiments.ExperimentDetailWhere.ID.IN(experimentIDsStr),
	}
	return c.deps.ExperimentsWrapper.GetExperiments(ctx, qmMods...)
}

func (c *Impl) GetActiveExperiments(
	ctx context.Context,
	domain enums.Domain,
	subjectType enums.SubjectType,
) ([]*models.Experiment, error) {
	qmMods := []qm.QueryMod{
		experiments.ExperimentDetailWhere.Domain.EQ(domain.String()),
		experiments.ExperimentDetailWhere.SubjectType.EQ(subjectType.String()),
		experiments.ExperimentDetailWhere.Enabled.EQ(true),
	}
	return c.deps.ExperimentsWrapper.GetExperiments(ctx, qmMods...)
}

func (c *Impl) GetActiveExperimentsByIds(
	ctx context.Context,
	experimentIds []uuid.UUID,
) ([]*models.Experiment, error) {
	experimentIDsStr := slice_utils.Map(experimentIds, func(id uuid.UUID) string {
		return id.String()
	})
	qmMods := []qm.QueryMod{
		experiments.ExperimentDetailWhere.Enabled.EQ(true),
		experiments.ExperimentDetailWhere.ID.IN(experimentIDsStr),
	}
	return c.deps.ExperimentsWrapper.GetExperiments(ctx, qmMods...)
}

func (c *Impl) AssignExperiments(
	ctx context.Context,
	subjectID string,
	enable bool,
	experiments []*models.Experiment,
) error {
	experimentIds := slice_utils.Map(experiments, func(experiment *models.Experiment) string {
		return experiment.Id.String()
	})

	variants, err := c.deps.ExperimentsWrapper.GetVariantsForExperiments(ctx, experimentIds)
	if err != nil {
		return errors.Wrap(err, "failed to get variants for experiments")
	}

	experimentToVariants := make(map[string][]*models.ExperimentVariant)
	for _, variant := range variants {
		variantExperimentId := variant.ExperimentId.String()
		experimentToVariants[variantExperimentId] = append(experimentToVariants[variantExperimentId], variant)
	}

	var assignments []*models.ExperimentAssignment
	for _, experiment := range experiments {
		totalVariants := experimentToVariants[experiment.Id.String()]
		selectedVariant, err := c.selectVariant(experiment.AllocationMethod, totalVariants)
		if err != nil {
			return errors.Wrap(err, "failed to select variant")
		}
		assignment := builders.NewExperimentAssignmentBuilder().
			WithId(uuid.New()).
			WithExperimentId(experiment.Id).
			WithVariantId(selectedVariant.Id).
			WithSubjectId(subjectID).
			WithDomain(experiment.Domain).
			WithEnabled(enable).
			WithCreatedAt(c.deps.Clock.Now()).
			WithUpdatedAt(c.deps.Clock.Now()).
			Build()
		assignments = append(assignments, &assignment)
	}

	return c.deps.ExperimentsWrapper.InsertAssignments(ctx, assignments)
}

// selectVariant returns the appropriate variant for an experiment given its allocation method.
//
// Currently, only AllocationMethodSingleVariant is supported, which assumes
// a single-variant experiment used for feature gating or eligibility control.
// Future allocation methods (e.g. A/B/n testing, hash-based allocation) can be added.
func (c *Impl) selectVariant(
	allocationMethod enums.AllocationMethod,
	variants []*models.ExperimentVariant,
) (*models.ExperimentVariant, error) {
	switch allocationMethod { //nolint:exhaustive
	case enums.AllocationMethodSingleVariant:
		if len(variants) != 1 {
			return nil, errors.New("invalid number of variants for single variant experiment")
		}
		return variants[0], nil
	default:
		return nil, errors.New("invalid allocation method")
	}
}

func (c *Impl) GetLatestExperimentsAssignations(
	ctx context.Context,
	domain enums.Domain,
	subjectID string,
) ([]*models.ExperimentAssignment, error) {
	return c.deps.ExperimentsWrapper.GetLatestAssignmentsPerExperiment(ctx, domain, subjectID)
}

func (c *Impl) RecordExperimentApplicability(
	ctx context.Context,
	applicability models.ExperimentApplicability,
) error {
	if !applicability.Validate() {
		return errors.New("invalid applicability")
	}
	return c.deps.ExperimentsWrapper.InsertApplicability(ctx, applicability)
}

func (c *Impl) RecordExperimentEvent(
	ctx context.Context,
	event models.ExperimentEvent,
) error {
	return c.deps.ExperimentsWrapper.InsertEvent(ctx, event)
}

func (c *Impl) GetLatestExperimentApplicability(
	ctx context.Context,
	experimentId uuid.UUID,
	subjectId string,
) (*models.ExperimentApplicability, error) {
	applicability, err := c.deps.ExperimentsWrapper.GetLatestApplicability(ctx, experimentId.String(), subjectId)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrApplicabilityNotFound
		}
		return nil,
			errors.Wrapf(err, "failed to get latest experiment applicability for subjectId %s", subjectId)
	}

	return applicability, nil
}

func (c *Impl) GetLatestExperimentApplicabilities(
	ctx context.Context,
	experimentId uuid.UUID,
	subjectIds []string,
) ([]models.ExperimentApplicability, error) {
	return c.deps.ExperimentsWrapper.GetLatestApplicabilities(ctx, experimentId.String(), subjectIds)
}

func (c *Impl) ActivateExperiment(
	ctx context.Context,
	experimentId uuid.UUID,
) error {
	return c.deps.ExperimentsWrapper.UpdateExperimentDetail(
		ctx,
		experimentId,
		func(experiment *models.Experiment) error {
			experiment.Enabled = true
			return nil
		},
	)
}

func (c *Impl) DeactivateExperiment(
	ctx context.Context,
	experimentId uuid.UUID,
) error {
	return c.deps.ExperimentsWrapper.UpdateExperimentDetail(
		ctx,
		experimentId,
		func(experiment *models.Experiment) error {
			experiment.Enabled = false
			return nil
		},
	)
}

var _ ExperimentsClient = &Impl{}
