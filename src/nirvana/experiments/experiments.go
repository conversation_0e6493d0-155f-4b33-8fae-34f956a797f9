package experiments

import (
	"nirvanatech.com/nirvana/experiments/builders"
	"nirvanatech.com/nirvana/experiments/client"
	"nirvanatech.com/nirvana/experiments/models"
)

type (
	Experiment            = models.Experiment
	Variant               = models.ExperimentVariant
	Assignment            = models.ExperimentAssignment
	Applicability         = models.ExperimentApplicability
	Event                 = models.ExperimentEvent
	ApplicabilityMetadata = models.ApplicabilityMetadata
)

type Client = client.ExperimentsClient

type ClientDeps = client.Deps

var ErrApplicabilityNotFound = client.ErrApplicabilityNotFound

func NewApplicabilityBuilder() *builders.ExperimentApplicabilityBuilder {
	return builders.NewExperimentApplicabilityBuilder()
}

func NewExperimentBuilder() *builders.ExperimentBuilder {
	return builders.NewExperimentBuilder()
}

func NewExperimentAssignmentBuilder() *builders.ExperimentAssignmentBuilder {
	return builders.NewExperimentAssignmentBuilder()
}
