package modifiers

//go:generate go run github.com/dmarkham/enumer -type=ModificationType -trimprefix=ModificationType -json -output=modification_type_enumer.go
type ModificationType int

const (
	ModificationTypeUnspecified ModificationType = iota
	ModificationTypeCoverage
	ModificationTypeSafetyCredit
	ModificationTypeLargeLosses
	ModificationTypeOptionalPlugins
	ModificationTypeAddDrivers
	ModificationTypeAddEquipment
	ModificationTypeCommodities
)
