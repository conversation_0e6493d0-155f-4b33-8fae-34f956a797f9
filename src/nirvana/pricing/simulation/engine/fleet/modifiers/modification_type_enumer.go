// Code generated by "enumer -type=ModificationType -trimprefix=ModificationType -json -output=modification_type_enumer.go"; DO NOT EDIT.

package modifiers

import (
	"encoding/json"
	"fmt"
	"strings"
)

const _ModificationTypeName = "UnspecifiedCoverageSafetyCreditLargeLossesOptionalPluginsAddDriversAddEquipmentCommodities"

var _ModificationTypeIndex = [...]uint8{0, 11, 19, 31, 42, 57, 67, 79, 90}

const _ModificationTypeLowerName = "unspecifiedcoveragesafetycreditlargelossesoptionalpluginsadddriversaddequipmentcommodities"

func (i ModificationType) String() string {
	if i < 0 || i >= ModificationType(len(_ModificationTypeIndex)-1) {
		return fmt.Sprintf("ModificationType(%d)", i)
	}
	return _ModificationTypeName[_ModificationTypeIndex[i]:_ModificationTypeIndex[i+1]]
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _ModificationTypeNoOp() {
	var x [1]struct{}
	_ = x[ModificationTypeUnspecified-(0)]
	_ = x[ModificationTypeCoverage-(1)]
	_ = x[ModificationTypeSafetyCredit-(2)]
	_ = x[ModificationTypeLargeLosses-(3)]
	_ = x[ModificationTypeOptionalPlugins-(4)]
	_ = x[ModificationTypeAddDrivers-(5)]
	_ = x[ModificationTypeAddEquipment-(6)]
	_ = x[ModificationTypeCommodities-(7)]
}

var _ModificationTypeValues = []ModificationType{ModificationTypeUnspecified, ModificationTypeCoverage, ModificationTypeSafetyCredit, ModificationTypeLargeLosses, ModificationTypeOptionalPlugins, ModificationTypeAddDrivers, ModificationTypeAddEquipment, ModificationTypeCommodities}

var _ModificationTypeNameToValueMap = map[string]ModificationType{
	_ModificationTypeName[0:11]:       ModificationTypeUnspecified,
	_ModificationTypeLowerName[0:11]:  ModificationTypeUnspecified,
	_ModificationTypeName[11:19]:      ModificationTypeCoverage,
	_ModificationTypeLowerName[11:19]: ModificationTypeCoverage,
	_ModificationTypeName[19:31]:      ModificationTypeSafetyCredit,
	_ModificationTypeLowerName[19:31]: ModificationTypeSafetyCredit,
	_ModificationTypeName[31:42]:      ModificationTypeLargeLosses,
	_ModificationTypeLowerName[31:42]: ModificationTypeLargeLosses,
	_ModificationTypeName[42:57]:      ModificationTypeOptionalPlugins,
	_ModificationTypeLowerName[42:57]: ModificationTypeOptionalPlugins,
	_ModificationTypeName[57:67]:      ModificationTypeAddDrivers,
	_ModificationTypeLowerName[57:67]: ModificationTypeAddDrivers,
	_ModificationTypeName[67:79]:      ModificationTypeAddEquipment,
	_ModificationTypeLowerName[67:79]: ModificationTypeAddEquipment,
	_ModificationTypeName[79:90]:      ModificationTypeCommodities,
	_ModificationTypeLowerName[79:90]: ModificationTypeCommodities,
}

var _ModificationTypeNames = []string{
	_ModificationTypeName[0:11],
	_ModificationTypeName[11:19],
	_ModificationTypeName[19:31],
	_ModificationTypeName[31:42],
	_ModificationTypeName[42:57],
	_ModificationTypeName[57:67],
	_ModificationTypeName[67:79],
	_ModificationTypeName[79:90],
}

// ModificationTypeString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func ModificationTypeString(s string) (ModificationType, error) {
	if val, ok := _ModificationTypeNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _ModificationTypeNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to ModificationType values", s)
}

// ModificationTypeValues returns all values of the enum
func ModificationTypeValues() []ModificationType {
	return _ModificationTypeValues
}

// ModificationTypeStrings returns a slice of all String values of the enum
func ModificationTypeStrings() []string {
	strs := make([]string, len(_ModificationTypeNames))
	copy(strs, _ModificationTypeNames)
	return strs
}

// IsAModificationType returns "true" if the value is listed in the enum definition. "false" otherwise
func (i ModificationType) IsAModificationType() bool {
	for _, v := range _ModificationTypeValues {
		if i == v {
			return true
		}
	}
	return false
}

// MarshalJSON implements the json.Marshaler interface for ModificationType
func (i ModificationType) MarshalJSON() ([]byte, error) {
	return json.Marshal(i.String())
}

// UnmarshalJSON implements the json.Unmarshaler interface for ModificationType
func (i *ModificationType) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return fmt.Errorf("ModificationType should be a string, got %s", data)
	}

	var err error
	*i, err = ModificationTypeString(s)
	return err
}
