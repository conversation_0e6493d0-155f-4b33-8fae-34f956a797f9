package CA_04_01_11_20

import (
	"encoding/json"

	"nirvanatech.com/nirvana/pdffill/fields"

	"nirvanatech.com/nirvana/pdffill"
)

const FormName = "CA_04_01_11_20.pdf"

type Request struct {
	PolicyNumber                                fields.StringM          `json:"PolicyNumber"`
	NamedInsured                                fields.StringXL         `json:"NamedInsured"`
	EndorsementEffectiveDate                    fields.NonZeroDate      `json:"EndorsementEffectiveDate"`
	DesignationOrDescriptionOfCoveredAutos      fields.StringXXL        `json:"DesignationOrDescriptionOfCoveredAutos"`
	DesignationOrDescriptionOfCoveredAutosShort fields.StringXXL        `json:"DesignationOrDescriptionOfCoveredAutosShort"`
	ComprehensiveLimitOfInsurance               fields.IntL             `json:"ComprehensiveLimitOfInsurance"`
	PDComprehensiveAllPerilsDeductible          fields.IntL             `json:"PDComprehensiveAllPerilsDeductible"`
	PDComprehensivePremium                      fields.OptionalStringL  `json:"PDComprehensivePremium"`
	CollisionLimitOfInsurance                   fields.IntL             `json:"CollisionLimitOfInsurance"`
	PDCollisionAllPerilsDeductible              fields.IntL             `json:"PDCollisionAllPerilsDeductible"`
	PDCollisionPremium                          fields.OptionalStringL  `json:"PDCollisionPremium"`
	CompanyOnForm                               fields.InsuranceCarrier `json:"Company"`
	PDCollision                                 fields.StringM          `json:"PDCollision"`
	PDComprehensive                             fields.StringM          `json:"PDComprehensive"`
	PDComprehensiveAndCollisionPremium          fields.IntL             `json:"PDComprehensiveAndCollisionPremium"`
	ScheduleCoverage                            fields.StringXL         `json:"ScheduleCoverage"`
}

var _ pdffill.Request = (*Request)(nil)

func (r *Request) MarshalJSON() ([]byte, error) {
	return json.Marshal(*r)
}

func (r Request) InputPath() string {
	return FormName
}
