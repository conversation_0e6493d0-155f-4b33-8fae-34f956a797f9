package CA_21_09_10_13

import (
	"encoding/json"

	"nirvanatech.com/nirvana/pdffill/fields"
)

const FormName = "CA_21_09_10_13.pdf"

type Request struct {
	PolicyNumber             fields.StringM     `json:"PolicyNumber"`
	NamedInsured             fields.StringXL    `json:"NamedInsured"`
	EndorsementEffectiveDate fields.NonZeroDate `json:"EndorsementEffectiveDate"`
	LimitOfInsuranceUMBI     fields.IntS        `json:"LimitOfInsuranceUMBI"`
}

func (r *Request) MarshalJSON() ([]byte, error) {
	return json.Marshal(*r)
}

func (r Request) InputPath() string {
	return FormName
}
