package coverage

import (
	"nirvanatech.com/nirvana/business-auto/model"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	ib_model "nirvanatech.com/nirvana/insurance-bundle/model"
)

//nolint:exhaustive
var LimitOptions = map[app_enums.Coverage][]float64{
	app_enums.CoverageAutoLiability:      {300_000, 500_000, 750_000, 1_000_000},
	app_enums.CoverageAutoPhysicalDamage: {50_000, 100_000, 250_000, 500_000, DefaultAPDLimit},
}

const DefaultAPDLimit = 1_000_000

// Generic limit amount constants that can be reused across states and coverages
const (
	Limit1K  = 1_000
	Limit1K5 = 1_500
	Limit2K5 = 2_500
	Limit5K  = 5_000
	Limit10K = 10_000
	Limit15K = 15_000
	Limit25K = 25_000
	Limit50K = 50_000
)

// LimitMapping represents the mapping from aggregate to monthly limit
type LimitMapping struct {
	Aggregate float64
	Monthly   float64
}

// StateCoverageLimitConfig holds limit configurations for a specific state and coverage combination
type StateCoverageLimitConfig struct {
	State    us_states.USState
	Coverage app_enums.Coverage
	Mappings []LimitMapping
}

// Standard Work Loss Benefits configurations that can be reused across states
var WorkLossBenefitsStandardMappings = []LimitMapping{
	{Aggregate: Limit5K, Monthly: Limit1K},
	{Aggregate: Limit10K, Monthly: Limit1K},
	{Aggregate: Limit15K, Monthly: Limit1K},
	{Aggregate: Limit25K, Monthly: Limit1K5},
	{Aggregate: Limit50K, Monthly: Limit2K5},
}

// StateCoverageLimitConfigs defines all limit configurations
var StateCoverageLimitConfigs = []StateCoverageLimitConfig{
	{
		State:    us_states.PA,
		Coverage: app_enums.CoverageWorkLossBenefits,
		Mappings: WorkLossBenefitsStandardMappings,
	},
}

// StateCoverageLimitKey represents a unique key for state, coverage, and aggregate limit combination
type StateCoverageLimitKey struct {
	State     us_states.USState
	Coverage  app_enums.Coverage
	Aggregate float64
}

// StateCoverageAggregateLimitToMonthlyLimitMap maps state + coverage + aggregate combinations to monthly limit amounts
// This map is built from the StateCoverageLimitConfigs for performance
var StateCoverageAggregateLimitToMonthlyLimitMap = buildLimitMappingMap()

// buildLimitMappingMap constructs the lookup map from the configuration
func buildLimitMappingMap() map[StateCoverageLimitKey]float64 {
	result := make(map[StateCoverageLimitKey]float64)

	for _, config := range StateCoverageLimitConfigs {
		for _, mapping := range config.Mappings {
			key := StateCoverageLimitKey{
				State:     config.State,
				Coverage:  config.Coverage,
				Aggregate: mapping.Aggregate,
			}
			result[key] = mapping.Monthly
		}
	}

	return result
}

// generateStateSpecificLimits creates state-specific limits for a coverage
// Returns a slice of limits that may include aggregate, monthly, or other state-specific limit types
// Returns empty slice if no state-specific handling is required
func generateStateSpecificLimits(
	state us_states.USState,
	coverage app_enums.Coverage,
	limitAmount float64,
) []model.Limit {
	var limits []model.Limit

	switch state {
	case us_states.PA:
		switch coverage {
		case app_enums.CoverageWorkLossBenefits:
			// Create aggregate limit
			limits = append(limits, model.Limit{
				SubCoverageIDs: []app_enums.Coverage{coverage},
				Amount:         limitAmount,
				Grouping:       ib_model.LimitGrouping_LimitGrouping_Single,
				Type:           pointer_utils.ToPointer(model.LimitTypeAggregate),
			})

			// Get corresponding monthly limit and create monthly limit
			if monthlyLimit, monthlyExists := GetMonthlyAggregateLimits(us_states.PA, coverage, limitAmount); monthlyExists {
				limits = append(limits, model.Limit{
					SubCoverageIDs: []app_enums.Coverage{coverage},
					Amount:         monthlyLimit,
					Grouping:       ib_model.LimitGrouping_LimitGrouping_Single,
					Type:           pointer_utils.ToPointer(model.LimitTypeMonthly),
				})
			}
		default:
			return limits
		}
	default:
		// No state-specific handling, return empty slice
		return limits
	}

	return limits
}

// GetMonthlyAggregateLimits returns the monthly limit for a given state, coverage, and aggregate limit
// Returns the monthly limit and a boolean indicating if the combination is supported
func GetMonthlyAggregateLimits(
	state us_states.USState,
	coverage app_enums.Coverage,
	aggregateLimit float64,
) (float64, bool) {
	key := StateCoverageLimitKey{
		State:     state,
		Coverage:  coverage,
		Aggregate: aggregateLimit,
	}
	monthlyLimit, exists := StateCoverageAggregateLimitToMonthlyLimitMap[key]
	return monthlyLimit, exists
}

// BuildAncillaryLimits builds limits for ancillary coverages with proper grouping logic
// Handles BI+PD grouping for AL and UMBI+UMPD grouping for Indiana
func BuildAncillaryLimits(
	ancillaryCoverages []app_enums.Coverage,
	limits map[app_enums.Coverage]float64,
	vehicleLimits map[app_enums.Coverage]map[string]float64,
	state us_states.USState,
) []model.Limit {
	var limitsList []model.Limit
	processedCoverages := make(map[app_enums.Coverage]bool)

	// Handle UMBI + UMPD grouping for Indiana
	if shouldGroupUMBIAndUMPD(ancillaryCoverages, state) {
		// Always mark these coverages as processed when they should be grouped
		// This prevents vehicle-level limits from being used for grouped coverages
		processedCoverages[app_enums.CoverageUninsuredMotoristBodilyInjury] = true
		processedCoverages[app_enums.CoverageUninsuredMotoristPropertyDamage] = true

		// Get the limit (should be same for both, but prefer UMBI)
		var limitAmount float64
		if umbiLimit, exists := limits[app_enums.CoverageUninsuredMotoristBodilyInjury]; exists {
			limitAmount = umbiLimit
		} else if umpdLimit, exists := limits[app_enums.CoverageUninsuredMotoristPropertyDamage]; exists {
			limitAmount = umpdLimit
		}

		// Only create the limit if we actually have a limit amount
		if limitAmount > 0 {
			limitsList = append(limitsList, model.Limit{
				SubCoverageIDs: []app_enums.Coverage{
					app_enums.CoverageUninsuredMotoristBodilyInjury,
					app_enums.CoverageUninsuredMotoristPropertyDamage,
				},
				Amount:   limitAmount,
				Grouping: ib_model.LimitGrouping_LimitGrouping_Combined,
				Type:     pointer_utils.ToPointer(model.LimitTypePerOccurrence), // Default to per occurrence
			})
		}
	}

	// Handle BI + PD grouping for AL (if both are present in ancillary coverages)
	if shouldGroupBIAndPD(ancillaryCoverages) {
		// Always mark these coverages as processed when they should be grouped
		// This prevents vehicle-level limits from being used for grouped coverages
		processedCoverages[app_enums.CoverageBodilyInjury] = true
		processedCoverages[app_enums.CoveragePropertyDamage] = true

		// Get the limit (should be same for both, but prefer BI)
		var limitAmount float64
		if biLimit, exists := limits[app_enums.CoverageBodilyInjury]; exists {
			limitAmount = biLimit
		} else if pdLimit, exists := limits[app_enums.CoveragePropertyDamage]; exists {
			limitAmount = pdLimit
		}

		// Only create the limit if we actually have a limit amount
		if limitAmount > 0 {
			limitsList = append(limitsList, model.Limit{
				SubCoverageIDs: []app_enums.Coverage{
					app_enums.CoverageBodilyInjury,
					app_enums.CoveragePropertyDamage,
				},
				Amount:   limitAmount,
				Grouping: ib_model.LimitGrouping_LimitGrouping_Combined,
				Type:     pointer_utils.ToPointer(model.LimitTypePerOccurrence), // Default to per occurrence
			})
		}
	}

	// Handle individual ancillary coverage limits
	for _, coverage := range ancillaryCoverages {
		if processedCoverages[coverage] {
			continue // Skip already processed coverages
		}

		// Check for state-specific limit handling first
		if limitAmount, exists := limits[coverage]; exists {
			stateSpecificLimits := generateStateSpecificLimits(state, coverage, limitAmount)
			if len(stateSpecificLimits) > 0 {
				// State-specific limits were generated, add them and continue
				limitsList = append(limitsList, stateSpecificLimits...)
				continue
			}
		}

		// Check if there are vehicle-specific limits first
		if vehicleLimitMap, hasVehicleLimits := vehicleLimits[coverage]; hasVehicleLimits {
			// Create vehicle-specific limits
			for vin, vehicleLimitAmount := range vehicleLimitMap {
				limitsList = append(limitsList, model.Limit{
					SubCoverageIDs: []app_enums.Coverage{coverage},
					Amount:         vehicleLimitAmount,
					Grouping:       ib_model.LimitGrouping_LimitGrouping_Single,
					VIN:            &vin,
					Type:           pointer_utils.ToPointer(model.LimitTypePerOccurrence), // Default to per occurrence
				})
			}
		} else if limitAmount, exists := limits[coverage]; exists {
			// Create standard policy-level limit
			limitsList = append(limitsList, model.Limit{
				SubCoverageIDs: []app_enums.Coverage{coverage},
				Amount:         limitAmount,
				Grouping:       ib_model.LimitGrouping_LimitGrouping_Single,
				Type:           pointer_utils.ToPointer(model.LimitTypePerOccurrence), // Default to per occurrence
			})
		}
	}

	return limitsList
}

// HasVehicleLevelLimit determines if a coverage should have vehicle-level limits
// based on the coverage type, selected coverages, and state
func HasVehicleLevelLimit(
	coverage app_enums.Coverage,
	selectedCoverages []app_enums.Coverage,
	state us_states.USState,
) bool {
	switch coverage {
	case app_enums.CoverageUninsuredMotoristPropertyDamage:
		// UMPD has vehicle-level limits unless it's grouped with UMBI in Indiana
		return !shouldGroupUMBIAndUMPD(selectedCoverages, state)
	case app_enums.CoverageRentalReimbursement:
		return true
	case app_enums.CoverageTowingLaborAndStorage:
		return true
	default:
		// Other coverages don't have vehicle-level limits by default
		return false
	}
}

// shouldGroupUMBIAndUMPD determines if UMBI and UMPD should be grouped together
// They are grouped in Indiana when both coverages are present
func shouldGroupUMBIAndUMPD(coverages []app_enums.Coverage, state us_states.USState) bool {
	if state != us_states.IN {
		return false
	}

	hasUMBI := containsCoverageInList(coverages, app_enums.CoverageUninsuredMotoristBodilyInjury)
	hasUMPD := containsCoverageInList(coverages, app_enums.CoverageUninsuredMotoristPropertyDamage)

	return hasUMBI && hasUMPD
}

// shouldGroupBIAndPD determines if BI and PD should be grouped together
// They are grouped when both coverages are present in ancillary coverages
func shouldGroupBIAndPD(coverages []app_enums.Coverage) bool {
	hasBIInAncillary := containsCoverageInList(coverages, app_enums.CoverageBodilyInjury)
	hasPDInAncillary := containsCoverageInList(coverages, app_enums.CoveragePropertyDamage)

	return hasBIInAncillary && hasPDInAncillary
}

// Helper function to check if a coverage is in a list
func containsCoverageInList(coverages []app_enums.Coverage, coverage app_enums.Coverage) bool {
	for _, c := range coverages {
		if c == coverage {
			return true
		}
	}
	return false
}
