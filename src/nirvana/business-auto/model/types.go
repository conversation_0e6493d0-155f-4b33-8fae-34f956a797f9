package model

import (
	"encoding/json"
	"sort"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/business-auto/enums"
	"nirvanatech.com/nirvana/common-go/short_id"
	"nirvanatech.com/nirvana/common-go/us_states"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	ib_model "nirvanatech.com/nirvana/insurance-bundle/model"
	nirvanaapp_enums "nirvanatech.com/nirvana/nirvanaapp/enums"
	"nirvanatech.com/nirvana/nirvanaapp/models/application"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
	"nirvanatech.com/nirvana/rating/rtypes"
	"nirvanatech.com/nirvana/telematics"
)

// LimitType represents the type of limit (per occurrence, aggregate, or monthly)
//
//go:generate go run github.com/dmarkham/enumer -type=LimitType -json -text -trimprefix=LimitType
type LimitType int

const (
	LimitTypeInvalid LimitType = iota
	LimitTypePerOccurrence
	LimitTypeAggregate
	LimitTypeMonthly
)

type BusinessAutoApp struct {
	ID                              uuid.UUID
	DataContextID                   uuid.UUID
	ShortID                         short_id.ShortID
	EffectiveDurationStart          time.Time
	EffectiveDurationEnd            time.Time
	CompanyInfo                     CompanyInfo
	VehiclesInfo                    *[]VehicleInfo
	FilingsInfo                     *FilingsInfo
	ProducerID                      uuid.UUID
	CreatedBy                       uuid.UUID
	CreatedAt                       time.Time
	UpdatedAt                       time.Time
	AgencyID                        uuid.UUID
	UWState                         enums.UWState
	CoveragesInfo                   *CoveragesInfo
	UnderwritingOverrides           *UnderwritingOverrides
	SignaturePacketID               *uuid.UUID
	TelematicsInfo                  *TelematicsInfo
	TSPConnHandleId                 *uuid.UUID
	PricingJobInfo                  *PricingInfo
	SelectedQuotingPricingContextID *uuid.UUID
	ModelPinConfig                  *ModelPinConfigInfo
	DocumentsInfo                   *DocumentsInfo
}

// LossFreeCredit represents loss free credit modifications by coverage
type LossFreeCredit struct {
	Mods []LossFreeCreditModification
}

// LossFreeCreditModification represents the loss free credit information for a specific coverage
type LossFreeCreditModification struct {
	// Coverage type this modification applies to
	Coverage app_enums.Coverage

	// Indicates if loss free credit applies (Y/N)
	Applies *bool
}

// DriverFactor represents the driver factor applied by UW (only for 1-10 units)
type DriverFactor struct {
	Factor *float64
}

// ScheduleMod represents schedule rating by coverage/line of business
type ScheduleMod struct {
	Mods map[app_enums.Coverage]*float64
}

// ExperienceMod represents experience rating by coverage/line of business
type ExperienceMod struct {
	Mods map[app_enums.Coverage]*float64
}

type VehicleInfo struct {
	// Basic Vehicle Information
	VIN   string
	Year  int32
	Make  string
	Model string

	// Vehicle Classification
	VehicleType          nirvanaapp_enums.VehicleType
	WeightClass          enums.WeightClass
	SpecialtyVehicleType enums.SpecialtyVehicleType
	VehicleUse           enums.VehicleUse
	BusinessUse          enums.BusinessUse

	// Trailer Information
	TrailerType *enums.TrailerType

	// Usage Information
	StateUsage           enums.StateUsage
	RadiusClassification enums.RadiusClassification

	// Physical Damage Information
	StatedValue *float64

	APDDeductible *int64

	PrincipalGaragingLocationZipCode string

	// Special Vehicle Designations
	IsGlassLinedTankTruckOrTrailer *bool
	IsRefrigeratedTruckOrTrailer   *bool
	IsDoubleTrailer                *bool
	HasAntiLockBrakes              *bool
}
type FilingsInfo struct {
	HasMultiStateFilings  bool
	HasSingleStateFilings bool
	HasFMCSAFilings       bool
	HasDOTFilings         bool
}

type PrimaryCoverage struct {
	ID             app_enums.Coverage
	SubCoverageIDs []app_enums.Coverage
}

type Limit struct {
	SubCoverageIDs []app_enums.Coverage
	Amount         float64
	Grouping       ib_model.LimitGrouping
	VIN            *string    // Optional VIN for vehicle-specific limits
	Type           *LimitType // Nullable for backward compatibility, nil means PerOccurrence
}

type Deductible struct {
	SubCoverageIDs []app_enums.Coverage
	Amount         float64
	VIN            *string // Optional VIN for vehicle-specific limits
}

type CoveragesInfo struct {
	PrimaryCoverages []PrimaryCoverage
	Limits           []Limit
	Deductibles      []Deductible
}

// GetLimitForSubcoverage finds the limit for a specific subcoverage
// Returns the limit amount if found, nil otherwise
// When multiple limits exist for the same coverage, prioritizes aggregate limits over monthly limits
// Treats nil LimitType as LimitTypePerOccurrence for backward compatibility
func (c *CoveragesInfo) GetLimitForSubcoverage(subcoverage app_enums.Coverage) *float64 {
	var covLimit *float64
	for _, limit := range c.Limits {
		for _, subCoverageID := range limit.SubCoverageIDs {
			if subCoverageID == subcoverage {
				if covLimit == nil {
					covLimit = &limit.Amount
				}

				// Prioritize aggregate limit over other types - return immediately when found
				// Treat nil LimitType as PerOccurrence for backward compatibility
				if limit.Type != nil && *limit.Type == LimitTypeAggregate {
					return &limit.Amount
				}
			}
		}
	}
	return covLimit
}

// UnderwritingOverrides represents all underwriting factor overrides stored in JSONB
type UnderwritingOverrides struct {
	CoveragesWithLossFreeCredits []app_enums.Coverage

	LossFreeCreditPercentage *float64

	// Driver factor - only applicable for 1-10 units
	DriverFactor *DriverFactor `json:"driverFactor,omitempty"`

	// Schedule mods - map keyed by coverage string, value is pointer to modifier
	ScheduleMods *ScheduleMod `json:"scheduleMods,omitempty"`

	// Experience mod - UW applied
	ExperienceMod *ExperienceMod `json:"experienceMod,omitempty"`

	QualityRatingGrade *enums.QualityRatingGrade
}

func (c *CoveragesInfo) ContainsCoverage(coverageToFind app_enums.Coverage) bool {
	if c == nil {
		return false
	}
	for _, coverage := range c.PrimaryCoverages {
		if coverage.ID == coverageToFind {
			return true
		}
	}
	return false
}

func (c *CoveragesInfo) GetPrimaryCoverageTypes() []app_enums.Coverage {
	if c == nil {
		return nil
	}
	retval := make([]app_enums.Coverage, 0, len(c.PrimaryCoverages))
	for _, cov := range c.PrimaryCoverages {
		retval = append(retval, cov.ID)
	}
	return retval
}

// TelematicsInfo represents the telematics information for business auto applications
type TelematicsInfo struct {
	FullName string `json:"fullName"`
	Email    string `json:"email"`

	// TSP information
	TSPEnum *telematics.TSP `json:"tspEnum,omitempty"`

	// Standard pipeline and data status information (same as fleet/nonfleet)
	PipelineInfo *application.TelematicsInfo `json:"pipelineInfo,omitempty"`
}

// CoveragePremium represents premium information for a specific coverage
type CoveragePremium struct {
	Coverage          app_enums.Coverage `json:"coverage"`
	TotalPremium      int64              `json:"totalPremium"`
	SubCoverageGroups []SubCoverageGroup `json:"subCoverageGroups,omitempty"`
}

// SubCoverageGroup represents a group of sub-coverages with the same premium
type SubCoverageGroup struct {
	Coverages    []string `json:"coverages"`
	TotalPremium int64    `json:"totalPremium"`
}

// SubCoveragePremium represents premium information for a sub-coverage with string type
type SubCoveragePremium struct {
	Coverage     string `json:"coverage"`
	TotalPremium int64  `json:"totalPremium"`
}

// SurchargePremium represents premium information for a specific surcharge
type SurchargePremium struct {
	Type   ptypes.Surcharge_Type `json:"surchargeType"`
	Amount int64                 `json:"amount"`
}

// SurchargeBreakdown represents surcharge breakdown with individual surcharges and total
type SurchargeBreakdown struct {
	Surcharges  []SurchargePremium `json:"surcharges"`
	TotalAmount int64              `json:"totalAmount"`
}

// PricingData represents aggregated pricing information for a business auto application
type PricingData struct {
	ApplicationID uuid.UUID          `json:"applicationId"`
	DataContextID uuid.UUID          `json:"dataContextId"`
	TotalPremium  int64              `json:"totalPremium"`
	Coverages     []CoveragePremium  `json:"coverages"`
	Surcharges    SurchargeBreakdown `json:"surcharges"`
	CreatedAt     time.Time          `json:"createdAt"`
}

// PricingStatus represents the possible states of a pricing operation
type PricingStatus string

const (
	PricingStatusPending   PricingStatus = "Pending"
	PricingStatusCompleted PricingStatus = "Completed"
	PricingStatusFailed    PricingStatus = "Failed"
)

// PricingInfo contains detailed pricing status information
type PricingInfo struct {
	Status       PricingStatus `json:"status"`
	StartedAt    time.Time     `json:"startedAt"`
	CompletedAt  *time.Time    `json:"completedAt,omitempty"`
	ErrorMessage *string       `json:"errorMessage,omitempty"`
	JobID        *string       `json:"jobId,omitempty"`
}

func NewModelPinConfigInfo(
	provider rtypes.RatemlModelProvider,
	state us_states.USState,
	version rtypes.RatemlModelVersion,
) *ModelPinConfigInfo {
	return &ModelPinConfigInfo{
		RateML: RateMLConfig{
			Provider: provider,
			USState:  state,
			Version:  version,
		},
	}
}

// ModelPinConfigInfo represents the model pin configuration keyed by state.
type ModelPinConfigInfo struct {
	RateML RateMLConfig `json:"rateML"`
}

// RateMLConfig represents the RateML configuration without flags (simplified version for business auto)
type RateMLConfig struct {
	Provider rtypes.RatemlModelProvider `json:"provider"`
	USState  us_states.USState          `json:"usState"`
	Version  rtypes.RatemlModelVersion  `json:"version"`
}

// UnmarshalJSON implements custom JSON unmarshaling for RateMLConfig to handle USState field
func (c *RateMLConfig) UnmarshalJSON(bytes []byte) error {
	aux := &struct {
		USState  *string                    `json:"usState"`
		Provider rtypes.RatemlModelProvider `json:"provider"`
		Version  rtypes.RatemlModelVersion  `json:"version"`
	}{}
	if err := json.Unmarshal(bytes, &aux); err != nil {
		return errors.Wrap(err, "unable to unmarshal into aux rate ml config")
	}

	c.Provider = aux.Provider
	c.Version = aux.Version

	if aux.USState != nil {
		if *aux.USState == "" {
			c.USState = us_states.InvalidStateCode
		} else {
			usState, err := us_states.StrToUSState(*aux.USState)
			if err != nil {
				return errors.Wrapf(err, "unable to parse %s us state for rate ml config", *aux.USState)
			}
			c.USState = usState
		}
	} else {
		// Handle nil case (absent or null usState field) consistently with empty string
		c.USState = us_states.InvalidStateCode
	}
	return nil
}

// DocumentsInfo represents document-related information for business auto applications
type DocumentsInfo struct {
	QuotePDFHandleID *uuid.UUID `json:"quotePDFHandleID,omitempty"`
}

// GetLimitForAllSubCoverages extracts coverage limits from CoveragesInfo
func (c *CoveragesInfo) GetLimitForAllSubCoverages() map[app_enums.Coverage]float64 {
	coverageLimits := make(map[app_enums.Coverage]float64)
	if c == nil {
		return coverageLimits
	}

	for _, limit := range c.Limits {
		for _, subCoverageID := range limit.SubCoverageIDs {
			coverageLimits[subCoverageID] = limit.Amount
		}
	}

	return coverageLimits
}

func (c *CoveragesInfo) GetAllPrimaryAndAncCoverages() []app_enums.Coverage {
	if c == nil {
		return []app_enums.Coverage{}
	}

	var selectedCoverages []app_enums.Coverage
	for _, primaryCoverage := range c.PrimaryCoverages {
		selectedCoverages = append(selectedCoverages, primaryCoverage.ID)
		selectedCoverages = append(selectedCoverages, primaryCoverage.SubCoverageIDs...)
	}

	return selectedCoverages
}

func (c *CoveragesInfo) GetAllAncillaryCoverageForPrimaryCoverage(
	primaryCoverage app_enums.Coverage,
) ([]app_enums.Coverage, error) {
	if c == nil {
		return []app_enums.Coverage{}, errors.New("coverages info is nil")
	}

	for _, coverage := range c.PrimaryCoverages {
		if coverage.ID == primaryCoverage {
			return coverage.SubCoverageIDs, nil
		}
	}

	return []app_enums.Coverage{}, errors.Newf("primary coverage %s not found in coverages info", primaryCoverage.String())
}

// SortCoveragePremiums sorts coverage premiums alphabetically by coverage type
func SortCoveragePremiums(coverages []CoveragePremium) {
	sort.Slice(coverages, func(i, j int) bool {
		return coverages[i].Coverage.String() < coverages[j].Coverage.String()
	})
}

// SortSurchargePremiums sorts surcharge premiums alphabetically by surcharge type
func SortSurchargePremiums(surcharges []SurchargePremium) {
	sort.Slice(surcharges, func(i, j int) bool {
		return surcharges[i].Type.String() < surcharges[j].Type.String()
	})
}
