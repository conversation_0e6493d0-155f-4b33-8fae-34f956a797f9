package model

import (
	"encoding/json"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/business-auto/enums"
	"nirvanatech.com/nirvana/common-go/us_states"
	"nirvanatech.com/nirvana/nirvanaapp/models/application"
)

type CompanyInfo struct {
	Name                            string
	DOTNumber                       *int
	FEIN                            *string
	NoOfPowerUnits                  *int32
	HasIndividualNamedInsured       *bool
	HasWorkCompPolicy               *bool
	NoOfEmployees                   *int32
	PerOfEmployeesOperatingOwnAutos *float32

	PrimaryIndustryClassification    *enums.IndustryClassification
	SecondaryIndustryClassifications []enums.IndustryClassification
	AnnualCostOfHire                 *float64
	USState                          us_states.USState
	MaximumValueOfHiredAutos         *float64
	Address                          *application.Address `json:"address,omitempty"`
}

// UnmarshalJSON implements json.Unmarshaler interface
func (c *CompanyInfo) UnmarshalJSON(data []byte) error {
	// Use type alias to avoid recursive UnmarshalJSON calls
	type Alias CompanyInfo
	aux := &struct {
		USState *string `json:"USState,omitempty"`
		*Alias
	}{
		Alias: (*Alias)(c),
	}

	if err := json.Unmarshal(data, &aux); err != nil {
		return errors.Wrap(err, "failed to unmarshal company info")
	}

	// Convert string state to USState if present
	if aux.USState != nil {
		usState, err := us_states.StrToUSState(*aux.USState)
		if err != nil {
			return errors.Wrapf(err, "failed to parse US state %s", *aux.USState)
		}
		c.USState = usState
	}

	return nil
}

// MarshalJSON implements json.Marshaler interface
func (c CompanyInfo) MarshalJSON() ([]byte, error) {
	type Alias CompanyInfo

	alias := Alias(c)
	alias.USState = nil // prevent duplicate key
	aux := struct {
		USState *string `json:"USState,omitempty"`
		Alias
	}{
		Alias: alias,
	}

	// Handle USState conversion to string
	usState := c.USState
	stateStr := usState.ToCode()
	aux.USState = &stateStr

	return json.Marshal(aux)
}
