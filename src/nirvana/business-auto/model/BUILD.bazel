load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "model",
    srcs = [
        "company_info.go",
        "limittype_enumer.go",
        "types.go",
    ],
    importpath = "nirvanatech.com/nirvana/business-auto/model",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/business-auto/enums",
        "//nirvana/common-go/short_id",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/insurance-bundle/model",
        "//nirvana/nirvanaapp/enums",
        "//nirvana/nirvanaapp/models/application",
        "//nirvana/rating/pricing/api/ptypes",
        "//nirvana/rating/rtypes",
        "//nirvana/telematics",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
    ],
)
