// Code generated by "enumer -type=Operation -trimprefix=Operation -json -transform=snake"; DO NOT EDIT.

package metrics

import (
	"encoding/json"
	"fmt"
	"strings"
)

const _OperationName = "payment_endpoint_executionmatch_producer_to_agency_in_payments_providerget_billing_info_for_policy_identifier"

var _OperationIndex = [...]uint8{0, 26, 71, 109}

const _OperationLowerName = "payment_endpoint_executionmatch_producer_to_agency_in_payments_providerget_billing_info_for_policy_identifier"

func (i Operation) String() string {
	i -= 1
	if i < 0 || i >= Operation(len(_OperationIndex)-1) {
		return fmt.Sprintf("Operation(%d)", i+1)
	}
	return _OperationName[_OperationIndex[i]:_OperationIndex[i+1]]
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _OperationNoOp() {
	var x [1]struct{}
	_ = x[OperationPaymentEndpointExecution-(1)]
	_ = x[OperationMatchProducerToAgencyInPaymentsProvider-(2)]
	_ = x[OperationGetBillingInfoForPolicyIdentifier-(3)]
}

var _OperationValues = []Operation{OperationPaymentEndpointExecution, OperationMatchProducerToAgencyInPaymentsProvider, OperationGetBillingInfoForPolicyIdentifier}

var _OperationNameToValueMap = map[string]Operation{
	_OperationName[0:26]:        OperationPaymentEndpointExecution,
	_OperationLowerName[0:26]:   OperationPaymentEndpointExecution,
	_OperationName[26:71]:       OperationMatchProducerToAgencyInPaymentsProvider,
	_OperationLowerName[26:71]:  OperationMatchProducerToAgencyInPaymentsProvider,
	_OperationName[71:109]:      OperationGetBillingInfoForPolicyIdentifier,
	_OperationLowerName[71:109]: OperationGetBillingInfoForPolicyIdentifier,
}

var _OperationNames = []string{
	_OperationName[0:26],
	_OperationName[26:71],
	_OperationName[71:109],
}

// OperationString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func OperationString(s string) (Operation, error) {
	if val, ok := _OperationNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _OperationNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to Operation values", s)
}

// OperationValues returns all values of the enum
func OperationValues() []Operation {
	return _OperationValues
}

// OperationStrings returns a slice of all String values of the enum
func OperationStrings() []string {
	strs := make([]string, len(_OperationNames))
	copy(strs, _OperationNames)
	return strs
}

// IsAOperation returns "true" if the value is listed in the enum definition. "false" otherwise
func (i Operation) IsAOperation() bool {
	for _, v := range _OperationValues {
		if i == v {
			return true
		}
	}
	return false
}

// MarshalJSON implements the json.Marshaler interface for Operation
func (i Operation) MarshalJSON() ([]byte, error) {
	return json.Marshal(i.String())
}

// UnmarshalJSON implements the json.Unmarshaler interface for Operation
func (i *Operation) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return fmt.Errorf("Operation should be a string, got %s", data)
	}

	var err error
	*i, err = OperationString(s)
	return err
}
