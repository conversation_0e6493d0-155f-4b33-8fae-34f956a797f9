// Package nonfleet provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/deepmap/oapi-codegen/v2 version v2.0.0 DO NOT EDIT.
package nonfleet

import (
	openapi_types "github.com/oapi-codegen/runtime/types"
	externalRef0 "nirvanatech.com/nirvana/openapi-specs/components/application"
	externalRef1 "nirvanatech.com/nirvana/openapi-specs/components/common"
	externalRef2 "nirvanatech.com/nirvana/openapi-specs/components/forms"
)

// Defines values for AdmittedCategoryType.
const (
	CategoryTypeAutoPartsAndAccessories               AdmittedCategoryType = "CategoryTypeAutoPartsAndAccessories"
	CategoryTypeBeveragesAlcoholic                    AdmittedCategoryType = "CategoryTypeBeveragesAlcoholic"
	CategoryTypeBeveragesNonalcoholic                 AdmittedCategoryType = "CategoryTypeBeveragesNonalcoholic"
	CategoryTypeBuildingMaterialsAndLumber            AdmittedCategoryType = "CategoryTypeBuildingMaterialsAndLumber"
	CategoryTypeElectronicsFinished                   AdmittedCategoryType = "CategoryTypeElectronicsFinished"
	CategoryTypeElectronicsParts                      AdmittedCategoryType = "CategoryTypeElectronicsParts"
	CategoryTypeFoodsBakedDryPackagedAndCanned        AdmittedCategoryType = "CategoryTypeFoodsBakedDryPackagedAndCanned"
	CategoryTypeFoodsFreshProduceAndNondry            AdmittedCategoryType = "CategoryTypeFoodsFreshProduceAndNondry"
	CategoryTypeFoodsFrozenRefrigeratedMeatAndSeafood AdmittedCategoryType = "CategoryTypeFoodsFrozenRefrigeratedMeatAndSeafood"
	CategoryTypeFoodsFrozenRefrigeratedMeatAtSeafood  AdmittedCategoryType = "CategoryTypeFoodsFrozenRefrigeratedMeatAtSeafood"
	CategoryTypeFoodsFrozenRefrigeratedNonmeat        AdmittedCategoryType = "CategoryTypeFoodsFrozenRefrigeratedNonmeat"
	CategoryTypeGarbage                               AdmittedCategoryType = "CategoryTypeGarbage"
	CategoryTypeGeneralFreight                        AdmittedCategoryType = "CategoryTypeGeneralFreight"
	CategoryTypeHouseholdGoods                        AdmittedCategoryType = "CategoryTypeHouseholdGoods"
	CategoryTypeHouseholdHardware                     AdmittedCategoryType = "CategoryTypeHouseholdHardware"
	CategoryTypeMailAndParcel                         AdmittedCategoryType = "CategoryTypeMailAndParcel"
	CategoryTypeMetalsNonprecious                     AdmittedCategoryType = "CategoryTypeMetalsNonprecious"
	CategoryTypeMobileHomes                           AdmittedCategoryType = "CategoryTypeMobileHomes"
	CategoryTypeOther                                 AdmittedCategoryType = "CategoryTypeOther"
	CategoryTypePaperAndPlasticProducts               AdmittedCategoryType = "CategoryTypePaperAndPlasticProducts"
	CategoryTypeTextiles                              AdmittedCategoryType = "CategoryTypeTextiles"
)

// Defines values for AdmittedCommodity.
const (
	AdmittedCommodityAggregatesGravelRockSandStone            AdmittedCommodity = "AdmittedCommodityAggregatesGravelRockSandStone"
	AdmittedCommodityAircraftEngines                          AdmittedCommodity = "AdmittedCommodityAircraftEngines"
	AdmittedCommodityAircraftPartsNotEngines                  AdmittedCommodity = "AdmittedCommodityAircraftPartsNotEngines"
	AdmittedCommodityAluminum                                 AdmittedCommodity = "AdmittedCommodityAluminum"
	AdmittedCommodityAnimalByProducts                         AdmittedCommodity = "AdmittedCommodityAnimalByProducts"
	AdmittedCommodityAppliances                               AdmittedCommodity = "AdmittedCommodityAppliances"
	AdmittedCommodityAsphaltCement                            AdmittedCommodity = "AdmittedCommodityAsphaltCement"
	AdmittedCommodityAutoAccessoriesPartsNotTires             AdmittedCommodity = "AdmittedCommodityAutoAccessoriesPartsNotTires"
	AdmittedCommodityAutomobiles                              AdmittedCommodity = "AdmittedCommodityAutomobiles"
	AdmittedCommodityBakedGoods                               AdmittedCommodity = "AdmittedCommodityBakedGoods"
	AdmittedCommodityBeerWineNoLiquor                         AdmittedCommodity = "AdmittedCommodityBeerWineNoLiquor"
	AdmittedCommodityBeverages                                AdmittedCommodity = "AdmittedCommodityBeverages"
	AdmittedCommodityBoatsGolfCartsRVTravelTrailers           AdmittedCommodity = "AdmittedCommodityBoatsGolfCartsRVTravelTrailers"
	AdmittedCommodityBottlesPlastic                           AdmittedCommodity = "AdmittedCommodityBottlesPlastic"
	AdmittedCommodityBusinessDocumentsNonNegotiableSecurities AdmittedCommodity = "AdmittedCommodityBusinessDocumentsNonNegotiableSecurities"
	AdmittedCommodityCDSDVDVideoGamesTapes                    AdmittedCommodity = "AdmittedCommodityCDSDVDVideoGamesTapes"
	AdmittedCommodityCannedGoods                              AdmittedCommodity = "AdmittedCommodityCannedGoods"
	AdmittedCommodityCarpetNotOriental                        AdmittedCommodity = "AdmittedCommodityCarpetNotOriental"
	AdmittedCommodityCaskets                                  AdmittedCommodity = "AdmittedCommodityCaskets"
	AdmittedCommodityChemicalsNonHazardous                    AdmittedCommodity = "AdmittedCommodityChemicalsNonHazardous"
	AdmittedCommodityCleaningSuppliesAndCompounds             AdmittedCommodity = "AdmittedCommodityCleaningSuppliesAndCompounds"
	AdmittedCommodityClothingAndShoesNonDesigner              AdmittedCommodity = "AdmittedCommodityClothingAndShoesNonDesigner"
	AdmittedCommodityCoal                                     AdmittedCommodity = "AdmittedCommodityCoal"
	AdmittedCommodityCoiledSteel                              AdmittedCommodity = "AdmittedCommodityCoiledSteel"
	AdmittedCommodityConstructionEquipment                    AdmittedCommodity = "AdmittedCommodityConstructionEquipment"
	AdmittedCommodityContainerizedFreight                     AdmittedCommodity = "AdmittedCommodityContainerizedFreight"
	AdmittedCommodityCosmeticsPerfume                         AdmittedCommodity = "AdmittedCommodityCosmeticsPerfume"
	AdmittedCommodityCottonNonGinned                          AdmittedCommodity = "AdmittedCommodityCottonNonGinned"
	AdmittedCommodityDairy                                    AdmittedCommodity = "AdmittedCommodityDairy"
	AdmittedCommodityDyesInkAndPaintsNonHazardous             AdmittedCommodity = "AdmittedCommodityDyesInkAndPaintsNonHazardous"
	AdmittedCommodityEggs                                     AdmittedCommodity = "AdmittedCommodityEggs"
	AdmittedCommodityElectricalCommunicationsEquipment        AdmittedCommodity = "AdmittedCommodityElectricalCommunicationsEquipment"
	AdmittedCommodityElectricalSupplies                       AdmittedCommodity = "AdmittedCommodityElectricalSupplies"
	AdmittedCommodityFeedFertilizer                           AdmittedCommodity = "AdmittedCommodityFeedFertilizer"
	AdmittedCommodityFlour                                    AdmittedCommodity = "AdmittedCommodityFlour"
	AdmittedCommodityFoodFrozenNotSeafood                     AdmittedCommodity = "AdmittedCommodityFoodFrozenNotSeafood"
	AdmittedCommodityFreshSeafood                             AdmittedCommodity = "AdmittedCommodityFreshSeafood"
	AdmittedCommodityFrozenSeafood                            AdmittedCommodity = "AdmittedCommodityFrozenSeafood"
	AdmittedCommodityFruits                                   AdmittedCommodity = "AdmittedCommodityFruits"
	AdmittedCommodityFurnitureNew                             AdmittedCommodity = "AdmittedCommodityFurnitureNew"
	AdmittedCommodityGarbage                                  AdmittedCommodity = "AdmittedCommodityGarbage"
	AdmittedCommodityGlassProducts                            AdmittedCommodity = "AdmittedCommodityGlassProducts"
	AdmittedCommodityGrainHay                                 AdmittedCommodity = "AdmittedCommodityGrainHay"
	AdmittedCommodityHouseholdGoodsMover                      AdmittedCommodity = "AdmittedCommodityHouseholdGoodsMover"
	AdmittedCommodityIronAndOre                               AdmittedCommodity = "AdmittedCommodityIronAndOre"
	AdmittedCommodityLiquidsNonChemicalOrNonPetroleum         AdmittedCommodity = "AdmittedCommodityLiquidsNonChemicalOrNonPetroleum"
	AdmittedCommodityLivestock                                AdmittedCommodity = "AdmittedCommodityLivestock"
	AdmittedCommodityLogs                                     AdmittedCommodity = "AdmittedCommodityLogs"
	AdmittedCommodityLumber                                   AdmittedCommodity = "AdmittedCommodityLumber"
	AdmittedCommodityMachinery                                AdmittedCommodity = "AdmittedCommodityMachinery"
	AdmittedCommodityMarbleGraniteOtherStoneSlabs             AdmittedCommodity = "AdmittedCommodityMarbleGraniteOtherStoneSlabs"
	AdmittedCommodityMeatsDressedPoultry                      AdmittedCommodity = "AdmittedCommodityMeatsDressedPoultry"
	AdmittedCommodityMedicalAndScientificEquipment            AdmittedCommodity = "AdmittedCommodityMedicalAndScientificEquipment"
	AdmittedCommodityMetalProducts                            AdmittedCommodity = "AdmittedCommodityMetalProducts"
	AdmittedCommodityMobileModularHomes                       AdmittedCommodity = "AdmittedCommodityMobileModularHomes"
	AdmittedCommodityMulchTopSoilAndFill                      AdmittedCommodity = "AdmittedCommodityMulchTopSoilAndFill"
	AdmittedCommodityMusicalInstruments                       AdmittedCommodity = "AdmittedCommodityMusicalInstruments"
	AdmittedCommodityOfficeEquipment                          AdmittedCommodity = "AdmittedCommodityOfficeEquipment"
	AdmittedCommodityOilfieldEquipment                        AdmittedCommodity = "AdmittedCommodityOilfieldEquipment"
	AdmittedCommodityOilsEdible                               AdmittedCommodity = "AdmittedCommodityOilsEdible"
	AdmittedCommodityOtherAutosAircrafts                      AdmittedCommodity = "AdmittedCommodityOtherAutosAircrafts"
	AdmittedCommodityOtherBuildingSupplies                    AdmittedCommodity = "AdmittedCommodityOtherBuildingSupplies"
	AdmittedCommodityOtherChemicals                           AdmittedCommodity = "AdmittedCommodityOtherChemicals"
	AdmittedCommodityOtherConstructionMaterials               AdmittedCommodity = "AdmittedCommodityOtherConstructionMaterials"
	AdmittedCommodityOtherConsumerGoods                       AdmittedCommodity = "AdmittedCommodityOtherConsumerGoods"
	AdmittedCommodityOtherFarmingAgriculture                  AdmittedCommodity = "AdmittedCommodityOtherFarmingAgriculture"
	AdmittedCommodityOtherFoodAndBeverages                    AdmittedCommodity = "AdmittedCommodityOtherFoodAndBeverages"
	AdmittedCommodityOtherMachineryEquipment                  AdmittedCommodity = "AdmittedCommodityOtherMachineryEquipment"
	AdmittedCommodityOtherMetalsMineralsCoal                  AdmittedCommodity = "AdmittedCommodityOtherMetalsMineralsCoal"
	AdmittedCommodityOtherMiscellaneous                       AdmittedCommodity = "AdmittedCommodityOtherMiscellaneous"
	AdmittedCommodityOtherPaperPlasticGlass                   AdmittedCommodity = "AdmittedCommodityOtherPaperPlasticGlass"
	AdmittedCommodityPackingMaterialsAndSupplies              AdmittedCommodity = "AdmittedCommodityPackingMaterialsAndSupplies"
	AdmittedCommodityPaperAndPaperProducts                    AdmittedCommodity = "AdmittedCommodityPaperAndPaperProducts"
	AdmittedCommodityPharmaceuticalsOverTheCounter            AdmittedCommodity = "AdmittedCommodityPharmaceuticalsOverTheCounter"
	AdmittedCommodityPipe                                     AdmittedCommodity = "AdmittedCommodityPipe"
	AdmittedCommodityPlantsShrubsTreesNotTempControlled       AdmittedCommodity = "AdmittedCommodityPlantsShrubsTreesNotTempControlled"
	AdmittedCommodityPlantsShrubsTreesTempControlled          AdmittedCommodity = "AdmittedCommodityPlantsShrubsTreesTempControlled"
	AdmittedCommodityPlasticProducts                          AdmittedCommodity = "AdmittedCommodityPlasticProducts"
	AdmittedCommodityPlumbingSupplies                         AdmittedCommodity = "AdmittedCommodityPlumbingSupplies"
	AdmittedCommodityPrintedMaterial                          AdmittedCommodity = "AdmittedCommodityPrintedMaterial"
	AdmittedCommodityRecyclingMaterials                       AdmittedCommodity = "AdmittedCommodityRecyclingMaterials"
	AdmittedCommodityResins                                   AdmittedCommodity = "AdmittedCommodityResins"
	AdmittedCommodityRubberProductsNotTires                   AdmittedCommodity = "AdmittedCommodityRubberProductsNotTires"
	AdmittedCommoditySalt                                     AdmittedCommodity = "AdmittedCommoditySalt"
	AdmittedCommodityScrapMetal                               AdmittedCommodity = "AdmittedCommodityScrapMetal"
	AdmittedCommoditySeeds                                    AdmittedCommodity = "AdmittedCommoditySeeds"
	AdmittedCommoditySpasHotTubs                              AdmittedCommodity = "AdmittedCommoditySpasHotTubs"
	AdmittedCommoditySportingGoods                            AdmittedCommodity = "AdmittedCommoditySportingGoods"
	AdmittedCommoditySteel                                    AdmittedCommodity = "AdmittedCommoditySteel"
	AdmittedCommodityTeaCoffeeSpices                          AdmittedCommodity = "AdmittedCommodityTeaCoffeeSpices"
	AdmittedCommodityTextilesSkinsFurs                        AdmittedCommodity = "AdmittedCommodityTextilesSkinsFurs"
	AdmittedCommodityTires                                    AdmittedCommodity = "AdmittedCommodityTires"
	AdmittedCommodityTobaccoLeafRaw                           AdmittedCommodity = "AdmittedCommodityTobaccoLeafRaw"
	AdmittedCommodityTools                                    AdmittedCommodity = "AdmittedCommodityTools"
	AdmittedCommodityToys                                     AdmittedCommodity = "AdmittedCommodityToys"
	AdmittedCommodityVegetables                               AdmittedCommodity = "AdmittedCommodityVegetables"
	AdmittedCommodityWire                                     AdmittedCommodity = "AdmittedCommodityWire"
	AdmittedCommodityWoodProductsNotFurnitureAndCaskets       AdmittedCommodity = "AdmittedCommodityWoodProductsNotFurnitureAndCaskets"
	AdmittedCommodityZinc                                     AdmittedCommodity = "AdmittedCommodityZinc"
)

// Defines values for AdmittedVehicleClass.
const (
	VehicleClassAgricultural  AdmittedVehicleClass = "VehicleClassAgricultural"
	VehicleClassAutoHauler    AdmittedVehicleClass = "VehicleClassAutoHauler"
	VehicleClassBulkCommodity AdmittedVehicleClass = "VehicleClassBulkCommodity"
	VehicleClassDryFreight    AdmittedVehicleClass = "VehicleClassDryFreight"
	VehicleClassDump          AdmittedVehicleClass = "VehicleClassDump"
	VehicleClassFlatbed       AdmittedVehicleClass = "VehicleClassFlatbed"
	VehicleClassFrontLoader   AdmittedVehicleClass = "VehicleClassFrontLoader"
	VehicleClassGarbage       AdmittedVehicleClass = "VehicleClassGarbage"
	VehicleClassLivestock     AdmittedVehicleClass = "VehicleClassLivestock"
	VehicleClassLogging       AdmittedVehicleClass = "VehicleClassLogging"
	VehicleClassLowboy        AdmittedVehicleClass = "VehicleClassLowboy"
	VehicleClassOther         AdmittedVehicleClass = "VehicleClassOther"
	VehicleClassPickupTruck   AdmittedVehicleClass = "VehicleClassPickupTruck"
	VehicleClassPole          AdmittedVehicleClass = "VehicleClassPole"
	VehicleClassRagTop        AdmittedVehicleClass = "VehicleClassRagTop"
	VehicleClassReefer        AdmittedVehicleClass = "VehicleClassReefer"
	VehicleClassStake         AdmittedVehicleClass = "VehicleClassStake"
	VehicleClassStraight      AdmittedVehicleClass = "VehicleClassStraight"
	VehicleClassTank          AdmittedVehicleClass = "VehicleClassTank"
	VehicleClassTilt          AdmittedVehicleClass = "VehicleClassTilt"
	VehicleClassTruckTractor  AdmittedVehicleClass = "VehicleClassTruckTractor"
	VehicleClassUnidentified  AdmittedVehicleClass = "VehicleClassUnidentified"
	VehicleClassUtility       AdmittedVehicleClass = "VehicleClassUtility"
)

// Defines values for AppState.
const (
	AppStateApproved               AppState = "AppStateApproved"
	AppStateBindableQuoteGenerated AppState = "AppStateBindableQuoteGenerated"
	AppStateClosed                 AppState = "AppStateClosed"
	AppStateComplete               AppState = "AppStateComplete"
	AppStateDeclined               AppState = "AppStateDeclined"
	AppStateIncomplete             AppState = "AppStateIncomplete"
	AppStatePanic                  AppState = "AppStatePanic"
	AppStatePolicyCreated          AppState = "AppStatePolicyCreated"
	AppStateQuoteGenerated         AppState = "AppStateQuoteGenerated"
	AppStateQuoteGenerating        AppState = "AppStateQuoteGenerating"
	AppStateUnderReferralReview    AppState = "AppStateUnderReferralReview"
	AppStateUnderUWReview          AppState = "AppStateUnderUWReview"
)

// Defines values for ApplicationTab.
const (
	ApplicationTabAll           ApplicationTab = "ApplicationTabAll"
	ApplicationTabBound         ApplicationTab = "ApplicationTabBound"
	ApplicationTabClosed        ApplicationTab = "ApplicationTabClosed"
	ApplicationTabDeclined      ApplicationTab = "ApplicationTabDeclined"
	ApplicationTabInProgress    ApplicationTab = "ApplicationTabInProgress"
	ApplicationTabReadyToBind   ApplicationTab = "ApplicationTabReadyToBind"
	ApplicationTabReadyToQuote  ApplicationTab = "ApplicationTabReadyToQuote"
	ApplicationTabUnderUWReview ApplicationTab = "ApplicationTabUnderUWReview"
)

// Defines values for BusinessStructure.
const (
	BusinessStructureCorporation    BusinessStructure = "BusinessStructureCorporation"
	BusinessStructureLLC            BusinessStructure = "BusinessStructureLLC"
	BusinessStructurePartnership    BusinessStructure = "BusinessStructurePartnership"
	BusinessStructureSoleProprietor BusinessStructure = "BusinessStructureSoleProprietor"
)

// Defines values for Category.
const (
	CategoryAlcoholBeerWine        Category = "CategoryAlcoholBeerWine"
	CategoryAppliances             Category = "CategoryAppliances"
	CategoryAutoParts              Category = "CategoryAutoParts"
	CategoryBuildingMaterials      Category = "CategoryBuildingMaterials"
	CategoryBulkBaggedNuts         Category = "CategoryBulkBaggedNuts"
	CategoryChemicals              Category = "CategoryChemicals"
	CategoryComputers              Category = "CategoryComputers"
	CategoryCosmetics              Category = "CategoryCosmetics"
	CategoryDVDCD                  Category = "CategoryDVD_CD"
	CategoryDryFreight             Category = "CategoryDryFreight"
	CategoryElectronics            Category = "CategoryElectronics"
	CategoryFrozenFoods            Category = "CategoryFrozenFoods"
	CategoryFurnitureNew           Category = "CategoryFurnitureNew"
	CategoryFurs                   Category = "CategoryFurs"
	CategoryGarments               Category = "CategoryGarments"
	CategoryLumberFinished         Category = "CategoryLumberFinished"
	CategoryMachineryLightHeavy    Category = "CategoryMachineryLightHeavy"
	CategoryMeat                   Category = "CategoryMeat"
	CategoryMilkDairyProducts      Category = "CategoryMilkDairyProducts"
	CategoryNonFerrousMetals       Category = "CategoryNonFerrousMetals"
	CategoryPharmaceuticals        Category = "CategoryPharmaceuticals"
	CategoryPoultryDressed         Category = "CategoryPoultryDressed"
	CategoryProduceNonRefrigerated Category = "CategoryProduceNonRefrigerated"
	CategorySeafoodUnlessCanned    Category = "CategorySeafoodUnlessCanned"
	CategorySolarPanels            Category = "CategorySolarPanels"
	CategorySportingGoods          Category = "CategorySportingGoods"
	CategoryTires                  Category = "CategoryTires"
	CategoryTobacco                Category = "CategoryTobacco"
	CategoryTools                  Category = "CategoryTools"
	CategoryToys                   Category = "CategoryToys"
)

// Defines values for DuplicateApplicationTag.
const (
	FlexQuote DuplicateApplicationTag = "FlexQuote"
)

// Defines values for JobRunStatus.
const (
	JobRunStatusFailure JobRunStatus = "failure"
	JobRunStatusRunning JobRunStatus = "running"
	JobRunStatusSuccess JobRunStatus = "success"
	JobRunStatusUnknown JobRunStatus = "unknown"
)

// Defines values for MaxRadiusOfOperation.
const (
	MaxRadiusOfOperation100     MaxRadiusOfOperation = "MaxRadiusOfOperation100"
	MaxRadiusOfOperation200     MaxRadiusOfOperation = "MaxRadiusOfOperation200"
	MaxRadiusOfOperation300     MaxRadiusOfOperation = "MaxRadiusOfOperation300"
	MaxRadiusOfOperation50      MaxRadiusOfOperation = "MaxRadiusOfOperation50"
	MaxRadiusOfOperation500     MaxRadiusOfOperation = "MaxRadiusOfOperation500"
	MaxRadiusOfOperation999plus MaxRadiusOfOperation = "MaxRadiusOfOperation999plus"
)

// Defines values for NRBVehicleType.
const (
	NRBVehicleTypePickupTruck NRBVehicleType = "Pickup Truck"
	NRBVehicleTypeSemiTrailer NRBVehicleType = "Semi_Trailer"
	NRBVehicleTypeTractor     NRBVehicleType = "Tractor"
	NRBVehicleTypeTrailer     NRBVehicleType = "Trailer"
	NRBVehicleTypeTruck       NRBVehicleType = "Truck"
	NRBVehicleTypeUnknown     NRBVehicleType = "Unknown"
)

// Defines values for PageState.
const (
	PageStateClassesAndCommodities PageState = "PageStateClassesAndCommodities"
	PageStateDecline               PageState = "PageStateDecline"
	PageStateDrivers               PageState = "PageStateDrivers"
	PageStateEquipments            PageState = "PageStateEquipments"
	PageStateOperations            PageState = "PageStateOperations"
	PageStateRecalculateQuote      PageState = "PageStateRecalculateQuote"
	PageStateReferral              PageState = "PageStateReferral"
	PageStateReview                PageState = "PageStateReview"
	PageStateSubmitted             PageState = "PageStateSubmitted"
	PageStateUnsubmitted           PageState = "PageStateUnsubmitted"
)

// Defines values for PaymentPlan.
const (
	PaymentPlanInstallmentWithEFT PaymentPlan = "PaymentPlanInstallmentWithEFT"
	PaymentPlanPaidInFull         PaymentPlan = "PaymentPlanPaidInFull"
)

// Defines values for PreTelematicsQuoteState.
const (
	NotReadyBecauseDataPipelineIssues             PreTelematicsQuoteState = "NotReadyBecauseDataPipelineIssues"
	NotReadyBecauseDataPipelinePending            PreTelematicsQuoteState = "NotReadyBecauseDataPipelinePending"
	ReadyBecauseDataPipelineSuccess               PreTelematicsQuoteState = "ReadyBecauseDataPipelineSuccess"
	ReadyBecauseTimeElapsedAndDataPipelinePending PreTelematicsQuoteState = "ReadyBecauseTimeElapsedAndDataPipelinePending"
	ReadyBecauseTimeElapsedAndDataPipelineSuccess PreTelematicsQuoteState = "ReadyBecauseTimeElapsedAndDataPipelineSuccess"
)

// Defines values for ProgramType.
const (
	ProgramTypeFleet               ProgramType = "ProgramTypeFleet"
	ProgramTypeInvalid             ProgramType = "ProgramTypeInvalid"
	ProgramTypeNonFleetAdmitted    ProgramType = "ProgramTypeNonFleetAdmitted"
	ProgramTypeNonFleetCanopiusNRB ProgramType = "ProgramTypeNonFleetCanopiusNRB"
)

// Defines values for SelectedProviderType.
const (
	NonPremier SelectedProviderType = "NonPremier"
	Premier    SelectedProviderType = "Premier"
	Unknown    SelectedProviderType = "Unknown"
)

// Defines values for VehicleType.
const (
	VehicleTypeNonOwnedSemiTrailer VehicleType = "VehicleTypeNonOwnedSemiTrailer"
	VehicleTypePickup              VehicleType = "VehicleTypePickup"
	VehicleTypeSemiTrailer         VehicleType = "VehicleTypeSemiTrailer"
	VehicleTypeSpareSemiTrailer    VehicleType = "VehicleTypeSpareSemiTrailer"
	VehicleTypeTractor             VehicleType = "VehicleTypeTractor"
	VehicleTypeTrailer             VehicleType = "VehicleTypeTrailer"
	VehicleTypeTruck               VehicleType = "VehicleTypeTruck"
	VehicleTypeUnknown             VehicleType = "VehicleTypeUnknown"
)

// Address defines model for Address.
type Address struct {
	City   string `json:"city"`
	State  string `json:"state"`
	Street string `json:"street"`
	Zip    string `json:"zip"`
}

// AdmittedApp defines model for AdmittedApp.
type AdmittedApp struct {
	DriversForm    *AdmittedAppDriversForm    `json:"driversForm,omitempty"`
	EquipmentsForm *AdmittedAppEquipmentsForm `json:"equipmentsForm,omitempty"`
	IndicationForm *AdmittedAppIndicationForm `json:"indicationForm,omitempty"`
	OperationsForm *AdmittedAppOperationsForm `json:"operationsForm,omitempty"`
}

// AdmittedAppBusinessOwner defines model for AdmittedAppBusinessOwner.
type AdmittedAppBusinessOwner struct {
	Address        Address `json:"address"`
	DateOfBirth    string  `json:"dateOfBirth"`
	DriverOnPolicy *bool   `json:"driverOnPolicy,omitempty"`
	Email          *string `json:"email,omitempty"`
	FirstName      string  `json:"firstName"`
	LastName       string  `json:"lastName"`
}

// AdmittedAppCoverageForm defines model for AdmittedAppCoverageForm.
type AdmittedAppCoverageForm struct {
	CoverageAutoLiability      *CoverageDetails `json:"CoverageAutoLiability,omitempty"`
	CoverageAutoPhysicalDamage *CoverageDetails `json:"CoverageAutoPhysicalDamage,omitempty"`
	CoverageGeneralLiability   *CoverageDetails `json:"CoverageGeneralLiability,omitempty"`
	CoverageMotorTruckCargo    *CoverageDetails `json:"CoverageMotorTruckCargo,omitempty"`
	IsAPDMTCDeductibleCombined *bool            `json:"isAPDMTCDeductibleCombined,omitempty"`
}

// AdmittedAppDriverDetails defines model for AdmittedAppDriverDetails.
type AdmittedAppDriverDetails struct {
	DateOfBirth               string `json:"dateOfBirth"`
	DateOfHire                string `json:"dateOfHire"`
	FirstName                 string `json:"firstName"`
	IsIncluded                bool   `json:"isIncluded"`
	IsOutOfState              bool   `json:"isOutOfState"`
	LastName                  string `json:"lastName"`
	LicenseNumber             string `json:"licenseNumber"`
	LicenseState              string `json:"licenseState"`
	ViolationInLastThreeYears bool   `json:"violationInLastThreeYears"`
	YearsOfExp                int    `json:"yearsOfExp"`
}

// AdmittedAppDriversForm defines model for AdmittedAppDriversForm.
type AdmittedAppDriversForm struct {
	Drivers *[]AdmittedAppDriverDetails `json:"drivers,omitempty"`
}

// AdmittedAppEquipmentsForm defines model for AdmittedAppEquipmentsForm.
type AdmittedAppEquipmentsForm struct {
	Vehicles *[]AdmittedAppVehicleDetails `json:"vehicles,omitempty"`
}

// AdmittedAppIndicationForm defines model for AdmittedAppIndicationForm.
type AdmittedAppIndicationForm struct {
	Coverages          *AdmittedAppCoverageForm       `json:"coverages,omitempty"`
	SelectedIndication *AdmittedAppSelectedIndication `json:"selectedIndication,omitempty"`
	TelematicsInfo     *externalRef0.TelematicsInfo   `json:"telematicsInfo,omitempty"`
}

// AdmittedAppOperationsForm defines model for AdmittedAppOperationsForm.
type AdmittedAppOperationsForm struct {
	AtFaultAccidents      *int                         `json:"atFaultAccidents,omitempty"`
	BusinessOwner         *AdmittedAppBusinessOwner    `json:"businessOwner,omitempty"`
	CommodityDistribution *[]AdmittedCommodityRecord   `json:"commodityDistribution,omitempty"`
	CompanyInfo           *CompanyInfo                 `json:"companyInfo,omitempty"`
	Coverages             *[]CoverageDetails           `json:"coverages,omitempty"`
	EffectiveDate         *openapi_types.Date          `json:"effectiveDate,omitempty"`
	HasHiredAuto          *bool                        `json:"hasHiredAuto,omitempty"`
	LossRunFiles          *[]externalRef1.FileMetadata `json:"lossRunFiles,omitempty"`
	MaxRadiusOfOperation  *MaxRadiusOfOperation        `json:"maxRadiusOfOperation,omitempty"`
	PrimaryCategory       *string                      `json:"primaryCategory,omitempty"`
	PrimaryCommodity      *AdmittedCommodity           `json:"primaryCommodity,omitempty"`
	PrimaryCommodityLabel *string                      `json:"primaryCommodityLabel,omitempty"`
	ProducerID            *string                      `json:"producerID,omitempty"`
	ProducerName          *string                      `json:"producerName,omitempty"`
	TerminalLocation      *AdmittedAppTerminalLocation `json:"terminalLocation,omitempty"`
}

// AdmittedAppPackages defines model for AdmittedAppPackages.
type AdmittedAppPackages struct {
	AdditionalCovs           *[]CoverageDetails                `json:"additionalCovs,omitempty"`
	AdditionalCovsNew        *[]CoverageDetails                `json:"additionalCovsNew,omitempty"`
	AncillaryCovsToHighlight *[]CoverageDetails                `json:"ancillaryCovsToHighlight,omitempty"`
	Id                       *string                           `json:"id,omitempty"`
	IsDisabled               *bool                             `json:"isDisabled,omitempty"`
	IsRecommended            *bool                             `json:"isRecommended,omitempty"`
	MaxSafetyDiscount        *int                              `json:"maxSafetyDiscount,omitempty"`
	PackageName              *externalRef0.IndicationOptionTag `json:"packageName,omitempty"`
	PrimaryCovs              *[]CoverageDetails                `json:"primaryCovs,omitempty"`
	StatutoryCovs            *[]CoverageDetails                `json:"statutoryCovs,omitempty"`
	Tiv                      *int                              `json:"tiv,omitempty"`
	TivPercentage            *float32                          `json:"tivPercentage,omitempty"`
	TivPercentageNew         *float32                          `json:"tivPercentageNew,omitempty"`
	TotalPremium             *int                              `json:"totalPremium,omitempty"`
}

// AdmittedAppSelectedIndication defines model for AdmittedAppSelectedIndication.
type AdmittedAppSelectedIndication struct {
	Id           *string                           `json:"id,omitempty"`
	PackageName  *externalRef0.IndicationOptionTag `json:"packageName,omitempty"`
	TotalPremium *int                              `json:"totalPremium,omitempty"`
}

// AdmittedAppTerminalLocation defines model for AdmittedAppTerminalLocation.
type AdmittedAppTerminalLocation struct {
	Address               Address `json:"address"`
	SameAsPhysicalAddress bool    `json:"sameAsPhysicalAddress"`
}

// AdmittedAppUpdateForm defines model for AdmittedAppUpdateForm.
type AdmittedAppUpdateForm struct {
	DriversForm    *AdmittedAppDriversForm    `json:"driversForm,omitempty"`
	EquipmentsForm *AdmittedAppEquipmentsForm `json:"equipmentsForm,omitempty"`
	IndicationForm *AdmittedAppIndicationForm `json:"indicationForm,omitempty"`
	OperationsForm *AdmittedAppOperationsForm `json:"operationsForm,omitempty"`
}

// AdmittedAppVehicleDetails defines model for AdmittedAppVehicleDetails.
type AdmittedAppVehicleDetails struct {
	LossPayee    *externalRef1.InsuredData `json:"lossPayee,omitempty"`
	Make         string                    `json:"make"`
	Model        string                    `json:"model"`
	StatedValue  *int                      `json:"statedValue,omitempty"`
	VehicleClass AdmittedVehicleClass      `json:"vehicleClass"`
	VehicleType  VehicleType               `json:"vehicleType"`
	Vin          string                    `json:"vin"`
	WeightClass  *string                   `json:"weightClass,omitempty"`
	Year         int                       `json:"year"`
}

// AdmittedCategoryType defines model for AdmittedCategoryType.
type AdmittedCategoryType string

// AdmittedCommodity defines model for AdmittedCommodity.
type AdmittedCommodity string

// AdmittedCommodityConstant defines model for AdmittedCommodityConstant.
type AdmittedCommodityConstant struct {
	Category string            `json:"category"`
	Label    string            `json:"label"`
	Value    AdmittedCommodity `json:"value"`
}

// AdmittedCommodityRecord defines model for AdmittedCommodityRecord.
type AdmittedCommodityRecord struct {
	Category      AdmittedCommodity `json:"category"`
	CategoryLabel string            `json:"categoryLabel"`
	Name          string            `json:"name"`
	Percentage    int               `json:"percentage"`
}

// AdmittedCommodityWithLabel defines model for AdmittedCommodityWithLabel.
type AdmittedCommodityWithLabel struct {
	Label string            `json:"label"`
	Value AdmittedCommodity `json:"value"`
}

// AdmittedCommodityWithLabelArray defines model for AdmittedCommodityWithLabelArray.
type AdmittedCommodityWithLabelArray = []AdmittedCommodityWithLabel

// AdmittedConstants defines model for AdmittedConstants.
type AdmittedConstants struct {
	Categories            *[]string                                     `json:"categories,omitempty"`
	Commodities           []AdmittedCommodityConstant                   `json:"commodities"`
	CommoditiesByCategory map[string]AdmittedCommodityWithLabelArray    `json:"commoditiesByCategory"`
	PaymentPlans          []PaymentPlanWithLabel                        `json:"paymentPlans"`
	VehicleClassByType    map[string]AdmittedVehicleClassWithLabelArray `json:"vehicleClassByType"`
	VehicleTypes          []AdmittedVehicleTypeWithLabel                `json:"vehicleTypes"`
}

// AdmittedVehicleClass defines model for AdmittedVehicleClass.
type AdmittedVehicleClass string

// AdmittedVehicleClassWithLabel defines model for AdmittedVehicleClassWithLabel.
type AdmittedVehicleClassWithLabel struct {
	Label string               `json:"label"`
	Value AdmittedVehicleClass `json:"value"`
}

// AdmittedVehicleClassWithLabelArray defines model for AdmittedVehicleClassWithLabelArray.
type AdmittedVehicleClassWithLabelArray = []AdmittedVehicleClassWithLabel

// AdmittedVehicleTypeWithLabel defines model for AdmittedVehicleTypeWithLabel.
type AdmittedVehicleTypeWithLabel struct {
	Label string      `json:"label"`
	Value VehicleType `json:"value"`
}

// AppState defines model for AppState.
type AppState string

// ApplicationCreationForm defines model for ApplicationCreationForm.
type ApplicationCreationForm struct {
	CompanyName            string             `json:"companyName"`
	DotNumber              int                `json:"dotNumber"`
	EffectiveDate          openapi_types.Date `json:"effectiveDate"`
	HasUndesiredOperations bool               `json:"hasUndesiredOperations"`
	IsAdmitted             bool               `json:"isAdmitted"`
	NumberOfPowerUnits     int                `json:"numberOfPowerUnits"`
	ProducerID             string             `json:"producerID"`
	ProducerName           string             `json:"producerName"`
}

// ApplicationDetails defines model for ApplicationDetails.
type ApplicationDetails struct {
	Admitted                                  *AdmittedApp             `json:"admitted,omitempty"`
	AppStatus                                 *string                  `json:"appStatus,omitempty"`
	ApplicationID                             *string                  `json:"applicationID,omitempty"`
	DeclineReason                             *string                  `json:"declineReason,omitempty"`
	FmcsaDataPresent                          *bool                    `json:"fmcsaDataPresent,omitempty"`
	IsEligibleForPreTelematicsQuoteExperiment *bool                    `json:"isEligibleForPreTelematicsQuoteExperiment,omitempty"`
	IsExpressLaneApplication                  *bool                    `json:"isExpressLaneApplication,omitempty"`
	IsOnNewCreditScore                        *bool                    `json:"isOnNewCreditScore,omitempty"`
	Nrb                                       *NRBApp                  `json:"nrb,omitempty"`
	PageState                                 *string                  `json:"pageState,omitempty"`
	PreTelematicsQuoteState                   *PreTelematicsQuoteState `json:"preTelematicsQuoteState,omitempty"`
	ProgramType                               *ProgramType             `json:"programType,omitempty"`
	RenewalMetadata                           *RenewalMetadata         `json:"renewalMetadata,omitempty"`
	ShortID                                   *string                  `json:"shortID,omitempty"`
	UnderwriterEmail                          *openapi_types.Email     `json:"underwriterEmail,omitempty"`
	UnderwriterName                           *string                  `json:"underwriterName,omitempty"`
}

// ApplicationTab defines model for ApplicationTab.
type ApplicationTab string

// ApplicationUpdateForm defines model for ApplicationUpdateForm.
type ApplicationUpdateForm struct {
	Admitted  *AdmittedAppUpdateForm `json:"admitted,omitempty"`
	Nrb       *NRBAppUpdateForm      `json:"nrb,omitempty"`
	PageState *PageState             `json:"pageState,omitempty"`
}

// BusinessStructure defines model for BusinessStructure.
type BusinessStructure string

// Category defines model for Category.
type Category string

// CategoryWithLabel defines model for CategoryWithLabel.
type CategoryWithLabel struct {
	Label string   `json:"label"`
	Name  Category `json:"name"`
}

// ClassInfo defines model for ClassInfo.
type ClassInfo struct {
	OwnsRestrictedClasses *bool                        `json:"ownsRestrictedClasses,omitempty"`
	PrimaryOperatingClass *externalRef1.OperatingClass `json:"primaryOperatingClass,omitempty"`
}

// ClassWithLabel defines model for ClassWithLabel.
type ClassWithLabel struct {
	Label string                      `json:"label"`
	Name  externalRef1.OperatingClass `json:"name"`
}

// ClassesAndCommodities defines model for ClassesAndCommodities.
type ClassesAndCommodities struct {
	Classes     []ClassWithLabel    `json:"classes"`
	Commodities []CategoryWithLabel `json:"commodities"`
}

// ClassesAndCommoditiesForm defines model for ClassesAndCommoditiesForm.
type ClassesAndCommoditiesForm struct {
	ClassInfo     *ClassInfo     `json:"classInfo,omitempty"`
	CommodityInfo *CommodityInfo `json:"commodityInfo,omitempty"`
}

// CommodityInfo defines model for CommodityInfo.
type CommodityInfo struct {
	CommodityDistribution      *[]CommodityRecord `json:"commodityDistribution,omitempty"`
	HaulsRestrictedCommodities *bool              `json:"haulsRestrictedCommodities,omitempty"`
}

// CommodityRecord defines model for CommodityRecord.
type CommodityRecord struct {
	Category      Category `json:"category"`
	CategoryLabel string   `json:"categoryLabel"`
	Name          string   `json:"name"`
	Percentage    int      `json:"percentage"`
}

// CompanyInfo defines model for CompanyInfo.
type CompanyInfo struct {
	Address                 *Address              `json:"address,omitempty"`
	AnnualCostOfHire        *int                  `json:"annualCostOfHire,omitempty"`
	BusinessStructure       *BusinessStructure    `json:"businessStructure,omitempty"`
	DotNumber               *int                  `json:"dotNumber,omitempty"`
	MailingAddress          *Address              `json:"mailingAddress,omitempty"`
	Name                    *string               `json:"name,omitempty"`
	NumberOfPowerUnits      *int                  `json:"numberOfPowerUnits,omitempty"`
	SelectedPremierProvider *string               `json:"selectedPremierProvider,omitempty"`
	SelectedProviderType    *SelectedProviderType `json:"selectedProviderType,omitempty"`
	UsState                 *string               `json:"usState,omitempty"`
}

// CoverageDetails defines model for CoverageDetails.
type CoverageDetails struct {
	CoverageType   externalRef1.CoverageType `json:"coverageType"`
	Deductible     *int                      `json:"deductible,omitempty"`
	IsRequired     bool                      `json:"isRequired"`
	Label          string                    `json:"label"`
	Limit          *int                      `json:"limit,omitempty"`
	Premium        *int                      `json:"premium,omitempty"`
	PremiumNew     *int                      `json:"premiumNew,omitempty"`
	PremiumPerUnit *int                      `json:"premiumPerUnit,omitempty"`
}

// CoverageInfo defines model for CoverageInfo.
type CoverageInfo struct {
	AncillaryCovs *[]CoverageDetails `json:"ancillaryCovs,omitempty"`
	PrimaryCovs   *[]CoverageDetails `json:"primaryCovs,omitempty"`
}

// CoverageOption defines model for CoverageOption.
type CoverageOption struct {
	Coverage          externalRef1.CoverageType `json:"coverage"`
	Deductible        *int                      `json:"deductible,omitempty"`
	DeductibleOptions *[]int                    `json:"deductibleOptions,omitempty"`
	DefaultDeductible *int                      `json:"defaultDeductible,omitempty"`
	DefaultLimit      *int                      `json:"defaultLimit,omitempty"`
	IsRequired        bool                      `json:"isRequired"`
	Limit             *int                      `json:"limit,omitempty"`
	LimitOptions      *[]int                    `json:"limitOptions,omitempty"`
}

// CoverageOptionMetadata defines model for CoverageOptionMetadata.
type CoverageOptionMetadata struct {
	HasReefer          bool                        `json:"hasReefer"`
	MandatoryCoverages []externalRef1.CoverageType `json:"mandatoryCoverages"`
	TargetCommodities  *[]string                   `json:"targetCommodities,omitempty"`
}

// CoveragesForm defines model for CoveragesForm.
type CoveragesForm struct {
	AncillaryCovs *map[string]CoverageDetails `json:"ancillaryCovs,omitempty"`
	PrimaryCovs   *map[string]CoverageDetails `json:"primaryCovs,omitempty"`
}

// DecodeVinResponse defines model for DecodeVinResponse.
type DecodeVinResponse struct {
	HasDecodingError bool   `json:"hasDecodingError"`
	Vin              string `json:"vin"`
}

// DriverDetails defines model for DriverDetails.
type DriverDetails struct {
	CdlYearsOfExperience *int               `json:"cdlYearsOfExperience,omitempty"`
	DateOfBirth          openapi_types.Date `json:"dateOfBirth"`
	FirstName            string             `json:"firstName"`
	IsIncluded           bool               `json:"isIncluded"`
	LastName             string             `json:"lastName"`
	LicenseIssueYear     int                `json:"licenseIssueYear"`
	LicenseNumber        string             `json:"licenseNumber"`
	LicenseState         string             `json:"licenseState"`
	OwnerOperator        bool               `json:"ownerOperator"`
}

// DriverInfo defines model for DriverInfo.
type DriverInfo struct {
	Drivers *[]DriverDetails `json:"drivers,omitempty"`
}

// DuplicateApplicationTag defines model for DuplicateApplicationTag.
type DuplicateApplicationTag string

// DuplicateApplications defines model for DuplicateApplications.
type DuplicateApplications struct {
	ShortId string                    `json:"shortId"`
	Tags    []DuplicateApplicationTag `json:"tags"`
}

// EquipmentInfo defines model for EquipmentInfo.
type EquipmentInfo struct {
	Vehicles *[]VehicleDetails `json:"vehicles,omitempty"`
}

// FinancialInfo defines model for FinancialInfo.
type FinancialInfo struct {
	RecentBankruptcies *bool `json:"recentBankruptcies,omitempty"`
}

// GetAdmittedAppIndicationResponse defines model for GetAdmittedAppIndicationResponse.
type GetAdmittedAppIndicationResponse struct {
	AppStatus   *string                `json:"appStatus,omitempty"`
	IsGLRemoved bool                   `json:"isGLRemoved"`
	Options     *[]CoverageOption      `json:"options,omitempty"`
	Packages    *[]AdmittedAppPackages `json:"packages,omitempty"`
	Status      JobRunStatus           `json:"status"`
}

// GetApplicationListResponse defines model for GetApplicationListResponse.
type GetApplicationListResponse struct {
	Data []ApplicationDetails `json:"data"`
}

// GetApplicationsResponse defines model for GetApplicationsResponse.
type GetApplicationsResponse struct {
	Cursor     *string              `json:"cursor,omitempty"`
	Data       []ApplicationDetails `json:"data"`
	TotalCount int                  `json:"totalCount"`
}

// GetBindableQuoteResponse defines model for GetBindableQuoteResponse.
type GetBindableQuoteResponse struct {
	AppDetails         ApplicationDetails        `json:"appDetails"`
	ExcludedDrivers    *[]string                 `json:"excludedDrivers,omitempty"`
	FormDetails        []externalRef2.FormRecord `json:"formDetails"`
	LastUpdatedAt      *string                   `json:"lastUpdatedAt,omitempty"`
	Quote              QuoteOption               `json:"quote"`
	QuotePDF           string                    `json:"quotePDF"`
	SignaturePacket    string                    `json:"signaturePacket"`
	SignaturePacketZip string                    `json:"signaturePacketZip"`
}

// GetQuoteResponse defines model for GetQuoteResponse.
type GetQuoteResponse struct {
	AppStatus *string                 `json:"appStatus,omitempty"`
	Metadata  *CoverageOptionMetadata `json:"metadata,omitempty"`
	Options   *[]CoverageOption       `json:"options,omitempty"`
	PageState *PageState              `json:"pageState,omitempty"`
	Quote     *QuoteOption            `json:"quote,omitempty"`
	Status    JobRunStatus            `json:"status"`
}

// GetVehicleInfoResponse defines model for GetVehicleInfoResponse.
type GetVehicleInfoResponse struct {
	Data []VehicleInfo `json:"data"`
}

// JobRunStatus defines model for JobRunStatus.
type JobRunStatus string

// LossInfo defines model for LossInfo.
type LossInfo struct {
	HasLosses        *bool                        `json:"hasLosses,omitempty"`
	HasLossesOver20k *bool                        `json:"hasLossesOver20k,omitempty"`
	LossRunFiles     *[]externalRef1.FileMetadata `json:"lossRunFiles,omitempty"`
}

// MaxRadiusOfOperation defines model for MaxRadiusOfOperation.
type MaxRadiusOfOperation string

// NRBApp defines model for NRBApp.
type NRBApp struct {
	ClassesAndCommoditiesForm *ClassesAndCommoditiesForm `json:"classesAndCommoditiesForm,omitempty"`
	CoveragesForm             *CoveragesForm             `json:"coveragesForm,omitempty"`
	DriversForm               *DriverInfo                `json:"driversForm,omitempty"`
	EquipmentsForm            *EquipmentInfo             `json:"equipmentsForm,omitempty"`
	OperationsForm            *OperationsForm            `json:"operationsForm,omitempty"`
}

// NRBAppUpdateForm defines model for NRBAppUpdateForm.
type NRBAppUpdateForm struct {
	ClassesAndCommoditiesForm *ClassesAndCommoditiesForm `json:"classesAndCommoditiesForm,omitempty"`
	CoveragesForm             *CoveragesForm             `json:"coveragesForm,omitempty"`
	DriversForm               *DriverInfo                `json:"driversForm,omitempty"`
	EquipmentsForm            *EquipmentInfo             `json:"equipmentsForm,omitempty"`
	OperationsForm            *OperationsForm            `json:"operationsForm,omitempty"`
}

// NRBVehicleType defines model for NRBVehicleType.
type NRBVehicleType string

// OperationsForm defines model for OperationsForm.
type OperationsForm struct {
	CompanyInfo             *CompanyInfo   `json:"companyInfo,omitempty"`
	CoverageInfo            *CoverageInfo  `json:"coverageInfo,omitempty"`
	EffectiveDate           *string        `json:"effectiveDate,omitempty"`
	FinancialInfo           *FinancialInfo `json:"financialInfo,omitempty"`
	LossInfo                *LossInfo      `json:"lossInfo,omitempty"`
	ProducerId              *string        `json:"producerId,omitempty"`
	ProducerName            *string        `json:"producerName,omitempty"`
	SeriousDriverViolations *bool          `json:"seriousDriverViolations,omitempty"`
}

// PageState defines model for PageState.
type PageState string

// PaymentPlan defines model for PaymentPlan.
type PaymentPlan string

// PaymentPlanWithLabel defines model for PaymentPlanWithLabel.
type PaymentPlanWithLabel struct {
	Label string      `json:"label"`
	Value PaymentPlan `json:"value"`
}

// PreTelematicsQuoteState defines model for PreTelematicsQuoteState.
type PreTelematicsQuoteState string

// ProgramType defines model for ProgramType.
type ProgramType string

// QuoteOption defines model for QuoteOption.
type QuoteOption struct {
	TIV                  int32             `json:"TIV"`
	TIVPercentage        float32           `json:"TIVPercentage"`
	Coverages            []CoverageDetails `json:"coverages"`
	FlatCharges          int32             `json:"flatCharges"`
	Id                   string            `json:"id"`
	SafetyDiscountAmount int32             `json:"safetyDiscountAmount"`
	StateSurcharge       int32             `json:"stateSurcharge"`
	SubTotalPremium      int32             `json:"subTotalPremium"`
	TotalPowerUnits      int32             `json:"totalPowerUnits"`
	TotalPremium         int32             `json:"totalPremium"`
}

// RenewalMetadata defines model for RenewalMetadata.
type RenewalMetadata struct {
	OriginalApplicationId string `json:"originalApplicationId"`
}

// SelectedProviderType defines model for SelectedProviderType.
type SelectedProviderType string

// VehicleDetails defines model for VehicleDetails.
type VehicleDetails struct {
	Class              *string `json:"class,omitempty"`
	Make               string  `json:"make"`
	Model              string  `json:"model"`
	OwnershipType      string  `json:"ownershipType"`
	ParkingLocationZIP string  `json:"parkingLocationZIP"`
	StatedValue        int     `json:"statedValue"`
	VehicleType        string  `json:"vehicleType"`
	Vin                string  `json:"vin"`
	WeightClass        *string `json:"weightClass,omitempty"`
	Year               int     `json:"year"`
}

// VehicleInfo defines model for VehicleInfo.
type VehicleInfo struct {
	Make         string         `json:"make"`
	Model        string         `json:"model"`
	ModelYear    string         `json:"modelYear"`
	Type         NRBVehicleType `json:"type"`
	TypeAdmitted VehicleType    `json:"typeAdmitted"`
	WeightClass  *string        `json:"weightClass,omitempty"`
}

// VehicleType defines model for VehicleType.
type VehicleType string
