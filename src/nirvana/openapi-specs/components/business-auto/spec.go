// Package business_auto provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/deepmap/oapi-codegen/v2 version v2.0.0 DO NOT EDIT.
package business_auto

import (
	"time"

	openapi_types "github.com/oapi-codegen/runtime/types"
	externalRef0 "nirvanatech.com/nirvana/openapi-specs/components/application"
	externalRef1 "nirvanatech.com/nirvana/openapi-specs/components/common"
	externalRef2 "nirvanatech.com/nirvana/openapi-specs/components/nirvana"
)

// Defines values for AppState.
const (
	Approved               AppState = "Approved"
	BindableQuoteGenerated AppState = "BindableQuoteGenerated"
	Closed                 AppState = "Closed"
	Created                AppState = "Created"
	Declined               AppState = "Declined"
	Panic                  AppState = "Panic"
	PolicyCreated          AppState = "PolicyCreated"
	QuoteGenerated         AppState = "QuoteGenerated"
	QuoteGenerating        AppState = "QuoteGenerating"
	UnderReview            AppState = "UnderReview"
)

// Defines values for AutoLiabilityCSL.
const (
	AutoLiabilityCSLInvalid AutoLiabilityCSL = "Invalid"
	AutoLiabilityCSLN1M     AutoLiabilityCSL = "1M"
	AutoLiabilityCSLN300K   AutoLiabilityCSL = "300K"
	AutoLiabilityCSLN500K   AutoLiabilityCSL = "500K"
	AutoLiabilityCSLN750K   AutoLiabilityCSL = "750K"
)

// Defines values for BusinessUse.
const (
	BusinessUseCommercial BusinessUse = "Commercial"
	BusinessUseInvalid    BusinessUse = "Invalid"
	BusinessUseRetail     BusinessUse = "Retail"
	BusinessUseService    BusinessUse = "Service"
)

// Defines values for Deductible.
const (
	DeductibleInvalid Deductible = "Invalid"
	DeductibleN0      Deductible = "0"
)

// Defines values for PricingStatus.
const (
	Completed  PricingStatus = "Completed"
	Failed     PricingStatus = "Failed"
	NotStarted PricingStatus = "NotStarted"
	Pending    PricingStatus = "Pending"
)

// Defines values for PrimaryIndustryClassification.
const (
	PrimaryIndustryClassificationContractorTrucks         PrimaryIndustryClassification = "ContractorTrucks"
	PrimaryIndustryClassificationInvalid                  PrimaryIndustryClassification = "Invalid"
	PrimaryIndustryClassificationWholesalersManufacturers PrimaryIndustryClassification = "WholesalersManufacturers"
)

// Defines values for QualityRatingGrade.
const (
	QualityRatingGradeA       QualityRatingGrade = "A"
	QualityRatingGradeB       QualityRatingGrade = "B"
	QualityRatingGradeC       QualityRatingGrade = "C"
	QualityRatingGradeD       QualityRatingGrade = "D"
	QualityRatingGradeE       QualityRatingGrade = "E"
	QualityRatingGradeF       QualityRatingGrade = "F"
	QualityRatingGradeInvalid QualityRatingGrade = "Invalid"
)

// Defines values for RadiusClassification.
const (
	RadiusClassificationGreaterThan301 RadiusClassification = "GreaterThan301"
	RadiusClassificationInvalid        RadiusClassification = "Invalid"
	RadiusClassificationN0To100        RadiusClassification = "0To100"
	RadiusClassificationN101To300      RadiusClassification = "101To300"
)

// Defines values for SecondaryIndustryClassification.
const (
	SecondaryIndustryClassificationCarCarriersNotForHire          SecondaryIndustryClassification = "CarCarriersNotForHire"
	SecondaryIndustryClassificationCatererVehicles                SecondaryIndustryClassification = "CatererVehicles"
	SecondaryIndustryClassificationCementMixers                   SecondaryIndustryClassification = "CementMixers"
	SecondaryIndustryClassificationContractorTrucks               SecondaryIndustryClassification = "ContractorTrucks"
	SecondaryIndustryClassificationCourierServiceVehicles         SecondaryIndustryClassification = "CourierServiceVehicles"
	SecondaryIndustryClassificationDumpingOperationsForHire       SecondaryIndustryClassification = "DumpingOperationsForHire"
	SecondaryIndustryClassificationDumpingOperationsNotForHire    SecondaryIndustryClassification = "DumpingOperationsNotForHire"
	SecondaryIndustryClassificationFarmerTrucks                   SecondaryIndustryClassification = "FarmerTrucks"
	SecondaryIndustryClassificationFoodDeliveryTrucks             SecondaryIndustryClassification = "FoodDeliveryTrucks"
	SecondaryIndustryClassificationGasOilHaulersNotForHire        SecondaryIndustryClassification = "GasOilHaulersNotForHire"
	SecondaryIndustryClassificationHouseMovers                    SecondaryIndustryClassification = "HouseMovers"
	SecondaryIndustryClassificationInvalid                        SecondaryIndustryClassification = "Invalid"
	SecondaryIndustryClassificationLawnTreeServiceTrucks          SecondaryIndustryClassification = "LawnTreeServiceTrucks"
	SecondaryIndustryClassificationLoggingTrucksForHire           SecondaryIndustryClassification = "LoggingTrucksForHire"
	SecondaryIndustryClassificationLoggingTrucksNotForHire        SecondaryIndustryClassification = "LoggingTrucksNotForHire"
	SecondaryIndustryClassificationMobileConcessionTrucks         SecondaryIndustryClassification = "MobileConcessionTrucks"
	SecondaryIndustryClassificationMovingOperations               SecondaryIndustryClassification = "MovingOperations"
	SecondaryIndustryClassificationNotOtherwiseClassifiedTruck    SecondaryIndustryClassification = "NotOtherwiseClassifiedTruck"
	SecondaryIndustryClassificationSalvageHaulers                 SecondaryIndustryClassification = "SalvageHaulers"
	SecondaryIndustryClassificationServiceUse                     SecondaryIndustryClassification = "ServiceUse"
	SecondaryIndustryClassificationSpecializedDeliveryVehicles    SecondaryIndustryClassification = "SpecializedDeliveryVehicles"
	SecondaryIndustryClassificationTowTrucksFullTime              SecondaryIndustryClassification = "TowTrucksFullTime"
	SecondaryIndustryClassificationTowTrucksIncidentalUse         SecondaryIndustryClassification = "TowTrucksIncidentalUse"
	SecondaryIndustryClassificationWasteDisposalTrucks            SecondaryIndustryClassification = "WasteDisposalTrucks"
	SecondaryIndustryClassificationWasteOilLiquidWasteTransporter SecondaryIndustryClassification = "WasteOilLiquidWasteTransporter"
	SecondaryIndustryClassificationWholesalersManufacturers       SecondaryIndustryClassification = "WholesalersManufacturers"
)

// Defines values for SpecialtyVehicleType.
const (
	SpecialtyVehicleTypeAllOtherContractorBodyTypes              SpecialtyVehicleType = "AllOtherContractorBodyTypes"
	SpecialtyVehicleTypeAllOtherFarmerTrucks                     SpecialtyVehicleType = "AllOtherFarmerTrucks"
	SpecialtyVehicleTypeAllOtherOilFieldBodyTypes                SpecialtyVehicleType = "AllOtherOilFieldBodyTypes"
	SpecialtyVehicleTypeAllOtherUnits                            SpecialtyVehicleType = "AllOtherUnits"
	SpecialtyVehicleTypeArtisanContractors                       SpecialtyVehicleType = "ArtisanContractors"
	SpecialtyVehicleTypeBoomTruck0To49Feet                       SpecialtyVehicleType = "BoomTruck0To49Feet"
	SpecialtyVehicleTypeBoomTruck101To120Feet                    SpecialtyVehicleType = "BoomTruck101To120Feet"
	SpecialtyVehicleTypeBoomTruck50To75Feet                      SpecialtyVehicleType = "BoomTruck50To75Feet"
	SpecialtyVehicleTypeBoomTruck76To100Feet                     SpecialtyVehicleType = "BoomTruck76To100Feet"
	SpecialtyVehicleTypeBoomTruckOver120Feet                     SpecialtyVehicleType = "BoomTruckOver120Feet"
	SpecialtyVehicleTypeCableTelecomUtilityContractors           SpecialtyVehicleType = "CableTelecomUtilityContractors"
	SpecialtyVehicleTypeCarCarriersNotForHire                    SpecialtyVehicleType = "CarCarriersNotForHire"
	SpecialtyVehicleTypeCarpentryContractors                     SpecialtyVehicleType = "CarpentryContractors"
	SpecialtyVehicleTypeCatererVehicles                          SpecialtyVehicleType = "CatererVehicles"
	SpecialtyVehicleTypeCourierServiceVehicles                   SpecialtyVehicleType = "CourierServiceVehicles"
	SpecialtyVehicleTypeCustomHarvesters                         SpecialtyVehicleType = "CustomHarvesters"
	SpecialtyVehicleTypeDriverTrainingTrucksTractors             SpecialtyVehicleType = "DriverTrainingTrucksTractors"
	SpecialtyVehicleTypeExcavatingDrillingAllOtherUnits          SpecialtyVehicleType = "ExcavatingDrillingAllOtherUnits"
	SpecialtyVehicleTypeExcavatingDrillingLightMediumHeavyTrucks SpecialtyVehicleType = "ExcavatingDrillingLightMediumHeavyTrucks"
	SpecialtyVehicleTypeExteriorBuildingConstructionContractors  SpecialtyVehicleType = "ExteriorBuildingConstructionContractors"
	SpecialtyVehicleTypeFireworkHaulersNotForHire                SpecialtyVehicleType = "FireworkHaulersNotForHire"
	SpecialtyVehicleTypeFoodDeliveryTrucks                       SpecialtyVehicleType = "FoodDeliveryTrucks"
	SpecialtyVehicleTypeGasOilAllOtherGasHaulers                 SpecialtyVehicleType = "GasOilAllOtherGasHaulers"
	SpecialtyVehicleTypeGasOilCrudeOilHaulers                    SpecialtyVehicleType = "GasOilCrudeOilHaulers"
	SpecialtyVehicleTypeGasOilFuelOilHaulers                     SpecialtyVehicleType = "GasOilFuelOilHaulers"
	SpecialtyVehicleTypeGasOilLPGPropaneBottled                  SpecialtyVehicleType = "GasOilLPGPropaneBottled"
	SpecialtyVehicleTypeGasOilLPGPropaneBulk                     SpecialtyVehicleType = "GasOilLPGPropaneBulk"
	SpecialtyVehicleTypeHarvesterGoatTrucks                      SpecialtyVehicleType = "HarvesterGoatTrucks"
	SpecialtyVehicleTypeHotOilLiquidAsphaltTrucksAllOther        SpecialtyVehicleType = "HotOilLiquidAsphaltTrucksAllOther"
	SpecialtyVehicleTypeHotOilLiquidAsphaltTrucksOilField        SpecialtyVehicleType = "HotOilLiquidAsphaltTrucksOilField"
	SpecialtyVehicleTypeHouseMovers                              SpecialtyVehicleType = "HouseMovers"
	SpecialtyVehicleTypeInvalid                                  SpecialtyVehicleType = "Invalid"
	SpecialtyVehicleTypeLawnTreeServiceTrucks                    SpecialtyVehicleType = "LawnTreeServiceTrucks"
	SpecialtyVehicleTypeMobileConcessionTruckInVehicleVending    SpecialtyVehicleType = "MobileConcessionTruckInVehicleVending"
	SpecialtyVehicleTypeMobileConcessionTruckNoFoodSales         SpecialtyVehicleType = "MobileConcessionTruckNoFoodSales"
	SpecialtyVehicleTypeMobileConcessionTruckOtherFoodVending    SpecialtyVehicleType = "MobileConcessionTruckOtherFoodVending"
	SpecialtyVehicleTypeMobileMechanicContractors                SpecialtyVehicleType = "MobileMechanicContractors"
	SpecialtyVehicleTypeMovingOperations                         SpecialtyVehicleType = "MovingOperations"
	SpecialtyVehicleTypeNotOtherwiseClassifiedTrucks             SpecialtyVehicleType = "NotOtherwiseClassifiedTrucks"
	SpecialtyVehicleTypePetroleumDistributionContractors         SpecialtyVehicleType = "PetroleumDistributionContractors"
	SpecialtyVehicleTypeRentalEquipmentProvider                  SpecialtyVehicleType = "RentalEquipmentProvider"
	SpecialtyVehicleTypeRoadConstructionContractors              SpecialtyVehicleType = "RoadConstructionContractors"
	SpecialtyVehicleTypeSalvageHaulers                           SpecialtyVehicleType = "SalvageHaulers"
	SpecialtyVehicleTypeSepticTankServiceTrucksAllOther          SpecialtyVehicleType = "SepticTankServiceTrucksAllOther"
	SpecialtyVehicleTypeSepticTankServiceTrucksIndividual        SpecialtyVehicleType = "SepticTankServiceTrucksIndividual"
	SpecialtyVehicleTypeServiceUseVehicles                       SpecialtyVehicleType = "ServiceUseVehicles"
	SpecialtyVehicleTypeSpecializedDeliveryVehicles              SpecialtyVehicleType = "SpecializedDeliveryVehicles"
	SpecialtyVehicleTypeStreetSweepers                           SpecialtyVehicleType = "StreetSweepers"
	SpecialtyVehicleTypeTrafficControlContractors                SpecialtyVehicleType = "TrafficControlContractors"
	SpecialtyVehicleTypeWasteDisposalTrucks                      SpecialtyVehicleType = "WasteDisposalTrucks"
	SpecialtyVehicleTypeWasteOilLiquidWasteTransporters          SpecialtyVehicleType = "WasteOilLiquidWasteTransporters"
	SpecialtyVehicleTypeWeldersMetalworkingContractors           SpecialtyVehicleType = "WeldersMetalworkingContractors"
	SpecialtyVehicleTypeWholesalersManufacturers                 SpecialtyVehicleType = "WholesalersManufacturers"
)

// Defines values for StateUsage.
const (
	StateUsageInterstate StateUsage = "Interstate"
	StateUsageIntrastate StateUsage = "Intrastate"
	StateUsageInvalid    StateUsage = "Invalid"
)

// Defines values for TrailerType.
const (
	TrailerTypeCustomHarvesterTrailers TrailerType = "CustomHarvesterTrailers"
	TrailerTypeFullTrailers            TrailerType = "FullTrailers"
	TrailerTypeInvalid                 TrailerType = "Invalid"
	TrailerTypeSemiTrailers            TrailerType = "SemiTrailers"
	TrailerTypeServiceUtilityTrailers  TrailerType = "ServiceUtilityTrailers"
)

// Defines values for VehicleType.
const (
	VehicleTypeInvalid VehicleType = "Invalid"
	VehicleTypeTractor VehicleType = "Tractor"
	VehicleTypeTrailer VehicleType = "Trailer"
	VehicleTypeTruck   VehicleType = "Truck"
)

// Defines values for VehicleUse.
const (
	VehicleUseDumpingOperations VehicleUse = "DumpingOperations"
	VehicleUseInvalid           VehicleUse = "Invalid"
	VehicleUseLoggingOperations VehicleUse = "LoggingOperations"
	VehicleUseOtherOperations   VehicleUse = "OtherOperations"
	VehicleUseTowingOperations  VehicleUse = "TowingOperations"
)

// Defines values for WeightClass.
const (
	ExtraHeavy WeightClass = "ExtraHeavy"
	Heavy      WeightClass = "Heavy"
	Invalid    WeightClass = "Invalid"
	Light      WeightClass = "Light"
	Medium     WeightClass = "Medium"
)

// AncillaryCoverage defines model for AncillaryCoverage.
type AncillaryCoverage struct {
	Coverage          externalRef1.CoverageType `json:"coverage"`
	DeductibleOptions *[]int64                  `json:"deductibleOptions,omitempty"`

	// IsEnabled Whether the ancillary coverage is enabled
	IsEnabled       bool                      `json:"isEnabled"`
	LimitOptions    *[]int64                  `json:"limitOptions,omitempty"`
	PrimaryCoverage externalRef1.CoverageType `json:"primaryCoverage"`

	// SelectedDeductible Default deductible value - use vehicleDeductibles for vehicle-specific deductibles
	SelectedDeductible *int64 `json:"selectedDeductible,omitempty"`
	SelectedLimit      *int64 `json:"selectedLimit,omitempty"`

	// VehicleDeductibles Vehicle-specific deductibles for this ancillary coverage. Only applicable when hasVehicleLevelDeductible is true.
	VehicleDeductibles *[]VehicleDeductible `json:"vehicleDeductibles,omitempty"`

	// VehicleLimits Vehicle-specific limits for this ancillary coverage. Only applicable when hasVehicleLevelLimit is true.
	VehicleLimits *[]VehicleLimit `json:"vehicleLimits,omitempty"`
}

// AppState defines model for AppState.
type AppState string

// AutoLiabilityCSL defines model for AutoLiabilityCSL.
type AutoLiabilityCSL string

// BusinessAutoApplicationListRecord defines model for BusinessAutoApplicationListRecord.
type BusinessAutoApplicationListRecord struct {
	CompanyName   string             `json:"companyName"`
	EffectiveDate openapi_types.Date `json:"effectiveDate"`
	Id            openapi_types.UUID `json:"id"`

	// IsInternal Whether this application belongs to a Nirvana internal agency
	IsInternal  *bool                     `json:"isInternal,omitempty"`
	ShortID     string                    `json:"shortID"`
	State       AppState                  `json:"state"`
	TspProvider *externalRef2.TSPProvider `json:"tspProvider,omitempty"`
}

// BusinessUse defines model for BusinessUse.
type BusinessUse string

// CompanyInfo defines model for CompanyInfo.
type CompanyInfo struct {
	DOTNumber                       *int                           `json:"DOTNumber,omitempty"`
	FEIN                            *string                        `json:"FEIN,omitempty"`
	Address                         *externalRef2.Address          `json:"address,omitempty"`
	AnnualCostOfHire                *float64                       `json:"annualCostOfHire,omitempty"`
	HasIndividualNamedInsured       *bool                          `json:"hasIndividualNamedInsured,omitempty"`
	HasWorkCompPolicy               *bool                          `json:"hasWorkCompPolicy,omitempty"`
	MaximumValueOfHiredAutos        *float64                       `json:"maximumValueOfHiredAutos,omitempty"`
	Name                            string                         `json:"name"`
	NoOfEmployees                   *int32                         `json:"noOfEmployees,omitempty"`
	NoOfPowerUnits                  *int32                         `json:"noOfPowerUnits,omitempty"`
	PerOfEmployeesOperatingOwnAutos *float32                       `json:"perOfEmployeesOperatingOwnAutos,omitempty"`
	PrimaryIndustryClassification   *PrimaryIndustryClassification `json:"primaryIndustryClassification,omitempty"`

	// SecondaryIndustryClassifications Secondary industry classifications if the company operates in multiple industries
	SecondaryIndustryClassifications *[]SecondaryIndustryClassification `json:"secondaryIndustryClassifications,omitempty"`

	// UsState Two character short code for the US state the driver is licensed in.
	UsState externalRef1.USState `json:"usState"`
}

// Coverage defines model for Coverage.
type Coverage struct {
	CoverageType externalRef1.CoverageType `json:"coverageType"`
	Deductible   *int64                    `json:"deductible,omitempty"`

	// IsEnabled Whether the coverage is enabled
	IsEnabled bool   `json:"isEnabled"`
	Limit     *int64 `json:"limit,omitempty"`
}

// CoverageModification defines model for CoverageModification.
type CoverageModification struct {
	Coverage externalRef1.CoverageType `json:"coverage"`

	// Modifier The modification factor to apply
	Modifier float64 `json:"modifier"`
}

// CoverageOption defines model for CoverageOption.
type CoverageOption struct {
	Coverage externalRef1.CoverageType `json:"coverage"`

	// HasVehicleLevelDeductible Whether this coverage supports vehicle-level deductibles
	HasVehicleLevelDeductible bool `json:"hasVehicleLevelDeductible"`

	// HasVehicleLevelLimit Whether this coverage supports vehicle-level limits
	HasVehicleLevelLimit bool      `json:"hasVehicleLevelLimit"`
	VinList              *[]string `json:"vinList,omitempty"`
}

// CoveragePremium defines model for CoveragePremium.
type CoveragePremium struct {
	CoverageType externalRef1.CoverageType `json:"coverageType"`

	// SubCoverageGroups Premium breakdown by sub-coverage groups (collapsible)
	SubCoverageGroups *[]SubCoverageGroup `json:"subCoverageGroups,omitempty"`

	// TotalPremium Total premium amount for this coverage in cents
	TotalPremium int64 `json:"totalPremium"`
}

// CoveragesInfo defines model for CoveragesInfo.
type CoveragesInfo struct {
	// Coverages Map of coverage type to coverage with limit and deductible
	Coverages map[string]Coverage `json:"coverages"`
}

// Deductible defines model for Deductible.
type Deductible string

// DriverFactor Driver factor - only applicable for 1-10 units
type DriverFactor struct {
	// Factor The factor value applied by underwriter
	Factor *float64 `json:"factor,omitempty"`
}

// ExperienceMod Experience rating modifications
type ExperienceMod struct {
	// Mods Array of coverage-specific experience modification values
	Mods *[]CoverageModification `json:"mods,omitempty"`
}

// FilingsInfo defines model for FilingsInfo.
type FilingsInfo struct {
	HasDOTFilings         bool `json:"hasDOTFilings"`
	HasFMCSAFilings       bool `json:"hasFMCSAFilings"`
	HasMultiStateFilings  bool `json:"hasMultiStateFilings"`
	HasSingleStateFilings bool `json:"hasSingleStateFilings"`
}

// GetAvailableProducersResponse defines model for GetAvailableProducersResponse.
type GetAvailableProducersResponse = []Producer

// GetBusinessAutoAppResponse defines model for GetBusinessAutoAppResponse.
type GetBusinessAutoAppResponse struct {
	CompanyInfo     CompanyInfo                  `json:"companyInfo"`
	CoveragesInfo   CoveragesInfo                `json:"coveragesInfo"`
	CreatedAt       time.Time                    `json:"createdAt"`
	EffectiveDate   openapi_types.Date           `json:"effectiveDate"`
	EffectiveDateTo openapi_types.Date           `json:"effectiveDateTo"`
	FilingsInfo     FilingsInfo                  `json:"filingsInfo"`
	Id              openapi_types.UUID           `json:"id"`
	ProducerInfo    Producer                     `json:"producerInfo"`
	ShortID         string                       `json:"shortID"`
	State           AppState                     `json:"state"`
	TelematicsInfo  *externalRef0.TelematicsInfo `json:"telematicsInfo,omitempty"`

	// TspConnHandleId The unique identifier for the TSP connection handle
	TspConnHandleId *openapi_types.UUID `json:"tspConnHandleId,omitempty"`

	// UnderwritingOverrides Underwriting factor overrides applied by underwriters
	UnderwritingOverrides *UnderwritingOverrides `json:"underwritingOverrides,omitempty"`
	VehiclesInfo          []VehicleInfo          `json:"vehiclesInfo"`
}

// GetCoverageOptionsRequestBody defines model for GetCoverageOptionsRequestBody.
type GetCoverageOptionsRequestBody struct {
	Coverages []Coverage `json:"coverages"`
}

// GetCoverageOptionsResponse List of coverage options with vehicle-level information
type GetCoverageOptionsResponse = []CoverageOption

// GetPricingResponse defines model for GetPricingResponse.
type GetPricingResponse struct {
	// ApplicationId The unique identifier of the business auto application
	ApplicationId openapi_types.UUID `json:"applicationId"`

	// Coverages Premium breakdown by coverage type (collapsible)
	Coverages []CoveragePremium `json:"coverages"`

	// CreatedAt When the pricing information was created
	CreatedAt time.Time `json:"createdAt"`

	// DataContextId The unique identifier of the data context
	DataContextId openapi_types.UUID `json:"dataContextId"`
	Surcharges    SurchargeBreakdown `json:"surcharges"`

	// TotalPremium Total premium amount across all coverages in cents
	TotalPremium int64 `json:"totalPremium"`
}

// GetPricingStatusResponse defines model for GetPricingStatusResponse.
type GetPricingStatusResponse struct {
	// ApplicationId The unique identifier of the business auto application
	ApplicationId openapi_types.UUID `json:"applicationId"`

	// CompletedAt When the pricing job was completed (null if still pending)
	CompletedAt *time.Time `json:"completedAt,omitempty"`

	// ErrorMessage Error message if the pricing job failed
	ErrorMessage *string `json:"errorMessage,omitempty"`

	// JobId The unique identifier of the pricing job
	JobId *string `json:"jobId,omitempty"`

	// StartedAt When the pricing job was started
	StartedAt time.Time `json:"startedAt"`

	// Status The status of the pricing operation
	Status PricingStatus `json:"status"`
}

// PatchBusinessAutoAppRequest defines model for PatchBusinessAutoAppRequest.
type PatchBusinessAutoAppRequest struct {
	CompanyInfo   *CompanyInfo        `json:"companyInfo,omitempty"`
	CoveragesInfo *CoveragesInfo      `json:"coveragesInfo,omitempty"`
	EffectiveDate *openapi_types.Date `json:"effectiveDate,omitempty"`
	FilingsInfo   *FilingsInfo        `json:"filingsInfo,omitempty"`
	ProducerId    *openapi_types.UUID `json:"producerId,omitempty"`
	VehiclesInfo  *[]VehicleInfo      `json:"vehiclesInfo,omitempty"`
}

// PatchPreBindInfoRequest Request to update pre-bind information (FEIN or DOT number) for a business auto application
type PatchPreBindInfoRequest struct {
	DotNumber *int `json:"dotNumber,omitempty"`

	// Fein Federal Employer Identification Number
	Fein string `json:"fein"`
}

// PatchStateTransitionRequest defines model for PatchStateTransitionRequest.
type PatchStateTransitionRequest struct {
	TransitionToState AppState `json:"transitionToState"`
}

// PatchStateTransitionResponse defines model for PatchStateTransitionResponse.
type PatchStateTransitionResponse struct {
	ApplicationId openapi_types.UUID `json:"applicationId"`
	NewState      AppState           `json:"newState"`
}

// PatchUnderwritingOverridesRequest Request to update underwriting overrides for a business auto application
type PatchUnderwritingOverridesRequest struct {
	// AlLossFreeCredit Whether loss free credit is applied for AL coverage
	AlLossFreeCredit   *bool                `json:"alLossFreeCredit,omitempty"`
	AncillaryCoverages *[]AncillaryCoverage `json:"ancillaryCoverages,omitempty"`

	// ApdLossFreeCredit Whether loss free credit is applied for APD coverage
	ApdLossFreeCredit *bool `json:"apdLossFreeCredit,omitempty"`

	// DriverFactor Driver factor - only applicable for 1-10 units
	DriverFactor *DriverFactor `json:"driverFactor,omitempty"`

	// ExperienceMod Experience rating modifications
	ExperienceMod *ExperienceMod `json:"experienceMod,omitempty"`

	// LossFreeCreditPercentage Percentage of loss free credit applied
	LossFreeCreditPercentage *float64 `json:"lossFreeCreditPercentage,omitempty"`

	// QualityRating Quality rating grade
	QualityRating *QualityRatingGrade `json:"qualityRating,omitempty"`

	// ScheduleMods Schedule rating modifications
	ScheduleMods *ScheduleMod `json:"scheduleMods,omitempty"`

	// VerifiableExperiencePeriod Verifiable experience period with no third party losses (in months)
	VerifiableExperiencePeriod *int `json:"verifiableExperiencePeriod,omitempty"`
}

// PostBusinessAutoAppRequest defines model for PostBusinessAutoAppRequest.
type PostBusinessAutoAppRequest struct {
	CompanyInfo   CompanyInfo        `json:"companyInfo"`
	CoveragesInfo *CoveragesInfo     `json:"coveragesInfo,omitempty"`
	EffectiveDate openapi_types.Date `json:"effectiveDate"`
	FilingsInfo   *FilingsInfo       `json:"filingsInfo,omitempty"`
	ProducerId    openapi_types.UUID `json:"producerId"`
	VehiclesInfo  *[]VehicleInfo     `json:"vehiclesInfo,omitempty"`
}

// PostBusinessAutoAppResponse defines model for PostBusinessAutoAppResponse.
type PostBusinessAutoAppResponse struct {
	Id openapi_types.UUID `json:"id"`
}

// PostTriggerPricingJobResponse defines model for PostTriggerPricingJobResponse.
type PostTriggerPricingJobResponse struct {
	// JobId The ID of the triggered pricing job
	JobId *string `json:"jobId,omitempty"`

	// Message Response message
	Message *string `json:"message,omitempty"`

	// Success Whether the pricing job was triggered successfully
	Success *bool `json:"success,omitempty"`
}

// PricingStatus The status of the pricing operation
type PricingStatus string

// PrimaryIndustryClassification defines model for PrimaryIndustryClassification.
type PrimaryIndustryClassification string

// Producer defines model for Producer.
type Producer struct {
	Email *string            `json:"email"`
	Id    openapi_types.UUID `json:"id"`
	Name  string             `json:"name"`
}

// PutBusinessAutoAppRequest defines model for PutBusinessAutoAppRequest.
type PutBusinessAutoAppRequest struct {
	CompanyInfo   CompanyInfo        `json:"companyInfo"`
	CoveragesInfo *CoveragesInfo     `json:"coveragesInfo,omitempty"`
	EffectiveDate openapi_types.Date `json:"effectiveDate"`
	FilingsInfo   *FilingsInfo       `json:"filingsInfo,omitempty"`
	ProducerId    openapi_types.UUID `json:"producerId"`
	VehiclesInfo  *[]VehicleInfo     `json:"vehiclesInfo,omitempty"`
}

// QualityRatingGrade Quality rating grade
type QualityRatingGrade string

// RadiusClassification defines model for RadiusClassification.
type RadiusClassification string

// ScheduleMod Schedule rating modifications
type ScheduleMod struct {
	// Mods Array of coverage-specific schedule modification values
	Mods *[]CoverageModification `json:"mods,omitempty"`
}

// SecondaryIndustryClassification defines model for SecondaryIndustryClassification.
type SecondaryIndustryClassification string

// SpecialtyVehicleType defines model for SpecialtyVehicleType.
type SpecialtyVehicleType string

// StateUsage defines model for StateUsage.
type StateUsage string

// SubCoverageGroup defines model for SubCoverageGroup.
type SubCoverageGroup struct {
	// Coverages List of sub-coverage types in this group
	Coverages []string `json:"coverages"`

	// TotalPremium Total premium amount for this sub-coverage group in cents
	TotalPremium int64 `json:"totalPremium"`
}

// SurchargeBreakdown defines model for SurchargeBreakdown.
type SurchargeBreakdown struct {
	// Surcharges Individual surcharge breakdown by surcharge type
	Surcharges []SurchargePremium `json:"surcharges"`

	// TotalAmount Total amount of all surcharges combined in cents
	TotalAmount int64 `json:"totalAmount"`
}

// SurchargePremium defines model for SurchargePremium.
type SurchargePremium struct {
	// Amount Surcharge amount
	Amount int64 `json:"amount"`

	// SurchargeType Type of surcharge
	SurchargeType externalRef1.SurchargeType `json:"surchargeType"`
}

// TrailerType defines model for TrailerType.
type TrailerType string

// UnderwritingOverrides Underwriting factor overrides applied by underwriters
type UnderwritingOverrides struct {
	// AlLossFreeCredit Whether loss free credit is applied for AL coverage
	AlLossFreeCredit   *bool                `json:"alLossFreeCredit,omitempty"`
	AncillaryCoverages *[]AncillaryCoverage `json:"ancillaryCoverages,omitempty"`

	// ApdLossFreeCredit Whether loss free credit is applied for APD coverage
	ApdLossFreeCredit *bool `json:"apdLossFreeCredit,omitempty"`

	// DriverFactor Driver factor - only applicable for 1-10 units
	DriverFactor *DriverFactor `json:"driverFactor,omitempty"`

	// ExperienceMod Experience rating modifications
	ExperienceMod *ExperienceMod `json:"experienceMod,omitempty"`

	// LossFreeCreditPercentage Percentage of loss free credit applied
	LossFreeCreditPercentage *float64 `json:"lossFreeCreditPercentage,omitempty"`

	// QualityRating Quality rating grade
	QualityRating *QualityRatingGrade `json:"qualityRating,omitempty"`

	// ScheduleMods Schedule rating modifications
	ScheduleMods *ScheduleMod `json:"scheduleMods,omitempty"`

	// VerifiableExperiencePeriod Verifiable experience period with no third party losses (in months)
	VerifiableExperiencePeriod *int `json:"verifiableExperiencePeriod,omitempty"`
}

// VehicleDeductible defines model for VehicleDeductible.
type VehicleDeductible struct {
	// SelectedDeductible Selected deductible amount in cents for this specific vehicle
	SelectedDeductible int64 `json:"selectedDeductible"`

	// Vin Vehicle Identification Number
	Vin string `json:"vin"`
}

// VehicleInfo defines model for VehicleInfo.
type VehicleInfo struct {
	ApdDeductible                    *int64               `json:"apdDeductible,omitempty"`
	BusinessUse                      BusinessUse          `json:"businessUse"`
	HasAntiLockBrakes                *bool                `json:"hasAntiLockBrakes,omitempty"`
	IsDoubleTrailer                  *bool                `json:"isDoubleTrailer,omitempty"`
	IsGlassLinedTankTruckOrTrailer   *bool                `json:"isGlassLinedTankTruckOrTrailer,omitempty"`
	IsRefrigeratedTruckOrTrailer     *bool                `json:"isRefrigeratedTruckOrTrailer,omitempty"`
	Make                             string               `json:"make"`
	Model                            string               `json:"model"`
	PrincipalGaragingLocationZipCode string               `json:"principalGaragingLocationZipCode"`
	RadiusClassification             RadiusClassification `json:"radiusClassification"`
	SpecialtyVehicleType             SpecialtyVehicleType `json:"specialtyVehicleType"`
	StateUsage                       StateUsage           `json:"stateUsage"`
	StatedValue                      *float64             `json:"statedValue,omitempty"`
	TrailerType                      *TrailerType         `json:"trailerType,omitempty"`
	VehicleType                      VehicleType          `json:"vehicleType"`
	VehicleUse                       VehicleUse           `json:"vehicleUse"`
	Vin                              string               `json:"vin"`
	WeightClass                      WeightClass          `json:"weightClass"`
	Year                             int32                `json:"year"`
}

// VehicleLimit defines model for VehicleLimit.
type VehicleLimit struct {
	// SelectedLimit Selected limit amount in cents for this specific vehicle
	SelectedLimit int64 `json:"selectedLimit"`

	// Vin Vehicle Identification Number
	Vin string `json:"vin"`
}

// VehicleType defines model for VehicleType.
type VehicleType string

// VehicleUse defines model for VehicleUse.
type VehicleUse string

// WeightClass defines model for WeightClass.
type WeightClass string

// ApplicationId defines model for applicationId.
type ApplicationId = openapi_types.UUID
