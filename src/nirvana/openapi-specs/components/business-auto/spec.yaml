openapi: 3.0.0
info:
  version: 1.0.0
  title: Business Auto Components
  description: Business Auto component schemas and parameters

components:
  parameters:
    applicationId:
      name: applicationId
      in: path
      required: true
      description: The unique identifier of the business auto application
      schema:
        type: string
        format: uuid
  schemas:
    PostBusinessAutoAppRequest:
      type: object
      required:
        - effectiveDate
        - companyInfo
        - producerId
      properties:
        effectiveDate:
          type: string
          format: date
          example: "2024-04-01"
        companyInfo:
          $ref: '#/components/schemas/CompanyInfo'
        vehiclesInfo:
          type: array
          items:
            $ref: '#/components/schemas/VehicleInfo'
        coveragesInfo:
          $ref: '#/components/schemas/CoveragesInfo'
        filingsInfo:
          $ref: '#/components/schemas/FilingsInfo'
        producerId:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"

    PostTriggerPricingJobResponse:
      type: object
      properties:
        success:
          type: boolean
          description: Whether the pricing job was triggered successfully
        message:
          type: string
          description: Response message
        jobId:
          type: string
          description: The ID of the triggered pricing job
          example: "BusinessAutoPricing::::1"

    CoveragePremium:
      type: object
      required:
        - coverageType
        - totalPremium
      properties:
        coverageType:
          $ref: '../common/spec.yaml#/components/schemas/CoverageType'
        totalPremium:
          type: integer
          format: int64
          description: Total premium amount for this coverage in cents
          example: 150000
        subCoverageGroups:
          type: array
          items:
            $ref: '#/components/schemas/SubCoverageGroup'
          description: Premium breakdown by sub-coverage groups (collapsible)

    SubCoverageGroup:
      type: object
      required:
        - coverages
        - totalPremium
      properties:
        coverages:
          type: array
          items:
            type: string
          description: List of sub-coverage types in this group
          example: ["SubCoverageType_BodilyInjury", "SubCoverageType_PropertyDamage"]
        totalPremium:
          type: integer
          format: int64
          description: Total premium amount for this sub-coverage group in cents
          example: 75000

    SurchargePremium:
      type: object
      required:
        - surchargeType
        - amount
      properties:
        surchargeType:
          $ref: '../common/spec.yaml#/components/schemas/SurchargeType'
        amount:
          type: integer
          format: int64
          description: Surcharge amount 
          example: 5000

    SurchargeBreakdown:
      type: object
      required:
        - surcharges
        - totalAmount
      properties:
        surcharges:
          type: array
          items:
            $ref: '#/components/schemas/SurchargePremium'
          description: Individual surcharge breakdown by surcharge type
        totalAmount:
          type: integer
          format: int64
          description: Total amount of all surcharges combined in cents
          example: 25000



    GetPricingResponse:
      type: object
      required:
        - applicationId
        - dataContextId
        - totalPremium
        - coverages
        - surcharges
        - createdAt
      properties:
        applicationId:
          type: string
          format: uuid
          description: The unique identifier of the business auto application
          example: "123e4567-e89b-12d3-a456-************"
        dataContextId:
          type: string
          format: uuid
          description: The unique identifier of the data context
          example: "987e6543-e21b-34d5-a654-************"
        totalPremium:
          type: integer
          format: int64
          description: Total premium amount across all coverages in cents
          example: 500000
        coverages:
          type: array
          items:
            $ref: '#/components/schemas/CoveragePremium'
          description: Premium breakdown by coverage type (collapsible)
        surcharges:
          $ref: '#/components/schemas/SurchargeBreakdown'
        createdAt:
          type: string
          format: date-time
          description: When the pricing information was created
          example: "2024-04-01T12:00:00Z"

    GetPricingStatusResponse:
      type: object
      required:
        - applicationId
        - status
        - startedAt
      properties:
        applicationId:
          type: string
          format: uuid
          description: The unique identifier of the business auto application
          example: "123e4567-e89b-12d3-a456-************"
        status:
          $ref: '#/components/schemas/PricingStatus'
        startedAt:
          type: string
          format: date-time
          description: When the pricing job was started
          example: "2024-01-01T10:00:00Z"
        completedAt:
          type: string
          format: date-time
          description: When the pricing job was completed (null if still pending)
          example: "2024-01-01T10:05:00Z"
        errorMessage:
          type: string
          description: Error message if the pricing job failed
          example: "Pricing calculation failed due to insufficient data"
        jobId:
          type: string
          description: The unique identifier of the pricing job
          example: "BusinessAutoPricing::::1"

    PricingStatus:
      type: string
      enum:
        - Pending
        - Completed
        - Failed
        - NotStarted
      description: The status of the pricing operation
      example: "Completed"

    CompanyInfo:
      type: object
      required:
        - name
        - usState
      properties:
        name:
          type: string
          example: "ABC Trucking LLC"
        usState:
          $ref: '../common/spec.yaml#/components/schemas/USState'
        address:
          $ref: '../nirvana/spec.yaml#/components/schemas/Address'
        DOTNumber:
          type: integer
          example: 12345
        FEIN:
          type: string
          example: "*********"
        noOfPowerUnits:
          type: integer
          format: int32
          minimum: 1
          example: 5
        hasIndividualNamedInsured:
          type: boolean
          example: false
        hasWorkCompPolicy:
          type: boolean
          example: false
        noOfEmployees:
          type: integer
          format: int32
          minimum: 1
          example: 10
        perOfEmployeesOperatingOwnAutos:
          type: number
          format: float
          minimum: 0
          maximum: 100
          example: 25.5
        primaryIndustryClassification:
          $ref: '#/components/schemas/PrimaryIndustryClassification'
          description: "The primary industry classification for the company vehicle operation"
        secondaryIndustryClassifications:
          type: array
          items:
            $ref: '#/components/schemas/SecondaryIndustryClassification'
          description: "Secondary industry classifications if the company operates in multiple industries"
        annualCostOfHire:
          type: number
          format: double
          example: 50000.00
        maximumValueOfHiredAutos:
          type: number
          format: double
          example: 100000.00

    VehicleInfo:
      type: object
      required:
        - vin
        - year
        - make
        - model
        - vehicleType
        - weightClass
        - specialtyVehicleType
        - vehicleUse
        - businessUse
        - stateUsage
        - radiusClassification
        - principalGaragingLocationZipCode
      properties:
        vin:
          type: string
          minLength: 17
          maxLength: 17
          example: "1HGCM82633A123456"
        year:
          type: integer
          format: int32
          minimum: 1900
          maximum: 2100
          example: 2023
        make:
          type: string
          example: "Ford"
        model:
          type: string
          example: "F-150"
        vehicleType:
          $ref: '#/components/schemas/VehicleType'
          description: "The general type of vehicle (Truck, Tractor, or Trailer)"
        weightClass:
          $ref: '#/components/schemas/WeightClass'
          description: "Weight class of the vehicle"
        specialtyVehicleType:
          $ref: '#/components/schemas/SpecialtyVehicleType'
          description: "Specific detailed classification of the vehicle (51 different types available)"
        vehicleUse:
          $ref: '#/components/schemas/VehicleUse'
          description: "Primary use of the vehicle (towing, dumping, logging, or other operations)"
        businessUse:
          $ref: '#/components/schemas/BusinessUse'
          description: "Business use classification of the vehicle"
        trailerType:
          $ref: '#/components/schemas/TrailerType'
        stateUsage:
          $ref: '#/components/schemas/StateUsage'
        radiusClassification:
          $ref: '#/components/schemas/RadiusClassification'
        principalGaragingLocationZipCode:
          type: string
          pattern: '^\d{5}(-\d{4})?$'
          example: "12345"
        apdDeductible:
          type: integer
          format: int64
          example: 500
        statedValue:
          type: number
          format: double
          example: 75000.00
        isGlassLinedTankTruckOrTrailer:
          type: boolean
          default: false
        isRefrigeratedTruckOrTrailer:
          type: boolean
          default: false
        isDoubleTrailer:
          type: boolean
          default: false
        hasAntiLockBrakes:
          type: boolean
          default: false

    CoveragesInfo:
      type: object
      required:
        - coverages
      properties:
        coverages:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/Coverage'
          description: Map of coverage type to coverage with limit and deductible
          example:
            CoverageAutoLiability:
              coverageType: CoverageAutoLiability
              limit: 1000000
              deductible: 0
            CoverageAutoPhysicalDamage:
              coverageType: CoverageAutoPhysicalDamage
              limit: 50000
              deductible: 500

    Coverage:
      type: object
      required:
        - coverageType
        - isEnabled
      properties:
        coverageType:
          $ref: '../common/spec.yaml#/components/schemas/CoverageType'
        limit:
          type: integer
          format: int64
          example: 1000000
        deductible:
          type: integer
          format: int64
          example: 1000
        isEnabled:
          type: boolean
          description: "Whether the coverage is enabled"
          example: true

    FilingsInfo:
      type: object
      required:
        - hasMultiStateFilings
        - hasSingleStateFilings
        - hasFMCSAFilings
        - hasDOTFilings
      properties:
        hasMultiStateFilings:
          type: boolean
          default: false
        hasSingleStateFilings:
          type: boolean
          default: false
        hasFMCSAFilings:
          type: boolean
          default: false
        hasDOTFilings:
          type: boolean
          default: false

    RadiusClassification:
      type: string
      enum:
        - Invalid
        - 0To100
        - 101To300
        - GreaterThan301
      x-enum-descriptions:
        - "Invalid"
        - "0-100 miles radius"
        - "101-300 miles radius"
        - "Greater than 301 miles radius"

    PostBusinessAutoAppResponse:
      type: object
      required:
        - id
      properties:
        id:
          type: string
          format: uuid

    GetBusinessAutoAppResponse:
      type: object
      required:
        - id
        - shortID
        - state
        - effectiveDate
        - effectiveDateTo
        - companyInfo
        - vehiclesInfo
        - coveragesInfo
        - filingsInfo
        - producerInfo
        - createdAt
      properties:
        id:
          type: string
          format: uuid
        shortID:
          type: string
        state:
          $ref: '#/components/schemas/AppState'
        effectiveDate:
          type: string
          format: date
        effectiveDateTo:
          type: string
          format: date
        companyInfo:
          $ref: '#/components/schemas/CompanyInfo'
        vehiclesInfo:
          type: array
          items:
            $ref: '#/components/schemas/VehicleInfo'
        coveragesInfo:
          $ref: '#/components/schemas/CoveragesInfo'
        filingsInfo:
          $ref: '#/components/schemas/FilingsInfo'
        producerInfo:
          $ref: '#/components/schemas/Producer'
        createdAt:
          type: string
          format: date-time
        underwritingOverrides:
          $ref: '#/components/schemas/UnderwritingOverrides'
        telematicsInfo:
          $ref: '../application/spec.yaml#/components/schemas/TelematicsInfo'
        tspConnHandleId:
          type: string
          format: uuid
          description: "The unique identifier for the TSP connection handle"

    PatchBusinessAutoAppRequest:
      type: object
      properties:
        effectiveDate:
          type: string
          format: date
          example: "2024-04-01"
        companyInfo:
          $ref: '#/components/schemas/CompanyInfo'
        vehiclesInfo:
          type: array
          items:
            $ref: '#/components/schemas/VehicleInfo'
        coveragesInfo:
          $ref: '#/components/schemas/CoveragesInfo'
        filingsInfo:
          $ref: '#/components/schemas/FilingsInfo'
        producerId:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"

    PatchUnderwritingOverridesRequest:
      type: object
      description: Request to update underwriting overrides for a business auto application
      properties:
        alLossFreeCredit:
          type: boolean
          description: "Whether loss free credit is applied for AL coverage"
          example: true
        apdLossFreeCredit:
          type: boolean
          description: "Whether loss free credit is applied for APD coverage"
          example: true
        verifiableExperiencePeriod:
          type: integer
          description: "Verifiable experience period with no third party losses (in months)"
          example: 36
        lossFreeCreditPercentage:
          type: number
          format: double
          description: "Percentage of loss free credit applied"
          example: 0.10
        driverFactor:
          $ref: '#/components/schemas/DriverFactor'
        scheduleMods:
          $ref: '#/components/schemas/ScheduleMod'
        experienceMod:
          $ref: '#/components/schemas/ExperienceMod'
        qualityRating:
          $ref: '#/components/schemas/QualityRatingGrade'
        ancillaryCoverages:
          type: array
          items:
            $ref: '#/components/schemas/AncillaryCoverage'

    PatchPreBindInfoRequest:
      type: object
      description: Request to update pre-bind information (FEIN or DOT number) for a business auto application
      required:
        - fein
      properties:
        fein:
          type: string
          description: "Federal Employer Identification Number"
          example: "*********"
        dotNumber:
          type: integer
          example: 12345

    # Enums
    PrimaryIndustryClassification:
      type: string
      enum:
        - Invalid
        - ContractorTrucks
        - WholesalersManufacturers

    SecondaryIndustryClassification:
      type: string
      enum:
        - Invalid
        - ContractorTrucks
        - SpecializedDeliveryVehicles
        - CourierServiceVehicles
        - FoodDeliveryTrucks
        - WasteDisposalTrucks
        - WasteOilLiquidWasteTransporter
        - FarmerTrucks
        - CementMixers
        - HouseMovers
        - MovingOperations
        - LawnTreeServiceTrucks
        - CatererVehicles
        - MobileConcessionTrucks
        - WholesalersManufacturers
        - SalvageHaulers
        - GasOilHaulersNotForHire
        - CarCarriersNotForHire
        - NotOtherwiseClassifiedTruck
        - LoggingTrucksForHire
        - LoggingTrucksNotForHire
        - TowTrucksIncidentalUse
        - TowTrucksFullTime
        - DumpingOperationsForHire
        - DumpingOperationsNotForHire
        - ServiceUse

    BusinessUse:
      type: string
      enum:
        - Invalid
        - Commercial
        - Retail
        - Service

    WeightClass:
      type: string
      enum:
        - Invalid
        - Light
        - Medium
        - Heavy
        - ExtraHeavy
      x-enum-descriptions:
        - "Invalid"
        - "GVW <=10,000 lbs"
        - "GVW 10,001 - 20,000 lbs"
        - "GVW 20,001 - 45000 lbs"
        - "GVW > 45000 lbs"

    VehicleType:
      type: string
      enum:
        - Invalid
        - Truck
        - Tractor
        - Trailer

    SpecialtyVehicleType:
      type: string
      enum:
        - Invalid
        - BoomTruck0To49Feet
        - BoomTruck50To75Feet
        - BoomTruck76To100Feet
        - BoomTruck101To120Feet
        - BoomTruckOver120Feet
        - CableTelecomUtilityContractors
        - ExcavatingDrillingLightMediumHeavyTrucks
        - ExcavatingDrillingAllOtherUnits
        - PetroleumDistributionContractors
        - HotOilLiquidAsphaltTrucksOilField
        - AllOtherOilFieldBodyTypes
        - SepticTankServiceTrucksIndividual
        - SepticTankServiceTrucksAllOther
        - WeldersMetalworkingContractors
        - AllOtherContractorBodyTypes
        - HotOilLiquidAsphaltTrucksAllOther
        - SpecializedDeliveryVehicles
        - CourierServiceVehicles
        - FoodDeliveryTrucks
        - WasteDisposalTrucks
        - WasteOilLiquidWasteTransporters
        - HarvesterGoatTrucks
        - AllOtherFarmerTrucks
        - HouseMovers
        - MovingOperations
        - LawnTreeServiceTrucks
        - CatererVehicles
        - MobileConcessionTruckInVehicleVending
        - MobileConcessionTruckOtherFoodVending
        - MobileConcessionTruckNoFoodSales
        - WholesalersManufacturers
        - GasOilLPGPropaneBottled
        - GasOilLPGPropaneBulk
        - GasOilCrudeOilHaulers
        - GasOilFuelOilHaulers
        - GasOilAllOtherGasHaulers
        - SalvageHaulers
        - CarCarriersNotForHire
        - ServiceUseVehicles
        - FireworkHaulersNotForHire
        - CustomHarvesters
        - DriverTrainingTrucksTractors
        - StreetSweepers
        - RentalEquipmentProvider
        - NotOtherwiseClassifiedTrucks
        - ArtisanContractors
        - CarpentryContractors
        - ExteriorBuildingConstructionContractors
        - RoadConstructionContractors
        - TrafficControlContractors
        - MobileMechanicContractors
        - AllOtherUnits

    VehicleUse:
      type: string
      enum:
        - Invalid
        - TowingOperations
        - DumpingOperations
        - LoggingOperations
        - OtherOperations

    TrailerType:
      type: string
      enum:
        - Invalid
        - SemiTrailers
        - FullTrailers
        - ServiceUtilityTrailers
        - CustomHarvesterTrailers

    StateUsage:
      type: string
      enum:
        - Invalid
        - Intrastate
        - Interstate

    QualityRatingGrade:
      type: string
      enum:
        - Invalid
        - A
        - B
        - C
        - D
        - E
        - F
      description: "Quality rating grade"

    AppState:
      type: string
      enum:
        - Created
        - UnderReview
        - QuoteGenerated
        - QuoteGenerating
        - Approved
        - Declined
        - Closed
        - PolicyCreated
        - BindableQuoteGenerated
        - Panic

    AutoLiabilityCSL:
      type: string
      enum:
        - Invalid
        - 300K
        - 500K
        - 750K
        - 1M

    Deductible:
      type: string
      enum:
        - Invalid
        - 0

    GetAvailableProducersResponse:
      type: array
      items:
        $ref: '#/components/schemas/Producer'

    Producer:
      type: object
      required:
        - id
        - name
      properties:
        id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        name:
          type: string
          example: "John Doe"
        email:
          type: string
          nullable: true
          example: "<EMAIL>"

    PatchStateTransitionRequest:
      type: object
      required:
        - transitionToState
      properties:
        transitionToState:
          $ref: '#/components/schemas/AppState'

    PatchStateTransitionResponse:
      type: object
      required:
        - applicationId
        - newState
      properties:
        applicationId:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        newState:
          $ref: '#/components/schemas/AppState'

    BusinessAutoApplicationListRecord:
      type: object
      required:
        - id
        - state
        - companyName
        - effectiveDate
        - shortID
      properties:
        id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        state:
          $ref: '#/components/schemas/AppState'
        companyName:
          type: string
          example: "ABC Trucking LLC"
        effectiveDate:
          type: string
          format: date
          example: "2024-04-01"
        shortID:
          type: string
          example: "BA-2024-0001"
        tspProvider:
          $ref: '../nirvana/spec.yaml#/components/schemas/TSPProvider'
        isInternal:
          type: boolean
          description: "Whether this application belongs to a Nirvana internal agency"
          example: false
    # Underwriting Factor Schemas
    UnderwritingOverrides:
      type: object
      description: "Underwriting factor overrides applied by underwriters"
      properties:
        alLossFreeCredit:
          type: boolean
          description: "Whether loss free credit is applied for AL coverage"
          example: true
        apdLossFreeCredit:
          type: boolean
          description: "Whether loss free credit is applied for APD coverage"
          example: true
        verifiableExperiencePeriod:
          type: integer
          description: "Verifiable experience period with no third party losses (in months)"
          example: 36
        lossFreeCreditPercentage:
          type: number
          format: double
          description: "Percentage of loss free credit applied"
          example: 0.10
        driverFactor:
          $ref: '#/components/schemas/DriverFactor'
        scheduleMods:
          $ref: '#/components/schemas/ScheduleMod'
        experienceMod:
          $ref: '#/components/schemas/ExperienceMod'
        qualityRating:
          $ref: '#/components/schemas/QualityRatingGrade'
        ancillaryCoverages:
            type: array
            items:
              $ref: '#/components/schemas/AncillaryCoverage'


    DriverFactor:
      type: object
      description: "Driver factor - only applicable for 1-10 units"
      properties:
        factor:
          type: number
          format: double
          description: "The factor value applied by underwriter"
          example: 0.85

    ScheduleMod:
      type: object
      description: "Schedule rating modifications"
      properties:
        mods:
          type: array
          items:
            $ref: '#/components/schemas/CoverageModification'
          description: "Array of coverage-specific schedule modification values"

    ExperienceMod:
      type: object
      description: "Experience rating modifications"
      properties:
        mods:
          type: array
          items:
            $ref: '#/components/schemas/CoverageModification'
          description: "Array of coverage-specific experience modification values"

    CoverageModification:
      type: object
      required:
        - coverage
        - modifier
      properties:
        coverage:
          $ref: '../common/spec.yaml#/components/schemas/CoverageType'
        modifier:
          type: number
          format: double
          description: "The modification factor to apply"
          example: 0.85

    PutBusinessAutoAppRequest:
      type: object
      required:
        - effectiveDate
        - companyInfo
        - producerId
      properties:
        effectiveDate:
          type: string
          format: date
          example: "2024-04-01"
        companyInfo:
          $ref: '#/components/schemas/CompanyInfo'
        vehiclesInfo:
          type: array
          items:
            $ref: '#/components/schemas/VehicleInfo'
        coveragesInfo:
          $ref: '#/components/schemas/CoveragesInfo'
        filingsInfo:
          $ref: '#/components/schemas/FilingsInfo'
        producerId:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"

    AncillaryCoverage:
      type: object
      required:
        - coverage
        - primaryCoverage
        - isEnabled
      properties:
        coverage:
          $ref: '../common/spec.yaml#/components/schemas/CoverageType'
        primaryCoverage:
          $ref: '../common/spec.yaml#/components/schemas/CoverageType'
        isEnabled:
          type: boolean
          description: "Whether the ancillary coverage is enabled"
          example: true
        limitOptions:
          type: array
          items:
            type: integer
            format: int64
            description: "Available limit options for the ancillary coverage"
            example: 1000000
        deductibleOptions:
          type: array
          items:
            type: integer
            format: int64
            description: "Available deductible options for the ancillary coverage"
            example: 1000
        selectedLimit:
          type: integer
          format: int64
          example: 1000000
        selectedDeductible:
          type: integer
          format: int64
          example: 1000
          description: "Default deductible value - use vehicleDeductibles for vehicle-specific deductibles"
        vehicleDeductibles:
          type: array
          items:
            $ref: '#/components/schemas/VehicleDeductible'
          description: "Vehicle-specific deductibles for this ancillary coverage. Only applicable when hasVehicleLevelDeductible is true."
        vehicleLimits:
          type: array
          items:
            $ref: '#/components/schemas/VehicleLimit'
          description: "Vehicle-specific limits for this ancillary coverage. Only applicable when hasVehicleLevelLimit is true."

    VehicleDeductible:
      type: object
      required:
        - vin
        - selectedDeductible
      properties:
        vin:
          type: string
          description: "Vehicle Identification Number"
          example: "1HGCM82633A123456"
        selectedDeductible:
          type: integer
          format: int64
          description: "Selected deductible amount in cents for this specific vehicle"
          example: 100000

    VehicleLimit:
      type: object
      required:
        - vin
        - selectedLimit
      properties:
        vin:
          type: string
          description: "Vehicle Identification Number"
          example: "1HGCM82633A123456"
        selectedLimit:
          type: integer
          format: int64
          description: "Selected limit amount in cents for this specific vehicle"
          example: 100000

    GetCoverageOptionsRequestBody:
      type: object
      required:
        - coverages
      properties:
        coverages:
          type: array
          items:
            $ref: '#/components/schemas/Coverage'

    GetCoverageOptionsResponse:
      type: array
      items:
        $ref: '#/components/schemas/CoverageOption'
      description: "List of coverage options with vehicle-level information"

    CoverageOption:
      type: object
      required:
        - coverage
        - vehicleList
        - hasVehicleLevelLimit
        - hasVehicleLevelDeductible
      properties:
        coverage:
          $ref: '../common/spec.yaml#/components/schemas/CoverageType'
        hasVehicleLevelLimit:
          type: boolean
          description: "Whether this coverage supports vehicle-level limits"
          example: true
        hasVehicleLevelDeductible:
          type: boolean
          description: "Whether this coverage supports vehicle-level deductibles"
          example: true
        vinList:
          type: array
          items:
            type: string



