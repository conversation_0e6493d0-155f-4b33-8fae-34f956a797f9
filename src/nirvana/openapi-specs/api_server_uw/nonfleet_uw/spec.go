// Package nonfleet_uw provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/deepmap/oapi-codegen/v2 version v2.0.0 DO NOT EDIT.
package nonfleet_uw

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"

	"github.com/labstack/echo/v4"
	"github.com/oapi-codegen/runtime"
	openapi_types "github.com/oapi-codegen/runtime/types"
	externalRef0 "nirvanatech.com/nirvana/openapi-specs/components/common"
	externalRef1 "nirvanatech.com/nirvana/openapi-specs/components/nonfleet_underwriting"
	externalRef2 "nirvanatech.com/nirvana/openapi-specs/components/underwriting"
)

const (
	SessionIdAuthScopes = "sessionIdAuth.Scopes"
)

// GetApplicationReviewsWithPaginationParams defines parameters for GetApplicationReviewsWithPagination.
type GetApplicationReviewsWithPaginationParams struct {
	Size                *int                               `form:"size,omitempty" json:"size,omitempty"`
	Cursor              *string                            `form:"cursor,omitempty" json:"cursor,omitempty"`
	Q                   *string                            `form:"q,omitempty" json:"q,omitempty"`
	EffectiveDateBefore *openapi_types.Date                `form:"effectiveDateBefore,omitempty" json:"effectiveDateBefore,omitempty"`
	EffectiveDateAfter  *openapi_types.Date                `form:"effectiveDateAfter,omitempty" json:"effectiveDateAfter,omitempty"`
	UnderWriterID       *string                            `form:"underWriterID,omitempty" json:"underWriterID,omitempty"`
	Tab                 *externalRef2.ApplicationReviewTab `form:"tab,omitempty" json:"tab,omitempty"`
}

// PatchExpressLaneFeedbackJSONBody defines parameters for PatchExpressLaneFeedback.
type PatchExpressLaneFeedbackJSONBody struct {
	OverallDecision   *externalRef1.DecisionType                  `json:"overall_decision,omitempty"`
	OverallReason     *string                                     `json:"overall_reason,omitempty"`
	PanelWiseDecision []externalRef1.ExpressLanePanelWiseDecision `json:"panel_wise_decision"`
}

// PostApplicationReviewQuoteSubmitParams defines parameters for PostApplicationReviewQuoteSubmit.
type PostApplicationReviewQuoteSubmitParams struct {
	Bindable *bool `form:"bindable,omitempty" json:"bindable,omitempty"`
}

// GetScrapeInfoParams defines parameters for GetScrapeInfo.
type GetScrapeInfoParams struct {
	// ScrapeType The type of scrape to trigger
	ScrapeType externalRef1.ScrapeType `form:"scrapeType" json:"scrapeType"`
}

// TriggerScrapeParams defines parameters for TriggerScrape.
type TriggerScrapeParams struct {
	// ScrapeType The type of scrape to trigger
	ScrapeType externalRef1.ScrapeType `form:"scrapeType" json:"scrapeType"`
}

// ApproveApplicationReviewJSONRequestBody defines body for ApproveApplicationReview for application/json ContentType.
type ApproveApplicationReviewJSONRequestBody = externalRef1.ApproveApplicationReviewForm

// UpdateApplicationReviewAssigneeJSONRequestBody defines body for UpdateApplicationReviewAssignee for application/json ContentType.
type UpdateApplicationReviewAssigneeJSONRequestBody = externalRef2.ApplicationReviewAssigneesForm

// CloseApplicationReviewJSONRequestBody defines body for CloseApplicationReview for application/json ContentType.
type CloseApplicationReviewJSONRequestBody = externalRef1.CloseApplicationReviewForm

// DeclineApplicationReviewJSONRequestBody defines body for DeclineApplicationReview for application/json ContentType.
type DeclineApplicationReviewJSONRequestBody = externalRef1.DeclineApplicationReviewForm

// UploadApplicationReviewDocumentsMultipartRequestBody defines body for UploadApplicationReviewDocuments for multipart/form-data ContentType.
type UploadApplicationReviewDocumentsMultipartRequestBody = externalRef0.UploadFileRequest

// UpdateApplicationReviewDriverJSONRequestBody defines body for UpdateApplicationReviewDriver for application/json ContentType.
type UpdateApplicationReviewDriverJSONRequestBody = externalRef1.UpdateApplicationReviewDriverRecordForm

// UpdateApplicationReviewDriversJSONRequestBody defines body for UpdateApplicationReviewDrivers for application/json ContentType.
type UpdateApplicationReviewDriversJSONRequestBody = externalRef1.UpdateApplicationReviewDriversForm

// UpdateApplicationReviewEquipmentsJSONRequestBody defines body for UpdateApplicationReviewEquipments for application/json ContentType.
type UpdateApplicationReviewEquipmentsJSONRequestBody = externalRef1.UpdateApplicationReviewEquipmentForm

// PatchExpressLaneFeedbackJSONRequestBody defines body for PatchExpressLaneFeedback for application/json ContentType.
type PatchExpressLaneFeedbackJSONRequestBody PatchExpressLaneFeedbackJSONBody

// UpdateApplicationReviewLossesJSONRequestBody defines body for UpdateApplicationReviewLosses for application/json ContentType.
type UpdateApplicationReviewLossesJSONRequestBody = externalRef1.UpdateApplicationReviewLossesForm

// UpdateApplicationReviewNotesJSONRequestBody defines body for UpdateApplicationReviewNotes for application/json ContentType.
type UpdateApplicationReviewNotesJSONRequestBody = externalRef1.ApplicationReviewNotes

// UpdateApplicationReviewOperationsJSONRequestBody defines body for UpdateApplicationReviewOperations for application/json ContentType.
type UpdateApplicationReviewOperationsJSONRequestBody = externalRef1.UpdateApplicationReviewOperationForm

// UpdateApplicationReviewPackagesJSONRequestBody defines body for UpdateApplicationReviewPackages for application/json ContentType.
type UpdateApplicationReviewPackagesJSONRequestBody = externalRef1.UpdateApplicationReviewPackagesForm

// ReferApplicationReviewJSONRequestBody defines body for ReferApplicationReview for application/json ContentType.
type ReferApplicationReviewJSONRequestBody = externalRef1.ReferApplicationReviewForm

// UpdateApplicationReviewSafetyJSONRequestBody defines body for UpdateApplicationReviewSafety for application/json ContentType.
type UpdateApplicationReviewSafetyJSONRequestBody = externalRef1.UpdateApplicationReviewSafetyForm

// UpdateApplicationReviewSafetyScoreV2JSONRequestBody defines body for UpdateApplicationReviewSafetyScoreV2 for application/json ContentType.
type UpdateApplicationReviewSafetyScoreV2JSONRequestBody = externalRef2.ApplicationReviewSafetyScoreFormV2

// UpdateApplicationReviewDriverV2JSONRequestBody defines body for UpdateApplicationReviewDriverV2 for application/json ContentType.
type UpdateApplicationReviewDriverV2JSONRequestBody = externalRef1.UpdateApplicationReviewDriverRecordForm

// RequestEditorFn  is the function signature for the RequestEditor callback function
type RequestEditorFn func(ctx context.Context, req *http.Request) error

// Doer performs HTTP requests.
//
// The standard http.Client implements this interface.
type HttpRequestDoer interface {
	Do(req *http.Request) (*http.Response, error)
}

// Client which conforms to the OpenAPI3 specification for this service.
type Client struct {
	// The endpoint of the server conforming to this interface, with scheme,
	// https://api.deepmap.com for example. This can contain a path relative
	// to the server, such as https://api.deepmap.com/dev-test, and all the
	// paths in the swagger spec will be appended to the server.
	Server string

	// Doer for performing requests, typically a *http.Client with any
	// customized settings, such as certificate chains.
	Client HttpRequestDoer

	// A list of callbacks for modifying requests which are generated before sending over
	// the network.
	RequestEditors []RequestEditorFn
}

// ClientOption allows setting custom parameters during construction
type ClientOption func(*Client) error

// Creates a new Client, with reasonable defaults
func NewClient(server string, opts ...ClientOption) (*Client, error) {
	// create a client with sane default values
	client := Client{
		Server: server,
	}
	// mutate client and add all optional params
	for _, o := range opts {
		if err := o(&client); err != nil {
			return nil, err
		}
	}
	// ensure the server URL always has a trailing slash
	if !strings.HasSuffix(client.Server, "/") {
		client.Server += "/"
	}
	// create httpClient, if not already present
	if client.Client == nil {
		client.Client = &http.Client{}
	}
	return &client, nil
}

// WithHTTPClient allows overriding the default Doer, which is
// automatically created using http.Client. This is useful for tests.
func WithHTTPClient(doer HttpRequestDoer) ClientOption {
	return func(c *Client) error {
		c.Client = doer
		return nil
	}
}

// WithRequestEditorFn allows setting up a callback function, which will be
// called right before sending the request. This can be used to mutate the request.
func WithRequestEditorFn(fn RequestEditorFn) ClientOption {
	return func(c *Client) error {
		c.RequestEditors = append(c.RequestEditors, fn)
		return nil
	}
}

// The interface specification for the client above.
type ClientInterface interface {
	// GetApplicationReviewsWithPagination request
	GetApplicationReviewsWithPagination(ctx context.Context, params *GetApplicationReviewsWithPaginationParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetApplicationReviews request
	GetApplicationReviews(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetNonFleetApplicationReviewActions request
	GetNonFleetApplicationReviewActions(ctx context.Context, applicationReviewID externalRef1.ApplicationReviewID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// ApproveApplicationReviewWithBody request with any body
	ApproveApplicationReviewWithBody(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	ApproveApplicationReview(ctx context.Context, applicationReviewID string, body ApproveApplicationReviewJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// UpdateApplicationReviewAssigneeWithBody request with any body
	UpdateApplicationReviewAssigneeWithBody(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	UpdateApplicationReviewAssignee(ctx context.Context, applicationReviewID string, body UpdateApplicationReviewAssigneeJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// CloseApplicationReviewWithBody request with any body
	CloseApplicationReviewWithBody(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	CloseApplicationReview(ctx context.Context, applicationReviewID string, body CloseApplicationReviewJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeclineApplicationReviewWithBody request with any body
	DeclineApplicationReviewWithBody(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	DeclineApplicationReview(ctx context.Context, applicationReviewID string, body DeclineApplicationReviewJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetApplicationReviewDocuments request
	GetApplicationReviewDocuments(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// UploadApplicationReviewDocumentsWithBody request with any body
	UploadApplicationReviewDocumentsWithBody(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	// UpdateApplicationReviewDriverWithBody request with any body
	UpdateApplicationReviewDriverWithBody(ctx context.Context, applicationReviewID string, dlNumber string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	UpdateApplicationReviewDriver(ctx context.Context, applicationReviewID string, dlNumber string, body UpdateApplicationReviewDriverJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetApplicationReviewDrivers request
	GetApplicationReviewDrivers(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// UpdateApplicationReviewDriversWithBody request with any body
	UpdateApplicationReviewDriversWithBody(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	UpdateApplicationReviewDrivers(ctx context.Context, applicationReviewID string, body UpdateApplicationReviewDriversJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetApplicationReviewEquipments request
	GetApplicationReviewEquipments(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// UpdateApplicationReviewEquipmentsWithBody request with any body
	UpdateApplicationReviewEquipmentsWithBody(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	UpdateApplicationReviewEquipments(ctx context.Context, applicationReviewID string, body UpdateApplicationReviewEquipmentsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetExpressLaneFeedback request
	GetExpressLaneFeedback(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PatchExpressLaneFeedbackWithBody request with any body
	PatchExpressLaneFeedbackWithBody(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PatchExpressLaneFeedback(ctx context.Context, applicationReviewID string, body PatchExpressLaneFeedbackJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetApplicationReviewFlags request
	GetApplicationReviewFlags(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetApplicationReviewLosses request
	GetApplicationReviewLosses(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// UpdateApplicationReviewLossesWithBody request with any body
	UpdateApplicationReviewLossesWithBody(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	UpdateApplicationReviewLosses(ctx context.Context, applicationReviewID string, body UpdateApplicationReviewLossesJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetApplicationReviewNotes request
	GetApplicationReviewNotes(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// UpdateApplicationReviewNotesWithBody request with any body
	UpdateApplicationReviewNotesWithBody(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	UpdateApplicationReviewNotes(ctx context.Context, applicationReviewID string, body UpdateApplicationReviewNotesJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetApplicationReviewOperations request
	GetApplicationReviewOperations(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// UpdateApplicationReviewOperationsWithBody request with any body
	UpdateApplicationReviewOperationsWithBody(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	UpdateApplicationReviewOperations(ctx context.Context, applicationReviewID string, body UpdateApplicationReviewOperationsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetApplicationReviewsPackages request
	GetApplicationReviewsPackages(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// UpdateApplicationReviewPackagesWithBody request with any body
	UpdateApplicationReviewPackagesWithBody(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	UpdateApplicationReviewPackages(ctx context.Context, applicationReviewID string, body UpdateApplicationReviewPackagesJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApplicationReviewQuoteSubmit request
	PostApplicationReviewQuoteSubmit(ctx context.Context, applicationReviewID string, params *PostApplicationReviewQuoteSubmitParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// ReferApplicationReviewWithBody request with any body
	ReferApplicationReviewWithBody(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	ReferApplicationReview(ctx context.Context, applicationReviewID string, body ReferApplicationReviewJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// RollbackApplicationReview request
	RollbackApplicationReview(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetApplicationReviewSafety request
	GetApplicationReviewSafety(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// UpdateApplicationReviewSafetyWithBody request with any body
	UpdateApplicationReviewSafetyWithBody(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	UpdateApplicationReviewSafety(ctx context.Context, applicationReviewID string, body UpdateApplicationReviewSafetyJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetApplicationReviewSafetyScore request
	GetApplicationReviewSafetyScore(ctx context.Context, applicationReviewID externalRef2.ApplicationReviewID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// UpdateApplicationReviewSafetyScoreV2WithBody request with any body
	UpdateApplicationReviewSafetyScoreV2WithBody(ctx context.Context, applicationReviewID externalRef2.ApplicationReviewID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	UpdateApplicationReviewSafetyScoreV2(ctx context.Context, applicationReviewID externalRef2.ApplicationReviewID, body UpdateApplicationReviewSafetyScoreV2JSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// SetApplicationReviewMVRPull request
	SetApplicationReviewMVRPull(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetApplicationReviewsSummary request
	GetApplicationReviewsSummary(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetApplicationReviewTimeline request
	GetApplicationReviewTimeline(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetDriverViolations request
	GetDriverViolations(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// UpdateApplicationReviewDriverV2WithBody request with any body
	UpdateApplicationReviewDriverV2WithBody(ctx context.Context, applicationReviewID string, dlNumber string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	UpdateApplicationReviewDriverV2(ctx context.Context, applicationReviewID string, dlNumber string, body UpdateApplicationReviewDriverV2JSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetApplicationReviewDriversV2 request
	GetApplicationReviewDriversV2(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetScrapeInfo request
	GetScrapeInfo(ctx context.Context, applicationReviewID string, params *GetScrapeInfoParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// TriggerScrape request
	TriggerScrape(ctx context.Context, applicationReviewID string, params *TriggerScrapeParams, reqEditors ...RequestEditorFn) (*http.Response, error)
}

func (c *Client) GetApplicationReviewsWithPagination(ctx context.Context, params *GetApplicationReviewsWithPaginationParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetApplicationReviewsWithPaginationRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetApplicationReviews(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetApplicationReviewsRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetNonFleetApplicationReviewActions(ctx context.Context, applicationReviewID externalRef1.ApplicationReviewID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetNonFleetApplicationReviewActionsRequest(c.Server, applicationReviewID)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) ApproveApplicationReviewWithBody(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewApproveApplicationReviewRequestWithBody(c.Server, applicationReviewID, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) ApproveApplicationReview(ctx context.Context, applicationReviewID string, body ApproveApplicationReviewJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewApproveApplicationReviewRequest(c.Server, applicationReviewID, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdateApplicationReviewAssigneeWithBody(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdateApplicationReviewAssigneeRequestWithBody(c.Server, applicationReviewID, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdateApplicationReviewAssignee(ctx context.Context, applicationReviewID string, body UpdateApplicationReviewAssigneeJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdateApplicationReviewAssigneeRequest(c.Server, applicationReviewID, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) CloseApplicationReviewWithBody(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewCloseApplicationReviewRequestWithBody(c.Server, applicationReviewID, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) CloseApplicationReview(ctx context.Context, applicationReviewID string, body CloseApplicationReviewJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewCloseApplicationReviewRequest(c.Server, applicationReviewID, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeclineApplicationReviewWithBody(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeclineApplicationReviewRequestWithBody(c.Server, applicationReviewID, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeclineApplicationReview(ctx context.Context, applicationReviewID string, body DeclineApplicationReviewJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeclineApplicationReviewRequest(c.Server, applicationReviewID, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetApplicationReviewDocuments(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetApplicationReviewDocumentsRequest(c.Server, applicationReviewID)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UploadApplicationReviewDocumentsWithBody(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUploadApplicationReviewDocumentsRequestWithBody(c.Server, applicationReviewID, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdateApplicationReviewDriverWithBody(ctx context.Context, applicationReviewID string, dlNumber string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdateApplicationReviewDriverRequestWithBody(c.Server, applicationReviewID, dlNumber, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdateApplicationReviewDriver(ctx context.Context, applicationReviewID string, dlNumber string, body UpdateApplicationReviewDriverJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdateApplicationReviewDriverRequest(c.Server, applicationReviewID, dlNumber, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetApplicationReviewDrivers(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetApplicationReviewDriversRequest(c.Server, applicationReviewID)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdateApplicationReviewDriversWithBody(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdateApplicationReviewDriversRequestWithBody(c.Server, applicationReviewID, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdateApplicationReviewDrivers(ctx context.Context, applicationReviewID string, body UpdateApplicationReviewDriversJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdateApplicationReviewDriversRequest(c.Server, applicationReviewID, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetApplicationReviewEquipments(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetApplicationReviewEquipmentsRequest(c.Server, applicationReviewID)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdateApplicationReviewEquipmentsWithBody(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdateApplicationReviewEquipmentsRequestWithBody(c.Server, applicationReviewID, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdateApplicationReviewEquipments(ctx context.Context, applicationReviewID string, body UpdateApplicationReviewEquipmentsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdateApplicationReviewEquipmentsRequest(c.Server, applicationReviewID, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetExpressLaneFeedback(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetExpressLaneFeedbackRequest(c.Server, applicationReviewID)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PatchExpressLaneFeedbackWithBody(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPatchExpressLaneFeedbackRequestWithBody(c.Server, applicationReviewID, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PatchExpressLaneFeedback(ctx context.Context, applicationReviewID string, body PatchExpressLaneFeedbackJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPatchExpressLaneFeedbackRequest(c.Server, applicationReviewID, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetApplicationReviewFlags(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetApplicationReviewFlagsRequest(c.Server, applicationReviewID)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetApplicationReviewLosses(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetApplicationReviewLossesRequest(c.Server, applicationReviewID)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdateApplicationReviewLossesWithBody(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdateApplicationReviewLossesRequestWithBody(c.Server, applicationReviewID, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdateApplicationReviewLosses(ctx context.Context, applicationReviewID string, body UpdateApplicationReviewLossesJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdateApplicationReviewLossesRequest(c.Server, applicationReviewID, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetApplicationReviewNotes(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetApplicationReviewNotesRequest(c.Server, applicationReviewID)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdateApplicationReviewNotesWithBody(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdateApplicationReviewNotesRequestWithBody(c.Server, applicationReviewID, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdateApplicationReviewNotes(ctx context.Context, applicationReviewID string, body UpdateApplicationReviewNotesJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdateApplicationReviewNotesRequest(c.Server, applicationReviewID, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetApplicationReviewOperations(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetApplicationReviewOperationsRequest(c.Server, applicationReviewID)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdateApplicationReviewOperationsWithBody(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdateApplicationReviewOperationsRequestWithBody(c.Server, applicationReviewID, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdateApplicationReviewOperations(ctx context.Context, applicationReviewID string, body UpdateApplicationReviewOperationsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdateApplicationReviewOperationsRequest(c.Server, applicationReviewID, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetApplicationReviewsPackages(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetApplicationReviewsPackagesRequest(c.Server, applicationReviewID)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdateApplicationReviewPackagesWithBody(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdateApplicationReviewPackagesRequestWithBody(c.Server, applicationReviewID, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdateApplicationReviewPackages(ctx context.Context, applicationReviewID string, body UpdateApplicationReviewPackagesJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdateApplicationReviewPackagesRequest(c.Server, applicationReviewID, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApplicationReviewQuoteSubmit(ctx context.Context, applicationReviewID string, params *PostApplicationReviewQuoteSubmitParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApplicationReviewQuoteSubmitRequest(c.Server, applicationReviewID, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) ReferApplicationReviewWithBody(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewReferApplicationReviewRequestWithBody(c.Server, applicationReviewID, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) ReferApplicationReview(ctx context.Context, applicationReviewID string, body ReferApplicationReviewJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewReferApplicationReviewRequest(c.Server, applicationReviewID, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) RollbackApplicationReview(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewRollbackApplicationReviewRequest(c.Server, applicationReviewID)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetApplicationReviewSafety(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetApplicationReviewSafetyRequest(c.Server, applicationReviewID)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdateApplicationReviewSafetyWithBody(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdateApplicationReviewSafetyRequestWithBody(c.Server, applicationReviewID, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdateApplicationReviewSafety(ctx context.Context, applicationReviewID string, body UpdateApplicationReviewSafetyJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdateApplicationReviewSafetyRequest(c.Server, applicationReviewID, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetApplicationReviewSafetyScore(ctx context.Context, applicationReviewID externalRef2.ApplicationReviewID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetApplicationReviewSafetyScoreRequest(c.Server, applicationReviewID)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdateApplicationReviewSafetyScoreV2WithBody(ctx context.Context, applicationReviewID externalRef2.ApplicationReviewID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdateApplicationReviewSafetyScoreV2RequestWithBody(c.Server, applicationReviewID, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdateApplicationReviewSafetyScoreV2(ctx context.Context, applicationReviewID externalRef2.ApplicationReviewID, body UpdateApplicationReviewSafetyScoreV2JSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdateApplicationReviewSafetyScoreV2Request(c.Server, applicationReviewID, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) SetApplicationReviewMVRPull(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewSetApplicationReviewMVRPullRequest(c.Server, applicationReviewID)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetApplicationReviewsSummary(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetApplicationReviewsSummaryRequest(c.Server, applicationReviewID)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetApplicationReviewTimeline(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetApplicationReviewTimelineRequest(c.Server, applicationReviewID)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetDriverViolations(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetDriverViolationsRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdateApplicationReviewDriverV2WithBody(ctx context.Context, applicationReviewID string, dlNumber string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdateApplicationReviewDriverV2RequestWithBody(c.Server, applicationReviewID, dlNumber, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdateApplicationReviewDriverV2(ctx context.Context, applicationReviewID string, dlNumber string, body UpdateApplicationReviewDriverV2JSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdateApplicationReviewDriverV2Request(c.Server, applicationReviewID, dlNumber, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetApplicationReviewDriversV2(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetApplicationReviewDriversV2Request(c.Server, applicationReviewID)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetScrapeInfo(ctx context.Context, applicationReviewID string, params *GetScrapeInfoParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetScrapeInfoRequest(c.Server, applicationReviewID, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) TriggerScrape(ctx context.Context, applicationReviewID string, params *TriggerScrapeParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewTriggerScrapeRequest(c.Server, applicationReviewID, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

// NewGetApplicationReviewsWithPaginationRequest generates requests for GetApplicationReviewsWithPagination
func NewGetApplicationReviewsWithPaginationRequest(server string, params *GetApplicationReviewsWithPaginationParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/application_review/list")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Size != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "size", runtime.ParamLocationQuery, *params.Size); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Cursor != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "cursor", runtime.ParamLocationQuery, *params.Cursor); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Q != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "q", runtime.ParamLocationQuery, *params.Q); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.EffectiveDateBefore != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "effectiveDateBefore", runtime.ParamLocationQuery, *params.EffectiveDateBefore); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.EffectiveDateAfter != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "effectiveDateAfter", runtime.ParamLocationQuery, *params.EffectiveDateAfter); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.UnderWriterID != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "underWriterID", runtime.ParamLocationQuery, *params.UnderWriterID); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Tab != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "tab", runtime.ParamLocationQuery, *params.Tab); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetApplicationReviewsRequest generates requests for GetApplicationReviews
func NewGetApplicationReviewsRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/application_reviews")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetNonFleetApplicationReviewActionsRequest generates requests for GetNonFleetApplicationReviewActions
func NewGetNonFleetApplicationReviewActionsRequest(server string, applicationReviewID externalRef1.ApplicationReviewID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/application_reviews/%s/actions", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewApproveApplicationReviewRequest calls the generic ApproveApplicationReview builder with application/json body
func NewApproveApplicationReviewRequest(server string, applicationReviewID string, body ApproveApplicationReviewJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewApproveApplicationReviewRequestWithBody(server, applicationReviewID, "application/json", bodyReader)
}

// NewApproveApplicationReviewRequestWithBody generates requests for ApproveApplicationReview with any type of body
func NewApproveApplicationReviewRequestWithBody(server string, applicationReviewID string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/application_reviews/%s/approve", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewUpdateApplicationReviewAssigneeRequest calls the generic UpdateApplicationReviewAssignee builder with application/json body
func NewUpdateApplicationReviewAssigneeRequest(server string, applicationReviewID string, body UpdateApplicationReviewAssigneeJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewUpdateApplicationReviewAssigneeRequestWithBody(server, applicationReviewID, "application/json", bodyReader)
}

// NewUpdateApplicationReviewAssigneeRequestWithBody generates requests for UpdateApplicationReviewAssignee with any type of body
func NewUpdateApplicationReviewAssigneeRequestWithBody(server string, applicationReviewID string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/application_reviews/%s/assignee", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PATCH", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewCloseApplicationReviewRequest calls the generic CloseApplicationReview builder with application/json body
func NewCloseApplicationReviewRequest(server string, applicationReviewID string, body CloseApplicationReviewJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewCloseApplicationReviewRequestWithBody(server, applicationReviewID, "application/json", bodyReader)
}

// NewCloseApplicationReviewRequestWithBody generates requests for CloseApplicationReview with any type of body
func NewCloseApplicationReviewRequestWithBody(server string, applicationReviewID string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/application_reviews/%s/close", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewDeclineApplicationReviewRequest calls the generic DeclineApplicationReview builder with application/json body
func NewDeclineApplicationReviewRequest(server string, applicationReviewID string, body DeclineApplicationReviewJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewDeclineApplicationReviewRequestWithBody(server, applicationReviewID, "application/json", bodyReader)
}

// NewDeclineApplicationReviewRequestWithBody generates requests for DeclineApplicationReview with any type of body
func NewDeclineApplicationReviewRequestWithBody(server string, applicationReviewID string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/application_reviews/%s/decline", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetApplicationReviewDocumentsRequest generates requests for GetApplicationReviewDocuments
func NewGetApplicationReviewDocumentsRequest(server string, applicationReviewID string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/application_reviews/%s/documents", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewUploadApplicationReviewDocumentsRequestWithBody generates requests for UploadApplicationReviewDocuments with any type of body
func NewUploadApplicationReviewDocumentsRequestWithBody(server string, applicationReviewID string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/application_reviews/%s/documents", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewUpdateApplicationReviewDriverRequest calls the generic UpdateApplicationReviewDriver builder with application/json body
func NewUpdateApplicationReviewDriverRequest(server string, applicationReviewID string, dlNumber string, body UpdateApplicationReviewDriverJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewUpdateApplicationReviewDriverRequestWithBody(server, applicationReviewID, dlNumber, "application/json", bodyReader)
}

// NewUpdateApplicationReviewDriverRequestWithBody generates requests for UpdateApplicationReviewDriver with any type of body
func NewUpdateApplicationReviewDriverRequestWithBody(server string, applicationReviewID string, dlNumber string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "dlNumber", runtime.ParamLocationPath, dlNumber)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/application_reviews/%s/driver/%s", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PATCH", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetApplicationReviewDriversRequest generates requests for GetApplicationReviewDrivers
func NewGetApplicationReviewDriversRequest(server string, applicationReviewID string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/application_reviews/%s/drivers", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewUpdateApplicationReviewDriversRequest calls the generic UpdateApplicationReviewDrivers builder with application/json body
func NewUpdateApplicationReviewDriversRequest(server string, applicationReviewID string, body UpdateApplicationReviewDriversJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewUpdateApplicationReviewDriversRequestWithBody(server, applicationReviewID, "application/json", bodyReader)
}

// NewUpdateApplicationReviewDriversRequestWithBody generates requests for UpdateApplicationReviewDrivers with any type of body
func NewUpdateApplicationReviewDriversRequestWithBody(server string, applicationReviewID string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/application_reviews/%s/drivers", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PATCH", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetApplicationReviewEquipmentsRequest generates requests for GetApplicationReviewEquipments
func NewGetApplicationReviewEquipmentsRequest(server string, applicationReviewID string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/application_reviews/%s/equipments", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewUpdateApplicationReviewEquipmentsRequest calls the generic UpdateApplicationReviewEquipments builder with application/json body
func NewUpdateApplicationReviewEquipmentsRequest(server string, applicationReviewID string, body UpdateApplicationReviewEquipmentsJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewUpdateApplicationReviewEquipmentsRequestWithBody(server, applicationReviewID, "application/json", bodyReader)
}

// NewUpdateApplicationReviewEquipmentsRequestWithBody generates requests for UpdateApplicationReviewEquipments with any type of body
func NewUpdateApplicationReviewEquipmentsRequestWithBody(server string, applicationReviewID string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/application_reviews/%s/equipments", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PATCH", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetExpressLaneFeedbackRequest generates requests for GetExpressLaneFeedback
func NewGetExpressLaneFeedbackRequest(server string, applicationReviewID string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/application_reviews/%s/express_lane_feedback/submit", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPatchExpressLaneFeedbackRequest calls the generic PatchExpressLaneFeedback builder with application/json body
func NewPatchExpressLaneFeedbackRequest(server string, applicationReviewID string, body PatchExpressLaneFeedbackJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPatchExpressLaneFeedbackRequestWithBody(server, applicationReviewID, "application/json", bodyReader)
}

// NewPatchExpressLaneFeedbackRequestWithBody generates requests for PatchExpressLaneFeedback with any type of body
func NewPatchExpressLaneFeedbackRequestWithBody(server string, applicationReviewID string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/application_reviews/%s/express_lane_feedback/submit", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PATCH", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetApplicationReviewFlagsRequest generates requests for GetApplicationReviewFlags
func NewGetApplicationReviewFlagsRequest(server string, applicationReviewID string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/application_reviews/%s/flags", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetApplicationReviewLossesRequest generates requests for GetApplicationReviewLosses
func NewGetApplicationReviewLossesRequest(server string, applicationReviewID string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/application_reviews/%s/losses", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewUpdateApplicationReviewLossesRequest calls the generic UpdateApplicationReviewLosses builder with application/json body
func NewUpdateApplicationReviewLossesRequest(server string, applicationReviewID string, body UpdateApplicationReviewLossesJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewUpdateApplicationReviewLossesRequestWithBody(server, applicationReviewID, "application/json", bodyReader)
}

// NewUpdateApplicationReviewLossesRequestWithBody generates requests for UpdateApplicationReviewLosses with any type of body
func NewUpdateApplicationReviewLossesRequestWithBody(server string, applicationReviewID string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/application_reviews/%s/losses", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PATCH", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetApplicationReviewNotesRequest generates requests for GetApplicationReviewNotes
func NewGetApplicationReviewNotesRequest(server string, applicationReviewID string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/application_reviews/%s/notes", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewUpdateApplicationReviewNotesRequest calls the generic UpdateApplicationReviewNotes builder with application/json body
func NewUpdateApplicationReviewNotesRequest(server string, applicationReviewID string, body UpdateApplicationReviewNotesJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewUpdateApplicationReviewNotesRequestWithBody(server, applicationReviewID, "application/json", bodyReader)
}

// NewUpdateApplicationReviewNotesRequestWithBody generates requests for UpdateApplicationReviewNotes with any type of body
func NewUpdateApplicationReviewNotesRequestWithBody(server string, applicationReviewID string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/application_reviews/%s/notes", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PATCH", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetApplicationReviewOperationsRequest generates requests for GetApplicationReviewOperations
func NewGetApplicationReviewOperationsRequest(server string, applicationReviewID string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/application_reviews/%s/operations", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewUpdateApplicationReviewOperationsRequest calls the generic UpdateApplicationReviewOperations builder with application/json body
func NewUpdateApplicationReviewOperationsRequest(server string, applicationReviewID string, body UpdateApplicationReviewOperationsJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewUpdateApplicationReviewOperationsRequestWithBody(server, applicationReviewID, "application/json", bodyReader)
}

// NewUpdateApplicationReviewOperationsRequestWithBody generates requests for UpdateApplicationReviewOperations with any type of body
func NewUpdateApplicationReviewOperationsRequestWithBody(server string, applicationReviewID string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/application_reviews/%s/operations", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PATCH", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetApplicationReviewsPackagesRequest generates requests for GetApplicationReviewsPackages
func NewGetApplicationReviewsPackagesRequest(server string, applicationReviewID string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/application_reviews/%s/packages", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewUpdateApplicationReviewPackagesRequest calls the generic UpdateApplicationReviewPackages builder with application/json body
func NewUpdateApplicationReviewPackagesRequest(server string, applicationReviewID string, body UpdateApplicationReviewPackagesJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewUpdateApplicationReviewPackagesRequestWithBody(server, applicationReviewID, "application/json", bodyReader)
}

// NewUpdateApplicationReviewPackagesRequestWithBody generates requests for UpdateApplicationReviewPackages with any type of body
func NewUpdateApplicationReviewPackagesRequestWithBody(server string, applicationReviewID string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/application_reviews/%s/packages", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PATCH", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApplicationReviewQuoteSubmitRequest generates requests for PostApplicationReviewQuoteSubmit
func NewPostApplicationReviewQuoteSubmitRequest(server string, applicationReviewID string, params *PostApplicationReviewQuoteSubmitParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/application_reviews/%s/quote/submit", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Bindable != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "bindable", runtime.ParamLocationQuery, *params.Bindable); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewReferApplicationReviewRequest calls the generic ReferApplicationReview builder with application/json body
func NewReferApplicationReviewRequest(server string, applicationReviewID string, body ReferApplicationReviewJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewReferApplicationReviewRequestWithBody(server, applicationReviewID, "application/json", bodyReader)
}

// NewReferApplicationReviewRequestWithBody generates requests for ReferApplicationReview with any type of body
func NewReferApplicationReviewRequestWithBody(server string, applicationReviewID string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/application_reviews/%s/refer", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewRollbackApplicationReviewRequest generates requests for RollbackApplicationReview
func NewRollbackApplicationReviewRequest(server string, applicationReviewID string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/application_reviews/%s/rollback", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetApplicationReviewSafetyRequest generates requests for GetApplicationReviewSafety
func NewGetApplicationReviewSafetyRequest(server string, applicationReviewID string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/application_reviews/%s/safety", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewUpdateApplicationReviewSafetyRequest calls the generic UpdateApplicationReviewSafety builder with application/json body
func NewUpdateApplicationReviewSafetyRequest(server string, applicationReviewID string, body UpdateApplicationReviewSafetyJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewUpdateApplicationReviewSafetyRequestWithBody(server, applicationReviewID, "application/json", bodyReader)
}

// NewUpdateApplicationReviewSafetyRequestWithBody generates requests for UpdateApplicationReviewSafety with any type of body
func NewUpdateApplicationReviewSafetyRequestWithBody(server string, applicationReviewID string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/application_reviews/%s/safety", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PATCH", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetApplicationReviewSafetyScoreRequest generates requests for GetApplicationReviewSafetyScore
func NewGetApplicationReviewSafetyScoreRequest(server string, applicationReviewID externalRef2.ApplicationReviewID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/application_reviews/%s/safety/safety_score", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewUpdateApplicationReviewSafetyScoreV2Request calls the generic UpdateApplicationReviewSafetyScoreV2 builder with application/json body
func NewUpdateApplicationReviewSafetyScoreV2Request(server string, applicationReviewID externalRef2.ApplicationReviewID, body UpdateApplicationReviewSafetyScoreV2JSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewUpdateApplicationReviewSafetyScoreV2RequestWithBody(server, applicationReviewID, "application/json", bodyReader)
}

// NewUpdateApplicationReviewSafetyScoreV2RequestWithBody generates requests for UpdateApplicationReviewSafetyScoreV2 with any type of body
func NewUpdateApplicationReviewSafetyScoreV2RequestWithBody(server string, applicationReviewID externalRef2.ApplicationReviewID, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/application_reviews/%s/safety/safety_score", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PATCH", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewSetApplicationReviewMVRPullRequest generates requests for SetApplicationReviewMVRPull
func NewSetApplicationReviewMVRPullRequest(server string, applicationReviewID string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/application_reviews/%s/set_mvr_pull", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetApplicationReviewsSummaryRequest generates requests for GetApplicationReviewsSummary
func NewGetApplicationReviewsSummaryRequest(server string, applicationReviewID string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/application_reviews/%s/summary", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetApplicationReviewTimelineRequest generates requests for GetApplicationReviewTimeline
func NewGetApplicationReviewTimelineRequest(server string, applicationReviewID string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/application_reviews/%s/timeline", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetDriverViolationsRequest generates requests for GetDriverViolations
func NewGetDriverViolationsRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/driver-violations")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewUpdateApplicationReviewDriverV2Request calls the generic UpdateApplicationReviewDriverV2 builder with application/json body
func NewUpdateApplicationReviewDriverV2Request(server string, applicationReviewID string, dlNumber string, body UpdateApplicationReviewDriverV2JSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewUpdateApplicationReviewDriverV2RequestWithBody(server, applicationReviewID, dlNumber, "application/json", bodyReader)
}

// NewUpdateApplicationReviewDriverV2RequestWithBody generates requests for UpdateApplicationReviewDriverV2 with any type of body
func NewUpdateApplicationReviewDriverV2RequestWithBody(server string, applicationReviewID string, dlNumber string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "dlNumber", runtime.ParamLocationPath, dlNumber)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/v2/application-reviews/%s/driver/%s", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PATCH", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetApplicationReviewDriversV2Request generates requests for GetApplicationReviewDriversV2
func NewGetApplicationReviewDriversV2Request(server string, applicationReviewID string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/v2/application-reviews/%s/drivers", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetScrapeInfoRequest generates requests for GetScrapeInfo
func NewGetScrapeInfoRequest(server string, applicationReviewID string, params *GetScrapeInfoParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/%s/scrape", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "scrapeType", runtime.ParamLocationQuery, params.ScrapeType); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewTriggerScrapeRequest generates requests for TriggerScrape
func NewTriggerScrapeRequest(server string, applicationReviewID string, params *TriggerScrapeParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, applicationReviewID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nonfleet/underwriting/%s/scrape", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "scrapeType", runtime.ParamLocationQuery, params.ScrapeType); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

func (c *Client) applyEditors(ctx context.Context, req *http.Request, additionalEditors []RequestEditorFn) error {
	for _, r := range c.RequestEditors {
		if err := r(ctx, req); err != nil {
			return err
		}
	}
	for _, r := range additionalEditors {
		if err := r(ctx, req); err != nil {
			return err
		}
	}
	return nil
}

// ClientWithResponses builds on ClientInterface to offer response payloads
type ClientWithResponses struct {
	ClientInterface
}

// NewClientWithResponses creates a new ClientWithResponses, which wraps
// Client with return type handling
func NewClientWithResponses(server string, opts ...ClientOption) (*ClientWithResponses, error) {
	client, err := NewClient(server, opts...)
	if err != nil {
		return nil, err
	}
	return &ClientWithResponses{client}, nil
}

// WithBaseURL overrides the baseURL.
func WithBaseURL(baseURL string) ClientOption {
	return func(c *Client) error {
		newBaseURL, err := url.Parse(baseURL)
		if err != nil {
			return err
		}
		c.Server = newBaseURL.String()
		return nil
	}
}

// ClientWithResponsesInterface is the interface specification for the client with responses above.
type ClientWithResponsesInterface interface {
	// GetApplicationReviewsWithPaginationWithResponse request
	GetApplicationReviewsWithPaginationWithResponse(ctx context.Context, params *GetApplicationReviewsWithPaginationParams, reqEditors ...RequestEditorFn) (*GetApplicationReviewsWithPaginationResponse, error)

	// GetApplicationReviewsWithResponse request
	GetApplicationReviewsWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetApplicationReviewsResponse, error)

	// GetNonFleetApplicationReviewActionsWithResponse request
	GetNonFleetApplicationReviewActionsWithResponse(ctx context.Context, applicationReviewID externalRef1.ApplicationReviewID, reqEditors ...RequestEditorFn) (*GetNonFleetApplicationReviewActionsResponse, error)

	// ApproveApplicationReviewWithBodyWithResponse request with any body
	ApproveApplicationReviewWithBodyWithResponse(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*ApproveApplicationReviewResponse, error)

	ApproveApplicationReviewWithResponse(ctx context.Context, applicationReviewID string, body ApproveApplicationReviewJSONRequestBody, reqEditors ...RequestEditorFn) (*ApproveApplicationReviewResponse, error)

	// UpdateApplicationReviewAssigneeWithBodyWithResponse request with any body
	UpdateApplicationReviewAssigneeWithBodyWithResponse(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewAssigneeResponse, error)

	UpdateApplicationReviewAssigneeWithResponse(ctx context.Context, applicationReviewID string, body UpdateApplicationReviewAssigneeJSONRequestBody, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewAssigneeResponse, error)

	// CloseApplicationReviewWithBodyWithResponse request with any body
	CloseApplicationReviewWithBodyWithResponse(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*CloseApplicationReviewResponse, error)

	CloseApplicationReviewWithResponse(ctx context.Context, applicationReviewID string, body CloseApplicationReviewJSONRequestBody, reqEditors ...RequestEditorFn) (*CloseApplicationReviewResponse, error)

	// DeclineApplicationReviewWithBodyWithResponse request with any body
	DeclineApplicationReviewWithBodyWithResponse(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*DeclineApplicationReviewResponse, error)

	DeclineApplicationReviewWithResponse(ctx context.Context, applicationReviewID string, body DeclineApplicationReviewJSONRequestBody, reqEditors ...RequestEditorFn) (*DeclineApplicationReviewResponse, error)

	// GetApplicationReviewDocumentsWithResponse request
	GetApplicationReviewDocumentsWithResponse(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*GetApplicationReviewDocumentsResponse, error)

	// UploadApplicationReviewDocumentsWithBodyWithResponse request with any body
	UploadApplicationReviewDocumentsWithBodyWithResponse(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UploadApplicationReviewDocumentsResponse, error)

	// UpdateApplicationReviewDriverWithBodyWithResponse request with any body
	UpdateApplicationReviewDriverWithBodyWithResponse(ctx context.Context, applicationReviewID string, dlNumber string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewDriverResponse, error)

	UpdateApplicationReviewDriverWithResponse(ctx context.Context, applicationReviewID string, dlNumber string, body UpdateApplicationReviewDriverJSONRequestBody, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewDriverResponse, error)

	// GetApplicationReviewDriversWithResponse request
	GetApplicationReviewDriversWithResponse(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*GetApplicationReviewDriversResponse, error)

	// UpdateApplicationReviewDriversWithBodyWithResponse request with any body
	UpdateApplicationReviewDriversWithBodyWithResponse(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewDriversResponse, error)

	UpdateApplicationReviewDriversWithResponse(ctx context.Context, applicationReviewID string, body UpdateApplicationReviewDriversJSONRequestBody, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewDriversResponse, error)

	// GetApplicationReviewEquipmentsWithResponse request
	GetApplicationReviewEquipmentsWithResponse(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*GetApplicationReviewEquipmentsResponse, error)

	// UpdateApplicationReviewEquipmentsWithBodyWithResponse request with any body
	UpdateApplicationReviewEquipmentsWithBodyWithResponse(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewEquipmentsResponse, error)

	UpdateApplicationReviewEquipmentsWithResponse(ctx context.Context, applicationReviewID string, body UpdateApplicationReviewEquipmentsJSONRequestBody, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewEquipmentsResponse, error)

	// GetExpressLaneFeedbackWithResponse request
	GetExpressLaneFeedbackWithResponse(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*GetExpressLaneFeedbackResponse, error)

	// PatchExpressLaneFeedbackWithBodyWithResponse request with any body
	PatchExpressLaneFeedbackWithBodyWithResponse(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PatchExpressLaneFeedbackResponse, error)

	PatchExpressLaneFeedbackWithResponse(ctx context.Context, applicationReviewID string, body PatchExpressLaneFeedbackJSONRequestBody, reqEditors ...RequestEditorFn) (*PatchExpressLaneFeedbackResponse, error)

	// GetApplicationReviewFlagsWithResponse request
	GetApplicationReviewFlagsWithResponse(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*GetApplicationReviewFlagsResponse, error)

	// GetApplicationReviewLossesWithResponse request
	GetApplicationReviewLossesWithResponse(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*GetApplicationReviewLossesResponse, error)

	// UpdateApplicationReviewLossesWithBodyWithResponse request with any body
	UpdateApplicationReviewLossesWithBodyWithResponse(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewLossesResponse, error)

	UpdateApplicationReviewLossesWithResponse(ctx context.Context, applicationReviewID string, body UpdateApplicationReviewLossesJSONRequestBody, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewLossesResponse, error)

	// GetApplicationReviewNotesWithResponse request
	GetApplicationReviewNotesWithResponse(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*GetApplicationReviewNotesResponse, error)

	// UpdateApplicationReviewNotesWithBodyWithResponse request with any body
	UpdateApplicationReviewNotesWithBodyWithResponse(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewNotesResponse, error)

	UpdateApplicationReviewNotesWithResponse(ctx context.Context, applicationReviewID string, body UpdateApplicationReviewNotesJSONRequestBody, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewNotesResponse, error)

	// GetApplicationReviewOperationsWithResponse request
	GetApplicationReviewOperationsWithResponse(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*GetApplicationReviewOperationsResponse, error)

	// UpdateApplicationReviewOperationsWithBodyWithResponse request with any body
	UpdateApplicationReviewOperationsWithBodyWithResponse(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewOperationsResponse, error)

	UpdateApplicationReviewOperationsWithResponse(ctx context.Context, applicationReviewID string, body UpdateApplicationReviewOperationsJSONRequestBody, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewOperationsResponse, error)

	// GetApplicationReviewsPackagesWithResponse request
	GetApplicationReviewsPackagesWithResponse(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*GetApplicationReviewsPackagesResponse, error)

	// UpdateApplicationReviewPackagesWithBodyWithResponse request with any body
	UpdateApplicationReviewPackagesWithBodyWithResponse(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewPackagesResponse, error)

	UpdateApplicationReviewPackagesWithResponse(ctx context.Context, applicationReviewID string, body UpdateApplicationReviewPackagesJSONRequestBody, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewPackagesResponse, error)

	// PostApplicationReviewQuoteSubmitWithResponse request
	PostApplicationReviewQuoteSubmitWithResponse(ctx context.Context, applicationReviewID string, params *PostApplicationReviewQuoteSubmitParams, reqEditors ...RequestEditorFn) (*PostApplicationReviewQuoteSubmitResponse, error)

	// ReferApplicationReviewWithBodyWithResponse request with any body
	ReferApplicationReviewWithBodyWithResponse(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*ReferApplicationReviewResponse, error)

	ReferApplicationReviewWithResponse(ctx context.Context, applicationReviewID string, body ReferApplicationReviewJSONRequestBody, reqEditors ...RequestEditorFn) (*ReferApplicationReviewResponse, error)

	// RollbackApplicationReviewWithResponse request
	RollbackApplicationReviewWithResponse(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*RollbackApplicationReviewResponse, error)

	// GetApplicationReviewSafetyWithResponse request
	GetApplicationReviewSafetyWithResponse(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*GetApplicationReviewSafetyResponse, error)

	// UpdateApplicationReviewSafetyWithBodyWithResponse request with any body
	UpdateApplicationReviewSafetyWithBodyWithResponse(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewSafetyResponse, error)

	UpdateApplicationReviewSafetyWithResponse(ctx context.Context, applicationReviewID string, body UpdateApplicationReviewSafetyJSONRequestBody, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewSafetyResponse, error)

	// GetApplicationReviewSafetyScoreWithResponse request
	GetApplicationReviewSafetyScoreWithResponse(ctx context.Context, applicationReviewID externalRef2.ApplicationReviewID, reqEditors ...RequestEditorFn) (*GetApplicationReviewSafetyScoreResponse, error)

	// UpdateApplicationReviewSafetyScoreV2WithBodyWithResponse request with any body
	UpdateApplicationReviewSafetyScoreV2WithBodyWithResponse(ctx context.Context, applicationReviewID externalRef2.ApplicationReviewID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewSafetyScoreV2Response, error)

	UpdateApplicationReviewSafetyScoreV2WithResponse(ctx context.Context, applicationReviewID externalRef2.ApplicationReviewID, body UpdateApplicationReviewSafetyScoreV2JSONRequestBody, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewSafetyScoreV2Response, error)

	// SetApplicationReviewMVRPullWithResponse request
	SetApplicationReviewMVRPullWithResponse(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*SetApplicationReviewMVRPullResponse, error)

	// GetApplicationReviewsSummaryWithResponse request
	GetApplicationReviewsSummaryWithResponse(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*GetApplicationReviewsSummaryResponse, error)

	// GetApplicationReviewTimelineWithResponse request
	GetApplicationReviewTimelineWithResponse(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*GetApplicationReviewTimelineResponse, error)

	// GetDriverViolationsWithResponse request
	GetDriverViolationsWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetDriverViolationsResponse, error)

	// UpdateApplicationReviewDriverV2WithBodyWithResponse request with any body
	UpdateApplicationReviewDriverV2WithBodyWithResponse(ctx context.Context, applicationReviewID string, dlNumber string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewDriverV2Response, error)

	UpdateApplicationReviewDriverV2WithResponse(ctx context.Context, applicationReviewID string, dlNumber string, body UpdateApplicationReviewDriverV2JSONRequestBody, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewDriverV2Response, error)

	// GetApplicationReviewDriversV2WithResponse request
	GetApplicationReviewDriversV2WithResponse(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*GetApplicationReviewDriversV2Response, error)

	// GetScrapeInfoWithResponse request
	GetScrapeInfoWithResponse(ctx context.Context, applicationReviewID string, params *GetScrapeInfoParams, reqEditors ...RequestEditorFn) (*GetScrapeInfoResponse, error)

	// TriggerScrapeWithResponse request
	TriggerScrapeWithResponse(ctx context.Context, applicationReviewID string, params *TriggerScrapeParams, reqEditors ...RequestEditorFn) (*TriggerScrapeResponse, error)
}

type GetApplicationReviewsWithPaginationResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *externalRef1.ApplicationReviewsWithPaginationResponse
	JSON422      *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r GetApplicationReviewsWithPaginationResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetApplicationReviewsWithPaginationResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetApplicationReviewsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *[]externalRef1.ApplicationReviewDetails
	JSON422      *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r GetApplicationReviewsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetApplicationReviewsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetNonFleetApplicationReviewActionsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *externalRef1.ApplicationReviewGetActionsResponse
	JSON404      *externalRef0.ErrorMessage
	JSONDefault  *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r GetNonFleetApplicationReviewActionsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetNonFleetApplicationReviewActionsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type ApproveApplicationReviewResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON422      *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r ApproveApplicationReviewResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r ApproveApplicationReviewResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type UpdateApplicationReviewAssigneeResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON422      *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r UpdateApplicationReviewAssigneeResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r UpdateApplicationReviewAssigneeResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type CloseApplicationReviewResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON422      *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r CloseApplicationReviewResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r CloseApplicationReviewResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeclineApplicationReviewResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON422      *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r DeclineApplicationReviewResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeclineApplicationReviewResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetApplicationReviewDocumentsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *externalRef1.ApplicationReviewDocuments
	JSONDefault  *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r GetApplicationReviewDocumentsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetApplicationReviewDocumentsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type UploadApplicationReviewDocumentsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *externalRef0.FileHandle
	JSONDefault  *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r UploadApplicationReviewDocumentsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r UploadApplicationReviewDocumentsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type UpdateApplicationReviewDriverResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSONDefault  *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r UpdateApplicationReviewDriverResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r UpdateApplicationReviewDriverResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetApplicationReviewDriversResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *externalRef1.ApplicationReviewDriver
	JSON422      *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r GetApplicationReviewDriversResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetApplicationReviewDriversResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type UpdateApplicationReviewDriversResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSONDefault  *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r UpdateApplicationReviewDriversResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r UpdateApplicationReviewDriversResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetApplicationReviewEquipmentsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *externalRef1.ApplicationReviewEquipment
	JSON422      *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r GetApplicationReviewEquipmentsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetApplicationReviewEquipmentsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type UpdateApplicationReviewEquipmentsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSONDefault  *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r UpdateApplicationReviewEquipmentsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r UpdateApplicationReviewEquipmentsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetExpressLaneFeedbackResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *externalRef1.ExpressLaneFeedbackGetResponse
	JSON404      *externalRef0.ErrorMessage
	JSONDefault  *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r GetExpressLaneFeedbackResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetExpressLaneFeedbackResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PatchExpressLaneFeedbackResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *externalRef1.ExpressLaneFeedbackPatchResponse
	JSON404      *externalRef0.ErrorMessage
	JSONDefault  *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r PatchExpressLaneFeedbackResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PatchExpressLaneFeedbackResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetApplicationReviewFlagsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *[]externalRef1.ApplicationReviewFlag
	JSON422      *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r GetApplicationReviewFlagsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetApplicationReviewFlagsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetApplicationReviewLossesResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *externalRef1.ApplicationReviewLosses
	JSON422      *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r GetApplicationReviewLossesResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetApplicationReviewLossesResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type UpdateApplicationReviewLossesResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSONDefault  *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r UpdateApplicationReviewLossesResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r UpdateApplicationReviewLossesResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetApplicationReviewNotesResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *externalRef1.ApplicationReviewNotes
	JSONDefault  *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r GetApplicationReviewNotesResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetApplicationReviewNotesResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type UpdateApplicationReviewNotesResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSONDefault  *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r UpdateApplicationReviewNotesResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r UpdateApplicationReviewNotesResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetApplicationReviewOperationsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *externalRef1.ApplicationReviewOperation
	JSON422      *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r GetApplicationReviewOperationsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetApplicationReviewOperationsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type UpdateApplicationReviewOperationsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSONDefault  *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r UpdateApplicationReviewOperationsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r UpdateApplicationReviewOperationsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetApplicationReviewsPackagesResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *externalRef1.ApplicationReviewPackages
	JSON422      *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r GetApplicationReviewsPackagesResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetApplicationReviewsPackagesResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type UpdateApplicationReviewPackagesResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSONDefault  *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r UpdateApplicationReviewPackagesResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r UpdateApplicationReviewPackagesResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApplicationReviewQuoteSubmitResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON422      *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r PostApplicationReviewQuoteSubmitResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApplicationReviewQuoteSubmitResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type ReferApplicationReviewResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON422      *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r ReferApplicationReviewResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r ReferApplicationReviewResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type RollbackApplicationReviewResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON422      *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r RollbackApplicationReviewResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r RollbackApplicationReviewResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetApplicationReviewSafetyResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *externalRef1.ApplicationReviewSafety
	JSON422      *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r GetApplicationReviewSafetyResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetApplicationReviewSafetyResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type UpdateApplicationReviewSafetyResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSONDefault  *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r UpdateApplicationReviewSafetyResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r UpdateApplicationReviewSafetyResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetApplicationReviewSafetyScoreResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *externalRef2.ApplicationReviewSafetyScoreV2
	JSONDefault  *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r GetApplicationReviewSafetyScoreResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetApplicationReviewSafetyScoreResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type UpdateApplicationReviewSafetyScoreV2Response struct {
	Body         []byte
	HTTPResponse *http.Response
	JSONDefault  *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r UpdateApplicationReviewSafetyScoreV2Response) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r UpdateApplicationReviewSafetyScoreV2Response) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type SetApplicationReviewMVRPullResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *externalRef0.MVRFlag
	JSON422      *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r SetApplicationReviewMVRPullResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r SetApplicationReviewMVRPullResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetApplicationReviewsSummaryResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *externalRef1.ApplicationReviewSummary
	JSON422      *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r GetApplicationReviewsSummaryResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetApplicationReviewsSummaryResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetApplicationReviewTimelineResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *externalRef1.ApplicationReviewTimeline
	JSON422      *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r GetApplicationReviewTimelineResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetApplicationReviewTimelineResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetDriverViolationsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *[]externalRef0.DriverViolation
	JSON422      *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r GetDriverViolationsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetDriverViolationsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type UpdateApplicationReviewDriverV2Response struct {
	Body         []byte
	HTTPResponse *http.Response
	JSONDefault  *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r UpdateApplicationReviewDriverV2Response) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r UpdateApplicationReviewDriverV2Response) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetApplicationReviewDriversV2Response struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *externalRef1.ApplicationReviewDriver
	JSON422      *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r GetApplicationReviewDriversV2Response) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetApplicationReviewDriversV2Response) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetScrapeInfoResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *externalRef1.ScrapeResponse
	JSON422      *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r GetScrapeInfoResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetScrapeInfoResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type TriggerScrapeResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON422      *externalRef0.ErrorMessage
}

// Status returns HTTPResponse.Status
func (r TriggerScrapeResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r TriggerScrapeResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

// GetApplicationReviewsWithPaginationWithResponse request returning *GetApplicationReviewsWithPaginationResponse
func (c *ClientWithResponses) GetApplicationReviewsWithPaginationWithResponse(ctx context.Context, params *GetApplicationReviewsWithPaginationParams, reqEditors ...RequestEditorFn) (*GetApplicationReviewsWithPaginationResponse, error) {
	rsp, err := c.GetApplicationReviewsWithPagination(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetApplicationReviewsWithPaginationResponse(rsp)
}

// GetApplicationReviewsWithResponse request returning *GetApplicationReviewsResponse
func (c *ClientWithResponses) GetApplicationReviewsWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetApplicationReviewsResponse, error) {
	rsp, err := c.GetApplicationReviews(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetApplicationReviewsResponse(rsp)
}

// GetNonFleetApplicationReviewActionsWithResponse request returning *GetNonFleetApplicationReviewActionsResponse
func (c *ClientWithResponses) GetNonFleetApplicationReviewActionsWithResponse(ctx context.Context, applicationReviewID externalRef1.ApplicationReviewID, reqEditors ...RequestEditorFn) (*GetNonFleetApplicationReviewActionsResponse, error) {
	rsp, err := c.GetNonFleetApplicationReviewActions(ctx, applicationReviewID, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetNonFleetApplicationReviewActionsResponse(rsp)
}

// ApproveApplicationReviewWithBodyWithResponse request with arbitrary body returning *ApproveApplicationReviewResponse
func (c *ClientWithResponses) ApproveApplicationReviewWithBodyWithResponse(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*ApproveApplicationReviewResponse, error) {
	rsp, err := c.ApproveApplicationReviewWithBody(ctx, applicationReviewID, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseApproveApplicationReviewResponse(rsp)
}

func (c *ClientWithResponses) ApproveApplicationReviewWithResponse(ctx context.Context, applicationReviewID string, body ApproveApplicationReviewJSONRequestBody, reqEditors ...RequestEditorFn) (*ApproveApplicationReviewResponse, error) {
	rsp, err := c.ApproveApplicationReview(ctx, applicationReviewID, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseApproveApplicationReviewResponse(rsp)
}

// UpdateApplicationReviewAssigneeWithBodyWithResponse request with arbitrary body returning *UpdateApplicationReviewAssigneeResponse
func (c *ClientWithResponses) UpdateApplicationReviewAssigneeWithBodyWithResponse(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewAssigneeResponse, error) {
	rsp, err := c.UpdateApplicationReviewAssigneeWithBody(ctx, applicationReviewID, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdateApplicationReviewAssigneeResponse(rsp)
}

func (c *ClientWithResponses) UpdateApplicationReviewAssigneeWithResponse(ctx context.Context, applicationReviewID string, body UpdateApplicationReviewAssigneeJSONRequestBody, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewAssigneeResponse, error) {
	rsp, err := c.UpdateApplicationReviewAssignee(ctx, applicationReviewID, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdateApplicationReviewAssigneeResponse(rsp)
}

// CloseApplicationReviewWithBodyWithResponse request with arbitrary body returning *CloseApplicationReviewResponse
func (c *ClientWithResponses) CloseApplicationReviewWithBodyWithResponse(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*CloseApplicationReviewResponse, error) {
	rsp, err := c.CloseApplicationReviewWithBody(ctx, applicationReviewID, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseCloseApplicationReviewResponse(rsp)
}

func (c *ClientWithResponses) CloseApplicationReviewWithResponse(ctx context.Context, applicationReviewID string, body CloseApplicationReviewJSONRequestBody, reqEditors ...RequestEditorFn) (*CloseApplicationReviewResponse, error) {
	rsp, err := c.CloseApplicationReview(ctx, applicationReviewID, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseCloseApplicationReviewResponse(rsp)
}

// DeclineApplicationReviewWithBodyWithResponse request with arbitrary body returning *DeclineApplicationReviewResponse
func (c *ClientWithResponses) DeclineApplicationReviewWithBodyWithResponse(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*DeclineApplicationReviewResponse, error) {
	rsp, err := c.DeclineApplicationReviewWithBody(ctx, applicationReviewID, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeclineApplicationReviewResponse(rsp)
}

func (c *ClientWithResponses) DeclineApplicationReviewWithResponse(ctx context.Context, applicationReviewID string, body DeclineApplicationReviewJSONRequestBody, reqEditors ...RequestEditorFn) (*DeclineApplicationReviewResponse, error) {
	rsp, err := c.DeclineApplicationReview(ctx, applicationReviewID, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeclineApplicationReviewResponse(rsp)
}

// GetApplicationReviewDocumentsWithResponse request returning *GetApplicationReviewDocumentsResponse
func (c *ClientWithResponses) GetApplicationReviewDocumentsWithResponse(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*GetApplicationReviewDocumentsResponse, error) {
	rsp, err := c.GetApplicationReviewDocuments(ctx, applicationReviewID, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetApplicationReviewDocumentsResponse(rsp)
}

// UploadApplicationReviewDocumentsWithBodyWithResponse request with arbitrary body returning *UploadApplicationReviewDocumentsResponse
func (c *ClientWithResponses) UploadApplicationReviewDocumentsWithBodyWithResponse(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UploadApplicationReviewDocumentsResponse, error) {
	rsp, err := c.UploadApplicationReviewDocumentsWithBody(ctx, applicationReviewID, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUploadApplicationReviewDocumentsResponse(rsp)
}

// UpdateApplicationReviewDriverWithBodyWithResponse request with arbitrary body returning *UpdateApplicationReviewDriverResponse
func (c *ClientWithResponses) UpdateApplicationReviewDriverWithBodyWithResponse(ctx context.Context, applicationReviewID string, dlNumber string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewDriverResponse, error) {
	rsp, err := c.UpdateApplicationReviewDriverWithBody(ctx, applicationReviewID, dlNumber, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdateApplicationReviewDriverResponse(rsp)
}

func (c *ClientWithResponses) UpdateApplicationReviewDriverWithResponse(ctx context.Context, applicationReviewID string, dlNumber string, body UpdateApplicationReviewDriverJSONRequestBody, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewDriverResponse, error) {
	rsp, err := c.UpdateApplicationReviewDriver(ctx, applicationReviewID, dlNumber, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdateApplicationReviewDriverResponse(rsp)
}

// GetApplicationReviewDriversWithResponse request returning *GetApplicationReviewDriversResponse
func (c *ClientWithResponses) GetApplicationReviewDriversWithResponse(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*GetApplicationReviewDriversResponse, error) {
	rsp, err := c.GetApplicationReviewDrivers(ctx, applicationReviewID, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetApplicationReviewDriversResponse(rsp)
}

// UpdateApplicationReviewDriversWithBodyWithResponse request with arbitrary body returning *UpdateApplicationReviewDriversResponse
func (c *ClientWithResponses) UpdateApplicationReviewDriversWithBodyWithResponse(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewDriversResponse, error) {
	rsp, err := c.UpdateApplicationReviewDriversWithBody(ctx, applicationReviewID, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdateApplicationReviewDriversResponse(rsp)
}

func (c *ClientWithResponses) UpdateApplicationReviewDriversWithResponse(ctx context.Context, applicationReviewID string, body UpdateApplicationReviewDriversJSONRequestBody, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewDriversResponse, error) {
	rsp, err := c.UpdateApplicationReviewDrivers(ctx, applicationReviewID, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdateApplicationReviewDriversResponse(rsp)
}

// GetApplicationReviewEquipmentsWithResponse request returning *GetApplicationReviewEquipmentsResponse
func (c *ClientWithResponses) GetApplicationReviewEquipmentsWithResponse(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*GetApplicationReviewEquipmentsResponse, error) {
	rsp, err := c.GetApplicationReviewEquipments(ctx, applicationReviewID, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetApplicationReviewEquipmentsResponse(rsp)
}

// UpdateApplicationReviewEquipmentsWithBodyWithResponse request with arbitrary body returning *UpdateApplicationReviewEquipmentsResponse
func (c *ClientWithResponses) UpdateApplicationReviewEquipmentsWithBodyWithResponse(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewEquipmentsResponse, error) {
	rsp, err := c.UpdateApplicationReviewEquipmentsWithBody(ctx, applicationReviewID, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdateApplicationReviewEquipmentsResponse(rsp)
}

func (c *ClientWithResponses) UpdateApplicationReviewEquipmentsWithResponse(ctx context.Context, applicationReviewID string, body UpdateApplicationReviewEquipmentsJSONRequestBody, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewEquipmentsResponse, error) {
	rsp, err := c.UpdateApplicationReviewEquipments(ctx, applicationReviewID, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdateApplicationReviewEquipmentsResponse(rsp)
}

// GetExpressLaneFeedbackWithResponse request returning *GetExpressLaneFeedbackResponse
func (c *ClientWithResponses) GetExpressLaneFeedbackWithResponse(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*GetExpressLaneFeedbackResponse, error) {
	rsp, err := c.GetExpressLaneFeedback(ctx, applicationReviewID, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetExpressLaneFeedbackResponse(rsp)
}

// PatchExpressLaneFeedbackWithBodyWithResponse request with arbitrary body returning *PatchExpressLaneFeedbackResponse
func (c *ClientWithResponses) PatchExpressLaneFeedbackWithBodyWithResponse(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PatchExpressLaneFeedbackResponse, error) {
	rsp, err := c.PatchExpressLaneFeedbackWithBody(ctx, applicationReviewID, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePatchExpressLaneFeedbackResponse(rsp)
}

func (c *ClientWithResponses) PatchExpressLaneFeedbackWithResponse(ctx context.Context, applicationReviewID string, body PatchExpressLaneFeedbackJSONRequestBody, reqEditors ...RequestEditorFn) (*PatchExpressLaneFeedbackResponse, error) {
	rsp, err := c.PatchExpressLaneFeedback(ctx, applicationReviewID, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePatchExpressLaneFeedbackResponse(rsp)
}

// GetApplicationReviewFlagsWithResponse request returning *GetApplicationReviewFlagsResponse
func (c *ClientWithResponses) GetApplicationReviewFlagsWithResponse(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*GetApplicationReviewFlagsResponse, error) {
	rsp, err := c.GetApplicationReviewFlags(ctx, applicationReviewID, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetApplicationReviewFlagsResponse(rsp)
}

// GetApplicationReviewLossesWithResponse request returning *GetApplicationReviewLossesResponse
func (c *ClientWithResponses) GetApplicationReviewLossesWithResponse(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*GetApplicationReviewLossesResponse, error) {
	rsp, err := c.GetApplicationReviewLosses(ctx, applicationReviewID, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetApplicationReviewLossesResponse(rsp)
}

// UpdateApplicationReviewLossesWithBodyWithResponse request with arbitrary body returning *UpdateApplicationReviewLossesResponse
func (c *ClientWithResponses) UpdateApplicationReviewLossesWithBodyWithResponse(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewLossesResponse, error) {
	rsp, err := c.UpdateApplicationReviewLossesWithBody(ctx, applicationReviewID, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdateApplicationReviewLossesResponse(rsp)
}

func (c *ClientWithResponses) UpdateApplicationReviewLossesWithResponse(ctx context.Context, applicationReviewID string, body UpdateApplicationReviewLossesJSONRequestBody, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewLossesResponse, error) {
	rsp, err := c.UpdateApplicationReviewLosses(ctx, applicationReviewID, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdateApplicationReviewLossesResponse(rsp)
}

// GetApplicationReviewNotesWithResponse request returning *GetApplicationReviewNotesResponse
func (c *ClientWithResponses) GetApplicationReviewNotesWithResponse(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*GetApplicationReviewNotesResponse, error) {
	rsp, err := c.GetApplicationReviewNotes(ctx, applicationReviewID, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetApplicationReviewNotesResponse(rsp)
}

// UpdateApplicationReviewNotesWithBodyWithResponse request with arbitrary body returning *UpdateApplicationReviewNotesResponse
func (c *ClientWithResponses) UpdateApplicationReviewNotesWithBodyWithResponse(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewNotesResponse, error) {
	rsp, err := c.UpdateApplicationReviewNotesWithBody(ctx, applicationReviewID, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdateApplicationReviewNotesResponse(rsp)
}

func (c *ClientWithResponses) UpdateApplicationReviewNotesWithResponse(ctx context.Context, applicationReviewID string, body UpdateApplicationReviewNotesJSONRequestBody, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewNotesResponse, error) {
	rsp, err := c.UpdateApplicationReviewNotes(ctx, applicationReviewID, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdateApplicationReviewNotesResponse(rsp)
}

// GetApplicationReviewOperationsWithResponse request returning *GetApplicationReviewOperationsResponse
func (c *ClientWithResponses) GetApplicationReviewOperationsWithResponse(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*GetApplicationReviewOperationsResponse, error) {
	rsp, err := c.GetApplicationReviewOperations(ctx, applicationReviewID, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetApplicationReviewOperationsResponse(rsp)
}

// UpdateApplicationReviewOperationsWithBodyWithResponse request with arbitrary body returning *UpdateApplicationReviewOperationsResponse
func (c *ClientWithResponses) UpdateApplicationReviewOperationsWithBodyWithResponse(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewOperationsResponse, error) {
	rsp, err := c.UpdateApplicationReviewOperationsWithBody(ctx, applicationReviewID, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdateApplicationReviewOperationsResponse(rsp)
}

func (c *ClientWithResponses) UpdateApplicationReviewOperationsWithResponse(ctx context.Context, applicationReviewID string, body UpdateApplicationReviewOperationsJSONRequestBody, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewOperationsResponse, error) {
	rsp, err := c.UpdateApplicationReviewOperations(ctx, applicationReviewID, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdateApplicationReviewOperationsResponse(rsp)
}

// GetApplicationReviewsPackagesWithResponse request returning *GetApplicationReviewsPackagesResponse
func (c *ClientWithResponses) GetApplicationReviewsPackagesWithResponse(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*GetApplicationReviewsPackagesResponse, error) {
	rsp, err := c.GetApplicationReviewsPackages(ctx, applicationReviewID, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetApplicationReviewsPackagesResponse(rsp)
}

// UpdateApplicationReviewPackagesWithBodyWithResponse request with arbitrary body returning *UpdateApplicationReviewPackagesResponse
func (c *ClientWithResponses) UpdateApplicationReviewPackagesWithBodyWithResponse(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewPackagesResponse, error) {
	rsp, err := c.UpdateApplicationReviewPackagesWithBody(ctx, applicationReviewID, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdateApplicationReviewPackagesResponse(rsp)
}

func (c *ClientWithResponses) UpdateApplicationReviewPackagesWithResponse(ctx context.Context, applicationReviewID string, body UpdateApplicationReviewPackagesJSONRequestBody, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewPackagesResponse, error) {
	rsp, err := c.UpdateApplicationReviewPackages(ctx, applicationReviewID, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdateApplicationReviewPackagesResponse(rsp)
}

// PostApplicationReviewQuoteSubmitWithResponse request returning *PostApplicationReviewQuoteSubmitResponse
func (c *ClientWithResponses) PostApplicationReviewQuoteSubmitWithResponse(ctx context.Context, applicationReviewID string, params *PostApplicationReviewQuoteSubmitParams, reqEditors ...RequestEditorFn) (*PostApplicationReviewQuoteSubmitResponse, error) {
	rsp, err := c.PostApplicationReviewQuoteSubmit(ctx, applicationReviewID, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApplicationReviewQuoteSubmitResponse(rsp)
}

// ReferApplicationReviewWithBodyWithResponse request with arbitrary body returning *ReferApplicationReviewResponse
func (c *ClientWithResponses) ReferApplicationReviewWithBodyWithResponse(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*ReferApplicationReviewResponse, error) {
	rsp, err := c.ReferApplicationReviewWithBody(ctx, applicationReviewID, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseReferApplicationReviewResponse(rsp)
}

func (c *ClientWithResponses) ReferApplicationReviewWithResponse(ctx context.Context, applicationReviewID string, body ReferApplicationReviewJSONRequestBody, reqEditors ...RequestEditorFn) (*ReferApplicationReviewResponse, error) {
	rsp, err := c.ReferApplicationReview(ctx, applicationReviewID, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseReferApplicationReviewResponse(rsp)
}

// RollbackApplicationReviewWithResponse request returning *RollbackApplicationReviewResponse
func (c *ClientWithResponses) RollbackApplicationReviewWithResponse(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*RollbackApplicationReviewResponse, error) {
	rsp, err := c.RollbackApplicationReview(ctx, applicationReviewID, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseRollbackApplicationReviewResponse(rsp)
}

// GetApplicationReviewSafetyWithResponse request returning *GetApplicationReviewSafetyResponse
func (c *ClientWithResponses) GetApplicationReviewSafetyWithResponse(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*GetApplicationReviewSafetyResponse, error) {
	rsp, err := c.GetApplicationReviewSafety(ctx, applicationReviewID, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetApplicationReviewSafetyResponse(rsp)
}

// UpdateApplicationReviewSafetyWithBodyWithResponse request with arbitrary body returning *UpdateApplicationReviewSafetyResponse
func (c *ClientWithResponses) UpdateApplicationReviewSafetyWithBodyWithResponse(ctx context.Context, applicationReviewID string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewSafetyResponse, error) {
	rsp, err := c.UpdateApplicationReviewSafetyWithBody(ctx, applicationReviewID, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdateApplicationReviewSafetyResponse(rsp)
}

func (c *ClientWithResponses) UpdateApplicationReviewSafetyWithResponse(ctx context.Context, applicationReviewID string, body UpdateApplicationReviewSafetyJSONRequestBody, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewSafetyResponse, error) {
	rsp, err := c.UpdateApplicationReviewSafety(ctx, applicationReviewID, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdateApplicationReviewSafetyResponse(rsp)
}

// GetApplicationReviewSafetyScoreWithResponse request returning *GetApplicationReviewSafetyScoreResponse
func (c *ClientWithResponses) GetApplicationReviewSafetyScoreWithResponse(ctx context.Context, applicationReviewID externalRef2.ApplicationReviewID, reqEditors ...RequestEditorFn) (*GetApplicationReviewSafetyScoreResponse, error) {
	rsp, err := c.GetApplicationReviewSafetyScore(ctx, applicationReviewID, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetApplicationReviewSafetyScoreResponse(rsp)
}

// UpdateApplicationReviewSafetyScoreV2WithBodyWithResponse request with arbitrary body returning *UpdateApplicationReviewSafetyScoreV2Response
func (c *ClientWithResponses) UpdateApplicationReviewSafetyScoreV2WithBodyWithResponse(ctx context.Context, applicationReviewID externalRef2.ApplicationReviewID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewSafetyScoreV2Response, error) {
	rsp, err := c.UpdateApplicationReviewSafetyScoreV2WithBody(ctx, applicationReviewID, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdateApplicationReviewSafetyScoreV2Response(rsp)
}

func (c *ClientWithResponses) UpdateApplicationReviewSafetyScoreV2WithResponse(ctx context.Context, applicationReviewID externalRef2.ApplicationReviewID, body UpdateApplicationReviewSafetyScoreV2JSONRequestBody, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewSafetyScoreV2Response, error) {
	rsp, err := c.UpdateApplicationReviewSafetyScoreV2(ctx, applicationReviewID, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdateApplicationReviewSafetyScoreV2Response(rsp)
}

// SetApplicationReviewMVRPullWithResponse request returning *SetApplicationReviewMVRPullResponse
func (c *ClientWithResponses) SetApplicationReviewMVRPullWithResponse(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*SetApplicationReviewMVRPullResponse, error) {
	rsp, err := c.SetApplicationReviewMVRPull(ctx, applicationReviewID, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseSetApplicationReviewMVRPullResponse(rsp)
}

// GetApplicationReviewsSummaryWithResponse request returning *GetApplicationReviewsSummaryResponse
func (c *ClientWithResponses) GetApplicationReviewsSummaryWithResponse(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*GetApplicationReviewsSummaryResponse, error) {
	rsp, err := c.GetApplicationReviewsSummary(ctx, applicationReviewID, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetApplicationReviewsSummaryResponse(rsp)
}

// GetApplicationReviewTimelineWithResponse request returning *GetApplicationReviewTimelineResponse
func (c *ClientWithResponses) GetApplicationReviewTimelineWithResponse(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*GetApplicationReviewTimelineResponse, error) {
	rsp, err := c.GetApplicationReviewTimeline(ctx, applicationReviewID, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetApplicationReviewTimelineResponse(rsp)
}

// GetDriverViolationsWithResponse request returning *GetDriverViolationsResponse
func (c *ClientWithResponses) GetDriverViolationsWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetDriverViolationsResponse, error) {
	rsp, err := c.GetDriverViolations(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetDriverViolationsResponse(rsp)
}

// UpdateApplicationReviewDriverV2WithBodyWithResponse request with arbitrary body returning *UpdateApplicationReviewDriverV2Response
func (c *ClientWithResponses) UpdateApplicationReviewDriverV2WithBodyWithResponse(ctx context.Context, applicationReviewID string, dlNumber string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewDriverV2Response, error) {
	rsp, err := c.UpdateApplicationReviewDriverV2WithBody(ctx, applicationReviewID, dlNumber, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdateApplicationReviewDriverV2Response(rsp)
}

func (c *ClientWithResponses) UpdateApplicationReviewDriverV2WithResponse(ctx context.Context, applicationReviewID string, dlNumber string, body UpdateApplicationReviewDriverV2JSONRequestBody, reqEditors ...RequestEditorFn) (*UpdateApplicationReviewDriverV2Response, error) {
	rsp, err := c.UpdateApplicationReviewDriverV2(ctx, applicationReviewID, dlNumber, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdateApplicationReviewDriverV2Response(rsp)
}

// GetApplicationReviewDriversV2WithResponse request returning *GetApplicationReviewDriversV2Response
func (c *ClientWithResponses) GetApplicationReviewDriversV2WithResponse(ctx context.Context, applicationReviewID string, reqEditors ...RequestEditorFn) (*GetApplicationReviewDriversV2Response, error) {
	rsp, err := c.GetApplicationReviewDriversV2(ctx, applicationReviewID, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetApplicationReviewDriversV2Response(rsp)
}

// GetScrapeInfoWithResponse request returning *GetScrapeInfoResponse
func (c *ClientWithResponses) GetScrapeInfoWithResponse(ctx context.Context, applicationReviewID string, params *GetScrapeInfoParams, reqEditors ...RequestEditorFn) (*GetScrapeInfoResponse, error) {
	rsp, err := c.GetScrapeInfo(ctx, applicationReviewID, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetScrapeInfoResponse(rsp)
}

// TriggerScrapeWithResponse request returning *TriggerScrapeResponse
func (c *ClientWithResponses) TriggerScrapeWithResponse(ctx context.Context, applicationReviewID string, params *TriggerScrapeParams, reqEditors ...RequestEditorFn) (*TriggerScrapeResponse, error) {
	rsp, err := c.TriggerScrape(ctx, applicationReviewID, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseTriggerScrapeResponse(rsp)
}

// ParseGetApplicationReviewsWithPaginationResponse parses an HTTP response from a GetApplicationReviewsWithPaginationWithResponse call
func ParseGetApplicationReviewsWithPaginationResponse(rsp *http.Response) (*GetApplicationReviewsWithPaginationResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetApplicationReviewsWithPaginationResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest externalRef1.ApplicationReviewsWithPaginationResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseGetApplicationReviewsResponse parses an HTTP response from a GetApplicationReviewsWithResponse call
func ParseGetApplicationReviewsResponse(rsp *http.Response) (*GetApplicationReviewsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetApplicationReviewsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest []externalRef1.ApplicationReviewDetails
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseGetNonFleetApplicationReviewActionsResponse parses an HTTP response from a GetNonFleetApplicationReviewActionsWithResponse call
func ParseGetNonFleetApplicationReviewActionsResponse(rsp *http.Response) (*GetNonFleetApplicationReviewActionsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetNonFleetApplicationReviewActionsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest externalRef1.ApplicationReviewGetActionsResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && true:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSONDefault = &dest

	}

	return response, nil
}

// ParseApproveApplicationReviewResponse parses an HTTP response from a ApproveApplicationReviewWithResponse call
func ParseApproveApplicationReviewResponse(rsp *http.Response) (*ApproveApplicationReviewResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &ApproveApplicationReviewResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseUpdateApplicationReviewAssigneeResponse parses an HTTP response from a UpdateApplicationReviewAssigneeWithResponse call
func ParseUpdateApplicationReviewAssigneeResponse(rsp *http.Response) (*UpdateApplicationReviewAssigneeResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &UpdateApplicationReviewAssigneeResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseCloseApplicationReviewResponse parses an HTTP response from a CloseApplicationReviewWithResponse call
func ParseCloseApplicationReviewResponse(rsp *http.Response) (*CloseApplicationReviewResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &CloseApplicationReviewResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseDeclineApplicationReviewResponse parses an HTTP response from a DeclineApplicationReviewWithResponse call
func ParseDeclineApplicationReviewResponse(rsp *http.Response) (*DeclineApplicationReviewResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeclineApplicationReviewResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseGetApplicationReviewDocumentsResponse parses an HTTP response from a GetApplicationReviewDocumentsWithResponse call
func ParseGetApplicationReviewDocumentsResponse(rsp *http.Response) (*GetApplicationReviewDocumentsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetApplicationReviewDocumentsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest externalRef1.ApplicationReviewDocuments
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && true:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSONDefault = &dest

	}

	return response, nil
}

// ParseUploadApplicationReviewDocumentsResponse parses an HTTP response from a UploadApplicationReviewDocumentsWithResponse call
func ParseUploadApplicationReviewDocumentsResponse(rsp *http.Response) (*UploadApplicationReviewDocumentsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &UploadApplicationReviewDocumentsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest externalRef0.FileHandle
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && true:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSONDefault = &dest

	}

	return response, nil
}

// ParseUpdateApplicationReviewDriverResponse parses an HTTP response from a UpdateApplicationReviewDriverWithResponse call
func ParseUpdateApplicationReviewDriverResponse(rsp *http.Response) (*UpdateApplicationReviewDriverResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &UpdateApplicationReviewDriverResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && true:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSONDefault = &dest

	}

	return response, nil
}

// ParseGetApplicationReviewDriversResponse parses an HTTP response from a GetApplicationReviewDriversWithResponse call
func ParseGetApplicationReviewDriversResponse(rsp *http.Response) (*GetApplicationReviewDriversResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetApplicationReviewDriversResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest externalRef1.ApplicationReviewDriver
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseUpdateApplicationReviewDriversResponse parses an HTTP response from a UpdateApplicationReviewDriversWithResponse call
func ParseUpdateApplicationReviewDriversResponse(rsp *http.Response) (*UpdateApplicationReviewDriversResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &UpdateApplicationReviewDriversResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && true:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSONDefault = &dest

	}

	return response, nil
}

// ParseGetApplicationReviewEquipmentsResponse parses an HTTP response from a GetApplicationReviewEquipmentsWithResponse call
func ParseGetApplicationReviewEquipmentsResponse(rsp *http.Response) (*GetApplicationReviewEquipmentsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetApplicationReviewEquipmentsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest externalRef1.ApplicationReviewEquipment
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseUpdateApplicationReviewEquipmentsResponse parses an HTTP response from a UpdateApplicationReviewEquipmentsWithResponse call
func ParseUpdateApplicationReviewEquipmentsResponse(rsp *http.Response) (*UpdateApplicationReviewEquipmentsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &UpdateApplicationReviewEquipmentsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && true:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSONDefault = &dest

	}

	return response, nil
}

// ParseGetExpressLaneFeedbackResponse parses an HTTP response from a GetExpressLaneFeedbackWithResponse call
func ParseGetExpressLaneFeedbackResponse(rsp *http.Response) (*GetExpressLaneFeedbackResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetExpressLaneFeedbackResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest externalRef1.ExpressLaneFeedbackGetResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && true:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSONDefault = &dest

	}

	return response, nil
}

// ParsePatchExpressLaneFeedbackResponse parses an HTTP response from a PatchExpressLaneFeedbackWithResponse call
func ParsePatchExpressLaneFeedbackResponse(rsp *http.Response) (*PatchExpressLaneFeedbackResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PatchExpressLaneFeedbackResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest externalRef1.ExpressLaneFeedbackPatchResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && true:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSONDefault = &dest

	}

	return response, nil
}

// ParseGetApplicationReviewFlagsResponse parses an HTTP response from a GetApplicationReviewFlagsWithResponse call
func ParseGetApplicationReviewFlagsResponse(rsp *http.Response) (*GetApplicationReviewFlagsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetApplicationReviewFlagsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest []externalRef1.ApplicationReviewFlag
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseGetApplicationReviewLossesResponse parses an HTTP response from a GetApplicationReviewLossesWithResponse call
func ParseGetApplicationReviewLossesResponse(rsp *http.Response) (*GetApplicationReviewLossesResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetApplicationReviewLossesResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest externalRef1.ApplicationReviewLosses
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseUpdateApplicationReviewLossesResponse parses an HTTP response from a UpdateApplicationReviewLossesWithResponse call
func ParseUpdateApplicationReviewLossesResponse(rsp *http.Response) (*UpdateApplicationReviewLossesResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &UpdateApplicationReviewLossesResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && true:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSONDefault = &dest

	}

	return response, nil
}

// ParseGetApplicationReviewNotesResponse parses an HTTP response from a GetApplicationReviewNotesWithResponse call
func ParseGetApplicationReviewNotesResponse(rsp *http.Response) (*GetApplicationReviewNotesResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetApplicationReviewNotesResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest externalRef1.ApplicationReviewNotes
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && true:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSONDefault = &dest

	}

	return response, nil
}

// ParseUpdateApplicationReviewNotesResponse parses an HTTP response from a UpdateApplicationReviewNotesWithResponse call
func ParseUpdateApplicationReviewNotesResponse(rsp *http.Response) (*UpdateApplicationReviewNotesResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &UpdateApplicationReviewNotesResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && true:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSONDefault = &dest

	}

	return response, nil
}

// ParseGetApplicationReviewOperationsResponse parses an HTTP response from a GetApplicationReviewOperationsWithResponse call
func ParseGetApplicationReviewOperationsResponse(rsp *http.Response) (*GetApplicationReviewOperationsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetApplicationReviewOperationsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest externalRef1.ApplicationReviewOperation
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseUpdateApplicationReviewOperationsResponse parses an HTTP response from a UpdateApplicationReviewOperationsWithResponse call
func ParseUpdateApplicationReviewOperationsResponse(rsp *http.Response) (*UpdateApplicationReviewOperationsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &UpdateApplicationReviewOperationsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && true:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSONDefault = &dest

	}

	return response, nil
}

// ParseGetApplicationReviewsPackagesResponse parses an HTTP response from a GetApplicationReviewsPackagesWithResponse call
func ParseGetApplicationReviewsPackagesResponse(rsp *http.Response) (*GetApplicationReviewsPackagesResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetApplicationReviewsPackagesResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest externalRef1.ApplicationReviewPackages
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseUpdateApplicationReviewPackagesResponse parses an HTTP response from a UpdateApplicationReviewPackagesWithResponse call
func ParseUpdateApplicationReviewPackagesResponse(rsp *http.Response) (*UpdateApplicationReviewPackagesResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &UpdateApplicationReviewPackagesResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && true:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSONDefault = &dest

	}

	return response, nil
}

// ParsePostApplicationReviewQuoteSubmitResponse parses an HTTP response from a PostApplicationReviewQuoteSubmitWithResponse call
func ParsePostApplicationReviewQuoteSubmitResponse(rsp *http.Response) (*PostApplicationReviewQuoteSubmitResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApplicationReviewQuoteSubmitResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseReferApplicationReviewResponse parses an HTTP response from a ReferApplicationReviewWithResponse call
func ParseReferApplicationReviewResponse(rsp *http.Response) (*ReferApplicationReviewResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &ReferApplicationReviewResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseRollbackApplicationReviewResponse parses an HTTP response from a RollbackApplicationReviewWithResponse call
func ParseRollbackApplicationReviewResponse(rsp *http.Response) (*RollbackApplicationReviewResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &RollbackApplicationReviewResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseGetApplicationReviewSafetyResponse parses an HTTP response from a GetApplicationReviewSafetyWithResponse call
func ParseGetApplicationReviewSafetyResponse(rsp *http.Response) (*GetApplicationReviewSafetyResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetApplicationReviewSafetyResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest externalRef1.ApplicationReviewSafety
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseUpdateApplicationReviewSafetyResponse parses an HTTP response from a UpdateApplicationReviewSafetyWithResponse call
func ParseUpdateApplicationReviewSafetyResponse(rsp *http.Response) (*UpdateApplicationReviewSafetyResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &UpdateApplicationReviewSafetyResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && true:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSONDefault = &dest

	}

	return response, nil
}

// ParseGetApplicationReviewSafetyScoreResponse parses an HTTP response from a GetApplicationReviewSafetyScoreWithResponse call
func ParseGetApplicationReviewSafetyScoreResponse(rsp *http.Response) (*GetApplicationReviewSafetyScoreResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetApplicationReviewSafetyScoreResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest externalRef2.ApplicationReviewSafetyScoreV2
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && true:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSONDefault = &dest

	}

	return response, nil
}

// ParseUpdateApplicationReviewSafetyScoreV2Response parses an HTTP response from a UpdateApplicationReviewSafetyScoreV2WithResponse call
func ParseUpdateApplicationReviewSafetyScoreV2Response(rsp *http.Response) (*UpdateApplicationReviewSafetyScoreV2Response, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &UpdateApplicationReviewSafetyScoreV2Response{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && true:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSONDefault = &dest

	}

	return response, nil
}

// ParseSetApplicationReviewMVRPullResponse parses an HTTP response from a SetApplicationReviewMVRPullWithResponse call
func ParseSetApplicationReviewMVRPullResponse(rsp *http.Response) (*SetApplicationReviewMVRPullResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &SetApplicationReviewMVRPullResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest externalRef0.MVRFlag
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseGetApplicationReviewsSummaryResponse parses an HTTP response from a GetApplicationReviewsSummaryWithResponse call
func ParseGetApplicationReviewsSummaryResponse(rsp *http.Response) (*GetApplicationReviewsSummaryResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetApplicationReviewsSummaryResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest externalRef1.ApplicationReviewSummary
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseGetApplicationReviewTimelineResponse parses an HTTP response from a GetApplicationReviewTimelineWithResponse call
func ParseGetApplicationReviewTimelineResponse(rsp *http.Response) (*GetApplicationReviewTimelineResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetApplicationReviewTimelineResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest externalRef1.ApplicationReviewTimeline
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseGetDriverViolationsResponse parses an HTTP response from a GetDriverViolationsWithResponse call
func ParseGetDriverViolationsResponse(rsp *http.Response) (*GetDriverViolationsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetDriverViolationsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest []externalRef0.DriverViolation
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseUpdateApplicationReviewDriverV2Response parses an HTTP response from a UpdateApplicationReviewDriverV2WithResponse call
func ParseUpdateApplicationReviewDriverV2Response(rsp *http.Response) (*UpdateApplicationReviewDriverV2Response, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &UpdateApplicationReviewDriverV2Response{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && true:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSONDefault = &dest

	}

	return response, nil
}

// ParseGetApplicationReviewDriversV2Response parses an HTTP response from a GetApplicationReviewDriversV2WithResponse call
func ParseGetApplicationReviewDriversV2Response(rsp *http.Response) (*GetApplicationReviewDriversV2Response, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetApplicationReviewDriversV2Response{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest externalRef1.ApplicationReviewDriver
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseGetScrapeInfoResponse parses an HTTP response from a GetScrapeInfoWithResponse call
func ParseGetScrapeInfoResponse(rsp *http.Response) (*GetScrapeInfoResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetScrapeInfoResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest externalRef1.ScrapeResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseTriggerScrapeResponse parses an HTTP response from a TriggerScrapeWithResponse call
func ParseTriggerScrapeResponse(rsp *http.Response) (*TriggerScrapeResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &TriggerScrapeResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest externalRef0.ErrorMessage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ServerInterface represents all server handlers.
type ServerInterface interface {

	// (GET /nonfleet/underwriting/application_review/list)
	GetApplicationReviewsWithPagination(ctx echo.Context, params GetApplicationReviewsWithPaginationParams) error

	// (GET /nonfleet/underwriting/application_reviews)
	GetApplicationReviews(ctx echo.Context) error

	// (GET /nonfleet/underwriting/application_reviews/{applicationReviewID}/actions)
	GetNonFleetApplicationReviewActions(ctx echo.Context, applicationReviewID externalRef1.ApplicationReviewID) error

	// (POST /nonfleet/underwriting/application_reviews/{applicationReviewID}/approve)
	ApproveApplicationReview(ctx echo.Context, applicationReviewID string) error

	// (PATCH /nonfleet/underwriting/application_reviews/{applicationReviewID}/assignee)
	UpdateApplicationReviewAssignee(ctx echo.Context, applicationReviewID string) error

	// (POST /nonfleet/underwriting/application_reviews/{applicationReviewID}/close)
	CloseApplicationReview(ctx echo.Context, applicationReviewID string) error

	// (POST /nonfleet/underwriting/application_reviews/{applicationReviewID}/decline)
	DeclineApplicationReview(ctx echo.Context, applicationReviewID string) error

	// (GET /nonfleet/underwriting/application_reviews/{applicationReviewID}/documents)
	GetApplicationReviewDocuments(ctx echo.Context, applicationReviewID string) error

	// (POST /nonfleet/underwriting/application_reviews/{applicationReviewID}/documents)
	UploadApplicationReviewDocuments(ctx echo.Context, applicationReviewID string) error

	// (PATCH /nonfleet/underwriting/application_reviews/{applicationReviewID}/driver/{dlNumber})
	UpdateApplicationReviewDriver(ctx echo.Context, applicationReviewID string, dlNumber string) error

	// (GET /nonfleet/underwriting/application_reviews/{applicationReviewID}/drivers)
	GetApplicationReviewDrivers(ctx echo.Context, applicationReviewID string) error

	// (PATCH /nonfleet/underwriting/application_reviews/{applicationReviewID}/drivers)
	UpdateApplicationReviewDrivers(ctx echo.Context, applicationReviewID string) error

	// (GET /nonfleet/underwriting/application_reviews/{applicationReviewID}/equipments)
	GetApplicationReviewEquipments(ctx echo.Context, applicationReviewID string) error

	// (PATCH /nonfleet/underwriting/application_reviews/{applicationReviewID}/equipments)
	UpdateApplicationReviewEquipments(ctx echo.Context, applicationReviewID string) error

	// (GET /nonfleet/underwriting/application_reviews/{applicationReviewID}/express_lane_feedback/submit)
	GetExpressLaneFeedback(ctx echo.Context, applicationReviewID string) error

	// (PATCH /nonfleet/underwriting/application_reviews/{applicationReviewID}/express_lane_feedback/submit)
	PatchExpressLaneFeedback(ctx echo.Context, applicationReviewID string) error

	// (GET /nonfleet/underwriting/application_reviews/{applicationReviewID}/flags)
	GetApplicationReviewFlags(ctx echo.Context, applicationReviewID string) error

	// (GET /nonfleet/underwriting/application_reviews/{applicationReviewID}/losses)
	GetApplicationReviewLosses(ctx echo.Context, applicationReviewID string) error

	// (PATCH /nonfleet/underwriting/application_reviews/{applicationReviewID}/losses)
	UpdateApplicationReviewLosses(ctx echo.Context, applicationReviewID string) error

	// (GET /nonfleet/underwriting/application_reviews/{applicationReviewID}/notes)
	GetApplicationReviewNotes(ctx echo.Context, applicationReviewID string) error

	// (PATCH /nonfleet/underwriting/application_reviews/{applicationReviewID}/notes)
	UpdateApplicationReviewNotes(ctx echo.Context, applicationReviewID string) error

	// (GET /nonfleet/underwriting/application_reviews/{applicationReviewID}/operations)
	GetApplicationReviewOperations(ctx echo.Context, applicationReviewID string) error

	// (PATCH /nonfleet/underwriting/application_reviews/{applicationReviewID}/operations)
	UpdateApplicationReviewOperations(ctx echo.Context, applicationReviewID string) error

	// (GET /nonfleet/underwriting/application_reviews/{applicationReviewID}/packages)
	GetApplicationReviewsPackages(ctx echo.Context, applicationReviewID string) error

	// (PATCH /nonfleet/underwriting/application_reviews/{applicationReviewID}/packages)
	UpdateApplicationReviewPackages(ctx echo.Context, applicationReviewID string) error

	// (POST /nonfleet/underwriting/application_reviews/{applicationReviewID}/quote/submit)
	PostApplicationReviewQuoteSubmit(ctx echo.Context, applicationReviewID string, params PostApplicationReviewQuoteSubmitParams) error

	// (POST /nonfleet/underwriting/application_reviews/{applicationReviewID}/refer)
	ReferApplicationReview(ctx echo.Context, applicationReviewID string) error

	// (POST /nonfleet/underwriting/application_reviews/{applicationReviewID}/rollback)
	RollbackApplicationReview(ctx echo.Context, applicationReviewID string) error

	// (GET /nonfleet/underwriting/application_reviews/{applicationReviewID}/safety)
	GetApplicationReviewSafety(ctx echo.Context, applicationReviewID string) error

	// (PATCH /nonfleet/underwriting/application_reviews/{applicationReviewID}/safety)
	UpdateApplicationReviewSafety(ctx echo.Context, applicationReviewID string) error

	// (GET /nonfleet/underwriting/application_reviews/{applicationReviewID}/safety/safety_score)
	GetApplicationReviewSafetyScore(ctx echo.Context, applicationReviewID externalRef2.ApplicationReviewID) error

	// (PATCH /nonfleet/underwriting/application_reviews/{applicationReviewID}/safety/safety_score)
	UpdateApplicationReviewSafetyScoreV2(ctx echo.Context, applicationReviewID externalRef2.ApplicationReviewID) error

	// (POST /nonfleet/underwriting/application_reviews/{applicationReviewID}/set_mvr_pull)
	SetApplicationReviewMVRPull(ctx echo.Context, applicationReviewID string) error

	// (GET /nonfleet/underwriting/application_reviews/{applicationReviewID}/summary)
	GetApplicationReviewsSummary(ctx echo.Context, applicationReviewID string) error

	// (GET /nonfleet/underwriting/application_reviews/{applicationReviewID}/timeline)
	GetApplicationReviewTimeline(ctx echo.Context, applicationReviewID string) error

	// (GET /nonfleet/underwriting/driver-violations)
	GetDriverViolations(ctx echo.Context) error

	// (PATCH /nonfleet/underwriting/v2/application-reviews/{applicationReviewID}/driver/{dlNumber})
	UpdateApplicationReviewDriverV2(ctx echo.Context, applicationReviewID string, dlNumber string) error

	// (GET /nonfleet/underwriting/v2/application-reviews/{applicationReviewID}/drivers)
	GetApplicationReviewDriversV2(ctx echo.Context, applicationReviewID string) error

	// (GET /nonfleet/underwriting/{applicationReviewID}/scrape)
	GetScrapeInfo(ctx echo.Context, applicationReviewID string, params GetScrapeInfoParams) error

	// (POST /nonfleet/underwriting/{applicationReviewID}/scrape)
	TriggerScrape(ctx echo.Context, applicationReviewID string, params TriggerScrapeParams) error
}

// ServerInterfaceWrapper converts echo contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler ServerInterface
}

// GetApplicationReviewsWithPagination converts echo context to params.
func (w *ServerInterfaceWrapper) GetApplicationReviewsWithPagination(ctx echo.Context) error {
	var err error

	ctx.Set(SessionIdAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetApplicationReviewsWithPaginationParams
	// ------------- Optional query parameter "size" -------------

	err = runtime.BindQueryParameter("form", true, false, "size", ctx.QueryParams(), &params.Size)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter size: %s", err))
	}

	// ------------- Optional query parameter "cursor" -------------

	err = runtime.BindQueryParameter("form", true, false, "cursor", ctx.QueryParams(), &params.Cursor)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter cursor: %s", err))
	}

	// ------------- Optional query parameter "q" -------------

	err = runtime.BindQueryParameter("form", true, false, "q", ctx.QueryParams(), &params.Q)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter q: %s", err))
	}

	// ------------- Optional query parameter "effectiveDateBefore" -------------

	err = runtime.BindQueryParameter("form", true, false, "effectiveDateBefore", ctx.QueryParams(), &params.EffectiveDateBefore)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter effectiveDateBefore: %s", err))
	}

	// ------------- Optional query parameter "effectiveDateAfter" -------------

	err = runtime.BindQueryParameter("form", true, false, "effectiveDateAfter", ctx.QueryParams(), &params.EffectiveDateAfter)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter effectiveDateAfter: %s", err))
	}

	// ------------- Optional query parameter "underWriterID" -------------

	err = runtime.BindQueryParameter("form", true, false, "underWriterID", ctx.QueryParams(), &params.UnderWriterID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter underWriterID: %s", err))
	}

	// ------------- Optional query parameter "tab" -------------

	err = runtime.BindQueryParameter("form", true, false, "tab", ctx.QueryParams(), &params.Tab)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter tab: %s", err))
	}

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.GetApplicationReviewsWithPagination(ctx, params)
	return err
}

// GetApplicationReviews converts echo context to params.
func (w *ServerInterfaceWrapper) GetApplicationReviews(ctx echo.Context) error {
	var err error

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.GetApplicationReviews(ctx)
	return err
}

// GetNonFleetApplicationReviewActions converts echo context to params.
func (w *ServerInterfaceWrapper) GetNonFleetApplicationReviewActions(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID externalRef1.ApplicationReviewID

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.GetNonFleetApplicationReviewActions(ctx, applicationReviewID)
	return err
}

// ApproveApplicationReview converts echo context to params.
func (w *ServerInterfaceWrapper) ApproveApplicationReview(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID string

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.ApproveApplicationReview(ctx, applicationReviewID)
	return err
}

// UpdateApplicationReviewAssignee converts echo context to params.
func (w *ServerInterfaceWrapper) UpdateApplicationReviewAssignee(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID string

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.UpdateApplicationReviewAssignee(ctx, applicationReviewID)
	return err
}

// CloseApplicationReview converts echo context to params.
func (w *ServerInterfaceWrapper) CloseApplicationReview(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID string

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.CloseApplicationReview(ctx, applicationReviewID)
	return err
}

// DeclineApplicationReview converts echo context to params.
func (w *ServerInterfaceWrapper) DeclineApplicationReview(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID string

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.DeclineApplicationReview(ctx, applicationReviewID)
	return err
}

// GetApplicationReviewDocuments converts echo context to params.
func (w *ServerInterfaceWrapper) GetApplicationReviewDocuments(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID string

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.GetApplicationReviewDocuments(ctx, applicationReviewID)
	return err
}

// UploadApplicationReviewDocuments converts echo context to params.
func (w *ServerInterfaceWrapper) UploadApplicationReviewDocuments(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID string

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.UploadApplicationReviewDocuments(ctx, applicationReviewID)
	return err
}

// UpdateApplicationReviewDriver converts echo context to params.
func (w *ServerInterfaceWrapper) UpdateApplicationReviewDriver(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID string

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	// ------------- Path parameter "dlNumber" -------------
	var dlNumber string

	err = runtime.BindStyledParameterWithLocation("simple", false, "dlNumber", runtime.ParamLocationPath, ctx.Param("dlNumber"), &dlNumber)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter dlNumber: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.UpdateApplicationReviewDriver(ctx, applicationReviewID, dlNumber)
	return err
}

// GetApplicationReviewDrivers converts echo context to params.
func (w *ServerInterfaceWrapper) GetApplicationReviewDrivers(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID string

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.GetApplicationReviewDrivers(ctx, applicationReviewID)
	return err
}

// UpdateApplicationReviewDrivers converts echo context to params.
func (w *ServerInterfaceWrapper) UpdateApplicationReviewDrivers(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID string

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.UpdateApplicationReviewDrivers(ctx, applicationReviewID)
	return err
}

// GetApplicationReviewEquipments converts echo context to params.
func (w *ServerInterfaceWrapper) GetApplicationReviewEquipments(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID string

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.GetApplicationReviewEquipments(ctx, applicationReviewID)
	return err
}

// UpdateApplicationReviewEquipments converts echo context to params.
func (w *ServerInterfaceWrapper) UpdateApplicationReviewEquipments(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID string

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.UpdateApplicationReviewEquipments(ctx, applicationReviewID)
	return err
}

// GetExpressLaneFeedback converts echo context to params.
func (w *ServerInterfaceWrapper) GetExpressLaneFeedback(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID string

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.GetExpressLaneFeedback(ctx, applicationReviewID)
	return err
}

// PatchExpressLaneFeedback converts echo context to params.
func (w *ServerInterfaceWrapper) PatchExpressLaneFeedback(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID string

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.PatchExpressLaneFeedback(ctx, applicationReviewID)
	return err
}

// GetApplicationReviewFlags converts echo context to params.
func (w *ServerInterfaceWrapper) GetApplicationReviewFlags(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID string

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.GetApplicationReviewFlags(ctx, applicationReviewID)
	return err
}

// GetApplicationReviewLosses converts echo context to params.
func (w *ServerInterfaceWrapper) GetApplicationReviewLosses(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID string

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.GetApplicationReviewLosses(ctx, applicationReviewID)
	return err
}

// UpdateApplicationReviewLosses converts echo context to params.
func (w *ServerInterfaceWrapper) UpdateApplicationReviewLosses(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID string

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.UpdateApplicationReviewLosses(ctx, applicationReviewID)
	return err
}

// GetApplicationReviewNotes converts echo context to params.
func (w *ServerInterfaceWrapper) GetApplicationReviewNotes(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID string

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.GetApplicationReviewNotes(ctx, applicationReviewID)
	return err
}

// UpdateApplicationReviewNotes converts echo context to params.
func (w *ServerInterfaceWrapper) UpdateApplicationReviewNotes(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID string

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.UpdateApplicationReviewNotes(ctx, applicationReviewID)
	return err
}

// GetApplicationReviewOperations converts echo context to params.
func (w *ServerInterfaceWrapper) GetApplicationReviewOperations(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID string

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.GetApplicationReviewOperations(ctx, applicationReviewID)
	return err
}

// UpdateApplicationReviewOperations converts echo context to params.
func (w *ServerInterfaceWrapper) UpdateApplicationReviewOperations(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID string

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.UpdateApplicationReviewOperations(ctx, applicationReviewID)
	return err
}

// GetApplicationReviewsPackages converts echo context to params.
func (w *ServerInterfaceWrapper) GetApplicationReviewsPackages(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID string

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.GetApplicationReviewsPackages(ctx, applicationReviewID)
	return err
}

// UpdateApplicationReviewPackages converts echo context to params.
func (w *ServerInterfaceWrapper) UpdateApplicationReviewPackages(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID string

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.UpdateApplicationReviewPackages(ctx, applicationReviewID)
	return err
}

// PostApplicationReviewQuoteSubmit converts echo context to params.
func (w *ServerInterfaceWrapper) PostApplicationReviewQuoteSubmit(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID string

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params PostApplicationReviewQuoteSubmitParams
	// ------------- Optional query parameter "bindable" -------------

	err = runtime.BindQueryParameter("form", true, false, "bindable", ctx.QueryParams(), &params.Bindable)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter bindable: %s", err))
	}

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.PostApplicationReviewQuoteSubmit(ctx, applicationReviewID, params)
	return err
}

// ReferApplicationReview converts echo context to params.
func (w *ServerInterfaceWrapper) ReferApplicationReview(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID string

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.ReferApplicationReview(ctx, applicationReviewID)
	return err
}

// RollbackApplicationReview converts echo context to params.
func (w *ServerInterfaceWrapper) RollbackApplicationReview(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID string

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.RollbackApplicationReview(ctx, applicationReviewID)
	return err
}

// GetApplicationReviewSafety converts echo context to params.
func (w *ServerInterfaceWrapper) GetApplicationReviewSafety(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID string

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.GetApplicationReviewSafety(ctx, applicationReviewID)
	return err
}

// UpdateApplicationReviewSafety converts echo context to params.
func (w *ServerInterfaceWrapper) UpdateApplicationReviewSafety(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID string

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.UpdateApplicationReviewSafety(ctx, applicationReviewID)
	return err
}

// GetApplicationReviewSafetyScore converts echo context to params.
func (w *ServerInterfaceWrapper) GetApplicationReviewSafetyScore(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID externalRef2.ApplicationReviewID

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.GetApplicationReviewSafetyScore(ctx, applicationReviewID)
	return err
}

// UpdateApplicationReviewSafetyScoreV2 converts echo context to params.
func (w *ServerInterfaceWrapper) UpdateApplicationReviewSafetyScoreV2(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID externalRef2.ApplicationReviewID

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.UpdateApplicationReviewSafetyScoreV2(ctx, applicationReviewID)
	return err
}

// SetApplicationReviewMVRPull converts echo context to params.
func (w *ServerInterfaceWrapper) SetApplicationReviewMVRPull(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID string

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.SetApplicationReviewMVRPull(ctx, applicationReviewID)
	return err
}

// GetApplicationReviewsSummary converts echo context to params.
func (w *ServerInterfaceWrapper) GetApplicationReviewsSummary(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID string

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.GetApplicationReviewsSummary(ctx, applicationReviewID)
	return err
}

// GetApplicationReviewTimeline converts echo context to params.
func (w *ServerInterfaceWrapper) GetApplicationReviewTimeline(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID string

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.GetApplicationReviewTimeline(ctx, applicationReviewID)
	return err
}

// GetDriverViolations converts echo context to params.
func (w *ServerInterfaceWrapper) GetDriverViolations(ctx echo.Context) error {
	var err error

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.GetDriverViolations(ctx)
	return err
}

// UpdateApplicationReviewDriverV2 converts echo context to params.
func (w *ServerInterfaceWrapper) UpdateApplicationReviewDriverV2(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID string

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	// ------------- Path parameter "dlNumber" -------------
	var dlNumber string

	err = runtime.BindStyledParameterWithLocation("simple", false, "dlNumber", runtime.ParamLocationPath, ctx.Param("dlNumber"), &dlNumber)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter dlNumber: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.UpdateApplicationReviewDriverV2(ctx, applicationReviewID, dlNumber)
	return err
}

// GetApplicationReviewDriversV2 converts echo context to params.
func (w *ServerInterfaceWrapper) GetApplicationReviewDriversV2(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID string

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.GetApplicationReviewDriversV2(ctx, applicationReviewID)
	return err
}

// GetScrapeInfo converts echo context to params.
func (w *ServerInterfaceWrapper) GetScrapeInfo(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID string

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetScrapeInfoParams
	// ------------- Required query parameter "scrapeType" -------------

	err = runtime.BindQueryParameter("form", true, true, "scrapeType", ctx.QueryParams(), &params.ScrapeType)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter scrapeType: %s", err))
	}

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.GetScrapeInfo(ctx, applicationReviewID, params)
	return err
}

// TriggerScrape converts echo context to params.
func (w *ServerInterfaceWrapper) TriggerScrape(ctx echo.Context) error {
	var err error
	// ------------- Path parameter "applicationReviewID" -------------
	var applicationReviewID string

	err = runtime.BindStyledParameterWithLocation("simple", false, "applicationReviewID", runtime.ParamLocationPath, ctx.Param("applicationReviewID"), &applicationReviewID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter applicationReviewID: %s", err))
	}

	ctx.Set(SessionIdAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params TriggerScrapeParams
	// ------------- Required query parameter "scrapeType" -------------

	err = runtime.BindQueryParameter("form", true, true, "scrapeType", ctx.QueryParams(), &params.ScrapeType)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter scrapeType: %s", err))
	}

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.TriggerScrape(ctx, applicationReviewID, params)
	return err
}

// This is a simple interface which specifies echo.Route addition functions which
// are present on both echo.Echo and echo.Group, since we want to allow using
// either of them for path registration
type EchoRouter interface {
	CONNECT(path string, h echo.HandlerFunc, m ...echo.MiddlewareFunc) *echo.Route
	DELETE(path string, h echo.HandlerFunc, m ...echo.MiddlewareFunc) *echo.Route
	GET(path string, h echo.HandlerFunc, m ...echo.MiddlewareFunc) *echo.Route
	HEAD(path string, h echo.HandlerFunc, m ...echo.MiddlewareFunc) *echo.Route
	OPTIONS(path string, h echo.HandlerFunc, m ...echo.MiddlewareFunc) *echo.Route
	PATCH(path string, h echo.HandlerFunc, m ...echo.MiddlewareFunc) *echo.Route
	POST(path string, h echo.HandlerFunc, m ...echo.MiddlewareFunc) *echo.Route
	PUT(path string, h echo.HandlerFunc, m ...echo.MiddlewareFunc) *echo.Route
	TRACE(path string, h echo.HandlerFunc, m ...echo.MiddlewareFunc) *echo.Route
}

// RegisterHandlers adds each server route to the EchoRouter.
func RegisterHandlers(router EchoRouter, si ServerInterface) {
	RegisterHandlersWithBaseURL(router, si, "")
}

// Registers handlers, and prepends BaseURL to the paths, so that the paths
// can be served under a prefix.
func RegisterHandlersWithBaseURL(router EchoRouter, si ServerInterface, baseURL string) {

	wrapper := ServerInterfaceWrapper{
		Handler: si,
	}

	router.GET(baseURL+"/nonfleet/underwriting/application_review/list", wrapper.GetApplicationReviewsWithPagination)
	router.GET(baseURL+"/nonfleet/underwriting/application_reviews", wrapper.GetApplicationReviews)
	router.GET(baseURL+"/nonfleet/underwriting/application_reviews/:applicationReviewID/actions", wrapper.GetNonFleetApplicationReviewActions)
	router.POST(baseURL+"/nonfleet/underwriting/application_reviews/:applicationReviewID/approve", wrapper.ApproveApplicationReview)
	router.PATCH(baseURL+"/nonfleet/underwriting/application_reviews/:applicationReviewID/assignee", wrapper.UpdateApplicationReviewAssignee)
	router.POST(baseURL+"/nonfleet/underwriting/application_reviews/:applicationReviewID/close", wrapper.CloseApplicationReview)
	router.POST(baseURL+"/nonfleet/underwriting/application_reviews/:applicationReviewID/decline", wrapper.DeclineApplicationReview)
	router.GET(baseURL+"/nonfleet/underwriting/application_reviews/:applicationReviewID/documents", wrapper.GetApplicationReviewDocuments)
	router.POST(baseURL+"/nonfleet/underwriting/application_reviews/:applicationReviewID/documents", wrapper.UploadApplicationReviewDocuments)
	router.PATCH(baseURL+"/nonfleet/underwriting/application_reviews/:applicationReviewID/driver/:dlNumber", wrapper.UpdateApplicationReviewDriver)
	router.GET(baseURL+"/nonfleet/underwriting/application_reviews/:applicationReviewID/drivers", wrapper.GetApplicationReviewDrivers)
	router.PATCH(baseURL+"/nonfleet/underwriting/application_reviews/:applicationReviewID/drivers", wrapper.UpdateApplicationReviewDrivers)
	router.GET(baseURL+"/nonfleet/underwriting/application_reviews/:applicationReviewID/equipments", wrapper.GetApplicationReviewEquipments)
	router.PATCH(baseURL+"/nonfleet/underwriting/application_reviews/:applicationReviewID/equipments", wrapper.UpdateApplicationReviewEquipments)
	router.GET(baseURL+"/nonfleet/underwriting/application_reviews/:applicationReviewID/express_lane_feedback/submit", wrapper.GetExpressLaneFeedback)
	router.PATCH(baseURL+"/nonfleet/underwriting/application_reviews/:applicationReviewID/express_lane_feedback/submit", wrapper.PatchExpressLaneFeedback)
	router.GET(baseURL+"/nonfleet/underwriting/application_reviews/:applicationReviewID/flags", wrapper.GetApplicationReviewFlags)
	router.GET(baseURL+"/nonfleet/underwriting/application_reviews/:applicationReviewID/losses", wrapper.GetApplicationReviewLosses)
	router.PATCH(baseURL+"/nonfleet/underwriting/application_reviews/:applicationReviewID/losses", wrapper.UpdateApplicationReviewLosses)
	router.GET(baseURL+"/nonfleet/underwriting/application_reviews/:applicationReviewID/notes", wrapper.GetApplicationReviewNotes)
	router.PATCH(baseURL+"/nonfleet/underwriting/application_reviews/:applicationReviewID/notes", wrapper.UpdateApplicationReviewNotes)
	router.GET(baseURL+"/nonfleet/underwriting/application_reviews/:applicationReviewID/operations", wrapper.GetApplicationReviewOperations)
	router.PATCH(baseURL+"/nonfleet/underwriting/application_reviews/:applicationReviewID/operations", wrapper.UpdateApplicationReviewOperations)
	router.GET(baseURL+"/nonfleet/underwriting/application_reviews/:applicationReviewID/packages", wrapper.GetApplicationReviewsPackages)
	router.PATCH(baseURL+"/nonfleet/underwriting/application_reviews/:applicationReviewID/packages", wrapper.UpdateApplicationReviewPackages)
	router.POST(baseURL+"/nonfleet/underwriting/application_reviews/:applicationReviewID/quote/submit", wrapper.PostApplicationReviewQuoteSubmit)
	router.POST(baseURL+"/nonfleet/underwriting/application_reviews/:applicationReviewID/refer", wrapper.ReferApplicationReview)
	router.POST(baseURL+"/nonfleet/underwriting/application_reviews/:applicationReviewID/rollback", wrapper.RollbackApplicationReview)
	router.GET(baseURL+"/nonfleet/underwriting/application_reviews/:applicationReviewID/safety", wrapper.GetApplicationReviewSafety)
	router.PATCH(baseURL+"/nonfleet/underwriting/application_reviews/:applicationReviewID/safety", wrapper.UpdateApplicationReviewSafety)
	router.GET(baseURL+"/nonfleet/underwriting/application_reviews/:applicationReviewID/safety/safety_score", wrapper.GetApplicationReviewSafetyScore)
	router.PATCH(baseURL+"/nonfleet/underwriting/application_reviews/:applicationReviewID/safety/safety_score", wrapper.UpdateApplicationReviewSafetyScoreV2)
	router.POST(baseURL+"/nonfleet/underwriting/application_reviews/:applicationReviewID/set_mvr_pull", wrapper.SetApplicationReviewMVRPull)
	router.GET(baseURL+"/nonfleet/underwriting/application_reviews/:applicationReviewID/summary", wrapper.GetApplicationReviewsSummary)
	router.GET(baseURL+"/nonfleet/underwriting/application_reviews/:applicationReviewID/timeline", wrapper.GetApplicationReviewTimeline)
	router.GET(baseURL+"/nonfleet/underwriting/driver-violations", wrapper.GetDriverViolations)
	router.PATCH(baseURL+"/nonfleet/underwriting/v2/application-reviews/:applicationReviewID/driver/:dlNumber", wrapper.UpdateApplicationReviewDriverV2)
	router.GET(baseURL+"/nonfleet/underwriting/v2/application-reviews/:applicationReviewID/drivers", wrapper.GetApplicationReviewDriversV2)
	router.GET(baseURL+"/nonfleet/underwriting/:applicationReviewID/scrape", wrapper.GetScrapeInfo)
	router.POST(baseURL+"/nonfleet/underwriting/:applicationReviewID/scrape", wrapper.TriggerScrape)

}
