package policies_test

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"go.uber.org/mock/gomock"

	file_enums "nirvanatech.com/nirvana/common-go/file_upload_lib/enums"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	application_builder "nirvanatech.com/nirvana/common-go/test_utils/builders/application"
	policy_builder "nirvanatech.com/nirvana/common-go/test_utils/builders/policy"
	submission_builder "nirvanatech.com/nirvana/common-go/test_utils/builders/submission"
	"nirvanatech.com/nirvana/common-go/uuid_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/endorsement/change"
	change_enums "nirvanatech.com/nirvana/db-api/db_wrappers/endorsement/change/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/endorsement/change/fleet"
	endorsement_review_wrapper "nirvanatech.com/nirvana/db-api/db_wrappers/endorsement/endorsement-review"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	pd_fleet "nirvanatech.com/nirvana/db-api/db_wrappers/policy/program_data/fleet"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy/program_data/shared"
	endorsementrequest "nirvanatech.com/nirvana/endorsement/endorsement-request"
	endorsementreview "nirvanatech.com/nirvana/endorsement/endorsement-review"
	gql_models "nirvanatech.com/nirvana/gqlschema/models"
	"nirvanatech.com/nirvana/infra/authz"
	policy_enums "nirvanatech.com/nirvana/policy/enums"
	app_state_enums "nirvanatech.com/nirvana/quoting/app_state_machine/enums"
)

func (p *getPolicyTestSuite) TestGetPolicy_Fleet() {
	ctx := context.Background()
	now := p.clk.Now()
	p.startDate = now
	p.endDate = p.startDate.AddDate(1, 0, -1)

	// When creating an endorsement, the salesforce client is called.
	// We don't really want to assert anything there, as this is not an endorsement creation test
	p.mockSalesforceClient.EXPECT().
		QuerySalesforceObject(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		AnyTimes()

	fleetPolicy1 := p.seedFleetPolicy(
		ctx,
		1100001,
		policy_enums.PolicyStateActive,
		"6006006",
		p.startDate,
		p.endDate,
	)
	claimForPolicy1 := p.seedClaim(ctx, fleetPolicy1, "claimForFleet1")

	p.seedFleetEndorsement(
		ctx,
		*fleetPolicy1,
		change_enums.ChangeTypeInsuredUpdate,
		&fleet.DesignatedInsuredChange{
			DesignatedInsureds: []shared.Insured{
				{
					Name: fmt.Sprintf("Insured %d", fleetPolicy1.CompanyInfo.DOTNumber),
				},
			},
		},
		p.startDate.AddDate(0, 1, 0),
	)

	fleetPolicy2 := p.seedFleetPolicy(
		ctx,
		2200002,
		policy_enums.PolicyStateActive,
		"7007007",
		p.startDate,
		p.endDate,
	)
	p.seedFleetEndorsement(
		ctx,
		*fleetPolicy2,
		change_enums.ChangeTypeInsuredUpdate,
		&fleet.DesignatedInsuredChange{
			DesignatedInsureds: []shared.Insured{
				{
					Name: fmt.Sprintf("Insured %d", fleetPolicy2.CompanyInfo.DOTNumber),
				},
			},
		},
		p.startDate.AddDate(0, 1, 0),
	)

	fleetPolicyExpired := p.seedFleetPolicy(
		ctx,
		3300003,
		policy_enums.PolicyStateExpired,
		"8008008",
		now.AddDate(-2, 0, 0),
		now.AddDate(-1, 0, -1),
	)

	claimForExpiredPolicy := p.seedClaim(ctx, fleetPolicyExpired, "claimForFleet2")

	p.seedFleetEndorsement(
		ctx,
		*fleetPolicyExpired,
		change_enums.ChangeTypeInsuredUpdate,
		&fleet.DesignatedInsuredChange{
			DesignatedInsureds: []shared.Insured{
				{
					Name: fmt.Sprintf("Insured %d", fleetPolicyExpired.CompanyInfo.DOTNumber),
				},
			},
		},
		now.AddDate(-2, 1, 0),
	)
	p.seedFleetEndorsement(
		ctx,
		*fleetPolicyExpired,
		change_enums.ChangeTypeNamedShipperLimitUpdate,
		&fleet.NamedShipperLimitUpdate{
			NamedShippers: []pd_fleet.NamedShipper{
				{
					Name:  "Elmo Trucking",
					Limit: 1000,
				},
			},
		},
		now.AddDate(-2, 2, 3),
	)

	testCases := []struct {
		name    string
		user    authz.User
		claimId uuid.UUID
		want    gql_models.Policy
		wantErr bool
	}{
		{
			name:    "With non existent claim",
			user:    p.UsersFixture.Superuser.AuthzUser(),
			claimId: uuid.New(),
			wantErr: true,
		},
		{
			name:    "With SuperUser for active Fleet policy",
			user:    p.UsersFixture.Superuser.AuthzUser(),
			claimId: claimForPolicy1.Id,
			want: gql_models.Policy{
				PolicyNumber: "NISTK6006006-24",
				SignedLink: gql_models.ExpirableLink{
					Link: "https://s3.amazonaws.com/nirvana-pdfgen",
				},
				Underwriter: gql_models.BasicUser{
					Email: p.uw.Email,
					Name:  p.uw.FullName(),
				},
				Coverages: []gql_models.PolicyCoverageEnums{
					"CoverageAutoLiability",
				},
				SubCoverages: []gql_models.CoverageWithSymbols{
					{
						Coverage: "CoverageAutoLiability",
						Symbols:  []string{},
					},
				},
				Endorsements: []gql_models.Endorsement{
					{
						SignedLink: gql_models.ExpirableLink{
							Link: "https://s3.amazonaws.com/nirvana-pdfgen",
						},
						EffectiveInterval: gql_models.EndorsementEffectiveInterval{
							EffectiveDate:  "2022-03-15",
							ExpirationDate: "2023-03-14",
						},
						Underwriter: gql_models.BasicUser{
							Email: p.uw.Email,
							Name:  p.uw.FullName(),
						},
						ChangeTypes: []string{"ChangeTypeInsuredUpdate"},
						SupportingDocsAndForms: []gql_models.SupportingDocumentOrForm{
							{
								Filename: "form-doc-test-1",
							},
							{
								Filename: "supporting-doc-test-1",
							},
						},
					},
				},
			},
		},
		{
			name:    "With ClaimsAdmin",
			user:    p.UsersFixture.ClaimsAdmin.AuthzUser(),
			claimId: claimForPolicy1.Id,
			want: gql_models.Policy{
				PolicyNumber: "NISTK6006006-24",
				SignedLink: gql_models.ExpirableLink{
					Link: "https://s3.amazonaws.com/nirvana-pdfgen",
				},
				Underwriter: gql_models.BasicUser{
					Email: p.uw.Email,
					Name:  p.uw.FullName(),
				},
				Coverages: []gql_models.PolicyCoverageEnums{
					"CoverageAutoLiability",
				},
				SubCoverages: []gql_models.CoverageWithSymbols{
					{
						Coverage: "CoverageAutoLiability",
						Symbols:  []string{},
					},
				},
				Endorsements: []gql_models.Endorsement{
					{
						SignedLink: gql_models.ExpirableLink{
							Link: "https://s3.amazonaws.com/nirvana-pdfgen",
						},
						EffectiveInterval: gql_models.EndorsementEffectiveInterval{
							EffectiveDate:  "2022-03-15",
							ExpirationDate: "2023-03-14",
						},
						Underwriter: gql_models.BasicUser{
							Email: p.uw.Email,
							Name:  p.uw.FullName(),
						},
						ChangeTypes: []string{"ChangeTypeInsuredUpdate"},
						SupportingDocsAndForms: []gql_models.SupportingDocumentOrForm{
							{
								Filename: "form-doc-test-1",
							},
							{
								Filename: "supporting-doc-test-1",
							},
						},
					},
				},
			},
		},
		{
			name:    "With FleetAdmin, without permission",
			user:    p.UsersFixture.FleetAdmin.AuthzUser(),
			claimId: claimForPolicy1.Id,
			wantErr: true,
		},
		{
			name:    "With SuperUser for expired Fleet policy",
			user:    p.UsersFixture.Superuser.AuthzUser(),
			claimId: claimForExpiredPolicy.Id,
			want: gql_models.Policy{
				PolicyNumber: "NISTK8008008-24",
				SignedLink: gql_models.ExpirableLink{
					Link: "https://s3.amazonaws.com/nirvana-pdfgen",
				},
				Underwriter: gql_models.BasicUser{
					Email: p.uw.Email,
					Name:  p.uw.FullName(),
				},
				Coverages: []gql_models.PolicyCoverageEnums{
					"CoverageAutoLiability",
				},
				SubCoverages: []gql_models.CoverageWithSymbols{
					{
						Coverage: "CoverageAutoLiability",
						Symbols:  []string{},
					},
				},
				Endorsements: []gql_models.Endorsement{
					{
						SignedLink: gql_models.ExpirableLink{
							Link: "https://s3.amazonaws.com/nirvana-pdfgen",
						},
						EffectiveInterval: gql_models.EndorsementEffectiveInterval{
							EffectiveDate:  "2022-03-15",
							ExpirationDate: "2023-03-14",
						},
						Underwriter: gql_models.BasicUser{
							Email: p.uw.Email,
							Name:  p.uw.FullName(),
						},
						ChangeTypes: []string{"ChangeTypeInsuredUpdate"},
					},
					{
						SignedLink: gql_models.ExpirableLink{
							Link: "https://s3.amazonaws.com/nirvana-pdfgen",
						},
						EffectiveInterval: gql_models.EndorsementEffectiveInterval{
							EffectiveDate:  "2022-03-15",
							ExpirationDate: "2023-03-14",
						},
						Underwriter: gql_models.BasicUser{
							Email: p.uw.Email,
							Name:  p.uw.FullName(),
						},
						ChangeTypes: []string{"ChangeTypeNamedShipperLimitUpdate"},
						SupportingDocsAndForms: []gql_models.SupportingDocumentOrForm{
							{
								Filename: "form-doc-test-1",
							},
							{
								Filename: "supporting-doc-test-1",
							},
						},
					},
				},
			},
		},
	}

	for _, tc := range testCases {
		p.Run(tc.name, func() {
			pol, err := getPolicy(ctx, tc.user, p.queryClient, tc.claimId.String())
			if tc.wantErr {
				p.Require().Error(err)
				return
			}
			p.Require().NoError(err)

			p.NotEqual(uuid.Nil, pol.Id)
			p.Equal(tc.want.PolicyNumber, pol.PolicyNumber)
			p.Contains(pol.SignedLink.Link, tc.want.SignedLink.Link)
			p.Equal(tc.want.Underwriter.Name, pol.Underwriter.Name)
			p.Equal(tc.want.Underwriter.Email, pol.Underwriter.Email)

			// Verify DocumentID is properly populated
			p.NotEqual(uuid.Nil, pol.DocumentID, "Policy DocumentID should not be nil")

			p.assertCoverages(tc.want.Coverages, pol.Coverages)
			p.assertEndorsements(tc.want.Endorsements, pol.Endorsements)
			p.assertSubCoverages(tc.want.SubCoverages, pol.SubCoverages)
		})
	}
}

func (p *getPolicyTestSuite) seedFleetPolicy(
	ctx context.Context,
	dotNumber int64,
	policyState policy_enums.PolicyState,
	policyIdentifier string,
	startDate, endDate time.Time,
) *policy.Policy {
	appId := uuid.New()
	app := application_builder.
		New(p.featureFlagClient).
		WithDefaultMockData().
		WithID(appId).
		WithAgencyID(p.Agency.ID).
		WithDOTNumber(dotNumber).
		WithApplicationState(app_state_enums.AppStatePolicyCreated).
		Build()
	p.Require().NoError(p.applicationWrapper.InsertApp(ctx, *app))

	subId := uuid.New()
	sub := submission_builder.
		New().
		WithBindable(true).
		FromApplication(*app).
		WithUnderwriterId(p.uw.ID).
		WithID(subId).
		Build()
	p.Require().NoError(p.applicationWrapper.InsertSubmission(ctx, *sub))

	pol := policy_builder.
		New().
		WithDefaultMockData().
		WithId(uuid.New()).
		WithApplicationId(appId).
		WithPolicyNumber("NISTK", policyIdentifier, 2024).
		WithDOTNumber(dotNumber).
		WithSubmissionId(subId).
		WithProgramType(enums.ProgramTypeFleet).
		WithDocumentHandleId(uuid.New()).
		WithAgencyId(p.Agency.ID).
		WithCompanyName("Nirvana").
		WithEffectiveDates(startDate, endDate).
		WithState(policyState).
		Build()
	p.Require().NoError(p.policyWrapper.InsertPolicy(ctx, pol))

	fileName := "policy-doc-test-1"
	err := p.fileUploadManager.UploadFile(
		ctx,
		strings.NewReader("mock-contents"),
		pol.DocumentHandleId,
		file_enums.FileTypePDFGenPDF,
		fileName,
		uuid_utils.StableUUID(fileName),
		file_enums.FileDestinationGroupPDFGen,
	)
	p.Require().NoError(err)

	return pol
}

func (p *getPolicyTestSuite) seedFleetEndorsement(
	ctx context.Context,
	pol policy.Policy,
	changeType change_enums.ChangeType,
	changeData change.EndorsementData,
	approvedAt time.Time,
) {
	req, err := p.endorsementRequestClient.Create(
		ctx,
		&endorsementrequest.CreateRequest{
			PolicyNumber: pol.PolicyNumber,
			AgencyID:     p.Agency.ID,
			CreatedBy:    p.uw.ID,
		},
	)
	p.Require().NoError(err)

	err = p.endorsementRequestClient.Submit(
		ctx,
		&endorsementrequest.SubmitRequest{
			EndorsementRequestID: req.ID,
			Changes: []change.Change{
				{
					Type: changeType,
					Data: changeData,
				},
			},
			EffectiveDate:   pol.EffectiveDate,
			EffectiveDateTo: pointer_utils.ToPointer(pol.EffectiveDateTo.AddDate(0, 0, -1)),
			ProducerID:      p.AgencyProducer.ID,
		},
	)
	p.Require().NoError(err)

	fileName := "policy-change-form-test-1"
	fileHandle := uuid.New()
	err = p.fileUploadManager.UploadFile(
		ctx,
		strings.NewReader("mock-contents"),
		fileHandle,
		file_enums.FileTypePDFGenPDF,
		fileName,
		uuid_utils.StableUUID(fileName),
		file_enums.FileDestinationGroupPDFGen,
	)
	p.Require().NoError(err)

	rev, err := p.endorsementReviewClient.Create(
		ctx,
		&endorsementreview.CreateEndorsementReviewRequest{
			EndorsementRequestID: req.ID,
			Data: &endorsement_review_wrapper.ReviewData{
				ChangeReviews: []*endorsement_review_wrapper.ChangeReview{
					{
						Change: change.Change{
							Type: changeType,
						},
					},
					// we add a second change of same type, to test that we only send each change type once
					{
						Change: change.Change{
							Type: changeType,
						},
					},
				},
			},
			UnderwriterID:   p.uw.ID,
			PolicyNumber:    pol.PolicyNumber,
			AgencyID:        p.Agency.ID,
			EffectiveDate:   pol.EffectiveDate,
			EffectiveDateTo: &pol.EffectiveDateTo,
		},
	)
	p.Require().NoError(err)

	// Create supporting documents for testing
	supportingDocFileName := "supporting-doc-test-1"
	supportingDocHandle := uuid.New()
	err = p.fileUploadManager.UploadFile(
		ctx,
		strings.NewReader("supporting doc contents"),
		supportingDocHandle,
		file_enums.FileTypePDFGenPDF,
		supportingDocFileName,
		uuid_utils.StableUUID(supportingDocFileName),
		file_enums.FileDestinationGroupPDFGen,
	)
	p.Require().NoError(err)

	// Create form documents for testing
	formFileName := "form-doc-test-1"
	formHandle := uuid.New()
	err = p.fileUploadManager.UploadFile(
		ctx,
		strings.NewReader("form doc contents"),
		formHandle,
		file_enums.FileTypePDFGenPDF,
		formFileName,
		uuid_utils.StableUUID(formFileName),
		file_enums.FileDestinationGroupPDFGen,
	)
	p.Require().NoError(err)

	err = p.endorsementReviewClient.Approve(
		ctx,
		rev.ID,
		nil,                              // reason
		[]uuid.UUID{formHandle},          // Forms
		[]uuid.UUID{supportingDocHandle}, // Supporting docs
	)
	p.Require().NoError(err)

	// while this looks unorthodox, it's the same logic that is being run in fill_policy_change_form.go
	// so that we can have the PolicyChangeFormHandleId set.
	err = p.endorsementReviewWrapper.Update(
		ctx, rev.ID,
		func(review *endorsement_review_wrapper.EndorsementReview) (*endorsement_review_wrapper.EndorsementReview, error) {
			review.PolicyChangeFormHandleId = &fileHandle
			review.ApprovedAt = &approvedAt
			return review, nil
		})
	p.Require().NoError(err)
}
