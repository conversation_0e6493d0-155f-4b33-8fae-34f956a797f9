package policies

import (
	"context"
	"strconv"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/samsarahq/thunder/graphql"
	claim_endorsements "nirvanatech.com/nirvana/claims/endorsements"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/endorsement/change"
	policy_db "nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	gql_models "nirvanatech.com/nirvana/gqlschema/models"
	"nirvanatech.com/nirvana/infra/authz"
)

func (r *Resolver) fetchPolicyEndorsementsForFleet(
	ctx context.Context,
	p policy_db.Policy,
) ([]gql_models.Endorsement, error) {
	pn := p.PolicyNumber.String()
	ends, err := r.deps.ClaimEndorsementClient.GetFleetEndorsements(ctx, p)
	if err != nil {
		return nil, graphql.WrapAsSafeError(err, "failed to get fleet endorsements for policy %s", pn)
	}

	uwIds := slice_utils.Map(
		ends,
		func(e claim_endorsements.FleetEndorsement) uuid.UUID {
			return e.Review.UnderwriterID
		},
	)

	uws, err := r.deps.AuthWrapper.FetchAuthzUsers(ctx, slice_utils.Dedup(uwIds))
	if err != nil {
		return nil, graphql.WrapAsSafeError(
			err,
			"failed to get UWs for endorsement reviews of policy %s",
			pn,
		)
	}

	response := make([]gql_models.Endorsement, 0, len(ends))
	for _, end := range ends {
		parsedEndorsement, err := r.constructEndorsementForFleet(
			ctx,
			uws,
			end,
		)
		if err != nil {
			return nil, graphql.WrapAsSafeError(
				err,
				"failed to construct endorsement for policy %s",
				pn,
			)
		}

		response = append(response, *parsedEndorsement)
	}

	return response, nil
}

func (r *Resolver) constructEndorsementForFleet(
	ctx context.Context,
	uws []authz.User,
	end claim_endorsements.FleetEndorsement,
) (*gql_models.Endorsement, error) {
	changeTypes := slice_utils.Map(end.Endorsement.ApprovedChanges, func(c change.Change) string {
		return c.Type.String()
	})
	changeTypes = slice_utils.Dedup(changeTypes)

	uw := slice_utils.Find(
		uws,
		func(u authz.User) bool {
			return u.ID == end.Review.UnderwriterID
		},
	)
	if uw == nil {
		return nil, errors.Newf(
			"underwriter %s not found for endorsement review %s",
			end.Review.UnderwriterID,
			end.Review.ID,
		)
	}

	if end.Review.ApprovedAt == nil {
		return nil, errors.Newf(
			"approved endorsement review %s does not have an approved date",
			end.Review.ID,
		)
	}

	if end.Endorsement.EffectiveDateTo == nil {
		return nil, errors.Newf(
			"endorsement %s does not have an effective date to",
			end.Endorsement.ID,
		)
	}

	if end.FileHandle == nil {
		return nil, errors.Newf(
			"endorsement %s does not have a file handle",
			end.Endorsement.ID,
		)
	}

	if end.SignedLink == nil {
		return nil, errors.Newf(
			"endorsement %s does not have a signed link",
			end.Endorsement.ID,
		)
	}

	// Process all supporting documents and forms using helper function
	supportingDocsAndForms, err := processSupportingDocsAndForms(
		ctx,
		r.deps.FileUploadManager,
		end.Review.FormsHandleIds,
		end.Review.SupportingDocsHandleIds,
	)
	if err != nil {
		return nil, errors.Wrapf(
			err,
			"failed to process supporting documents and forms for endorsement %s",
			end.Endorsement.ID,
		)
	}

	return &gql_models.Endorsement{
		Id: end.Endorsement.ID.String(),
		SignedLink: gql_models.ExpirableLink{
			Link:       *end.SignedLink,
			Expiration: end.SignedLinkExpiration.Format(time_utils.ReferenceLayout),
		},
		DocumentID: *end.FileHandle,
		EffectiveInterval: gql_models.EndorsementEffectiveInterval{
			EffectiveDate:  end.Endorsement.EffectiveDate.Format(time_utils.ReferenceLayout),
			ExpirationDate: end.Endorsement.EffectiveDateTo.Format(time_utils.ReferenceLayout),
		},
		ApprovedAt: end.Review.ApprovedAt.Format(time_utils.ReferenceLayout),
		Underwriter: gql_models.BasicUser{
			Name:  uw.FullName(),
			Email: uw.Email,
		},
		ChangeTypes:            changeTypes,
		SupportingDocsAndForms: supportingDocsAndForms,
	}, nil
}

func (r *Resolver) fetchPolicyForFleet(
	ctx context.Context,
	p policy_db.Policy,
) (*gql_models.Policy, error) {
	sub, err := r.deps.ApplicationWrapper.GetSubmissionById(ctx, p.SubmissionId.String())
	if err != nil {
		return nil, graphql.WrapAsSafeError(err, "failed to get submission")
	}

	now := time.Now()
	policyDocumentLink, err := r.signLink(ctx, p.DocumentHandleId)
	if err != nil {
		log.Error(ctx, "couldn't generate policy document download link", log.Err(err))
		return nil, err
	}

	subcoverages := buildSubCoveragesForFleet(sub.CoverageInfo.Coverages)

	uw, err := r.deps.AuthWrapper.FetchUserInfo(ctx, sub.UnderwriterID)
	if err != nil {
		log.Error(ctx, "failed to fetch underwriter info", log.Err(err))
		return nil, graphql.WrapAsSafeError(err, "failed to fetch underwriter info")
	}

	policyType, err := p.PolicyNumber.GetPolicyType()
	if err != nil {
		log.Error(ctx, "failed to get policy type", log.Err(err))
		return nil, graphql.WrapAsSafeError(err, "failed to get policy type")
	}

	return &gql_models.Policy{
		Id:               p.Id,
		InsuredName:      p.InsuredName,
		InsuredDOTNumber: strconv.FormatInt(p.CompanyInfo.DOTNumber, 10),
		Coverages: []gql_models.PolicyCoverageEnums{
			gql_models.PolicyCoverageEnums(policyType.String()),
		},
		State:        p.State,
		StartDate:    p.EffectiveDate,
		EndDate:      p.EffectiveDateTo,
		PolicyNumber: p.PolicyNumber.String(),
		SubCoverages: subcoverages,
		SignedLink: gql_models.ExpirableLink{
			Link:       *policyDocumentLink,
			Expiration: now.Add(documentLinkExpiration).Format(time_utils.ReferenceLayout),
		},
		DocumentID: p.DocumentHandleId,
		Underwriter: gql_models.BasicUser{
			Email: uw.Email,
			Name:  uw.FullName(),
		},
	}, nil
}

func buildSubCoveragesForFleet(
	coverages []application.CoverageDetails,
) []gql_models.CoverageWithSymbols {
	var result []gql_models.CoverageWithSymbols
	for _, cov := range coverages {
		var symbols []string
		if cov.SymbolsAndDefinitions != nil {
			for _, sym := range *cov.SymbolsAndDefinitions {
				symbols = append(symbols, sym.Symbol)
			}
		}
		result = append(result, gql_models.CoverageWithSymbols{
			Coverage: cov.CoverageType.String(),
			Symbols:  symbols,
		})
	}
	return result
}
