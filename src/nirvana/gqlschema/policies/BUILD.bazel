load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "policies",
    srcs = [
        "deps.go",
        "get_policies.go",
        "get_policy.go",
        "get_policy_fleet.go",
        "get_policy_non_fleet.go",
        "resolver.go",
        "supporting_docs_helpers.go",
    ],
    importpath = "nirvanatech.com/nirvana/gqlschema/policies",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/api-server/handlers/common/endorsement",
        "//nirvana/application/endorsementapp/endorsement-request",
        "//nirvana/application/endorsementapp/endorsement-review",
        "//nirvana/application/normalizer",
        "//nirvana/claims/client",
        "//nirvana/claims/endorsements",
        "//nirvana/common-go/file_upload_lib",
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/auth",
        "//nirvana/db-api/db_wrappers/endorsement/change",
        "//nirvana/db-api/db_wrappers/endorsement/endorsement",
        "//nirvana/db-api/db_wrappers/endorsementapp",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-review",
        "//nirvana/db-api/db_wrappers/fleet",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/policy",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/endorsement/endorsement-review",
        "//nirvana/gqlschema/models",
        "//nirvana/gqlschema/models/enums",
        "//nirvana/gqlschema/resolver",
        "//nirvana/infra/authz",
        "//nirvana/infra/authz/checker",
        "//nirvana/insurance-bundle/config",
        "//nirvana/insurance-bundle/model",
        "//nirvana/insurance-bundle/service",
        "//nirvana/insurance-core/proto",
        "//nirvana/openapi-specs/components/endorsementapp/intake",
        "//nirvana/policy",
        "//nirvana/policy/enums",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_samsarahq_thunder//graphql",
        "@com_github_samsarahq_thunder//graphql/schemabuilder",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "policies_test",
    srcs = [
        "get_policies_test.go",
        "get_policy_fleet_test.go",
        "get_policy_non_fleet_test.go",
        "get_policy_test.go",
    ],
    deps = [
        "//nirvana/application/endorsementapp/endorsement-request",
        "//nirvana/application/endorsementapp/endorsement-review",
        "//nirvana/claims/client",
        "//nirvana/claims/db",
        "//nirvana/claims/enums",
        "//nirvana/common-go/feature_flag_lib",
        "//nirvana/common-go/file_upload_lib",
        "//nirvana/common-go/file_upload_lib/enums",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/proto",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/test_utils",
        "//nirvana/common-go/test_utils/builders/admitted_app",
        "//nirvana/common-go/test_utils/builders/admitted_submission",
        "//nirvana/common-go/test_utils/builders/application",
        "//nirvana/common-go/test_utils/builders/policy",
        "//nirvana/common-go/test_utils/builders/submission",
        "//nirvana/common-go/time_utils",
        "//nirvana/common-go/uuid_utils",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/auth",
        "//nirvana/db-api/db_wrappers/endorsement/change",
        "//nirvana/db-api/db_wrappers/endorsement/change/enums",
        "//nirvana/db-api/db_wrappers/endorsement/change/fleet",
        "//nirvana/db-api/db_wrappers/endorsement/endorsement-review",
        "//nirvana/db-api/db_wrappers/endorsementapp",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-request",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-review",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-review/enums",
        "//nirvana/db-api/db_wrappers/fleet",
        "//nirvana/db-api/db_wrappers/forms",
        "//nirvana/db-api/db_wrappers/forms/enums",
        "//nirvana/db-api/db_wrappers/insurance-bundle/policyv2",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/policy",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/db-api/db_wrappers/policy/program_data/fleet",
        "//nirvana/db-api/db_wrappers/policy/program_data/nonfleet/admitted",
        "//nirvana/db-api/db_wrappers/policy/program_data/shared",
        "//nirvana/endorsement/endorsement-request",
        "//nirvana/endorsement/endorsement-review",
        "//nirvana/external_client/salesforce/client",
        "//nirvana/gqlschema/models",
        "//nirvana/gqlschema/models/enums",
        "//nirvana/graphql-server/queryclient",
        "//nirvana/infra/authz",
        "//nirvana/infra/fx/testfixtures/agency_fixture",
        "//nirvana/infra/fx/testfixtures/fleet_fixture",
        "//nirvana/infra/fx/testfixtures/users_fixture",
        "//nirvana/infra/fx/testloader",
        "//nirvana/insurance-bundle/model",
        "//nirvana/insurance-bundle/model/endorsement",
        "//nirvana/insurance-bundle/service",
        "//nirvana/insurance-core/proto",
        "//nirvana/insured/model",
        "//nirvana/nonfleet/model",
        "//nirvana/nonfleet/model/endorsement",
        "//nirvana/policy/enums",
        "//nirvana/policy_common/forms_generator/compilation",
        "//nirvana/quoting/app_state_machine/enums",
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//suite",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
        "@org_uber_go_mock//gomock",
    ],
)
