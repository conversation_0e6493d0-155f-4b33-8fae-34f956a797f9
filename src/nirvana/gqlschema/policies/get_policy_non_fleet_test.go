package policies_test

import (
	"context"
	"fmt"
	"slices"
	"strings"
	"time"

	"github.com/google/uuid"
	"google.golang.org/protobuf/types/known/timestamppb"

	file_enums "nirvanatech.com/nirvana/common-go/file_upload_lib/enums"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/proto"
	admitted_app_builder "nirvanatech.com/nirvana/common-go/test_utils/builders/admitted_app"
	admitted_sub_builder "nirvanatech.com/nirvana/common-go/test_utils/builders/admitted_submission"
	policy_builder "nirvanatech.com/nirvana/common-go/test_utils/builders/policy"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/common-go/uuid_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	db_endorsementapp "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp"
	ib_db_endorsement_request "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-request"
	ib_db_endorsement_review "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-review"
	endreviewenums "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-review/enums"
	forms_enums "nirvanatech.com/nirvana/db-api/db_wrappers/forms/enums"
	ib_policy "nirvanatech.com/nirvana/db-api/db_wrappers/insurance-bundle/policyv2"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy/program_data/nonfleet/admitted"
	gql_models "nirvanatech.com/nirvana/gqlschema/models"
	"nirvanatech.com/nirvana/infra/authz"
	ib_model "nirvanatech.com/nirvana/insurance-bundle/model"
	"nirvanatech.com/nirvana/insurance-bundle/model/endorsement"
	ib_service "nirvanatech.com/nirvana/insurance-bundle/service"
	insurancecoreproto "nirvanatech.com/nirvana/insurance-core/proto"
	insured_model "nirvanatech.com/nirvana/insured/model"
	nf_model "nirvanatech.com/nirvana/nonfleet/model"
	nf_types "nirvanatech.com/nirvana/nonfleet/model/endorsement"
	policy_enums "nirvanatech.com/nirvana/policy/enums"
	"nirvanatech.com/nirvana/policy_common/forms_generator/compilation"
)

func (p *getPolicyTestSuite) TestGetPolicy_NonFleet() {
	ctx := context.Background()
	nonFleetPolicy1, ibId1 := p.seedNonFleetPolicy(
		ctx,
		1100001,
		policy_enums.PolicyStateActive,
		"6006006",
		p.startDate,
		p.endDate,
	)
	claimForPolicy1 := p.seedClaim(ctx, nonFleetPolicy1, "claimForNF1")

	p.seedNonFleetEndorsement(
		ctx,
		*nonFleetPolicy1,
		ibId1,
	)

	testCases := []struct {
		name    string
		user    authz.User
		claimId uuid.UUID
		want    gql_models.Policy
		wantErr bool
	}{
		{
			name:    "With non existent policy",
			user:    p.UsersFixture.Superuser.AuthzUser(),
			claimId: uuid.New(),
			wantErr: true,
		},
		{
			name:    "With ClaimsAdmin for active NF policy",
			user:    p.UsersFixture.ClaimsAdmin.AuthzUser(),
			claimId: claimForPolicy1.Id,
			want: gql_models.Policy{
				PolicyNumber: "NNFTK6006006-24",
				SignedLink: gql_models.ExpirableLink{
					Link: "https://s3.amazonaws.com/nirvana-pdfgen",
				},
				Underwriter: gql_models.BasicUser{
					Email: p.uw.Email,
					Name:  p.uw.FullName(),
				},
				Coverages: []gql_models.PolicyCoverageEnums{
					"CoverageAutoLiability",
				},
				SubCoverages: []gql_models.CoverageWithSymbols{
					{
						Coverage: "CoveragePersonalInjuryProtection",
						Symbols:  []string{"64"},
					},
				},
				Endorsements: []gql_models.Endorsement{
					{
						SignedLink: gql_models.ExpirableLink{
							Link: "https://s3.amazonaws.com/nirvana-pdfgen",
						},

						EffectiveInterval: gql_models.EndorsementEffectiveInterval{
							EffectiveDate:  "2022-03-15",
							ExpirationDate: "2023-03-14",
						},
						Underwriter: gql_models.BasicUser{
							Email: p.uw.Email,
							Name:  p.uw.FullName(),
						},
						ChangeTypes: []string{"NonFleetChangeType_Driver"},
						SupportingDocsAndForms: []gql_models.SupportingDocumentOrForm{
							{
								Filename: "policy-change-form-test-1",
							},
							{
								Filename: "supporting-doc-nf-test-1",
							},
						},
					},
				},
				Vehicles: []gql_models.PolicyVehicle{
					{
						Vin:   "3AKJGLD51FSGH8069",
						Make:  "FREIGHTLINER",
						Model: "CASCADIA",
						Year:  2015,
					},
					{
						Vin:   "1HTMMAAN87H409832",
						Make:  "INTERNATIONAL",
						Model: "PROSTAR",
						Year:  2018,
					},
				},
				Drivers: []gql_models.PolicyDriver{
					{
						FirstName:          "Bogdan",
						LastName:           "Bazhan",
						DateOfBirth:        time_utils.NewDate(1985, 2, 4).String(),
						DateOfHire:         time_utils.NewDate(2005, 7, 4).String(),
						YearsOfExperience:  10,
						LicenseNumber:      "61786778",
						LicenseState:       "GA",
						IsOutOfState:       true,
						IsIncludedInPolicy: true,
					},
					{
						FirstName:          "Ihor",
						LastName:           "Fedyushnko",
						DateOfBirth:        time_utils.NewDate(1956, 2, 25).String(),
						DateOfHire:         time_utils.NewDate(2005, 7, 7).String(),
						YearsOfExperience:  9,
						LicenseNumber:      "F32040056059",
						LicenseState:       "IL",
						IsOutOfState:       false,
						IsIncludedInPolicy: true,
					},
					{
						FirstName:          "Ihor",
						LastName:           "Peleh",
						DateOfBirth:        time_utils.NewDate(1974, 5, 24).String(),
						DateOfHire:         time_utils.NewDate(2020, 12, 11).String(),
						YearsOfExperience:  4,
						LicenseNumber:      "SP731660",
						LicenseState:       "OH",
						IsOutOfState:       true,
						IsIncludedInPolicy: true,
					},
					{
						FirstName:          "Oleh",
						LastName:           "Shevchuk",
						DateOfBirth:        time_utils.NewDate(1980, 8, 7).String(),
						DateOfHire:         time_utils.NewDate(2005, 7, 1).String(),
						YearsOfExperience:  7,
						LicenseNumber:      "250603403",
						LicenseState:       "NY",
						IsOutOfState:       true,
						IsIncludedInPolicy: true,
					},
					{
						FirstName:          "Valentin",
						LastName:           "Minashkin",
						DateOfBirth:        time_utils.NewDate(1967, 9, 4).String(),
						DateOfHire:         time_utils.NewDate(2020, 12, 1).String(),
						YearsOfExperience:  5,
						LicenseNumber:      "60718247",
						LicenseState:       "GA",
						IsOutOfState:       true,
						IsIncludedInPolicy: true,
					},
					{
						FirstName:          "Zakaria",
						LastName:           "Barkjudanashvili",
						DateOfBirth:        time_utils.NewDate(1978, 10, 18).String(),
						DateOfHire:         time_utils.NewDate(2017, 7, 1).String(),
						YearsOfExperience:  7,
						LicenseNumber:      "059918327",
						LicenseState:       "GA",
						IsOutOfState:       true,
						IsIncludedInPolicy: true,
					},
					{
						FirstName:          "Zviadi",
						LastName:           "Bukhaidze",
						DateOfBirth:        time_utils.NewDate(1981, 3, 6).String(),
						DateOfHire:         time_utils.NewDate(2020, 8, 1).String(),
						YearsOfExperience:  4,
						LicenseNumber:      "61896233",
						LicenseState:       "GA",
						IsOutOfState:       true,
						IsIncludedInPolicy: true,
					},
					{
						FirstName:          "Vasyl Y",
						LastName:           "Yakovishak",
						DateOfBirth:        time_utils.NewDate(1973, 6, 6).String(),
						DateOfHire:         time_utils.NewDate(2021, 8, 4).String(),
						YearsOfExperience:  12,
						LicenseNumber:      "Y21287973161",
						LicenseState:       "IL",
						IsOutOfState:       false,
						IsIncludedInPolicy: true,
					},
					{
						FirstName:          "Gueorgui",
						LastName:           "Hadjinitchev",
						DateOfBirth:        time_utils.NewDate(1973, 8, 6).String(),
						DateOfHire:         time_utils.NewDate(2018, 1, 1).String(),
						YearsOfExperience:  8,
						LicenseNumber:      "57315824",
						LicenseState:       "GA",
						IsOutOfState:       true,
						IsIncludedInPolicy: true,
					},
					{
						FirstName:          "Hennadiy",
						LastName:           "Marchuk",
						DateOfBirth:        time_utils.NewDate(1981, 12, 19).String(),
						DateOfHire:         time_utils.NewDate(2023, 2, 2).String(),
						YearsOfExperience:  15,
						LicenseNumber:      "52488416",
						LicenseState:       "GA",
						IsOutOfState:       true,
						IsIncludedInPolicy: true,
					},
					{
						FirstName:          "KAKHABER",
						LastName:           "GIKASHVILI",
						DateOfBirth:        time_utils.NewDate(1979, 3, 16).String(),
						DateOfHire:         time_utils.NewDate(2023, 1, 23).String(),
						YearsOfExperience:  4,
						LicenseNumber:      "G42744240003792",
						LicenseState:       "NJ",
						IsOutOfState:       true,
						IsIncludedInPolicy: true,
					},
					{
						FirstName:          "Ludbin",
						LastName:           "Garcia",
						DateOfBirth:        time_utils.NewDate(1980, 12, 31).String(),
						DateOfHire:         time_utils.NewDate(2022, 10, 3).String(),
						YearsOfExperience:  7,
						LicenseNumber:      "45048511",
						LicenseState:       "TX",
						IsOutOfState:       true,
						IsIncludedInPolicy: true,
					},
					{
						FirstName:          "Vlad",
						LastName:           "Sokolianski",
						DateOfBirth:        time_utils.NewDate(1962, 10, 4).String(),
						DateOfHire:         time_utils.NewDate(2023, 2, 7).String(),
						YearsOfExperience:  13,
						LicenseNumber:      "53798200",
						LicenseState:       "GA",
						IsOutOfState:       true,
						IsIncludedInPolicy: true,
					},
				},
			},
		},
		{
			name:    "With SuperUser for active NF policy",
			user:    p.UsersFixture.Superuser.AuthzUser(),
			claimId: claimForPolicy1.Id,
			want: gql_models.Policy{
				PolicyNumber: "NNFTK6006006-24",
				SignedLink: gql_models.ExpirableLink{
					Link: "https://s3.amazonaws.com/nirvana-pdfgen",
				},
				Underwriter: gql_models.BasicUser{
					Email: p.uw.Email,
					Name:  p.uw.FullName(),
				},
				Coverages: []gql_models.PolicyCoverageEnums{
					"CoverageAutoLiability",
				},
				SubCoverages: []gql_models.CoverageWithSymbols{
					{
						Coverage: "CoveragePersonalInjuryProtection",
						Symbols:  []string{"64"},
					},
				},
				Endorsements: []gql_models.Endorsement{
					{
						SignedLink: gql_models.ExpirableLink{
							Link: "https://s3.amazonaws.com/nirvana-pdfgen",
						},

						EffectiveInterval: gql_models.EndorsementEffectiveInterval{
							EffectiveDate:  "2022-03-15",
							ExpirationDate: "2023-03-14",
						},
						Underwriter: gql_models.BasicUser{
							Email: p.uw.Email,
							Name:  p.uw.FullName(),
						},
						ChangeTypes: []string{"NonFleetChangeType_Driver"},
						SupportingDocsAndForms: []gql_models.SupportingDocumentOrForm{
							{
								Filename: "policy-change-form-test-1",
							},
							{
								Filename: "supporting-doc-nf-test-1",
							},
						},
					},
				},
				Vehicles: []gql_models.PolicyVehicle{
					{
						Vin:   "3AKJGLD51FSGH8069",
						Make:  "FREIGHTLINER",
						Model: "CASCADIA",
						Year:  2015,
					},
					{
						Vin:   "1HTMMAAN87H409832",
						Make:  "INTERNATIONAL",
						Model: "PROSTAR",
						Year:  2018,
					},
				},
				Drivers: []gql_models.PolicyDriver{
					{
						FirstName:          "Bogdan",
						LastName:           "Bazhan",
						DateOfBirth:        time_utils.NewDate(1985, 2, 4).String(),
						DateOfHire:         time_utils.NewDate(2005, 7, 4).String(),
						YearsOfExperience:  10,
						LicenseNumber:      "61786778",
						LicenseState:       "GA",
						IsOutOfState:       true,
						IsIncludedInPolicy: true,
					},
					{
						FirstName:          "Ihor",
						LastName:           "Fedyushnko",
						DateOfBirth:        time_utils.NewDate(1956, 2, 25).String(),
						DateOfHire:         time_utils.NewDate(2005, 7, 7).String(),
						YearsOfExperience:  9,
						LicenseNumber:      "F32040056059",
						LicenseState:       "IL",
						IsOutOfState:       false,
						IsIncludedInPolicy: true,
					},
					{
						FirstName:          "Ihor",
						LastName:           "Peleh",
						DateOfBirth:        time_utils.NewDate(1974, 5, 24).String(),
						DateOfHire:         time_utils.NewDate(2020, 12, 11).String(),
						YearsOfExperience:  4,
						LicenseNumber:      "SP731660",
						LicenseState:       "OH",
						IsOutOfState:       true,
						IsIncludedInPolicy: true,
					},
					{
						FirstName:          "Oleh",
						LastName:           "Shevchuk",
						DateOfBirth:        time_utils.NewDate(1980, 8, 7).String(),
						DateOfHire:         time_utils.NewDate(2005, 7, 1).String(),
						YearsOfExperience:  7,
						LicenseNumber:      "250603403",
						LicenseState:       "NY",
						IsOutOfState:       true,
						IsIncludedInPolicy: true,
					},
					{
						FirstName:          "Valentin",
						LastName:           "Minashkin",
						DateOfBirth:        time_utils.NewDate(1967, 9, 4).String(),
						DateOfHire:         time_utils.NewDate(2020, 12, 1).String(),
						YearsOfExperience:  5,
						LicenseNumber:      "60718247",
						LicenseState:       "GA",
						IsOutOfState:       true,
						IsIncludedInPolicy: true,
					},
					{
						FirstName:          "Zakaria",
						LastName:           "Barkjudanashvili",
						DateOfBirth:        time_utils.NewDate(1978, 10, 18).String(),
						DateOfHire:         time_utils.NewDate(2017, 7, 1).String(),
						YearsOfExperience:  7,
						LicenseNumber:      "059918327",
						LicenseState:       "GA",
						IsOutOfState:       true,
						IsIncludedInPolicy: true,
					},
					{
						FirstName:          "Zviadi",
						LastName:           "Bukhaidze",
						DateOfBirth:        time_utils.NewDate(1981, 3, 6).String(),
						DateOfHire:         time_utils.NewDate(2020, 8, 1).String(),
						YearsOfExperience:  4,
						LicenseNumber:      "61896233",
						LicenseState:       "GA",
						IsOutOfState:       true,
						IsIncludedInPolicy: true,
					},
					{
						FirstName:          "Vasyl Y",
						LastName:           "Yakovishak",
						DateOfBirth:        time_utils.NewDate(1973, 6, 6).String(),
						DateOfHire:         time_utils.NewDate(2021, 8, 4).String(),
						YearsOfExperience:  12,
						LicenseNumber:      "Y21287973161",
						LicenseState:       "IL",
						IsOutOfState:       false,
						IsIncludedInPolicy: true,
					},
					{
						FirstName:          "Gueorgui",
						LastName:           "Hadjinitchev",
						DateOfBirth:        time_utils.NewDate(1973, 8, 6).String(),
						DateOfHire:         time_utils.NewDate(2018, 1, 1).String(),
						YearsOfExperience:  8,
						LicenseNumber:      "57315824",
						LicenseState:       "GA",
						IsOutOfState:       true,
						IsIncludedInPolicy: true,
					},
					{
						FirstName:          "Hennadiy",
						LastName:           "Marchuk",
						DateOfBirth:        time_utils.NewDate(1981, 12, 19).String(),
						DateOfHire:         time_utils.NewDate(2023, 2, 2).String(),
						YearsOfExperience:  15,
						LicenseNumber:      "52488416",
						LicenseState:       "GA",
						IsOutOfState:       true,
						IsIncludedInPolicy: true,
					},
					{
						FirstName:          "KAKHABER",
						LastName:           "GIKASHVILI",
						DateOfBirth:        time_utils.NewDate(1979, 3, 16).String(),
						DateOfHire:         time_utils.NewDate(2023, 1, 23).String(),
						YearsOfExperience:  4,
						LicenseNumber:      "G42744240003792",
						LicenseState:       "NJ",
						IsOutOfState:       true,
						IsIncludedInPolicy: true,
					},
					{
						FirstName:          "Ludbin",
						LastName:           "Garcia",
						DateOfBirth:        time_utils.NewDate(1980, 12, 31).String(),
						DateOfHire:         time_utils.NewDate(2022, 10, 3).String(),
						YearsOfExperience:  7,
						LicenseNumber:      "45048511",
						LicenseState:       "TX",
						IsOutOfState:       true,
						IsIncludedInPolicy: true,
					},
					{
						FirstName:          "Vlad",
						LastName:           "Sokolianski",
						DateOfBirth:        time_utils.NewDate(1962, 10, 4).String(),
						DateOfHire:         time_utils.NewDate(2023, 2, 7).String(),
						YearsOfExperience:  13,
						LicenseNumber:      "53798200",
						LicenseState:       "GA",
						IsOutOfState:       true,
						IsIncludedInPolicy: true,
					},
				},
			},
		},
		{
			name:    "With fleets admin user you shouln't be able to see the policy",
			user:    p.UsersFixture.FleetAdmin.AuthzUser(),
			claimId: claimForPolicy1.Id,
			wantErr: true,
		},
	}

	for _, tc := range testCases {
		p.Run(tc.name, func() {
			pol, err := getPolicy(ctx, tc.user, p.queryClient, tc.claimId.String())
			if tc.wantErr {
				p.Require().Error(err)
				return
			}
			p.Require().NoError(err)

			p.NotEqual(uuid.Nil, pol.Id)
			p.Equal(tc.want.PolicyNumber, pol.PolicyNumber)
			p.Contains(pol.SignedLink.Link, tc.want.SignedLink.Link)
			p.Equal(tc.want.Underwriter.Name, pol.Underwriter.Name)
			p.Equal(tc.want.Underwriter.Email, pol.Underwriter.Email)

			// Verify DocumentID is properly populated
			p.NotEqual(uuid.Nil, pol.DocumentID, "Policy DocumentID should not be nil")

			p.assertCoverages(tc.want.Coverages, pol.Coverages)
			// TODO: fix race condition on seeding, as sometimes we see no approved endorsements
			// p.assertEndorsements(tc.want.Endorsements, pol.Endorsements)
			p.assertSubCoverages(tc.want.SubCoverages, pol.SubCoverages)

			p.assertVehicles(tc.want.Vehicles, pol.Vehicles)
			p.assertDrivers(tc.want.Drivers, pol.Drivers)
		})
	}
}

func (p *getPolicyTestSuite) seedNonFleetPolicy(
	ctx context.Context,
	dotNumber int64,
	policyState policy_enums.PolicyState,
	policyIdentifier string,
	startDate, endDate time.Time,
) (*policy.Policy, string) {
	appId := uuid.New()
	app := admitted_app_builder.
		New().
		WithDefaultMockData().
		WithID(uuid.New()).
		WithAgencyID(p.Agency.ID).
		WithPrimaryCovs([]nf_app.CoverageDetails{
			{
				CoverageType: app_enums.CoveragePersonalInjuryProtection,
				Limit:        pointer_utils.ToPointer(100000),
				IsRequired:   true,
				SymbolsAndDefinitions: &[]application.SymbolAndDefinition{
					{
						Symbol: "64",
					},
				},
			},
		}).
		WithUnderwriterID(p.uw.ID).
		Build()
	p.Require().NoError(p.admittedAppWrapper.InsertApp(ctx, *app))

	subId := uuid.New()
	sub := admitted_sub_builder.
		New().
		WithBindable(true).
		FromApplication(*app).
		WithID(subId).
		Build()
	p.Require().NoError(p.admittedAppWrapper.InsertSubmission(ctx, *sub))

	pol := policy_builder.
		New().
		WithDefaultMockData().
		WithId(uuid.New()).
		WithApplicationId(appId).
		WithPolicyNumber("NNFTK", policyIdentifier, 2024).
		WithDOTNumber(dotNumber).
		WithProgramType(enums.ProgramTypeNonFleetAdmitted).
		WithSubmissionId(subId).
		WithDocumentHandleId(uuid.New()).
		WithAgencyId(p.Agency.ID).
		WithCompanyName("Nirvana").
		WithEffectiveDates(startDate, endDate).
		WithState(policyState).
		Build()
	err := pol.SetProgramData(
		admitted.ProgramData{},
	)
	p.Require().NoError(err)
	p.Require().NoError(p.policyWrapper.InsertPolicy(ctx, pol))

	fileName := "policy-doc-test-1"
	err = p.fileUploadManager.UploadFile(
		ctx,
		strings.NewReader("mock-contents"),
		pol.DocumentHandleId,
		file_enums.FileTypePDFGenPDF,
		fileName,
		uuid_utils.StableUUID(fileName),
		file_enums.FileDestinationGroupPDFGen,
	)
	p.Require().NoError(err)

	compilationId := uuid.New()
	mockCompilation := compilation.NewFormsCompilation(
		compilationId,
		compilation.MockFormCompilationData(),
		compilation.MockFormCompilationOptionalData(),
		compilation.MockFormCompilationMetaData(),
		compilation.CompilationTypePolicy,
		p.clk.Now(),
		p.clk.Now(),
		forms_enums.FormCompilationStateInitialized,
		&pol.DocumentHandleId,
	)
	p.Require().NoError(p.formsWrapper.InsertFormCompilation(ctx, &mockCompilation))

	ibId := p.seedInsuranceBundleEntities(
		ctx,
		*pol,
		appId,
		subId,
		pol.DocumentHandleId,
		compilationId,
	)

	return pol, ibId
}

func (p *getPolicyTestSuite) seedInsuranceBundleEntities(
	ctx context.Context,
	pol policy.Policy,
	appId, subId, fileHandleId, compilationId uuid.UUID,
) string {
	pn := pol.PolicyNumber.String()
	ibPolicy := ib_model.NewPolicyBuilder(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).
		WithPolicyNumber(pn).
		WithApplicationId(appId).
		WithBindableSubmissionId(subId).
		WithCoreForm(&insurancecoreproto.FormCore{
			FormCompilationId:   compilationId.String(),
			FormCompilationType: insurancecoreproto.FormCompilationType_FormCompilationTypeSignaturePacket,
			DocumentHandleId:    fileHandleId.String(),
			FormCodes:           []string{"al-form-1", "al-form-2"},
		}).
		WithNFProgramData(nf_model.NewNFAdmittedProgramDataV1Builder().Build()).
		Build()

	policies := map[string]*ib_model.Policy{}
	policies[pn] = ibPolicy

	insured := insured_model.NewInsuredBuilder().
		WithId("").
		Build()

	ibSegment1 := ib_model.NewInsuranceBundleSegmentBuilder(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).
		WithPolicies(policies).
		WithInterval(&proto.Interval{
			Start: timestamppb.New(pol.EffectiveDate),
			End:   timestamppb.New(pol.EffectiveDateTo),
		}).
		Build()

	mockInsuranceBundle := ib_model.NewInsuranceBundleBuilder(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).
		WithInternalId("").
		WithExternalId("").
		WithState(ib_model.InsuranceBundleState_InsuranceBundleState_Invalid).
		WithRootApplicationId(appId).
		WithRootBindableSubmissionId(subId).
		WithAdditionalForms([]*insurancecoreproto.FormCore{
			{
				DocumentHandleId:    fileHandleId.String(),
				FormCompilationId:   uuid.New().String(),
				FormCompilationType: insurancecoreproto.FormCompilationType_FormCompilationTypeSignaturePacket,
				FormCodes:           []string{"al-form-1", "al-form-2"},
			},
		}).
		WithSegments([]*ib_model.InsuranceBundleSegment{ibSegment1}).
		WithDefaultEffectiveDuration(&proto.Interval{
			Start: timestamppb.New(pol.EffectiveDate),
			End:   timestamppb.New(pol.EffectiveDateTo),
		}).
		Build()

	ib, err := p.ibManager.UpsertInsuranceBundle(
		ctx,
		&ib_service.UpsertInsuranceBundleRequest{
			InsuranceBundle: mockInsuranceBundle,
			Insured:         insured,
		},
	)
	p.Require().NoError(err)

	err = p.ibPolicyWrapper.InsertPolicies(
		ctx,
		[]ib_policy.PolicyDBRepresentation{
			{
				ID:          uuid.New().String(),
				IBSegmentID: ibSegment1.Id,
				Policy:      ibPolicy,
			},
		},
	)
	p.Require().NoError(err)

	return ib.InsuranceBundleInternalId
}

func (p *getPolicyTestSuite) seedNonFleetEndorsement(
	ctx context.Context,
	pol policy.Policy,
	ibId string,
) {
	bundleExternalId := fmt.Sprintf(
		"%s-%d",
		pol.PolicyNumber.GetPolicyIdentifier(),
		pol.PolicyNumber.GetPolicyIssuanceYear().Year()%2000,
	)

	parsedBundleInternalId, err := uuid.Parse(ibId)
	p.Require().NoError(err)

	reqId, err := p.ibEndorsementRequestManager.Create(
		ctx,
		ib_db_endorsement_request.CreateEndorsementRequestArgs{
			BundleExternalID:       bundleExternalId,
			BaseID:                 parsedBundleInternalId,
			ProgramType:            insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted,
			CreatedBy:              p.uw.ID,
			AgencyID:               p.Agency.ID,
			InsuranceBundleVersion: 0,
		},
	)
	p.Require().NoError(err)

	err = p.ibEndorsementRequestManager.UpdateChanges(
		ctx,
		reqId,
		nil,
		[]*db_endorsementapp.Change{
			{
				Id:            uuid.New().String(),
				PolicyNumbers: []string{pol.PolicyNumber.String()},
				Data: &endorsement.ChangeData{
					Data: &endorsement.ChangeData_NonFleetChange{
						NonFleetChange: &nf_types.NonFleetChange{
							ChangeType: nf_types.NonFleetChangeType_NonFleetChangeType_Driver,
							Data: &nf_types.NonFleetChange_DriverChange{DriverChange: &nf_types.DriverChange{
								Remove: []string{"F32040056059"},
							}},
						},
					},
				},
				IsActive: true,
			},
			// we add a second change of same type, to test that we only send each change type once
			{
				Id:            uuid.New().String(),
				PolicyNumbers: []string{pol.PolicyNumber.String()},
				Data: &endorsement.ChangeData{
					Data: &endorsement.ChangeData_NonFleetChange{
						NonFleetChange: &nf_types.NonFleetChange{
							ChangeType: nf_types.NonFleetChangeType_NonFleetChangeType_Driver,
							Data: &nf_types.NonFleetChange_DriverChange{DriverChange: &nf_types.DriverChange{
								Remove: []string{"A12345678901"},
							}},
						},
					},
				},
				IsActive: true,
			},
		},
		&proto.Interval{
			Start: timestamppb.New(p.startDate),
			End:   timestamppb.New(p.endDate),
		},
	)
	p.Require().NoError(err)

	revId, err := p.ibEndorsementReviewManager.Create(
		ctx,
		ib_db_endorsement_review.CreateEndorsementReviewArgs{
			RequestID:               reqId,
			DefaultEffectiveDate:    p.startDate,
			PrimaryInsuredName:      pol.InsuredName,
			UnderwritingAssistantID: p.uw.ID,
		},
	)
	p.Require().NoError(err)

	err = p.ibEndorsementRequestManager.SubmitForUWReview(ctx, reqId, *revId)
	p.Require().NoError(err)
	err = p.ibEndorsementReviewManager.EndPriceUpdate(ctx, *revId)
	p.Require().NoError(err)
	// Create supporting documents for testing
	supportingDocFileName := "supporting-doc-nf-test-1"
	supportingDocHandle := uuid.New()
	err = p.fileUploadManager.UploadFile(
		ctx,
		strings.NewReader("supporting doc contents"),
		supportingDocHandle,
		file_enums.FileTypePDFGenPDF,
		supportingDocFileName,
		uuid_utils.StableUUID(supportingDocFileName),
		file_enums.FileDestinationGroupPDFGen,
	)
	p.Require().NoError(err)

	err = p.ibEndorsementReviewWrapper.Update(
		ctx,
		*revId,
		func(review *ib_db_endorsement_review.Review) *ib_db_endorsement_review.Review {
			review.SupportingDocsHandleIds = []uuid.UUID{supportingDocHandle}
			return review
		},
	)
	p.Require().NoError(err)

	err = p.ibEndorsementReviewManager.Approve(ctx, *revId, nil)
	p.Require().NoError(err)

	fileName := "endorsement-change-test-1"
	fileHandle := uuid.New()
	err = p.fileUploadManager.UploadFile(
		ctx,
		strings.NewReader("mock-contents"),
		fileHandle,
		file_enums.FileTypePDFGenPDF,
		fileName,
		fileHandle,
		file_enums.FileDestinationGroupPDFGen,
	)
	p.Require().NoError(err)

	err = p.ibEndorsementReviewWrapper.Update(
		ctx,
		*revId,
		func(review *ib_db_endorsement_review.Review) *ib_db_endorsement_review.Review {
			review.PolicyChangeFormHandleIds = []ib_db_endorsement_review.PolicyChangeFormHandleId{
				{
					HandleId:     fileHandle,
					PolicyNumber: pol.PolicyNumber.String(),
				},
			}
			// We shouldn't need to do this, but the endorsement review approval has a race condition.
			// Sometimes the state is not set to approved, so we need to set it manually.
			// It's not important for this test, since it's not endorsement specific
			review.State = endreviewenums.EndorsementReviewStateApproved
			review.ApprovedAt = pointer_utils.ToPointer(time.Now())
			return review
		},
	)
	p.Require().NoError(err)
}

func (p *getPolicyTestSuite) assertVehicles(
	wantVehicles []gql_models.PolicyVehicle,
	gotVehicles []gql_models.PolicyVehicle,
) {
	p.Require().Equal(len(wantVehicles), len(gotVehicles))

	slices.SortFunc(gotVehicles, func(a, b gql_models.PolicyVehicle) int {
		return strings.Compare(a.Vin, b.Vin)
	})
	slices.SortFunc(wantVehicles, func(a, b gql_models.PolicyVehicle) int {
		return strings.Compare(a.Vin, b.Vin)
	})

	for i, veh := range wantVehicles {
		got := gotVehicles[i]
		p.Equal(veh.Vin, got.Vin)
		p.Equal(veh.Year, got.Year)
		p.Equal(veh.Make, got.Make)
		p.Equal(veh.Model, got.Model)
	}
}

func (p *getPolicyTestSuite) assertDrivers(
	wantDrivers []gql_models.PolicyDriver,
	gotDrivers []gql_models.PolicyDriver,
) {
	p.Require().Equal(len(wantDrivers), len(gotDrivers))

	slices.SortFunc(gotDrivers, func(a, b gql_models.PolicyDriver) int {
		return strings.Compare(a.LicenseNumber, b.LicenseNumber)
	})
	slices.SortFunc(wantDrivers, func(a, b gql_models.PolicyDriver) int {
		return strings.Compare(a.LicenseNumber, b.LicenseNumber)
	})

	for i, driver := range wantDrivers {
		got := gotDrivers[i]
		p.Equal(driver.FirstName, got.FirstName)
		p.Equal(driver.LastName, got.LastName)
		p.Equal(driver.IsIncludedInPolicy, got.IsIncludedInPolicy)
		p.Equal(driver.IsOutOfState, got.IsOutOfState)
		p.Equal(driver.IsOwner, got.IsOwner)
		p.Equal(driver.LicenseState, got.LicenseState)
		p.Equal(driver.LicenseNumber, got.LicenseNumber)
		p.Equal(driver.YearsOfExperience, got.YearsOfExperience)
	}
}
