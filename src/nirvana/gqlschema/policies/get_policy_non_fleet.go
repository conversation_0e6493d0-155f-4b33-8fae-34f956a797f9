package policies

import (
	"context"
	"strconv"
	"time"

	insurancecoreproto "nirvanatech.com/nirvana/insurance-core/proto"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/samsarahq/thunder/graphql"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/api-server/handlers/common/endorsement"
	claim_endorsements "nirvanatech.com/nirvana/claims/endorsements"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	ib_endorsementapp_wrapper "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp"
	ib_db_endorsement_review "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-review"
	non_fleet "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	policy_db "nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	gql_models "nirvanatech.com/nirvana/gqlschema/models"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/insurance-bundle/config"
	ib_model "nirvanatech.com/nirvana/insurance-bundle/model"
	ib_service "nirvanatech.com/nirvana/insurance-bundle/service"
	endorsementapp_oapi "nirvanatech.com/nirvana/openapi-specs/components/endorsementapp/intake"
)

func (r *Resolver) fetchPolicyEndorsementsForNonFleet(
	ctx context.Context,
	pn string,
) ([]gql_models.Endorsement, error) {
	endorsements, err := r.deps.ClaimEndorsementClient.GetNonFleetEndorsements(ctx, pn)
	if err != nil {
		return nil, graphql.WrapAsSafeError(
			err,
			"failed to get endorsements for policy %s",
			pn,
		)
	}

	uwIds := slice_utils.Map(
		endorsements,
		func(end claim_endorsements.NonFleetEndorsement) uuid.UUID {
			return end.Review.UnderwritingAssistantID
		},
	)

	uws, err := r.deps.AuthWrapper.FetchAuthzUsers(ctx, slice_utils.Dedup(uwIds))
	if err != nil {
		return nil, graphql.WrapAsSafeError(
			err,
			"failed to get UWs for endorsement reviews of policy %s",
			pn,
		)
	}

	response := make([]gql_models.Endorsement, 0, len(endorsements))
	for _, end := range endorsements {
		parsedEndorsement, err := r.constructEndorsementForNonFleet(ctx, uws, end)
		if err != nil {
			return nil, errors.Wrapf(
				err,
				"failed to construct endorsement for policy %s",
				pn,
			)
		}

		response = append(response, *parsedEndorsement)
	}

	return response, nil
}

func (r *Resolver) constructEndorsementForNonFleet(
	ctx context.Context,
	uws []authz.User,
	end claim_endorsements.NonFleetEndorsement,
) (*gql_models.Endorsement, error) {
	uw := slice_utils.Find(
		uws,
		func(u authz.User) bool {
			return u.ID == end.Review.UnderwritingAssistantID
		},
	)
	if uw == nil {
		return nil, errors.Newf(
			"underwriting assistant %s not found for endorsement review %s",
			end.Review.UnderwritingAssistantID,
			end.Review.ID,
		)
	}

	expirableLink := gql_models.ExpirableLink{
		Link:       end.SignedLink,
		Expiration: end.SignedLinkExpiration.Format(time_utils.ReferenceLayout),
	}

	if end.Review.ApprovedAt == nil {
		return nil, errors.Newf(
			"approved endorsement review %s does not have an approved date",
			end.Review.ID,
		)
	}

	changeTypes := slice_utils.Dedup(
		slice_utils.Map(
			// this is passed to pointers to avoid passing locks by value
			slice_utils.ToSliceOfPointers(end.Request.Changes),
			func(c *ib_endorsementapp_wrapper.Change) string {
				nfChange := c.Data.GetNonFleetChange()
				if nfChange == nil {
					return ""
				}
				return nfChange.ChangeType.String()
			},
		),
	)

	var endorsementExpirationDate time.Time
	for _, change := range slice_utils.ToSliceOfPointers(end.Request.Changes) {
		changeExpirationDate := change.EffectiveInterval.End.AsTime()
		if endorsementExpirationDate.IsZero() || changeExpirationDate.After(endorsementExpirationDate) {
			endorsementExpirationDate = changeExpirationDate
		}
	}

	// Extract form handle IDs from PolicyChangeFormHandleIds
	formHandleIds := slice_utils.Map(
		end.Review.PolicyChangeFormHandleIds,
		func(f ib_db_endorsement_review.PolicyChangeFormHandleId) uuid.UUID {
			return f.HandleId
		},
	)

	// Process all supporting documents and forms using helper function
	supportingDocsAndForms, err := processSupportingDocsAndForms(
		ctx,
		r.deps.FileUploadManager,
		formHandleIds,
		end.Review.SupportingDocsHandleIds,
	)
	if err != nil {
		return nil, errors.Wrapf(
			err,
			"failed to process supporting documents and forms for endorsement review %s",
			end.Review.ID,
		)
	}

	return &gql_models.Endorsement{
		Id:         end.Request.ID.String(),
		SignedLink: expirableLink,
		DocumentID: end.FileHandle,
		EffectiveInterval: gql_models.EndorsementEffectiveInterval{
			EffectiveDate:  end.Review.DefaultEffectiveDate.Format(time_utils.ReferenceLayout),
			ExpirationDate: endorsementExpirationDate.Format(time_utils.ReferenceLayout),
		},
		ApprovedAt: end.Review.ApprovedAt.Format(time_utils.ReferenceLayout),
		Underwriter: gql_models.BasicUser{
			Name:  uw.FullName(),
			Email: uw.Email,
		},
		ChangeTypes:            changeTypes,
		SupportingDocsAndForms: supportingDocsAndForms,
	}, nil
}

func (r *Resolver) fetchPolicyForNonFleet(
	ctx context.Context,
	p policy_db.Policy,
	ibAt time.Time,
) (*gql_models.Policy, error) {
	sub, err := r.deps.AdmittedAppWrapper.GetSubmissionById(ctx, p.SubmissionId)
	if err != nil {
		return nil, graphql.WrapAsSafeError(err, "failed to get submission")
	}

	now := time.Now()
	policyDocumentLink, err := r.signLink(ctx, p.DocumentHandleId)
	if err != nil {
		log.Error(ctx, "couldn't generate policy document download link", log.Err(err))
		return nil, err
	}

	subcoverages := buildSubCoveragesForNonFleet(sub.Info.CoverageInfo.PrimaryCovs)

	uw, err := r.deps.AuthWrapper.FetchUserInfo(ctx, sub.UnderwriterID)
	if err != nil {
		log.Error(ctx, "failed to fetch underwriter info", log.Err(err))
		return nil, graphql.WrapAsSafeError(err, "failed to fetch underwriter info")
	}

	policyType, err := p.PolicyNumber.GetPolicyType()
	if err != nil {
		log.Error(ctx, "failed to get policy type", log.Err(err))
		return nil, graphql.WrapAsSafeError(err, "failed to get policy type")
	}

	ib, err := r.getInsuranceBundleForPolicy(ctx, p.PolicyNumber.String(), &ibAt)
	if err != nil {
		return nil, graphql.WrapAsSafeError(
			err,
			"failed to get insurance bundle for policy %s for date %s",
			p.PolicyNumber.String(),
			ibAt,
		)
	}

	driverProcessor, err := endorsement.GetProcessor[endorsement.DriverChangeProcessor](
		insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted,
	)
	if err != nil {
		return nil, graphql.WrapAsSafeError(
			err,
			"failed to get driver processor",
		)
	}

	vehicleProcessor, err := endorsement.GetProcessor[endorsement.VehicleChangeProcessor](
		insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted,
	)
	if err != nil {
		return nil, graphql.WrapAsSafeError(
			err,
			"failed to get vehicle processor",
		)
	}

	drivers, vehicles, err := extractDriversAndVehicles(
		ctx,
		vehicleProcessor,
		driverProcessor,
		ib,
	)
	if err != nil {
		return nil, graphql.WrapAsSafeError(
			err,
			"failed to get insurance bundle segment",
		)
	}

	return &gql_models.Policy{
		Id:               p.Id,
		InsuredName:      p.InsuredName,
		InsuredDOTNumber: strconv.FormatInt(p.CompanyInfo.DOTNumber, 10),
		Coverages: []gql_models.PolicyCoverageEnums{
			gql_models.PolicyCoverageEnums(policyType.String()),
		},
		State:        p.State,
		StartDate:    p.EffectiveDate,
		EndDate:      p.EffectiveDateTo,
		PolicyNumber: p.PolicyNumber.String(),
		SubCoverages: subcoverages,
		SignedLink: gql_models.ExpirableLink{
			Link:       *policyDocumentLink,
			Expiration: now.Add(documentLinkExpiration).Format(time_utils.ReferenceLayout),
		},
		DocumentID: p.DocumentHandleId,
		Underwriter: gql_models.BasicUser{
			Email: uw.Email,
			Name:  uw.FullName(),
		},
		Drivers:  drivers,
		Vehicles: vehicles,
	}, nil
}

// extractDriversAndVehicles gets the vehicle and driver list for the policy for the
// given insurance bundle.
func extractDriversAndVehicles(
	ctx context.Context,
	vehicleProcessor endorsement.VehicleChangeProcessor,
	driverProcessor endorsement.DriverChangeProcessor,
	ib *ib_model.InsuranceBundle,
) ([]gql_models.PolicyDriver, []gql_models.PolicyVehicle, error) {
	segment := ib.GetLastSegment()
	if segment == nil {
		return nil, nil, errors.New("failed to get insurance bundle segment")
	}
	driverChanges := driverProcessor.ExtractInitialDrivers(ctx, segment)
	vehicleChanges := vehicleProcessor.ExtractInitialVehicles(ctx, segment)

	drivers := slice_utils.Map(
		driverChanges,
		func(change endorsementapp_oapi.DriverChange) gql_models.PolicyDriver {
			return gql_models.PolicyDriver{
				DateOfBirth:        change.Driver.DateOfBirth.Format(time_utils.ReferenceLayout),
				DateOfHire:         change.Driver.DateOfHire.Format(time_utils.ReferenceLayout),
				FirstName:          change.Driver.FirstName,
				IsIncludedInPolicy: change.Driver.IsIncludedInPolicy,
				IsOutOfState:       change.Driver.IsOutOfState,
				IsOwner:            change.Driver.IsOwner,
				LastName:           change.Driver.LastName,
				LicenseNumber:      change.Driver.LicenseNumber,
				LicenseState:       change.Driver.LicenseState,
				YearsOfExperience:  int(change.Driver.YearsOfExperience),
			}
		})

	vehicles := slice_utils.Map(vehicleChanges, func(change endorsementapp_oapi.VehicleChange) gql_models.PolicyVehicle {
		var statedValue *int
		if change.Vehicle.StatedValue != nil {
			statedValue = pointer_utils.ToPointer(int(*change.Vehicle.StatedValue))
		}
		return gql_models.PolicyVehicle{
			Make:         change.Vehicle.Make,
			Model:        change.Vehicle.Model,
			StatedValue:  statedValue,
			VehicleClass: string(change.Vehicle.VehicleClass),
			VehicleType:  string(change.Vehicle.VehicleType),
			Vin:          change.Vehicle.Vin,
			WeightClass:  string(change.Vehicle.WeightClass),
			Year:         int(change.Vehicle.Year),
		}
	})

	return drivers, vehicles, nil
}

func buildSubCoveragesForNonFleet(
	coverages []non_fleet.CoverageDetails,
) []gql_models.CoverageWithSymbols {
	result := make([]gql_models.CoverageWithSymbols, 0, len(coverages))
	for _, cov := range coverages {
		var symbols []string
		if cov.SymbolsAndDefinitions != nil {
			for _, sym := range *cov.SymbolsAndDefinitions {
				symbols = append(symbols, sym.Symbol)
			}
		}
		result = append(result, gql_models.CoverageWithSymbols{
			Coverage: cov.CoverageType.String(),
			Symbols:  symbols,
		})
	}
	return result
}

func (r *Resolver) getInsuranceBundleForPolicy(
	ctx context.Context,
	pn string,
	ibAt *time.Time,
) (*ib_model.InsuranceBundle, error) {
	ibExternalID, err := config.GetInsuranceBundleExternalIdFromPolicyNumber(
		pn,
		insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted)
	if err != nil {
		return nil, err
	}

	// If "ibAt" is passed, we'll get the IB for that specific date.
	// This is useful to get a "snapshot" of the IB at that specific time.
	var insuranceBundleAt *timestamppb.Timestamp
	if ibAt != nil {
		insuranceBundleAt = timestamppb.New(*ibAt)
	}

	insuranceBundle, err := r.deps.InsuranceBundleManagerClient.GetInsuranceBundle(ctx,
		&ib_service.GetInsuranceBundleRequest{
			PrimaryFilter: &ib_service.GetInsuranceBundleRequest_PrimaryFilter{
				Identifier: &ib_service.GetInsuranceBundleRequest_PrimaryFilter_ExternalId{
					ExternalId: ibExternalID,
				},
			},
			SecondaryFilter: &ib_service.GetInsuranceBundleRequest_SecondaryFilter{
				ProgramType:       pointer_utils.ToPointer(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted),
				InsuranceBundleAt: insuranceBundleAt,
			},
		})
	if err != nil {
		return nil, err
	}

	return insuranceBundle.GetInsuranceBundle(), nil
}
