package policies_test

import (
	"context"
	"slices"
	"strings"
	"testing"
	"time"

	"github.com/benb<PERSON><PERSON><PERSON>/clock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"

	ib_endorsement_request "nirvanatech.com/nirvana/application/endorsementapp/endorsement-request"
	ib_endorsement_review "nirvanatech.com/nirvana/application/endorsementapp/endorsement-review"
	claim_client "nirvanatech.com/nirvana/claims/client"
	claim_builder "nirvanatech.com/nirvana/claims/db"
	claim_enums "nirvanatech.com/nirvana/claims/enums"
	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/common-go/file_upload_lib"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	db_endorsement_review "nirvanatech.com/nirvana/db-api/db_wrappers/endorsement/endorsement-review"
	ib_db_endorsement_review "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-review"
	"nirvanatech.com/nirvana/db-api/db_wrappers/forms"
	ib_policy "nirvanatech.com/nirvana/db-api/db_wrappers/insurance-bundle/policyv2"
	non_fleet "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	policy_wrapper "nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	endorsementrequest "nirvanatech.com/nirvana/endorsement/endorsement-request"
	endorsementreview "nirvanatech.com/nirvana/endorsement/endorsement-review"
	"nirvanatech.com/nirvana/external_client/salesforce/client"
	gql_models "nirvanatech.com/nirvana/gqlschema/models"
	"nirvanatech.com/nirvana/graphql-server/queryclient"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/agency_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/users_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	ibservice "nirvanatech.com/nirvana/insurance-bundle/service"
)

type getPolicyTestSuite struct {
	fx.In
	fxapp *fxtest.App
	suite.Suite

	clk               *clock.Mock
	featureFlagClient feature_flag_lib.Client
	queryClient       *queryclient.QueryClient

	claimsClient *claim_client.Client

	admittedAppWrapper          non_fleet.Wrapper[*admitted_app.AdmittedApp]
	applicationWrapper          application.DataWrapper
	endorsementReviewClient     endorsementreview.Client
	endorsementReviewWrapper    db_endorsement_review.Wrapper
	endorsementRequestClient    endorsementrequest.Client
	fileUploadManager           file_upload_lib.FileUploadManager[file_upload_lib.DefaultS3Keygen]
	formsWrapper                forms.FormWrapper
	policyWrapper               policy_wrapper.DataWrapper
	ibEndorsementReviewManager  ib_endorsement_review.Manager
	ibEndorsementRequestManager ib_endorsement_request.Manager
	ibManager                   ibservice.InsuranceBundleManagerClient
	ibPolicyWrapper             ib_policy.Wrapper
	ibEndorsementReviewWrapper  ib_db_endorsement_review.Wrapper

	mockSalesforceClient *client.MockSalesforceClient

	startDate time.Time
	endDate   time.Time
	uw        users_fixture.User

	*agency_fixture.AgencyFixture
	*users_fixture.UsersFixture
}

func TestGetPolicy(t *testing.T) {
	suite.Run(t, new(getPolicyTestSuite))
}

func (p *getPolicyTestSuite) SetupTest() {
	var env struct {
		fx.In

		Clk               *clock.Mock
		FeatureFlagClient feature_flag_lib.Client
		QueryClient       *queryclient.QueryClient

		ClaimsClient *claim_client.Client

		AdmittedAppWrapper          non_fleet.Wrapper[*admitted_app.AdmittedApp]
		ApplicationWrapper          application.DataWrapper
		EndorsementReviewClient     endorsementreview.Client
		EndorsementReviewWrapper    db_endorsement_review.Wrapper
		EndorsementRequestClient    endorsementrequest.Client
		FileUploadManager           file_upload_lib.FileUploadManager[file_upload_lib.DefaultS3Keygen]
		FormsWrapper                forms.FormWrapper
		IbEndorsementReviewManager  ib_endorsement_review.Manager
		IbEndorsementRequestManager ib_endorsement_request.Manager
		IBManager                   ibservice.InsuranceBundleManagerClient
		IBPolicyWrapper             ib_policy.Wrapper
		IbEndorsementReviewWrapper  ib_db_endorsement_review.Wrapper
		PolicyWrapper               policy_wrapper.DataWrapper

		MockSalesforceClient *client.MockSalesforceClient

		AgencyFixture *agency_fixture.AgencyFixture
		UsersFixture  *users_fixture.UsersFixture
	}

	p.fxapp = testloader.RequireStart(p.T(), &env)

	p.Require().Greater(len(env.UsersFixture.Underwriters), 0)
	p.uw = env.UsersFixture.Underwriters[0]
	p.clk = env.Clk
	p.featureFlagClient = env.FeatureFlagClient
	p.queryClient = env.QueryClient

	p.claimsClient = env.ClaimsClient

	p.admittedAppWrapper = env.AdmittedAppWrapper
	p.applicationWrapper = env.ApplicationWrapper
	p.endorsementReviewClient = env.EndorsementReviewClient
	p.endorsementReviewWrapper = env.EndorsementReviewWrapper
	p.endorsementRequestClient = env.EndorsementRequestClient
	p.fileUploadManager = env.FileUploadManager
	p.formsWrapper = env.FormsWrapper
	p.ibEndorsementReviewManager = env.IbEndorsementReviewManager
	p.ibEndorsementRequestManager = env.IbEndorsementRequestManager
	p.ibManager = env.IBManager
	p.ibEndorsementReviewWrapper = env.IbEndorsementReviewWrapper
	p.ibPolicyWrapper = env.IBPolicyWrapper
	p.policyWrapper = env.PolicyWrapper

	p.mockSalesforceClient = env.MockSalesforceClient

	p.AgencyFixture = env.AgencyFixture
	p.UsersFixture = env.UsersFixture

	p.startDate = p.clk.Now().AddDate(0, -6, 0)
	p.endDate = p.clk.Now().AddDate(1, -6, -1)
}

func (p *getPolicyTestSuite) TearDownTest() {
	defer p.fxapp.RequireStop()
}

func (p *getPolicyTestSuite) assertCoverages(
	wantCoverages, gotCoverages []gql_models.PolicyCoverageEnums,
) {
	p.Require().Equal(len(wantCoverages), len(gotCoverages))
	slices.SortFunc(gotCoverages, func(a, b gql_models.PolicyCoverageEnums) int {
		return strings.Compare(string(a), string(b))
	})
	slices.SortFunc(wantCoverages, func(a, b gql_models.PolicyCoverageEnums) int {
		return strings.Compare(string(a), string(b))
	})

	for i, want := range wantCoverages {
		got := gotCoverages[i]
		p.Equal(want, got)
	}
}

func (p *getPolicyTestSuite) assertEndorsements(
	wantEndorsements, gotEndorsements []gql_models.Endorsement,
) {
	p.Require().Equal(len(wantEndorsements), len(gotEndorsements))

	slices.SortFunc(gotEndorsements, func(a, b gql_models.Endorsement) int {
		return strings.Compare(a.ApprovedAt, b.ApprovedAt)
	})
	slices.SortFunc(wantEndorsements, func(a, b gql_models.Endorsement) int {
		return strings.Compare(a.ApprovedAt, b.ApprovedAt)
	})

	for i, want := range wantEndorsements {
		got := gotEndorsements[i]
		p.Equal(want.Underwriter.Email, got.Underwriter.Email)
		p.Equal(want.Underwriter.Name, got.Underwriter.Name)
		p.Require().Equal(len(want.ChangeTypes), len(got.ChangeTypes))
		p.Require().ElementsMatch(want.ChangeTypes, got.ChangeTypes)
		p.Contains(got.SignedLink.Link, want.SignedLink.Link)

		// Verify DocumentID is properly populated for endorsements
		p.NotEqual(uuid.Nil, got.DocumentID, "Endorsement DocumentID should not be nil")
		
		// Verify supporting docs and forms if expected
		if len(want.SupportingDocsAndForms) > 0 {
			p.assertSupportingDocsAndForms(want.SupportingDocsAndForms, got.SupportingDocsAndForms)
		}
	}
}

func (p *getPolicyTestSuite) assertSubCoverages(
	wantSubCoverages, gotSubCoverages []gql_models.CoverageWithSymbols,
) {
	p.Require().Equal(len(wantSubCoverages), len(gotSubCoverages))

	slices.SortFunc(gotSubCoverages, func(a, b gql_models.CoverageWithSymbols) int {
		return strings.Compare(a.Coverage, b.Coverage)
	})
	slices.SortFunc(wantSubCoverages, func(a, b gql_models.CoverageWithSymbols) int {
		return strings.Compare(a.Coverage, b.Coverage)
	})

	for i, want := range wantSubCoverages {
		got := gotSubCoverages[i]

		p.Equal(want.Coverage, got.Coverage)
		p.ElementsMatch(want.Symbols, got.Symbols)
	}
}

func (p *getPolicyTestSuite) assertSupportingDocsAndForms(
	wantDocs, gotDocs []gql_models.SupportingDocumentOrForm,
) {
	p.Require().Equal(len(wantDocs), len(gotDocs))

	slices.SortFunc(gotDocs, func(a, b gql_models.SupportingDocumentOrForm) int {
		return strings.Compare(a.Filename, b.Filename)
	})
	slices.SortFunc(wantDocs, func(a, b gql_models.SupportingDocumentOrForm) int {
		return strings.Compare(a.Filename, b.Filename)
	})

	for i, want := range wantDocs {
		got := gotDocs[i]

		p.Equal(want.Filename, got.Filename)
		p.NotEqual(uuid.Nil, got.DocumentID, "Supporting document ID should not be nil")
		p.NotEmpty(got.SignedLink.Link, "Supporting document signed link should not be empty")
		p.NotEmpty(got.SignedLink.Expiration, "Supporting document link expiration should not be empty")
	}
}

// seedClaim persists a claim that happened on the first day of the policy.
func (p *getPolicyTestSuite) seedClaim(
	ctx context.Context,
	pol *policy.Policy,
	externalId string,
) *claim_builder.Claim {
	claimId := uuid.New()
	claim, err := claim_builder.New(claim_enums.ClaimsProviderNars).
		WithId(claimId).
		WithExternalId(externalId).
		WithPolicyNumber(pol.PolicyNumber.String()).
		WithLossDatetime(pointer_utils.ToPointer(pol.EffectiveDate)).
		Build()
	p.Require().NoError(err)
	p.Require().NoError(p.claimsClient.InsertClaim(ctx, claim))

	return claim
}

func getPolicy(
	ctx context.Context,
	principal authz.User,
	qc *queryclient.QueryClient,
	claimId string,
) (gql_models.Policy, error) {
	query := `query ($claimId: string!) {
		policy(claimId: $claimId) {
			id
			policyNumber
			coverages
			documentID
			signedLink {
				link
				expiration
			}
			underwriter {
				name
				email
			}
			subCoverages {
				coverage
				 symbols
			}
			endorsements {
				id
				documentID
				signedLink {
					link
					expiration
				}
				underwriter {
					name
					email
				}
				approvedAt
				changeTypes
				effectiveInterval {
					effectiveDate
					expirationDate
				}
				supportingDocsAndForms {
					documentID
					filename
					signedLink {
						link
						expiration
					}
				}
			}
			vehicles {
				make
				model
				statedValue
				vehicleClass
				vehicleType
				vin
				weightClass
				year
			}
			drivers {
				dateOfBirth
				dateOfHire
				firstName
				isIncludedInPolicy
				isOutOfState
				isOwner
				lastName
				licenseNumber
				licenseState
				yearsOfExperience
			}
		}
	}`

	var queryOutput struct {
		Policy gql_models.Policy
	}

	queryVars := map[string]any{
		"claimId": claimId,
	}

	queryRes := qc.Query(ctx, principal, query, queryVars)
	err := queryRes.ResultAs(&queryOutput)
	return queryOutput.Policy, err
}
