package policies

import (
	"context"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/samsarahq/thunder/graphql"

	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	gql_models "nirvanatech.com/nirvana/gqlschema/models"
	"nirvanatech.com/nirvana/infra/authz"
)

type fetchPolicyArgs struct {
	ClaimId string
}

func (r *Resolver) fetchPolicyEndorsements(
	ctx context.Context,
	gqlPolicy gql_models.Policy,
) ([]gql_models.Endorsement, error) {
	pn := gqlPolicy.PolicyNumber
	authzUser := authz.UserFromContext(ctx)
	if !authzUser.IsSuperuser() && !authzUser.IsClaimsAdmin() {
		return nil, errors.Wrap(
			authz.ErrUnauthorized,
			"user does not have permission to view policy's endorsements",
		)
	}

	// we check that the policy exists
	p, err := r.deps.PolicyClient.GetLatestPolicy(ctx, pn, false)
	if err != nil {
		return nil, graphql.WrapAsSafeError(err, "failed to get policy %s", pn)
	}

	if p.ProgramType == enums.ProgramTypeFleet {
		return r.fetchPolicyEndorsementsForFleet(ctx, *p)
	}

	return r.fetchPolicyEndorsementsForNonFleet(ctx, pn)
}

func (r *Resolver) fetchPolicyForClaim(
	ctx context.Context,
	args fetchPolicyArgs,
) (*gql_models.Policy, error) {
	authzUser := authz.UserFromContext(ctx)
	if !authzUser.IsSuperuser() && !authzUser.IsClaimsAdmin() {
		return nil, errors.Wrap(
			authz.ErrUnauthorized,
			"user does not have permission to view the policy",
		)
	}

	parsedClaimId, err := uuid.Parse(args.ClaimId)
	if err != nil {
		return nil, graphql.WrapAsSafeError(
			err,
			"failed to parse claim id %s as an UUID",
			args.ClaimId,
		)
	}

	claim, err := r.deps.ClaimsClient.GetClaim(ctx, parsedClaimId)
	if err != nil {
		return nil, graphql.WrapAsSafeError(err, "failed to get claim %s", parsedClaimId)
	}

	// we check that the policy exists
	p, err := r.deps.PolicyClient.GetLatestPolicy(ctx, claim.PolicyNumber, false)
	if err != nil {
		return nil, graphql.WrapAsSafeError(err, "failed to get policy %s", claim.PolicyNumber)
	}

	if p.ProgramType == enums.ProgramTypeFleet {
		return r.fetchPolicyForFleet(ctx, *p)
	}

	if claim.LossDatetime == nil {
		return nil, errors.New("claim's loss datetime should be set to fetch the policy details")
	}

	return r.fetchPolicyForNonFleet(ctx, *p, *claim.LossDatetime)
}

func (r *Resolver) signLink(
	ctx context.Context,
	fileHandleId uuid.UUID,
) (*string, error) {
	documentLink, err := r.deps.FileUploadManager.GenerateSTADownloadLink(
		ctx,
		fileHandleId,
		documentLinkExpiration,
	)
	if err != nil {
		log.Error(ctx, "couldn't generate document download link", log.Err(err))
		return nil, graphql.WrapAsSafeError(err, "couldn't generate document download link")
	}

	return &documentLink, nil
}
