package authz_test

import (
	"context"
	_ "embed"
	"fmt"
	"testing"

	"nirvanatech.com/nirvana/infra/fx/testfixtures/common_fixture"

	"github.com/google/uuid"
	"github.com/samsarahq/go/snapshotter"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/common-go/test_utils"
	"nirvanatech.com/nirvana/graphql-server/queryclient"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/users_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

//go:embed testdata/mock_user_data.sql
var mocks string

var mockData = fx.Invoke(func(ff *common_fixture.CommonNirvanaFixture) {
	ff.SqlData = mocks
})

func TestCreateUserWithRole(t *testing.T) {
	ctx := context.Background()
	var env struct {
		fx.In
		*common_fixture.CommonNirvanaFixture
		QueryClient *queryclient.QueryClient
	}
	defer testloader.RequireStart(t, &env, testloader.Use(mockData)).RequireStop()

	qc := env.QueryClient
	agency, err := test_utils.CreateAgency(ctx, test_utils.Superuser(), qc, "Agency 1")
	require.NoError(t, err)

	// First test the creation of a user with role as part of CreateUser.
	agencyReaderRole := test_utils.CreateUserRoleArgs{
		Group:    authz.AgencyAdminReaderRole,
		AgencyID: &agency.ID,
	}
	createdUser, err := test_utils.CreateUser(ctx, test_utils.Superuser(), qc,
		"Admin", "One", "<EMAIL>", "Test1234", nil, nil, nil, agencyReaderRole)
	require.NoError(t, err)
	require.Len(t, createdUser.Roles, 1)

	var zeroUUID uuid.UUID
	expectedRoles := []authz.Role{
		{
			ID:       createdUser.Roles[0].ID,
			UserID:   createdUser.ID,
			AgencyID: &agency.ID,
			FleetID:  &zeroUUID,
			Group:    authz.AgencyAdminReaderRole,
			Domain:   fmt.Sprintf("/agency/%s/*", agency.ID.String()),
		},
	}
	assert.Equal(t, expectedRoles, createdUser.Roles)
}

func TestGetAllUserRoles(t *testing.T) {
	var env struct {
		fx.In
		QueryClient *queryclient.QueryClient
		Snap        *snapshotter.Snapshotter
		*users_fixture.UsersFixture
	}
	defer testloader.RequireStart(t, &env).RequireStop()

	query := `query GetAllUserRoles {
		users {
			id
			firstName
			lastName
			email
			roles {
				userID
				group
				domain
				agency {
					id
					name
				}
			}
		}
	}`
	queryVars := map[string]interface{}{}

	var getUserRolesOutput struct {
		Users []struct {
			ID    string
			Email string
			Roles []struct {
				UserID string
				Group  string
				Domain string
				Agency struct {
					ID   string
					Name string
				}
			}
		}
	}
	ctx := context.Background()
	assert.NoError(t, env.QueryClient.Query(ctx, test_utils.Superuser(), query, queryVars).ResultAs(&getUserRolesOutput))
	env.Snap.Snapshot("Get all test users", getUserRolesOutput)
}

func TestRoleCRUD(t *testing.T) {
	ctx := context.Background()
	var env struct {
		fx.In
		*common_fixture.CommonNirvanaFixture
		QueryClient *queryclient.QueryClient
	}
	defer testloader.RequireStart(t, &env, testloader.Use(mockData)).RequireStop()

	qc := env.QueryClient
	var zeroUUID uuid.UUID

	agency, err := test_utils.CreateAgency(ctx, test_utils.Superuser(), qc, "Agency 1")
	require.NoError(t, err)

	// Create the second user with no roles by default.
	createdUser, err := test_utils.CreateUser(ctx, test_utils.Superuser(), qc,
		"Admin", "Two", "<EMAIL>", "Test1234", nil, nil, nil)
	require.NoError(t, err)

	createRoleArgs := test_utils.CreateRoleArgs{
		UserID:   createdUser.ID,
		AgencyID: &agency.ID,
		Group:    authz.AgencyAdminRole,
	}

	// Verify that a user cannot assign themselves to another agency's role.
	_, err = test_utils.CreateRole(ctx, createdUser, qc, createRoleArgs)
	assert.Error(t, err)

	adminRole, err := test_utils.CreateRole(ctx, test_utils.Superuser(), qc, createRoleArgs)
	require.NoError(t, err)
	assert.Equal(t, &agency.ID, adminRole.AgencyID)

	expectedUser := authz.User{
		UserInfo: authz.UserInfo{
			ID:        createdUser.ID,
			FirstName: "Admin",
			LastName:  "Two",
			Email:     "<EMAIL>",
		},
		Roles: []authz.Role{
			{
				ID:       adminRole.ID,
				UserID:   createdUser.ID,
				AgencyID: &agency.ID,
				FleetID:  &zeroUUID,
				Group:    authz.AgencyAdminRole,
				Domain:   fmt.Sprintf("/agency/%s/*", agency.ID.String()),
			},
		},
	}

	actualUser, err := test_utils.GetUser(ctx, test_utils.Superuser(), qc, createdUser.ID)
	require.NoError(t, err)
	assert.Equal(t, expectedUser, actualUser)

	// Verify that an unprivileged user cannot delete the role.
	var unprivilegedUser authz.User
	_, err = test_utils.DeleteRole(ctx, unprivilegedUser, qc, actualUser.ID, adminRole.ID)
	assert.Error(t, err)

	// Verify that the user can delete their own role.
	success, err := test_utils.DeleteRole(ctx, actualUser, qc, actualUser.ID, adminRole.ID)
	assert.NoError(t, err)
	assert.True(t, success)

	// Verify that delete returns false after a role has been deleted.
	success, err = test_utils.DeleteRole(ctx, test_utils.Superuser(), qc, createdUser.ID, adminRole.ID)
	assert.NoError(t, err)
	assert.False(t, success)

	// Verify that our user now has no roles.
	actualUser, err = test_utils.GetUser(ctx, test_utils.Superuser(), qc, createdUser.ID)
	require.NoError(t, err)
	expectedUser.Roles = []authz.Role{}
	assert.Equal(t, expectedUser, actualUser)
}

func TestSeniorSupportUserCreation(t *testing.T) {
	const firstName, lastName, email = "John", "Doe", "<EMAIL>"
	const dotNumber = "123456" // Use the mock DOT number for this fleet.
	var qc *queryclient.QueryClient
	defer testloader.RequireStart(t, &qc).RequireStop()

	var zeroUUID uuid.UUID
	ctx := context.Background()
	authzUser := test_utils.SupportUser()

	flt, err := test_utils.GetOrCreateFleetByDOT(ctx, authzUser, qc, dotNumber)
	require.NoError(t, err)

	role := test_utils.CreateUserRoleArgs{
		FleetID: &flt.ID,
		Group:   authz.FleetAdminRole,
	}

	createdUser, err := test_utils.CreateUser(ctx, authzUser, qc, firstName, lastName, email, "Test1234!", nil, nil, nil, role)
	assert.NoError(t, err)

	actualUser, err := test_utils.GetUser(ctx, test_utils.Superuser(), qc, createdUser.ID)
	require.NoError(t, err)

	expectedUser := authz.User{
		UserInfo: authz.UserInfo{
			ID:        createdUser.ID,
			FirstName: firstName,
			LastName:  lastName,
			Email:     email,
		},
		Roles: []authz.Role{
			{
				ID:       actualUser.Roles[0].ID,
				UserID:   createdUser.ID,
				AgencyID: &zeroUUID, // Workaround for mapstructure always returning a zero UUID.
				FleetID:  &flt.ID,
				Group:    authz.FleetAdminRole,
				Domain:   fmt.Sprintf("/fleets/%s/*", flt.ID.String()),
			},
		},
	}

	assert.Equal(t, expectedUser, actualUser)
}
