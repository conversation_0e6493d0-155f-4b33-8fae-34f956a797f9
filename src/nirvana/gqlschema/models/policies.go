package models

import (
	"strconv"
	"time"

	"github.com/google/uuid"

	"nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	policy_db_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	gql_enums "nirvanatech.com/nirvana/gqlschema/models/enums"
	policy_enums "nirvanatech.com/nirvana/policy/enums"
)

type PolicyCoverageEnums string

type Policy struct {
	Id               uuid.UUID `graphql:"id,key"`
	PolicyNumber     string
	ProgramType      gql_enums.ProgramType
	Coverages        []PolicyCoverageEnums
	InsuredName      string
	InsuredDOTNumber string
	IsTest           bool
	State            policy_enums.PolicyState
	StartDate        time.Time
	EndDate          time.Time
	SignedLink       ExpirableLink
	DocumentID       uuid.UUID `graphql:"documentID"`
	SubCoverages     []CoverageWithSymbols
	Underwriter      BasicUser
	Endorsements     []Endorsement
	// The vehicle list won't be populated for fleet policies
	Vehicles []PolicyVehicle
	// The driver list won't be populated for fleet policies
	Drivers []PolicyDriver
}

// PolicyFromDB converts a policy.Policy to a Policy for GraphQL.
func PolicyFromDB(p policy.Policy, isTest bool) *Policy {
	policyType, _ := p.PolicyNumber.GetPolicyType()

	return &Policy{
		Id:           p.Id,
		PolicyNumber: p.PolicyNumber.String(),
		ProgramType:  programTypeFromDB(p.PolicyNumber.ProgramType()),
		Coverages: []PolicyCoverageEnums{
			PolicyCoverageEnums(policyType.String()),
		},
		InsuredName:      p.InsuredName,
		InsuredDOTNumber: strconv.FormatInt(p.CompanyInfo.DOTNumber, 10),
		State:            p.State,
		StartDate:        p.EffectiveDate,
		EndDate:          p.EffectiveDateTo,
		DocumentID:       p.DocumentHandleId,
		IsTest:           isTest,
	}
}

func programTypeFromDB(programType policy_db_enums.ProgramType) gql_enums.ProgramType {
	switch programType {
	case policy_db_enums.ProgramTypeFleet:
		return gql_enums.ProgramTypeFleet
	case policy_db_enums.ProgramTypeNonFleetAdmitted, policy_db_enums.ProgramTypeNonFleetCanopiusNRB:
		return gql_enums.ProgramTypeNonFleet
	case policy_db_enums.ProgramTypeBusinessAuto:
		return gql_enums.ProgramTypeBusinessAuto
	default:
		return gql_enums.ProgramTypeUnkown
	}
}

type CoverageWithSymbols struct {
	Coverage string
	Symbols  []string
}
