package models

import (
	"github.com/google/uuid"
)

type EndorsementEffectiveInterval struct {
	// This is a string representation of the effective date of the endorsement.
	EffectiveDate string
	// This is a string representation of the "end date" of the endorsement.
	ExpirationDate string
}

type ExpirableLink struct {
	Link string
	// This is a string representation of the expiration time of the link.
	Expiration string
}

type SupportingDocumentOrForm struct {
	DocumentID uuid.UUID `graphql:"documentID"`
	Filename   string
	SignedLink ExpirableLink
}

type Endorsement struct {
	Id                string
	SignedLink        ExpirableLink
	DocumentID        uuid.UUID `graphql:"documentID"`
	EffectiveInterval EndorsementEffectiveInterval
	ApprovedAt        string
	Underwriter       BasicUser
	// TODO: move to enum once we know the values that it can take from Fleet and NF endorsements
	ChangeTypes            []string
	SupportingDocsAndForms []SupportingDocumentOrForm
}
