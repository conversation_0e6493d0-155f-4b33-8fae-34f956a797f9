package models

import (
	"time"

	"nirvanatech.com/nirvana/common-go/pointer_utils"

	"nirvanatech.com/nirvana/fmcsa/basic"
	"nirvanatech.com/nirvana/fmcsa/basic/generated/registry"

	datagov_models "nirvanatech.com/nirvana/fmcsa/db_models/datagov"
)

type DatagovViolation struct {
	ID                      int64 `graphql:"id,key"`
	InspectionID            int64
	SeqNumber               int
	PartNumber              string
	PartNumberSection       string
	InspViolUnit            string
	VehicleID               *int64 `graphql:"-"`
	InspViolationCategoryID int
	OOS                     *bool `graphql:"oos"`
	DefectVerificationID    *int
	CitationNumber          *string
	ChangeDate              time.Time
	Code                    *string
	Group                   *basic.ViolationGroup
	Severity                *int
	Description             *string
	CreatedAt               time.Time
	DeletedAt               *time.Time
}

// ViolationFromDB converts a datagov_models.InspectionViolation to a DatagovViolation for GraphQL.
func ViolationFromDB(violation *datagov_models.InspectionViolation) *DatagovViolation {
	retval := &DatagovViolation{
		ID:                      violation.ID,
		InspectionID:            violation.InspectionID,
		SeqNumber:               violation.SeqNumber,
		PartNumber:              violation.PartNumber,
		PartNumberSection:       violation.PartNumberSection.String,
		InspViolUnit:            violation.InspViolUnit,
		VehicleID:               violation.VehicleID.Ptr(),
		InspViolationCategoryID: violation.InspViolationCategoryID,
		OOS:                     violation.Oos.Ptr(),
		DefectVerificationID:    violation.DefectVerificationID.Ptr(),
		CitationNumber:          violation.CitationNumber.Ptr(),
		ChangeDate:              violation.ChangeDate,
		Code:                    violation.Code.Ptr(),
		CreatedAt:               violation.CreatedAt,
		DeletedAt:               violation.DeletedAt.Ptr(),
	}
	if metadata := registry.Violation(violation.Code.String); metadata != nil {
		retval.Code = &metadata.HumanReadable
		retval.Group = &metadata.Group
		retval.Severity = &metadata.Severity
		retval.Description = &metadata.Description
	}

	// The code "395.8A" has a violation severity of '5' for violations that occurred before 2023-09-01,
	// and a violation severity of '1' for violations occurring on or after 2023-09-01.
	cutoffDate := time.Date(2023, 9, 1, 0, 0, 0, 0, time.UTC)
	if violation.Code.String == "3958a" && violation.ChangeDate.Before(cutoffDate) {
		retval.Severity = pointer_utils.ToPointer(5)
	}

	return retval
}
