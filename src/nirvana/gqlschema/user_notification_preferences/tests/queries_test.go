package tests

import (
	"context"
	"testing"

	"nirvanatech.com/nirvana/common-go/knock_client"
	"nirvanatech.com/nirvana/graphql-server/queryclient"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/users_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"

	"github.com/stretchr/testify/require"
	"go.uber.org/fx"
	"go.uber.org/mock/gomock"
)

func TestFetchUserNotificationPreferences(t *testing.T) {
	ctx := context.Background()
	var env struct {
		fx.In

		KnockClient  knock_client.KnockClient
		QueryClient  *queryclient.QueryClient
		UsersFixture *users_fixture.UsersFixture
	}

	defer testloader.RequireStart(t, &env).RequireStop()

	fetchUserNotificationPreferencesQuery := `query getUserNotificationPreferences {
		userNotificationPreferences {
			email
			sms
			workflow
		}
	}`
	type queryResponse struct {
		UserNotificationPreferences []struct {
			Email    bool
			SMS      bool
			Workflow string
		}
	}

	expectedResult := queryResponse{
		UserNotificationPreferences: []struct {
			Email    bool
			SMS      bool
			Workflow string
		}{
			{
				Email:    true,
				SMS:      false,
				Workflow: string(knock_client.WorkflowSendViolationAlerts),
			},
		},
	}

	user := env.UsersFixture.FleetAdmin.AuthzUser()

	qc := env.QueryClient
	a := require.New(t)

	mockResponse := knock_client.UserWorkflowPreferences{
		{
			Email:    true,
			SMS:      false,
			Workflow: knock_client.WorkflowSendViolationAlerts,
		},
	}

	env.KnockClient.(*knock_client.MockKnockClient).
		EXPECT().
		GetUserWorkflowPreferences(gomock.Any(), user.ID).
		Return(mockResponse, nil).
		Times(1)

	var response queryResponse
	err := qc.Query(ctx, user, fetchUserNotificationPreferencesQuery, nil).ResultAs(&response)
	a.NoError(err)
	a.Equal(expectedResult, response)
}
