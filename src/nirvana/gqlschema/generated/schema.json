{"__schema": {"directives": [{"args": [{"defaultValue": null, "description": "Included when true.", "name": "if", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}], "description": "Directs the executor to include this field or fragment only when the `if` argument is true.", "locations": ["FIELD", "FRAGMENT_SPREAD", "INLINE_FRAGMENT"], "name": "include"}, {"args": [{"defaultValue": null, "description": "Skipped when true.", "name": "if", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}], "description": "Directs the executor to skip this field or fragment only when the `if` argument is true.", "locations": ["FIELD", "FRAGMENT_SPREAD", "INLINE_FRAGMENT"], "name": "skip"}, {"args": [], "description": "Client-side-only directive that instructs the type generator to mark this field as optional. This is useful for making the generated types compliant with Troy persistence schema.", "locations": ["FIELD"], "name": "type_as_optional"}], "mutationType": {"name": "Mutation"}, "queryType": {"name": "Query"}, "types": [{"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "city", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "state", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "street1", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "street2", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "zip", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "Address", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [], "inputFields": [{"defaultValue": null, "description": "", "name": "city", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "state", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "street1", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "street2", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "zip", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "interfaces": [], "kind": "INPUT_OBJECT", "name": "Address_InputObject", "possibleTypes": []}, {"description": "", "enumValues": [{"deprecationReason": null, "description": "Accounting", "isDeprecated": false, "name": "Accounting"}, {"deprecationReason": null, "description": "Billing", "isDeprecated": false, "name": "Billing"}, {"deprecationReason": null, "description": "BrandMarketer", "isDeprecated": false, "name": "BrandMarketer"}, {"deprecationReason": null, "description": "<PERSON><PERSON><PERSON>", "isDeprecated": false, "name": "<PERSON><PERSON><PERSON>"}, {"deprecationReason": null, "description": "ContentDistributor", "isDeprecated": false, "name": "ContentDistributor"}, {"deprecationReason": null, "description": "CustomerService", "isDeprecated": false, "name": "CustomerService"}, {"deprecationReason": null, "description": "DirectBilling", "isDeprecated": false, "name": "DirectBilling"}, {"deprecationReason": null, "description": "Leadership", "isDeprecated": false, "name": "Leadership"}, {"deprecationReason": null, "description": "LegalOrCompliance", "isDeprecated": false, "name": "LegalOrCompliance"}, {"deprecationReason": null, "description": "Marketer", "isDeprecated": false, "name": "Marketer"}, {"deprecationReason": null, "description": "Producer", "isDeprecated": false, "name": "Producer"}, {"deprecationReason": null, "description": "RiskServices", "isDeprecated": false, "name": "RiskServices"}, {"deprecationReason": null, "description": "Safety", "isDeprecated": false, "name": "Safety"}], "fields": [], "inputFields": [], "interfaces": [], "kind": "ENUM", "name": "AgencyRole", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "measure", "type": {"kind": "SCALAR", "name": "float64", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "name", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "percentile", "type": {"kind": "SCALAR", "name": "int64", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "threshold", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "float64", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "BasicScoreDetail", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "email", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "name", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "BasicUser", "possibleTypes": []}, {"description": "", "enumValues": [{"deprecationReason": null, "description": "MSTransverse", "isDeprecated": false, "name": "MSTransverse"}], "fields": [], "inputFields": [], "interfaces": [], "kind": "ENUM", "name": "Carrier", "possibleTypes": []}, {"description": "", "enumValues": [{"deprecationReason": null, "description": "ControlledSubstancesAlcohol", "isDeprecated": false, "name": "ControlledSubstancesAlcohol"}, {"deprecationReason": null, "description": "CrashIndicator", "isDeprecated": false, "name": "CrashIndicator"}, {"deprecationReason": null, "description": "DriverFitness", "isDeprecated": false, "name": "DriverFitness"}, {"deprecationReason": null, "description": "HMCompliance", "isDeprecated": false, "name": "HMCompliance"}, {"deprecationReason": null, "description": "HOSCompliance", "isDeprecated": false, "name": "HOSCompliance"}, {"deprecationReason": null, "description": "InsuranceOther", "isDeprecated": false, "name": "InsuranceOther"}, {"deprecationReason": null, "description": "UnsafeDriving", "isDeprecated": false, "name": "UnsafeDriving"}, {"deprecationReason": null, "description": "Unspecified", "isDeprecated": false, "name": "Unspecified"}, {"deprecationReason": null, "description": "VehicleMaintenance", "isDeprecated": false, "name": "VehicleMaintenance"}], "fields": [], "inputFields": [], "interfaces": [], "kind": "ENUM", "name": "Category", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "geoJSON", "type": {"kind": "OBJECT", "name": "GeoJSON", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "projection", "type": {"kind": "OBJECT", "name": "Line<PERSON>hart", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "table", "type": {"kind": "OBJECT", "name": "Table", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "title", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "url", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "Chart", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "category", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "FeedbackCategor<PERSON>", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "claimId", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "createdAt", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "created<PERSON>y", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "rating", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "value", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "possibleTypes": []}, {"description": "", "enumValues": [{"deprecationReason": null, "description": "Cancelled", "isDeprecated": false, "name": "Cancelled"}, {"deprecationReason": null, "description": "Closed", "isDeprecated": false, "name": "Closed"}, {"deprecationReason": null, "description": "CreatedInError", "isDeprecated": false, "name": "CreatedInError"}, {"deprecationReason": null, "description": "Invalid", "isDeprecated": false, "name": "Invalid"}, {"deprecationReason": null, "description": "Open", "isDeprecated": false, "name": "Open"}, {"deprecationReason": null, "description": "Reopen", "isDeprecated": false, "name": "Reopen"}], "fields": [], "inputFields": [], "interfaces": [], "kind": "ENUM", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "claimExternalId", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "createdAt", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "value", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "ClaimStatusChange", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "claimId", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "createdAt", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "feedback", "type": {"kind": "SCALAR", "name": "bool", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "intervalEnd", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "intervalStart", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "scheduled", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "summary", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "title", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "claimSummaryId", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "createdAt", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "created<PERSON>y", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "rating", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "updatedAt", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "ClaimSummaryFeedback", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [], "inputFields": [{"defaultValue": null, "description": "", "name": "registrationNumber", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "vin", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}], "interfaces": [], "kind": "INPUT_OBJECT", "name": "ClaimVehicle_InputObject", "possibleTypes": []}, {"description": "", "enumValues": [{"deprecationReason": null, "description": "Nars", "isDeprecated": false, "name": "Nars"}, {"deprecationReason": null, "description": "Snapsheet", "isDeprecated": false, "name": "Snapsheet"}, {"deprecationReason": null, "description": "Undetermined", "isDeprecated": false, "name": "Undetermined"}], "fields": [], "inputFields": [], "interfaces": [], "kind": "ENUM", "name": "<PERSON><PERSON><PERSON><PERSON>rov<PERSON>", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "comparisons", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "ComparisonMarkdown", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "coverage", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "symbols", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "CoverageWithSymbols", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "xVal", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "yVals", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "float64", "ofType": null}}}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "Data", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "deletedCount", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int64", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "DeleteSentInspectionsResponse", "possibleTypes": []}, {"description": "", "enumValues": [{"deprecationReason": null, "description": "ConditionalRating", "isDeprecated": false, "name": "ConditionalRating"}, {"deprecationReason": null, "description": "SatisfactoryRating", "isDeprecated": false, "name": "SatisfactoryRating"}, {"deprecationReason": null, "description": "Unrated", "isDeprecated": false, "name": "Unrated"}, {"deprecationReason": null, "description": "UnsatisfactoryRating", "isDeprecated": false, "name": "UnsatisfactoryRating"}], "fields": [], "inputFields": [], "interfaces": [], "kind": "ENUM", "name": "DotRating", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "handleId", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "key", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "url", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "DraftFnolAttachment", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "draftFnolId", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "email", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "firstName", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "lastName", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "phone", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "DraftFnolContact", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "draftFnolId", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "isInsuredVehicle", "type": {"kind": "SCALAR", "name": "bool", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "registrationNumber", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "vin", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "DraftFnolVehicle", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [], "inputFields": [{"defaultValue": null, "description": "", "name": "email", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "firstName", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "id", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "lastName", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "phone", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}], "interfaces": [], "kind": "INPUT_OBJECT", "name": "DraftReporter_InputObject", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [], "inputFields": [{"defaultValue": null, "description": "", "name": "id", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "registrationNumber", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "vin", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}], "interfaces": [], "kind": "INPUT_OBJECT", "name": "DraftVehicle_InputObject", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "approvedAt", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "changeTypes", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "documentID", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "effectiveInterval", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "EndorsementEffectiveInterval", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "signedLink", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "ExpirableLink", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "supportingDocsAndForms", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "SupportingDocumentOrForm", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "underwriter", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "BasicUser", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "Endorsement", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "effectiveDate", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "expirationDate", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "EndorsementEffectiveInterval", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "expiration", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "link", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "ExpirableLink", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "category", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "percentile", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "float64", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "recommendedAmountComparison", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "recommendedAmountComparisonMarkdown", "type": {"kind": "OBJECT", "name": "ComparisonMarkdown", "ofType": null}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "ExplainabilityRecommendation", "possibleTypes": []}, {"description": "", "enumValues": [{"deprecationReason": null, "description": "FasterClaimResolution", "isDeprecated": false, "name": "FasterClaimResolution"}, {"deprecationReason": null, "description": "FrequentAdjusterCommunication", "isDeprecated": false, "name": "FrequentAdjusterCommunication"}, {"deprecationReason": null, "description": "Invalid", "isDeprecated": false, "name": "Invalid"}, {"deprecationReason": null, "description": "OfferFairerCompensation", "isDeprecated": false, "name": "OfferFairerCompensation"}, {"deprecationReason": null, "description": "Other", "isDeprecated": false, "name": "Other"}, {"deprecationReason": null, "description": "OutstandingService", "isDeprecated": false, "name": "OutstandingService"}, {"deprecationReason": null, "description": "ProvideBetterSupport", "isDeprecated": false, "name": "ProvideBetterSupport"}, {"deprecationReason": null, "description": "RequireFewerDocuments", "isDeprecated": false, "name": "RequireFewerDocuments"}], "fields": [], "inputFields": [], "interfaces": [], "kind": "ENUM", "name": "FeedbackCategor<PERSON>", "possibleTypes": []}, {"description": "", "enumValues": [{"deprecationReason": null, "description": "BASICS", "isDeprecated": false, "name": "BASICS"}, {"deprecationReason": null, "description": "Crashes", "isDeprecated": false, "name": "Crashes"}, {"deprecationReason": null, "description": "DOTRating", "isDeprecated": false, "name": "DOTRating"}, {"deprecationReason": null, "description": "Insurance", "isDeprecated": false, "name": "Insurance"}, {"deprecationReason": null, "description": "RelatedEntities", "isDeprecated": false, "name": "RelatedEntities"}, {"deprecationReason": null, "description": "Violations", "isDeprecated": false, "name": "Violations"}], "fields": [], "inputFields": [], "interfaces": [], "kind": "ENUM", "name": "FlagCategory", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "edges", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "FlagEdge", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "pageInfo", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "PageInfo", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "totalCount", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int64", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "FlagConnection", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "cursor", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "node", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "flag", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "FlagEdge", "possibleTypes": []}, {"description": "", "enumValues": [{"deprecationReason": null, "description": "Critical", "isDeprecated": false, "name": "Critical"}, {"deprecationReason": null, "description": "Minor", "isDeprecated": false, "name": "Minor"}, {"deprecationReason": null, "description": "Moderate", "isDeprecated": false, "name": "Moderate"}], "fields": [], "inputFields": [], "interfaces": [], "kind": "ENUM", "name": "FlagSeverity", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "edges", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "FleetSafetyReportEdge", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "pageInfo", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "PageInfo", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "totalCount", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int64", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "FleetSafetyReportConnection", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "cursor", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "node", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "fleetSafetyReport", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "FleetSafetyReportEdge", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "dotNumber", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "lastViewedAt", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "name", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "state", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "FleetSearchResult", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "attachments", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "FnolAttachment", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "clientClaimNumber", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "contacts", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "FnolContact", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "createdAt", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "created<PERSON>y", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "incidentDescription", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "injuriesInvolved", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "lossDatetime", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "lossLocation", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "lossState", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "noticeType", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "FnolNoticeType", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "policeAgencyName", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "policeReportNumber", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "policy", "type": {"kind": "OBJECT", "name": "policy", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "policyNumber", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "provider", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "<PERSON><PERSON><PERSON><PERSON>rov<PERSON>", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "status", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "FnolStatus", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "submittedFrom", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "FnolSource", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "vehicles", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "FnolVehicle", "ofType": null}}}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "Fnol", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "createdAt", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "fnolId", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "handleId", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "FnolAttachment", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "contactType", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "email", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "firstName", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "fnolId", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "lastName", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "phone", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "FnolContact", "possibleTypes": []}, {"description": "", "enumValues": [{"deprecationReason": null, "description": "<PERSON><PERSON><PERSON>", "isDeprecated": false, "name": "<PERSON><PERSON><PERSON>"}, {"deprecationReason": null, "description": "Invalid", "isDeprecated": false, "name": "Invalid"}, {"deprecationReason": null, "description": "Report", "isDeprecated": false, "name": "Report"}], "fields": [], "inputFields": [], "interfaces": [], "kind": "ENUM", "name": "FnolNoticeType", "possibleTypes": []}, {"description": "", "enumValues": [{"deprecationReason": null, "description": "FinolaAutoSubmit", "isDeprecated": false, "name": "FinolaAutoSubmit"}, {"deprecationReason": null, "description": "FinolaEmail", "isDeprecated": false, "name": "FinolaEmail"}, {"deprecationReason": null, "description": "SafetyApp", "isDeprecated": false, "name": "SafetyApp"}, {"deprecationReason": null, "description": "SupportApp", "isDeprecated": false, "name": "SupportApp"}, {"deprecationReason": null, "description": "Unknown", "isDeprecated": false, "name": "Unknown"}], "fields": [], "inputFields": [], "interfaces": [], "kind": "ENUM", "name": "FnolSource", "possibleTypes": []}, {"description": "", "enumValues": [{"deprecationReason": null, "description": "Draft", "isDeprecated": false, "name": "Draft"}, {"deprecationReason": null, "description": "Invalid", "isDeprecated": false, "name": "Invalid"}, {"deprecationReason": null, "description": "Sendable", "isDeprecated": false, "name": "Sendable"}, {"deprecationReason": null, "description": "<PERSON><PERSON>", "isDeprecated": false, "name": "<PERSON><PERSON>"}], "fields": [], "inputFields": [], "interfaces": [], "kind": "ENUM", "name": "FnolStatus", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "fnolId", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "isInsuredVehicle", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "registrationNumber", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "vIN", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "FnolVehicle", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "downloadURL", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "GenerateBordereauxReportResponse", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "geometry", "type": {"kind": "OBJECT", "name": "Geometry", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "properties", "type": {"kind": "OBJECT", "name": "Properties", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "type", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "GeoFeature", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "features", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GeoFeature", "ofType": null}}}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "GeoJSON", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "coordinates", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "float64"}}}}}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "type", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "Geometry", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "date", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "discount", "type": {"kind": "SCALAR", "name": "float64", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "errorMessage", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "hardBrakingCountPer1000Miles", "type": {"kind": "SCALAR", "name": "float64", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "hardBrakingPercentile", "type": {"kind": "SCALAR", "name": "float64", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "month", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "speedingCountPer1000Miles", "type": {"kind": "SCALAR", "name": "float64", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "speedingPercentile", "type": {"kind": "SCALAR", "name": "float64", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "value", "type": {"kind": "SCALAR", "name": "float64", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "year", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "ISSScore", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "browserWSEndpoint", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "twoFactorUrl", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "InitiateDriverViolationFetchResults", "possibleTypes": []}, {"description": "", "enumValues": [{"deprecationReason": null, "description": "<PERSON><PERSON><PERSON><PERSON>", "isDeprecated": false, "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"deprecationReason": null, "description": "Full", "isDeprecated": false, "name": "Full"}, {"deprecationReason": null, "description": "Material", "isDeprecated": false, "name": "Material"}, {"deprecationReason": null, "description": "SpecialStudy", "isDeprecated": false, "name": "SpecialStudy"}, {"deprecationReason": null, "description": "Terminal", "isDeprecated": false, "name": "Terminal"}, {"deprecationReason": null, "description": "Unknown", "isDeprecated": false, "name": "Unknown"}, {"deprecationReason": null, "description": "WalkAround", "isDeprecated": false, "name": "WalkAround"}], "fields": [], "inputFields": [], "interfaces": [], "kind": "ENUM", "name": "InspectionLevel", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "edges", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "InspectionRecordEdge", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "pageInfo", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "PageInfo", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "totalCount", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int64", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "InspectionRecordConnection", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "cursor", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "node", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "inspection", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "InspectionRecordEdge", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "carrier", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "effectiveDateFrom", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "effectiveDateTo", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "policyNumber", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "status", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "types", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "InsuranceRecord", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "data", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Data", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "lineConfigs", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "LineConfig", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "threshold", "type": {"kind": "SCALAR", "name": "float64", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "xField", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "xLabel", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "yLabel", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "Line<PERSON>hart", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "dashed", "type": {"kind": "SCALAR", "name": "bool", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "name", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "LineConfig", "possibleTypes": []}, {"description": "", "enumValues": [{"deprecationReason": null, "description": "AutoLiability", "isDeprecated": false, "name": "AutoLiability"}, {"deprecationReason": null, "description": "GeneralLiability", "isDeprecated": false, "name": "GeneralLiability"}, {"deprecationReason": null, "description": "Invalid", "isDeprecated": false, "name": "Invalid"}, {"deprecationReason": null, "description": "MotorTruckCargo", "isDeprecated": false, "name": "MotorTruckCargo"}], "fields": [], "inputFields": [], "interfaces": [], "kind": "ENUM", "name": "LineOfBusiness", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "countyCode", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "countyName", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "Location", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "mileage", "type": {"kind": "SCALAR", "name": "float64", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "month", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "pU", "type": {"kind": "SCALAR", "name": "float64", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "utilization", "type": {"kind": "SCALAR", "name": "float64", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "year", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "MonthlyValues", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [{"defaultValue": null, "description": "", "name": "email", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "firstName", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "lastName", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "password", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "phoneNumber", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "profilePicture", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "title", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "activateUser", "type": {"kind": "OBJECT", "name": "activateUserResponse", "ofType": null}}, {"args": [{"defaultValue": null, "description": "", "name": "ids", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "archiveDraftFnols", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}, {"args": [{"defaultValue": null, "description": "", "name": "browserWSEndpoint", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "reportId", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "twoFA", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "twoFactorUrl", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "completeDriverViolationFetch", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}, {"args": [{"defaultValue": null, "description": "", "name": "address", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "Address_InputObject", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "fleetBD", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "name", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "nonFleetBD", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "createAgencyAndBDMapping", "type": {"kind": "OBJECT", "name": "agency", "ofType": null}}, {"args": [{"defaultValue": null, "description": "", "name": "category", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "FeedbackCategor<PERSON>", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "claimId", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "rating", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "value", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "createClaimFeedback", "type": {"kind": "OBJECT", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ofType": null}}, {"args": [{"defaultValue": null, "description": "", "name": "attachment<PERSON>eys", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}}, {"defaultValue": null, "description": "", "name": "description", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "draftFnolId", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "injuriesInvolved", "type": {"kind": "SCALAR", "name": "bool", "ofType": null}}, {"defaultValue": null, "description": "", "name": "insuredName", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "insuredVehicles", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "ClaimVehicle_InputObject", "ofType": null}}}}, {"defaultValue": null, "description": "", "name": "lineOfBusiness", "type": {"kind": "ENUM", "name": "LineOfBusiness", "ofType": null}}, {"defaultValue": null, "description": "", "name": "lossDate", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "lossLocation", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "lossState", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "noticeType", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "FnolNoticeType", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "otherVehicles", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "ClaimVehicle_InputObject", "ofType": null}}}}, {"defaultValue": null, "description": "", "name": "police", "type": {"kind": "INPUT_OBJECT", "name": "Police_InputObject", "ofType": null}}, {"defaultValue": null, "description": "", "name": "policyNumber", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "provider", "type": {"kind": "ENUM", "name": "<PERSON><PERSON><PERSON><PERSON>rov<PERSON>", "ofType": null}}, {"defaultValue": null, "description": "", "name": "reporter", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "Reporter_InputObject", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "source", "type": {"kind": "ENUM", "name": "FnolSource", "ofType": null}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "createFNOL", "type": {"kind": "OBJECT", "name": "Fnol", "ofType": null}}, {"args": [{"defaultValue": null, "description": "", "name": "delegateUserID", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "dotNumber", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "starred", "type": {"kind": "SCALAR", "name": "bool", "ofType": null}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "createFleetSafetyReport", "type": {"kind": "OBJECT", "name": "fleetSafetyReport", "ofType": null}}, {"args": [{"defaultValue": null, "description": "", "name": "agencyID", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "fleetID", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "group", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "RoleGroupEnum", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "userID", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "createRole", "type": {"kind": "OBJECT", "name": "role", "ofType": null}}, {"args": [{"defaultValue": null, "description": "", "name": "email", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "firstName", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "lastName", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "password", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "phoneNumber", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "profilePicture", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "roles", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "createUserRoleArgs_InputObject", "ofType": null}}}}}, {"defaultValue": null, "description": "", "name": "title", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "createUser", "type": {"kind": "OBJECT", "name": "user", "ofType": null}}, {"args": [{"defaultValue": null, "description": "", "name": "userID", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "deactivateUser", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}, {"args": [{"defaultValue": null, "description": "", "name": "roleID", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "userID", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "deleteRole", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}, {"args": [{"defaultValue": null, "description": "", "name": "inspectionIDs", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int64", "ofType": null}}}}}, {"defaultValue": null, "description": "", "name": "reportID", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "deleteSentInspections", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "DeleteSentInspectionsResponse", "ofType": null}}}, {"args": [{"defaultValue": null, "description": "", "name": "authCode", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "error", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "scope", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "state", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "tsp", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "TSP", "ofType": null}}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "finalizeTelematics", "type": {"kind": "OBJECT", "name": "finalizeTelematicsResponse", "ofType": null}}, {"args": [{"defaultValue": null, "description": "", "name": "carrier", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "Carrier", "ofType": null}}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "generateBordereauxReport", "type": {"kind": "OBJECT", "name": "GenerateBordereauxReportResponse", "ofType": null}}, {"args": [{"defaultValue": null, "description": "", "name": "claimId", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "generateClaimSummary", "type": {"kind": "OBJECT", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ofType": null}}, {"args": [{"defaultValue": null, "description": "", "name": "safetyReportID", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "tsp", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "TSP", "ofType": null}}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "initiateTelematics", "type": {"kind": "OBJECT", "name": "initiateTelematicsResponse", "ofType": null}}, {"args": [{"defaultValue": null, "description": "", "name": "agencyID", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "email", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "role", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "RoleGroupEnum", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "sFDCAgencyRoles", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "AgencyRole", "ofType": null}}}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "inviteAgencyUserFromAgents", "type": {"kind": "OBJECT", "name": "agencyUserInvite", "ofType": null}}, {"args": [{"defaultValue": null, "description": "", "name": "agencyID", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "email", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "role", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "RoleGroupEnum", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "sFDCAgencyRoles", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "AgencyRole", "ofType": null}}}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "inviteAgencyUserFromSupport", "type": {"kind": "OBJECT", "name": "agencyUserInvite", "ofType": null}}, {"args": [{"defaultValue": null, "description": "", "name": "email", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "fleetSafetyReportID", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "inviteFleetUser", "type": {"kind": "OBJECT", "name": "fleetUserInvite", "ofType": null}}, {"args": [{"defaultValue": null, "description": "", "name": "address", "type": {"kind": "INPUT_OBJECT", "name": "Address_InputObject", "ofType": null}}, {"defaultValue": null, "description": "", "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "name", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "patchAgency", "type": {"kind": "OBJECT", "name": "agency", "ofType": null}}, {"args": [{"defaultValue": null, "description": "", "name": "email", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "firstName", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "lastName", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "oldPassword", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "password", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "phoneNumber", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "profilePicture", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "title", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "patchUser", "type": {"kind": "OBJECT", "name": "user", "ofType": null}}, {"args": [{"defaultValue": null, "description": "", "name": "endTime", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}, {"defaultValue": null, "description": "", "name": "reportID", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "startTime", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "printFleetSafetyReport", "type": {"kind": "OBJECT", "name": "PrintFleetSafetyReportLink", "ofType": null}}, {"args": [{"defaultValue": null, "description": "", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "reportId", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "userID", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "setFleetStarredStatus", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}, {"args": [{"defaultValue": null, "description": "", "name": "delegateUserID", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "shareSafetyReport", "type": {"kind": "OBJECT", "name": "SafetyReportShare", "ofType": null}}, {"args": [{"defaultValue": null, "description": "", "name": "attachment<PERSON>eys", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}}, {"defaultValue": null, "description": "", "name": "description", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "draftFnolId", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "injuriesInvolved", "type": {"kind": "SCALAR", "name": "bool", "ofType": null}}, {"defaultValue": null, "description": "", "name": "insuredName", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "insuredVehicles", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "ClaimVehicle_InputObject", "ofType": null}}}}, {"defaultValue": null, "description": "", "name": "lineOfBusiness", "type": {"kind": "ENUM", "name": "LineOfBusiness", "ofType": null}}, {"defaultValue": null, "description": "", "name": "lossDate", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "lossLocation", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "lossState", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "noticeType", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "FnolNoticeType", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "otherVehicles", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "ClaimVehicle_InputObject", "ofType": null}}}}, {"defaultValue": null, "description": "", "name": "police", "type": {"kind": "INPUT_OBJECT", "name": "Police_InputObject", "ofType": null}}, {"defaultValue": null, "description": "", "name": "policyNumber", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "provider", "type": {"kind": "ENUM", "name": "<PERSON><PERSON><PERSON><PERSON>rov<PERSON>", "ofType": null}}, {"defaultValue": null, "description": "", "name": "reporter", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "Reporter_InputObject", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "source", "type": {"kind": "ENUM", "name": "FnolSource", "ofType": null}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "submitFnol", "type": {"kind": "OBJECT", "name": "SubmitFnolResponse", "ofType": null}}, {"args": [{"defaultValue": null, "description": "", "name": "officeDepartmentRegion", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "phoneNumber", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "preferredStates", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}}}, {"defaultValue": null, "description": "", "name": "sFDCAgencyRoles", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "AgencyRole", "ofType": null}}}}, {"defaultValue": null, "description": "", "name": "title", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "workLocation", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "updateAgentDetail", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}, {"args": [{"defaultValue": null, "description": "", "name": "effectiveDate", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}, {"defaultValue": null, "description": "", "name": "expirationDate", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}, {"defaultValue": null, "description": "", "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "licenseNumber", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "licenseStatus", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "licenseType", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "state", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "updateAgentLicense", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}, {"args": [{"defaultValue": null, "description": "", "name": "preferences", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "UserWorkflowPreference_InputObject", "ofType": null}}}}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "updateUserNotificationPreferences", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "UserWorkflowPreference", "ofType": null}}}}}, {"args": [{"defaultValue": null, "description": "", "name": "claimSummaryId", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "rating", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "upsertClaimSummaryFeedback", "type": {"kind": "OBJECT", "name": "ClaimSummaryFeedback", "ofType": null}}, {"args": [{"defaultValue": null, "description": "", "name": "attachment<PERSON>eys", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}}, {"defaultValue": null, "description": "", "name": "dotNumber", "type": {"kind": "SCALAR", "name": "int64", "ofType": null}}, {"defaultValue": null, "description": "", "name": "id", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "incidentDescription", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "injuriesInvolved", "type": {"kind": "SCALAR", "name": "bool", "ofType": null}}, {"defaultValue": null, "description": "", "name": "insuredName", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "insuredVehicles", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "DraftVehicle_InputObject", "ofType": null}}}}, {"defaultValue": null, "description": "", "name": "lossDatetime", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}, {"defaultValue": null, "description": "", "name": "lossLocation", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "lossState", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "noticeType", "type": {"kind": "ENUM", "name": "FnolNoticeType", "ofType": null}}, {"defaultValue": null, "description": "", "name": "otherVehicles", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "DraftVehicle_InputObject", "ofType": null}}}}, {"defaultValue": null, "description": "", "name": "police", "type": {"kind": "INPUT_OBJECT", "name": "Police_InputObject", "ofType": null}}, {"defaultValue": null, "description": "", "name": "policyNumber", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "reporter", "type": {"kind": "INPUT_OBJECT", "name": "DraftReporter_InputObject", "ofType": null}}, {"defaultValue": null, "description": "", "name": "source", "type": {"kind": "ENUM", "name": "FnolSource", "ofType": null}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "upsertDraftFNOL", "type": {"kind": "OBJECT", "name": "draftFnols", "ofType": null}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "Mutation", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "category", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "claimExternalId", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "createdAt", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "externalId", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "modifiedAt", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "source", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "<PERSON><PERSON><PERSON><PERSON>rov<PERSON>", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "updatedAt", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "value", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "Note", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "category", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "inspections", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "nationalAverage", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "float64", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "oOSPercent", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "float64", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "oOSViolations", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "OOSSummary", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "endCursor", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "hasNextPage", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "hasPrevPage", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "pages", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "startCursor", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "PageInfo", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [], "inputFields": [{"defaultValue": null, "description": "", "name": "agencyName", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "reportNumber", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}], "interfaces": [], "kind": "INPUT_OBJECT", "name": "Police_InputObject", "possibleTypes": []}, {"description": "", "enumValues": [{"deprecationReason": null, "description": "CoverageAutoLiability", "isDeprecated": false, "name": "CoverageAutoLiability"}, {"deprecationReason": null, "description": "CoverageAutoPhysicalDamage", "isDeprecated": false, "name": "CoverageAutoPhysicalDamage"}, {"deprecationReason": null, "description": "CoverageGeneralLiability", "isDeprecated": false, "name": "CoverageGeneralLiability"}, {"deprecationReason": null, "description": "CoverageMotorTruckCargo", "isDeprecated": false, "name": "CoverageMotorTruckCargo"}], "fields": [], "inputFields": [], "interfaces": [], "kind": "ENUM", "name": "PolicyCoverageEnums", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "dateOfBirth", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "dateOfHire", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "firstName", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "isIncludedInPolicy", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "isOutOfState", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "isOwner", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "lastName", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "licenseNumber", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "licenseState", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "yearsOfExperience", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "PolicyDriver", "possibleTypes": []}, {"description": "", "enumValues": [{"deprecationReason": null, "description": "Active", "isDeprecated": false, "name": "Active"}, {"deprecationReason": null, "description": "CancellationFiled", "isDeprecated": false, "name": "CancellationFiled"}, {"deprecationReason": null, "description": "Cancelled", "isDeprecated": false, "name": "Cancelled"}, {"deprecationReason": null, "description": "Created", "isDeprecated": false, "name": "Created"}, {"deprecationReason": null, "description": "Expired", "isDeprecated": false, "name": "Expired"}, {"deprecationReason": null, "description": "Generated", "isDeprecated": false, "name": "Generated"}, {"deprecationReason": null, "description": "Invalid", "isDeprecated": false, "name": "Invalid"}, {"deprecationReason": null, "description": "Stale", "isDeprecated": false, "name": "Stale"}], "fields": [], "inputFields": [], "interfaces": [], "kind": "ENUM", "name": "PolicyState", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "make", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "model", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "statedValue", "type": {"kind": "SCALAR", "name": "int", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "vehicleClass", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "vehicleType", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "vin", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "weightClass", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "year", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "PolicyVehicle", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "key", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "url", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "PresignedUploadLink", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "url", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "PrintFleetSafetyReportLink", "possibleTypes": []}, {"description": "", "enumValues": [{"deprecationReason": null, "description": "BusinessAuto", "isDeprecated": false, "name": "BusinessAuto"}, {"deprecationReason": null, "description": "Fleet", "isDeprecated": false, "name": "Fleet"}, {"deprecationReason": null, "description": "NonFleet", "isDeprecated": false, "name": "NonFleet"}, {"deprecationReason": null, "description": "Unkown", "isDeprecated": false, "name": "Unkown"}], "fields": [], "inputFields": [], "interfaces": [], "kind": "ENUM", "name": "ProgramType", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "countyCode", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "inspectionRecords", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "inspection", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "inspection_count", "type": {"kind": "SCALAR", "name": "int64", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "inspection_percentage", "type": {"kind": "SCALAR", "name": "float64", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "name", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "violation_count", "type": {"kind": "SCALAR", "name": "int64", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "violation_percentage", "type": {"kind": "SCALAR", "name": "float64", "ofType": null}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "Properties", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "agencies", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "agency", "ofType": null}}}}}, {"args": [{"defaultValue": null, "description": "", "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "agency", "type": {"kind": "OBJECT", "name": "agency", "ofType": null}}, {"args": [{"defaultValue": null, "description": "", "name": "endTime", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "policyNumber", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "startTime", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "cameraEvents", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "cameraEvents", "ofType": null}}}}}, {"args": [{"defaultValue": null, "description": "", "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "claimById", "type": {"kind": "OBJECT", "name": "claims", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "claims", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "claims", "ofType": null}}}}}, {"args": [{"defaultValue": null, "description": "", "name": "dotNumber", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "claimsByDOTNumber", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "claims", "ofType": null}}}}}, {"args": [{"defaultValue": null, "description": "", "name": "fileNames", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "claimsPresignedUploadLinks", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "PresignedUploadLink", "ofType": null}}}}}, {"args": [{"defaultValue": null, "description": "", "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "draftFnolById", "type": {"kind": "OBJECT", "name": "draftFnols", "ofType": null}}, {"args": [{"defaultValue": null, "description": "", "name": "hasFNOL", "type": {"kind": "SCALAR", "name": "bool", "ofType": null}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "draftFnols", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "draftFnols", "ofType": null}}}}}, {"args": [{"defaultValue": null, "description": "", "name": "searchText", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "fetchFleetSafetySearch", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "FleetSearchResult", "ofType": null}}}}}, {"args": [{"defaultValue": null, "description": "", "name": "dotNumber", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "id", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "fleet", "type": {"kind": "OBJECT", "name": "fleet", "ofType": null}}, {"args": [{"defaultValue": null, "description": "", "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "fleetSafetyReport", "type": {"kind": "OBJECT", "name": "fleetSafetyReport", "ofType": null}}, {"args": [{"defaultValue": null, "description": "", "name": "after", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "before", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "filterText", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "filterTextFields", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}}, {"defaultValue": null, "description": "", "name": "filterType", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "first", "type": {"kind": "SCALAR", "name": "int64", "ofType": null}}, {"defaultValue": null, "description": "", "name": "last", "type": {"kind": "SCALAR", "name": "int64", "ofType": null}}, {"defaultValue": null, "description": "", "name": "sortBy", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "sortOrder", "type": {"kind": "ENUM", "name": "SortOrder", "ofType": null}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "fleetSafetyReports", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "FleetSafetyReportConnection", "ofType": null}}}, {"args": [{"defaultValue": null, "description": "", "name": "dotNumber", "type": {"kind": "SCALAR", "name": "int64", "ofType": null}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "fnols", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Fnol", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "getBordereauxReports", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "bordereauxReport", "ofType": null}}}}}, {"args": [{"defaultValue": null, "description": "", "name": "policyNumber", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "getSubmittableProviderOptions", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "SubmittableOptions", "ofType": null}}}, {"args": [{"defaultValue": null, "description": "", "name": "password", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "reportId", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "username", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "initiateDriverViolationFetch", "type": {"kind": "OBJECT", "name": "InitiateDriverViolationFetchResults", "ofType": null}}, {"args": [{"defaultValue": null, "description": "", "name": "activeDateIn", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}, {"defaultValue": null, "description": "", "name": "dotNumber", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "policyStates", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "PolicyState", "ofType": null}}}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "policies", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "policy", "ofType": null}}}}}, {"args": [{"defaultValue": null, "description": "", "name": "claimId", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "policy", "type": {"kind": "OBJECT", "name": "policy", "ofType": null}}, {"args": [{"defaultValue": null, "description": "", "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "summariesForClaimId", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ofType": null}}}}}, {"args": [{"defaultValue": null, "description": "", "name": "id", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "user", "type": {"kind": "OBJECT", "name": "user", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "userNotificationPreferences", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "UserWorkflowPreference", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "users", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "user", "ofType": null}}}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "Query", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "basicCategory", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "link", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "vINs", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "violationCount", "type": {"kind": "SCALAR", "name": "int", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "violationGroup", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "weightage", "type": {"kind": "SCALAR", "name": "float64", "ofType": null}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "Reason", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "edges", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "RecommendationEdge", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "pageInfo", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "PageInfo", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "totalCount", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int64", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "RecommendationConnection", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "cursor", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "node", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "recommendation", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "RecommendationEdge", "possibleTypes": []}, {"description": "", "enumValues": [{"deprecationReason": null, "description": "High", "isDeprecated": false, "name": "High"}, {"deprecationReason": null, "description": "Low", "isDeprecated": false, "name": "Low"}, {"deprecationReason": null, "description": "Medium", "isDeprecated": false, "name": "Medium"}], "fields": [], "inputFields": [], "interfaces": [], "kind": "ENUM", "name": "RecommendationImpact", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "chart", "type": {"kind": "OBJECT", "name": "Chart", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "markdown", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "reasons", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Reason", "ofType": null}}}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "RecommendationSection", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [], "inputFields": [{"defaultValue": null, "description": "", "name": "email", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "firstName", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "lastName", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "phone", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}], "interfaces": [], "kind": "INPUT_OBJECT", "name": "Reporter_InputObject", "possibleTypes": []}, {"description": "", "enumValues": [{"deprecationReason": null, "description": "AgencyAccountManagerRole", "isDeprecated": false, "name": "AgencyAccountManagerRole"}, {"deprecationReason": null, "description": "AgencyAdminReaderRole", "isDeprecated": false, "name": "AgencyAdminReaderRole"}, {"deprecationReason": null, "description": "AgencyAdminRole", "isDeprecated": false, "name": "AgencyAdminRole"}, {"deprecationReason": null, "description": "AgencyProducerRole", "isDeprecated": false, "name": "AgencyProducerRole"}, {"deprecationReason": null, "description": "AgencyServiceMemberRole", "isDeprecated": false, "name": "AgencyServiceMemberRole"}, {"deprecationReason": null, "description": "BillingAdminRole", "isDeprecated": false, "name": "BillingAdminRole"}, {"deprecationReason": null, "description": "BusinessAutoContractorRole", "isDeprecated": false, "name": "BusinessAutoContractorRole"}, {"deprecationReason": null, "description": "CanopiusUnderwriterRole", "isDeprecated": false, "name": "CanopiusUnderwriterRole"}, {"deprecationReason": null, "description": "ClaimsAdminRole", "isDeprecated": false, "name": "ClaimsAdminRole"}, {"deprecationReason": null, "description": "FleetAdminRole", "isDeprecated": false, "name": "FleetAdminRole"}, {"deprecationReason": null, "description": "FleetBDRole", "isDeprecated": false, "name": "FleetBDRole"}, {"deprecationReason": null, "description": "FleetReaderRole", "isDeprecated": false, "name": "FleetReaderRole"}, {"deprecationReason": null, "description": "Level1UnderwriterRole", "isDeprecated": false, "name": "Level1UnderwriterRole"}, {"deprecationReason": null, "description": "Level2UnderwriterRole", "isDeprecated": false, "name": "Level2UnderwriterRole"}, {"deprecationReason": null, "description": "Level3UnderwriterRole", "isDeprecated": false, "name": "Level3UnderwriterRole"}, {"deprecationReason": null, "description": "Level4UnderwriterRole", "isDeprecated": false, "name": "Level4UnderwriterRole"}, {"deprecationReason": null, "description": "Level5UnderwriterRole", "isDeprecated": false, "name": "Level5UnderwriterRole"}, {"deprecationReason": null, "description": "Level6UnderwriterRole", "isDeprecated": false, "name": "Level6UnderwriterRole"}, {"deprecationReason": null, "description": "Level7UnderwriterRole", "isDeprecated": false, "name": "Level7UnderwriterRole"}, {"deprecationReason": null, "description": "NirvanaAPIUserRole", "isDeprecated": false, "name": "NirvanaAPIUserRole"}, {"deprecationReason": null, "description": "NonFleetBDRole", "isDeprecated": false, "name": "NonFleetBDRole"}, {"deprecationReason": null, "description": "PibitAPIRole", "isDeprecated": false, "name": "PibitAPIRole"}, {"deprecationReason": null, "description": "PolicyAdminReaderRole", "isDeprecated": false, "name": "PolicyAdminReaderRole"}, {"deprecationReason": null, "description": "PowerUserRole", "isDeprecated": false, "name": "PowerUserRole"}, {"deprecationReason": null, "description": "QAUnderwriterRole", "isDeprecated": false, "name": "QAUnderwriterRole"}, {"deprecationReason": null, "description": "SafetyConsultantReader", "isDeprecated": false, "name": "SafetyConsultantReader"}, {"deprecationReason": null, "description": "SeniorSupportRole", "isDeprecated": false, "name": "SeniorSupportRole"}, {"deprecationReason": null, "description": "SeniorUnderwriterRole", "isDeprecated": false, "name": "SeniorUnderwriterRole"}, {"deprecationReason": null, "description": "SharedFleetTelematicsRole", "isDeprecated": false, "name": "SharedFleetTelematicsRole"}, {"deprecationReason": null, "description": "SharedSafetyReaderRole", "isDeprecated": false, "name": "SharedSafetyReaderRole"}, {"deprecationReason": null, "description": "SuperuserRole", "isDeprecated": false, "name": "SuperuserRole"}, {"deprecationReason": null, "description": "UnderwriterManagerRole", "isDeprecated": false, "name": "UnderwriterManagerRole"}, {"deprecationReason": null, "description": "UserOwnerRole", "isDeprecated": false, "name": "UserOwnerRole"}], "fields": [], "inputFields": [], "interfaces": [], "kind": "ENUM", "name": "RoleGroupEnum", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "expiresAt", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "SafetyReportShare", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "explainabilityRecommendations", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "ExplainabilityRecommendation", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "hasExplainabilityData", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "isConfidentScore", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "lowConfidenceReason", "type": {"kind": "SCALAR", "name": "int", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "score", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "float64", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "timestamp", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "ScoreTrendItem", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "inspectionsCount", "type": {"kind": "SCALAR", "name": "int", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "shipperName", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "ShipperData", "possibleTypes": []}, {"description": "", "enumValues": [{"deprecationReason": null, "description": "0", "isDeprecated": false, "name": "asc"}, {"deprecationReason": null, "description": "1", "isDeprecated": false, "name": "desc"}], "fields": [], "inputFields": [], "interfaces": [], "kind": "ENUM", "name": "SortOrder", "possibleTypes": []}, {"description": "", "enumValues": [{"deprecationReason": null, "description": "Created", "isDeprecated": false, "name": "Created"}, {"deprecationReason": null, "description": "Invalid", "isDeprecated": false, "name": "Invalid"}, {"deprecationReason": null, "description": "Undefined", "isDeprecated": false, "name": "Undefined"}, {"deprecationReason": null, "description": "<PERSON><PERSON>", "isDeprecated": false, "name": "<PERSON><PERSON>"}, {"deprecationReason": null, "description": "Validating", "isDeprecated": false, "name": "Validating"}], "fields": [], "inputFields": [], "interfaces": [], "kind": "ENUM", "name": "Status", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "externalId", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "url", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "SubmitFnolResponse", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "nars", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "snapsheet", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "SubmittableOptions", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "documentID", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "filename", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "signedLink", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "ExpirableLink", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "SupportingDocumentOrForm", "possibleTypes": []}, {"description": "", "enumValues": [{"deprecationReason": null, "description": "TSP2BROELD", "isDeprecated": false, "name": "TSP2BROELD"}, {"deprecationReason": null, "description": "TSP3MD", "isDeprecated": false, "name": "TSP3MD"}, {"deprecationReason": null, "description": "TSP888ELD", "isDeprecated": false, "name": "TSP888ELD"}, {"deprecationReason": null, "description": "TSPARIFleet", "isDeprecated": false, "name": "TSPARIFleet"}, {"deprecationReason": null, "description": "TSPATAndTFleet", "isDeprecated": false, "name": "TSPATAndTFleet"}, {"deprecationReason": null, "description": "TSPATAndTFleetComplete", "isDeprecated": false, "name": "TSPATAndTFleetComplete"}, {"deprecationReason": null, "description": "TSPATELD", "isDeprecated": false, "name": "TSPATELD"}, {"deprecationReason": null, "description": "TSPActionELD", "isDeprecated": false, "name": "TSPActionELD"}, {"deprecationReason": null, "description": "TSPActsoftEncore", "isDeprecated": false, "name": "TSPActsoftEncore"}, {"deprecationReason": null, "description": "TSPAdvantageAssetTracking", "isDeprecated": false, "name": "TSPAdvantageAssetTracking"}, {"deprecationReason": null, "description": "TSPAdvantageOne", "isDeprecated": false, "name": "TSPAdvantageOne"}, {"deprecationReason": null, "description": "TSPAgilisLinxup", "isDeprecated": false, "name": "TSPAgilisLinxup"}, {"deprecationReason": null, "description": "TSPAirELD", "isDeprecated": false, "name": "TSPAirELD"}, {"deprecationReason": null, "description": "TSPAlfaELD", "isDeprecated": false, "name": "TSPAlfaELD"}, {"deprecationReason": null, "description": "TSPApexUltima", "isDeprecated": false, "name": "TSPApexUltima"}, {"deprecationReason": null, "description": "TSPApolloELD", "isDeprecated": false, "name": "TSPApolloELD"}, {"deprecationReason": null, "description": "TSPArgosConnectedSolutions", "isDeprecated": false, "name": "TSPArgosConnectedSolutions"}, {"deprecationReason": null, "description": "TSPAssuredTelematics", "isDeprecated": false, "name": "TSPAssuredTelematics"}, {"deprecationReason": null, "description": "TSPAttriX", "isDeprecated": false, "name": "TSPAttriX"}, {"deprecationReason": null, "description": "TSPAwareGPS", "isDeprecated": false, "name": "TSPAwareGPS"}, {"deprecationReason": null, "description": "TSPAzuga", "isDeprecated": false, "name": "TSPAzuga"}, {"deprecationReason": null, "description": "TSPBELLFAMELD", "isDeprecated": false, "name": "TSPBELLFAMELD"}, {"deprecationReason": null, "description": "TSPBadgerFleetSolutions", "isDeprecated": false, "name": "TSPBadgerFleetSolutions"}, {"deprecationReason": null, "description": "TSPBigRoad", "isDeprecated": false, "name": "TSPBigRoad"}, {"deprecationReason": null, "description": "TSPBlackBearELD", "isDeprecated": false, "name": "TSPBlackBearELD"}, {"deprecationReason": null, "description": "TSPBlueArrow", "isDeprecated": false, "name": "TSPBlueArrow"}, {"deprecationReason": null, "description": "TSPBlueArrowTelematics", "isDeprecated": false, "name": "TSPBlueArrowTelematics"}, {"deprecationReason": null, "description": "TSPBlueHorseELD", "isDeprecated": false, "name": "TSPBlueHorseELD"}, {"deprecationReason": null, "description": "TSPBlueInkTechnology", "isDeprecated": false, "name": "TSPBlueInkTechnology"}, {"deprecationReason": null, "description": "TSPBlueStarELD", "isDeprecated": false, "name": "TSPBlueStarELD"}, {"deprecationReason": null, "description": "TSPCNELD", "isDeprecated": false, "name": "TSPCNELD"}, {"deprecationReason": null, "description": "TSPCTELogELD", "isDeprecated": false, "name": "TSPCTELogELD"}, {"deprecationReason": null, "description": "TSPCarrierHQ", "isDeprecated": false, "name": "TSPCarrierHQ"}, {"deprecationReason": null, "description": "TSPClearPathGPS", "isDeprecated": false, "name": "TSPClearPathGPS"}, {"deprecationReason": null, "description": "TSPClutchELD", "isDeprecated": false, "name": "TSPClutchELD"}, {"deprecationReason": null, "description": "TSPColumbusELD", "isDeprecated": false, "name": "TSPColumbusELD"}, {"deprecationReason": null, "description": "TSPCommandGPS", "isDeprecated": false, "name": "TSPCommandGPS"}, {"deprecationReason": null, "description": "TSPContiGO", "isDeprecated": false, "name": "TSPContiGO"}, {"deprecationReason": null, "description": "TSPCoretex", "isDeprecated": false, "name": "TSPCoretex"}, {"deprecationReason": null, "description": "TSPCyntrXELDPlus", "isDeprecated": false, "name": "TSPCyntrXELDPlus"}, {"deprecationReason": null, "description": "TSPDSGELOGS", "isDeprecated": false, "name": "TSPDSGELOGS"}, {"deprecationReason": null, "description": "TSPDailyELD", "isDeprecated": false, "name": "TSPDailyELD"}, {"deprecationReason": null, "description": "TSPDigitalELD", "isDeprecated": false, "name": "TSPDigitalELD"}, {"deprecationReason": null, "description": "TSPDreamELD", "isDeprecated": false, "name": "TSPDreamELD"}, {"deprecationReason": null, "description": "TSPDriveEDR", "isDeprecated": false, "name": "TSPDriveEDR"}, {"deprecationReason": null, "description": "TSPELDBooks", "isDeprecated": false, "name": "TSPELDBooks"}, {"deprecationReason": null, "description": "TSPELDFleet", "isDeprecated": false, "name": "TSPELDFleet"}, {"deprecationReason": null, "description": "TSPELDMandate", "isDeprecated": false, "name": "TSPELDMandate"}, {"deprecationReason": null, "description": "TSPELDMandatePlus", "isDeprecated": false, "name": "TSPELDMandatePlus"}, {"deprecationReason": null, "description": "TSPELDMandatePro", "isDeprecated": false, "name": "TSPELDMandatePro"}, {"deprecationReason": null, "description": "TSPELDOne", "isDeprecated": false, "name": "TSPELDOne"}, {"deprecationReason": null, "description": "TSPELDRider", "isDeprecated": false, "name": "TSPELDRider"}, {"deprecationReason": null, "description": "TSPELDTab", "isDeprecated": false, "name": "TSPELDTab"}, {"deprecationReason": null, "description": "TSPELOG42", "isDeprecated": false, "name": "TSPELOG42"}, {"deprecationReason": null, "description": "TSPEROAD", "isDeprecated": false, "name": "TSPEROAD"}, {"deprecationReason": null, "description": "TSPEVAELD", "isDeprecated": false, "name": "TSPEVAELD"}, {"deprecationReason": null, "description": "TSPEVOELD", "isDeprecated": false, "name": "TSPEVOELD"}, {"deprecationReason": null, "description": "TSPEZELDSolutions", "isDeprecated": false, "name": "TSPEZELDSolutions"}, {"deprecationReason": null, "description": "TSPEZFleet", "isDeprecated": false, "name": "TSPEZFleet"}, {"deprecationReason": null, "description": "TSPEZLogz", "isDeprecated": false, "name": "TSPEZLogz"}, {"deprecationReason": null, "description": "TSPEagleWireless", "isDeprecated": false, "name": "TSPEagleWireless"}, {"deprecationReason": null, "description": "TSPElevenELD", "isDeprecated": false, "name": "TSPElevenELD"}, {"deprecationReason": null, "description": "TSPEnVueTelematics", "isDeprecated": false, "name": "TSPEnVueTelematics"}, {"deprecationReason": null, "description": "TSPExpressWayELD", "isDeprecated": false, "name": "TSPExpressWayELD"}, {"deprecationReason": null, "description": "TSPFACTORELD", "isDeprecated": false, "name": "TSPFACTORELD"}, {"deprecationReason": null, "description": "TSPFMELD", "isDeprecated": false, "name": "TSPFMELD"}, {"deprecationReason": null, "description": "TSPFirstELD", "isDeprecated": false, "name": "TSPFirstELD"}, {"deprecationReason": null, "description": "TSPFleetBossGPS", "isDeprecated": false, "name": "TSPFleetBossGPS"}, {"deprecationReason": null, "description": "TSPFleetComplete", "isDeprecated": false, "name": "TSPFleetComplete"}, {"deprecationReason": null, "description": "TSPFleetLocate21", "isDeprecated": false, "name": "TSPFleetLocate21"}, {"deprecationReason": null, "description": "TSPFleetLocateAdvancedAndCompliance", "isDeprecated": false, "name": "TSPFleetLocateAdvancedAndCompliance"}, {"deprecationReason": null, "description": "TSPFleetLocateELD", "isDeprecated": false, "name": "TSPFleetLocateELD"}, {"deprecationReason": null, "description": "TSPFleetNavSystems", "isDeprecated": false, "name": "TSPFleetNavSystems"}, {"deprecationReason": null, "description": "TSPFleetProfitCenter", "isDeprecated": false, "name": "TSPFleetProfitCenter"}, {"deprecationReason": null, "description": "TSPFleetSharp", "isDeprecated": false, "name": "TSPFleetSharp"}, {"deprecationReason": null, "description": "TSPFleetistics", "isDeprecated": false, "name": "TSPFleetistics"}, {"deprecationReason": null, "description": "TSPFleetmaster", "isDeprecated": false, "name": "TSPFleetmaster"}, {"deprecationReason": null, "description": "TSPFleetmatics", "isDeprecated": false, "name": "TSPFleetmatics"}, {"deprecationReason": null, "description": "TSPFlexport", "isDeprecated": false, "name": "TSPFlexport"}, {"deprecationReason": null, "description": "TSPForceByMojio", "isDeprecated": false, "name": "TSPForceByMojio"}, {"deprecationReason": null, "description": "TSPForwardThinkingELD", "isDeprecated": false, "name": "TSPForwardThinkingELD"}, {"deprecationReason": null, "description": "TSPGPSCommander", "isDeprecated": false, "name": "TSPGPSCommander"}, {"deprecationReason": null, "description": "TSPGPSFleetFinder", "isDeprecated": false, "name": "TSPGPSFleetFinder"}, {"deprecationReason": null, "description": "TSPGPSInsight", "isDeprecated": false, "name": "TSPGPSInsight"}, {"deprecationReason": null, "description": "TSPGPSSolutions", "isDeprecated": false, "name": "TSPGPSSolutions"}, {"deprecationReason": null, "description": "TSPGPSTab", "isDeprecated": false, "name": "TSPGPSTab"}, {"deprecationReason": null, "description": "TSPGPSTrackingCanada", "isDeprecated": false, "name": "TSPGPSTrackingCanada"}, {"deprecationReason": null, "description": "TSPGPSTrackit", "isDeprecated": false, "name": "TSPGPSTrackit"}, {"deprecationReason": null, "description": "TSPGarmin", "isDeprecated": false, "name": "TSPGarmin"}, {"deprecationReason": null, "description": "TSPGeoforce", "isDeprecated": false, "name": "TSPGeoforce"}, {"deprecationReason": null, "description": "TSPGeotab", "isDeprecated": false, "name": "TSPGeotab"}, {"deprecationReason": null, "description": "TSPGlobalELD", "isDeprecated": false, "name": "TSPGlobalELD"}, {"deprecationReason": null, "description": "TSPGlostone", "isDeprecated": false, "name": "TSPGlostone"}, {"deprecationReason": null, "description": "TSPGoFleet", "isDeprecated": false, "name": "TSPGoFleet"}, {"deprecationReason": null, "description": "TSPGoGPS", "isDeprecated": false, "name": "TSPGoGPS"}, {"deprecationReason": null, "description": "TSPGoodDealGPS", "isDeprecated": false, "name": "TSPGoodDealGPS"}, {"deprecationReason": null, "description": "TSPGorillaSafety", "isDeprecated": false, "name": "TSPGorillaSafety"}, {"deprecationReason": null, "description": "TSPGrayboxSolutions", "isDeprecated": false, "name": "TSPGrayboxSolutions"}, {"deprecationReason": null, "description": "TSPGridline", "isDeprecated": false, "name": "TSPGridline"}, {"deprecationReason": null, "description": "TSPHOS247", "isDeprecated": false, "name": "TSPHOS247"}, {"deprecationReason": null, "description": "TSPHighPointGPS", "isDeprecated": false, "name": "TSPHighPointGPS"}, {"deprecationReason": null, "description": "TSPHorizonPathELD", "isDeprecated": false, "name": "TSPHorizonPathELD"}, {"deprecationReason": null, "description": "TSPHutchSystems", "isDeprecated": false, "name": "TSPHutchSystems"}, {"deprecationReason": null, "description": "TSPIDELD", "isDeprecated": false, "name": "TSPIDELD"}, {"deprecationReason": null, "description": "TSPISAACInstruments", "isDeprecated": false, "name": "TSPISAACInstruments"}, {"deprecationReason": null, "description": "TSPInTouchGPS", "isDeprecated": false, "name": "TSPInTouchGPS"}, {"deprecationReason": null, "description": "TSPInsightMobileData", "isDeprecated": false, "name": "TSPInsightMobileData"}, {"deprecationReason": null, "description": "TSPIntellishift", "isDeprecated": false, "name": "TSPIntellishift"}, {"deprecationReason": null, "description": "TSPIntouchELD", "isDeprecated": false, "name": "TSPIntouchELD"}, {"deprecationReason": null, "description": "TSPIoTab", "isDeprecated": false, "name": "TSPIoTab"}, {"deprecationReason": null, "description": "TSPJJKeller", "isDeprecated": false, "name": "TSPJJKeller"}, {"deprecationReason": null, "description": "TSPKSKELD", "isDeprecated": false, "name": "TSPKSKELD"}, {"deprecationReason": null, "description": "TSP<PERSON>eep<PERSON><PERSON><PERSON>n", "isDeprecated": false, "name": "TSP<PERSON>eep<PERSON><PERSON><PERSON>n"}, {"deprecationReason": null, "description": "TSPKeepTruckinSG", "isDeprecated": false, "name": "TSPKeepTruckinSG"}, {"deprecationReason": null, "description": "TSPKeepTruckinSafety", "isDeprecated": false, "name": "TSPKeepTruckinSafety"}, {"deprecationReason": null, "description": "TSPKonexial", "isDeprecated": false, "name": "TSPKonexial"}, {"deprecationReason": null, "description": "TSPLBTechnology", "isDeprecated": false, "name": "TSPLBTechnology"}, {"deprecationReason": null, "description": "TSPLEGACYELD", "isDeprecated": false, "name": "TSPLEGACYELD"}, {"deprecationReason": null, "description": "TSPLightAndTravelELD", "isDeprecated": false, "name": "TSPLightAndTravelELD"}, {"deprecationReason": null, "description": "TSPLogPlusELD", "isDeprecated": false, "name": "TSPLogPlusELD"}, {"deprecationReason": null, "description": "TSPLookTruckELD", "isDeprecated": false, "name": "TSPLookTruckELD"}, {"deprecationReason": null, "description": "TSPLynx", "isDeprecated": false, "name": "TSPLynx"}, {"deprecationReason": null, "description": "TSPLytXDriveCam", "isDeprecated": false, "name": "TSPLytXDriveCam"}, {"deprecationReason": null, "description": "TSPMGKELD", "isDeprecated": false, "name": "TSPMGKELD"}, {"deprecationReason": null, "description": "TSPMOONLIGHTELD", "isDeprecated": false, "name": "TSPMOONLIGHTELD"}, {"deprecationReason": null, "description": "TSPMTELD", "isDeprecated": false, "name": "TSPMTELD"}, {"deprecationReason": null, "description": "TSPMasterELD", "isDeprecated": false, "name": "TSPMasterELD"}, {"deprecationReason": null, "description": "TSPMatrack", "isDeprecated": false, "name": "TSPMatrack"}, {"deprecationReason": null, "description": "TSPMaxELD", "isDeprecated": false, "name": "TSPMaxELD"}, {"deprecationReason": null, "description": "TSPMock", "isDeprecated": false, "name": "TSPMock"}, {"deprecationReason": null, "description": "TSPMonarchGPS", "isDeprecated": false, "name": "TSPMonarchGPS"}, {"deprecationReason": null, "description": "TSPMondo", "isDeprecated": false, "name": "TSPMondo"}, {"deprecationReason": null, "description": "TSPMotionELD", "isDeprecated": false, "name": "TSPMotionELD"}, {"deprecationReason": null, "description": "TSPMountainELD", "isDeprecated": false, "name": "TSPMountainELD"}, {"deprecationReason": null, "description": "TSPMy20ELD", "isDeprecated": false, "name": "TSPMy20ELD"}, {"deprecationReason": null, "description": "TSPNetradyneInc", "isDeprecated": false, "name": "TSPNetradyneInc"}, {"deprecationReason": null, "description": "TSPNewELDWorld", "isDeprecated": false, "name": "TSPNewELDWorld"}, {"deprecationReason": null, "description": "TSPNextraq", "isDeprecated": false, "name": "TSPNextraq"}, {"deprecationReason": null, "description": "TSPNexusELD", "isDeprecated": false, "name": "TSPNexusELD"}, {"deprecationReason": null, "description": "TSPNoorELD", "isDeprecated": false, "name": "TSPNoorELD"}, {"deprecationReason": null, "description": "TSPNotConnected", "isDeprecated": false, "name": "TSPNotConnected"}, {"deprecationReason": null, "description": "TSPOaneELD", "isDeprecated": false, "name": "TSPOaneELD"}, {"deprecationReason": null, "description": "TSPOmnitracs", "isDeprecated": false, "name": "TSPOmnitracs"}, {"deprecationReason": null, "description": "TSPOmnitracsES", "isDeprecated": false, "name": "TSPOmnitracsES"}, {"deprecationReason": null, "description": "TSPOmnitracsXRS", "isDeprecated": false, "name": "TSPOmnitracsXRS"}, {"deprecationReason": null, "description": "TSPOnTrakSolutions", "isDeprecated": false, "name": "TSPOnTrakSolutions"}, {"deprecationReason": null, "description": "TSPOnePlusELD", "isDeprecated": false, "name": "TSPOnePlusELD"}, {"deprecationReason": null, "description": "TSPOneStepGPS", "isDeprecated": false, "name": "TSPOneStepGPS"}, {"deprecationReason": null, "description": "TSPOntimeELD", "isDeprecated": false, "name": "TSPOntimeELD"}, {"deprecationReason": null, "description": "TSPOptimaELD", "isDeprecated": false, "name": "TSPOptimaELD"}, {"deprecationReason": null, "description": "TSPOrbcomm", "isDeprecated": false, "name": "TSPOrbcomm"}, {"deprecationReason": null, "description": "TSPOrientELD", "isDeprecated": false, "name": "TSPOrientELD"}, {"deprecationReason": null, "description": "TSPOrion", "isDeprecated": false, "name": "TSPOrion"}, {"deprecationReason": null, "description": "TSPOther", "isDeprecated": false, "name": "TSPOther"}, {"deprecationReason": null, "description": "TSPPeopleNet", "isDeprecated": false, "name": "TSPPeopleNet"}, {"deprecationReason": null, "description": "TSPPhoenixELD", "isDeprecated": false, "name": "TSPPhoenixELD"}, {"deprecationReason": null, "description": "TSPPlatformScience", "isDeprecated": false, "name": "TSPPlatformScience"}, {"deprecationReason": null, "description": "TSPPositrace", "isDeprecated": false, "name": "TSPPositrace"}, {"deprecationReason": null, "description": "TSPPowerELD", "isDeprecated": false, "name": "TSPPowerELD"}, {"deprecationReason": null, "description": "TSPPowerFleet", "isDeprecated": false, "name": "TSPPowerFleet"}, {"deprecationReason": null, "description": "TSPPrePassELD", "isDeprecated": false, "name": "TSPPrePassELD"}, {"deprecationReason": null, "description": "TSPProLogs", "isDeprecated": false, "name": "TSPProLogs"}, {"deprecationReason": null, "description": "TSPProRideELD", "isDeprecated": false, "name": "TSPProRideELD"}, {"deprecationReason": null, "description": "TSPQualityGPS", "isDeprecated": false, "name": "TSPQualityGPS"}, {"deprecationReason": null, "description": "TSPRMJTechnologies", "isDeprecated": false, "name": "TSPRMJTechnologies"}, {"deprecationReason": null, "description": "TSPRadicalELD", "isDeprecated": false, "name": "TSPRadicalELD"}, {"deprecationReason": null, "description": "TSPRandMcNally", "isDeprecated": false, "name": "TSPRandMcNally"}, {"deprecationReason": null, "description": "TSPRealELD", "isDeprecated": false, "name": "TSPRealELD"}, {"deprecationReason": null, "description": "TSPReliableELD", "isDeprecated": false, "name": "TSPReliableELD"}, {"deprecationReason": null, "description": "TSPRenaissanceELD", "isDeprecated": false, "name": "TSPRenaissanceELD"}, {"deprecationReason": null, "description": "TSPRigbot", "isDeprecated": false, "name": "TSPRigbot"}, {"deprecationReason": null, "description": "TSPRightTruckingELD", "isDeprecated": false, "name": "TSPRightTruckingELD"}, {"deprecationReason": null, "description": "TSPRoadReadySolutions", "isDeprecated": false, "name": "TSPRoadReadySolutions"}, {"deprecationReason": null, "description": "TSPRoadStarELD", "isDeprecated": false, "name": "TSPRoadStarELD"}, {"deprecationReason": null, "description": "TSPRouteELD", "isDeprecated": false, "name": "TSPRouteELD"}, {"deprecationReason": null, "description": "TSPRushEnterpises", "isDeprecated": false, "name": "TSPRushEnterpises"}, {"deprecationReason": null, "description": "TSPSFELD", "isDeprecated": false, "name": "TSPSFELD"}, {"deprecationReason": null, "description": "TSPSMARTCHOICELOGSELD", "isDeprecated": false, "name": "TSPSMARTCHOICELOGSELD"}, {"deprecationReason": null, "description": "TSPSRELD", "isDeprecated": false, "name": "TSPSRELD"}, {"deprecationReason": null, "description": "TSPSTATEELOGS", "isDeprecated": false, "name": "TSPSTATEELOGS"}, {"deprecationReason": null, "description": "TSPSafetyComplianceSolutions", "isDeprecated": false, "name": "TSPSafetyComplianceSolutions"}, {"deprecationReason": null, "description": "TSPSafetyVision", "isDeprecated": false, "name": "TSPSafetyVision"}, {"deprecationReason": null, "description": "TSPSamsara", "isDeprecated": false, "name": "TSPSamsara"}, {"deprecationReason": null, "description": "TSPSamsaraSG", "isDeprecated": false, "name": "TSPSamsaraSG"}, {"deprecationReason": null, "description": "TSPSamsaraSafety", "isDeprecated": false, "name": "TSPSamsaraSafety"}, {"deprecationReason": null, "description": "TSPSimpleELOG", "isDeprecated": false, "name": "TSPSimpleELOG"}, {"deprecationReason": null, "description": "TSPSimpleTruckELD", "isDeprecated": false, "name": "TSPSimpleTruckELD"}, {"deprecationReason": null, "description": "TSPSmartDrive", "isDeprecated": false, "name": "TSPSmartDrive"}, {"deprecationReason": null, "description": "TSPSmartWitness", "isDeprecated": false, "name": "TSPSmartWitness"}, {"deprecationReason": null, "description": "TSPSmartelds", "isDeprecated": false, "name": "TSPSmartelds"}, {"deprecationReason": null, "description": "TSPSpeedELD", "isDeprecated": false, "name": "TSPSpeedELD"}, {"deprecationReason": null, "description": "TSPSpireonFleetLocate", "isDeprecated": false, "name": "TSPSpireonFleetLocate"}, {"deprecationReason": null, "description": "TSPStreetEagle", "isDeprecated": false, "name": "TSPStreetEagle"}, {"deprecationReason": null, "description": "TSPSwiftELD", "isDeprecated": false, "name": "TSPSwiftELD"}, {"deprecationReason": null, "description": "TSPTFMELD", "isDeprecated": false, "name": "TSPTFMELD"}, {"deprecationReason": null, "description": "TSPTMELD", "isDeprecated": false, "name": "TSPTMELD"}, {"deprecationReason": null, "description": "TSPTMobile", "isDeprecated": false, "name": "TSPTMobile"}, {"deprecationReason": null, "description": "TSPTRCeLOGS", "isDeprecated": false, "name": "TSPTRCeLOGS"}, {"deprecationReason": null, "description": "TSPTRUSTELD", "isDeprecated": false, "name": "TSPTRUSTELD"}, {"deprecationReason": null, "description": "TSPTTELD", "isDeprecated": false, "name": "TSPTTELD"}, {"deprecationReason": null, "description": "TSPTangerine", "isDeprecated": false, "name": "TSPTangerine"}, {"deprecationReason": null, "description": "TSPTeletracNavman", "isDeprecated": false, "name": "TSPTeletracNavman"}, {"deprecationReason": null, "description": "TSPTrackEnsureInc", "isDeprecated": false, "name": "TSPTrackEnsureInc"}, {"deprecationReason": null, "description": "TSPTrackOnHOS", "isDeprecated": false, "name": "TSPTrackOnHOS"}, {"deprecationReason": null, "description": "TSPTransflo", "isDeprecated": false, "name": "TSPTransflo"}, {"deprecationReason": null, "description": "TSPTraxxisGPS", "isDeprecated": false, "name": "TSPTraxxisGPS"}, {"deprecationReason": null, "description": "TSPTrendyELD", "isDeprecated": false, "name": "TSPTrendyELD"}, {"deprecationReason": null, "description": "TSPTrimble", "isDeprecated": false, "name": "TSPTrimble"}, {"deprecationReason": null, "description": "TSPTruPathSystems", "isDeprecated": false, "name": "TSPTruPathSystems"}, {"deprecationReason": null, "description": "TSPTruckXELD", "isDeprecated": false, "name": "TSPTruckXELD"}, {"deprecationReason": null, "description": "TSPTruckerPathELDPro", "isDeprecated": false, "name": "TSPTruckerPathELDPro"}, {"deprecationReason": null, "description": "TSPTrueRoadELD", "isDeprecated": false, "name": "TSPTrueRoadELD"}, {"deprecationReason": null, "description": "TSPUnityELD", "isDeprecated": false, "name": "TSPUnityELD"}, {"deprecationReason": null, "description": "TSPVLogELD", "isDeprecated": false, "name": "TSPVLogELD"}, {"deprecationReason": null, "description": "TSPVTS", "isDeprecated": false, "name": "TSPVTS"}, {"deprecationReason": null, "description": "TSPVerizonConnect", "isDeprecated": false, "name": "TSPVerizonConnect"}, {"deprecationReason": null, "description": "TSPVerizonConnectFleet", "isDeprecated": false, "name": "TSPVerizonConnectFleet"}, {"deprecationReason": null, "description": "TSPVerizonConnectReveal", "isDeprecated": false, "name": "TSPVerizonConnectReveal"}, {"deprecationReason": null, "description": "TSPVertrax", "isDeprecated": false, "name": "TSPVertrax"}, {"deprecationReason": null, "description": "TSPVistaELD", "isDeprecated": false, "name": "TSPVistaELD"}, {"deprecationReason": null, "description": "TSPVulcansols", "isDeprecated": false, "name": "TSPVulcansols"}, {"deprecationReason": null, "description": "TSPWebfleet", "isDeprecated": false, "name": "TSPWebfleet"}, {"deprecationReason": null, "description": "TSPWorldTruckingELD", "isDeprecated": false, "name": "TSPWorldTruckingELD"}, {"deprecationReason": null, "description": "TSPXELD", "isDeprecated": false, "name": "TSPXELD"}, {"deprecationReason": null, "description": "TSPZELD", "isDeprecated": false, "name": "TSPZELD"}, {"deprecationReason": null, "description": "TSPZenduit", "isDeprecated": false, "name": "TSPZenduit"}, {"deprecationReason": null, "description": "TSPZippyELD", "isDeprecated": false, "name": "TSPZippyELD"}, {"deprecationReason": null, "description": "TSPZonar", "isDeprecated": false, "name": "TSPZonar"}, {"deprecationReason": null, "description": "TSPeasiTrack", "isDeprecated": false, "name": "TSPeasiTrack"}], "fields": [], "inputFields": [], "interfaces": [], "kind": "ENUM", "name": "TSP", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "records", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}}}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "Table", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "handleID", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "mileage", "type": {"kind": "OBJECT", "name": "VehicleMileage", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "riskScores", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "vehicleScoreTrendItem", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "tspID", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "vin", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "TelematicsVehicle", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [], "inputFields": [], "interfaces": [], "kind": "SCALAR", "name": "Time", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "email", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "sms", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "workflow", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "UserWorkflowPreference", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [], "inputFields": [{"defaultValue": null, "description": "", "name": "email", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "sms", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "workflow", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "interfaces": [], "kind": "INPUT_OBJECT", "name": "UserWorkflowPreference_InputObject", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "distanceMiles", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "float64", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "VehicleMileage", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "key<PERSON><PERSON><PERSON><PERSON>", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "riskScore", "type": {"kind": "SCALAR", "name": "float64", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "riskScoreErrorMessages", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "vIN", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "vehicleName", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "vehicleTSPName", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "violationCount", "type": {"kind": "SCALAR", "name": "int", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "violationSeverityTimeWeighted", "type": {"kind": "SCALAR", "name": "float64", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "violationSeverityWeight", "type": {"kind": "SCALAR", "name": "float64", "ofType": null}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "VehicleStat", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "vehicles", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "VehicleStat", "ofType": null}}}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "VehiclesStatsList", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "bASICCategory", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "code", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "description", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "group", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "inspectionDate", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "location", "type": {"kind": "OBJECT", "name": "Location", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "severityWeight", "type": {"kind": "SCALAR", "name": "int", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "vINs", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "ViolationStat", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "violations", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "ViolationStat", "ofType": null}}}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "ViolationsStatsList", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "expiration", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "fleetSafetyReportId", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "sessionId", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "activateUserResponse", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "address", "type": {"kind": "OBJECT", "name": "Address", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "createdAt", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "isTestAgency", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "name", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "updatedAt", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "agency", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "shareID", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "agencyUserInvite", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "discount", "type": {"kind": "SCALAR", "name": "float64", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "scoreDate", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "scores", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "BasicScoreDetail", "ofType": null}}}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "basicScore", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [], "inputFields": [], "interfaces": [], "kind": "SCALAR", "name": "bool", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "carrier", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "Carrier", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "downloadURL", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "errorMessage", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "generatedAt", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "generatedBy", "type": {"kind": "OBJECT", "name": "user", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "generatedByUserId", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "bordereauxReport", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "coachingState", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "forwardVideoURL", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "inwardVideoURL", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "labels", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "time", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "trackedInwardVideoURL", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "vin", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "cameraEvents", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "adjusterEmail", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "adjusterName", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "canSubmitFeedback", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "claimNumber", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "externalId", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "feedbacks", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "hasNotesSinceLastScheduleSummary", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "lineOfBusiness", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "lossDatetime", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "modifiedAt", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "notes", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Note", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "policy", "type": {"kind": "OBJECT", "name": "policy", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "policyNumber", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "reportedAt", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "reportedBy", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "source", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "<PERSON><PERSON><PERSON><PERSON>rov<PERSON>", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "status", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "statusChanges", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "ClaimStatusChange", "ofType": null}}}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "claims", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [], "inputFields": [{"defaultValue": null, "description": "", "name": "agencyID", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "fleetID", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "group", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "RoleGroupEnum", "ofType": null}}}], "interfaces": [], "kind": "INPUT_OBJECT", "name": "createUserRoleArgs_InputObject", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "alcoholControlSub", "type": {"kind": "SCALAR", "name": "bool", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "changeDate", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "countyCode", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "countyCodeState", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "countyName", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "createdAt", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "deletedAt", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "docketNumber", "type": {"kind": "SCALAR", "name": "int64", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "dotNumber", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int64", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "driverOOSTotal", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "driver<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "drugIntrdctnArrests", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "drugIntrdctnSearch", "type": {"kind": "SCALAR", "name": "bool", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "grossCompVehicleWeight", "type": {"kind": "SCALAR", "name": "int", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "hazmatOOSTotal", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "hazmatViolTotal", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int64", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "inspEndTime", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "inspFacility", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "inspInterstate", "type": {"kind": "SCALAR", "name": "bool", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "inspLevelID", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "inspStartTime", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "inspectionDate", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "localEnfJurisdiction", "type": {"kind": "SCALAR", "name": "bool", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "location", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "locationDesc", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "mcmisAddDate", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "oosTotal", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "postAccInd", "type": {"kind": "SCALAR", "name": "bool", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "region", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "reportNumber", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "reportState", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "shipperName", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "shippingPaperNumber", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "sizeWeightEnf", "type": {"kind": "SCALAR", "name": "bool", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "statusCode", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "telematicsAssignments", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "telematicsAssignment", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "trafficEnf", "type": {"kind": "SCALAR", "name": "bool", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "vehicleOOSTotal", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "vehicleViolTotal", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "vehicles", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "datagovVehicle", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "violTotal", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "violations", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "datagovViolation", "ofType": null}}}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "datagovInspection", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "changeDate", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "company", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "createdAt", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "decalIssued", "type": {"kind": "SCALAR", "name": "bool", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "decalNumber", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "deletedAt", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int64", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "inspectionID", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int64", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "licensePlate", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "licenseState", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "make", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "sequenceNumber", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "vehicleTypeID", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "vin", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "datagovVehicle", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "changeDate", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "citationNumber", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "code", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "createdAt", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "defectVerificationID", "type": {"kind": "SCALAR", "name": "int", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "deletedAt", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "description", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "group", "type": {"kind": "OBJECT", "name": "violationGroup", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int64", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "inspViolUnit", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "inspViolationCategoryID", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "inspectionID", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int64", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "oos", "type": {"kind": "SCALAR", "name": "bool", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "partNumber", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "partNumberSection", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "seqNumber", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "severity", "type": {"kind": "SCALAR", "name": "int", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "vehicle", "type": {"kind": "OBJECT", "name": "datagovVehicle", "ofType": null}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "datagovViolation", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "archivedAt", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "attachments", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "DraftFnolAttachment", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "contacts", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "DraftFnolContact", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "createdAt", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "created<PERSON>y", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "dotNumber", "type": {"kind": "SCALAR", "name": "int64", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "fnolId", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "incidentDescription", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "injuriesInvolved", "type": {"kind": "SCALAR", "name": "bool", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "insuredName", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "lossDatetime", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "lossLocation", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "lossState", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "noticeType", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "FnolNoticeType", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "policeAgencyName", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "policeReportNumber", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "policyNumber", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "submittedFrom", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "FnolSource", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "updatedAt", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "vehicles", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "DraftFnolVehicle", "ofType": null}}}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "draftFnols", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "licenseNumber", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "licenseState", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "name", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "driver", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "error", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "handleID", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "safetyReportID", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "finalizeTelematicsResponse", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "category", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "FlagCategory", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "chart", "type": {"kind": "OBJECT", "name": "Chart", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "description", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "severity", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "FlagSeverity", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "title", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "flag", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "createdAt", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "dotNumber", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "name", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "updatedAt", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "fleet", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "DotRating", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "DotRating", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "GeneralTrends", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "MonthlyValues", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "InsuranceSummary", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "InsuranceRecord", "ofType": null}}}}}, {"args": [{"defaultValue": null, "description": "", "name": "countType", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"defaultValue": null, "description": "", "name": "endTime", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}, {"defaultValue": null, "description": "", "name": "startTime", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "LocationStats", "type": {"kind": "OBJECT", "name": "GeoJSON", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "OOSSummary", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "OOSSummary", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "ShipperList", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "ShipperData", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "TspProvider", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "TSP", "ofType": null}}}, {"args": [{"defaultValue": null, "description": "", "name": "endTime", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}, {"defaultValue": null, "description": "", "name": "startTime", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "VehiclesStatsList", "type": {"kind": "OBJECT", "name": "VehiclesStatsList", "ofType": null}}, {"args": [{"defaultValue": null, "description": "", "name": "endTime", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}, {"defaultValue": null, "description": "", "name": "startTime", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "ViolationStats", "type": {"kind": "OBJECT", "name": "ViolationsStatsList", "ofType": null}}, {"args": [{"defaultValue": null, "description": "", "name": "fromTimestamp", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}, {"defaultValue": null, "description": "", "name": "toTimestamp", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "basicScores", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "basicScore", "ofType": null}}}}}, {"args": [{"defaultValue": null, "description": "", "name": "scoreCount", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "basicScoresByCount", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "basicScore", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "createdAt", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "created<PERSON>y", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [{"defaultValue": null, "description": "", "name": "endDate", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}, {"defaultValue": null, "description": "", "name": "startDate", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "datagovInspections", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "datagovInspection", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "dotNumber", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [{"defaultValue": null, "description": "", "name": "after", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "before", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "category", "type": {"kind": "ENUM", "name": "FlagCategory", "ofType": null}}, {"defaultValue": null, "description": "", "name": "filterText", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "filterTextFields", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}}, {"defaultValue": null, "description": "", "name": "filterType", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "first", "type": {"kind": "SCALAR", "name": "int64", "ofType": null}}, {"defaultValue": null, "description": "", "name": "last", "type": {"kind": "SCALAR", "name": "int64", "ofType": null}}, {"defaultValue": null, "description": "", "name": "severity", "type": {"kind": "ENUM", "name": "FlagSeverity", "ofType": null}}, {"defaultValue": null, "description": "", "name": "sortBy", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "sortOrder", "type": {"kind": "ENUM", "name": "SortOrder", "ofType": null}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "flags", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "FlagConnection", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "fleet", "type": {"kind": "OBJECT", "name": "fleet", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "fmcsaAuthStatus", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "Status", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "hasTelematicsConnection", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [{"defaultValue": null, "description": "", "name": "endDate", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}, {"defaultValue": null, "description": "", "name": "minViolations", "type": {"kind": "SCALAR", "name": "int64", "ofType": null}}, {"defaultValue": null, "description": "", "name": "startDate", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "inspections", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "inspection", "ofType": null}}}}}, {"args": [{"defaultValue": null, "description": "", "name": "after", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "before", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "endDate", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}, {"defaultValue": null, "description": "", "name": "filterText", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "filterTextFields", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}}, {"defaultValue": null, "description": "", "name": "filterType", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "first", "type": {"kind": "SCALAR", "name": "int64", "ofType": null}}, {"defaultValue": null, "description": "", "name": "last", "type": {"kind": "SCALAR", "name": "int64", "ofType": null}}, {"defaultValue": null, "description": "", "name": "minViolations", "type": {"kind": "SCALAR", "name": "int64", "ofType": null}}, {"defaultValue": null, "description": "", "name": "sortBy", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "sortOrder", "type": {"kind": "ENUM", "name": "SortOrder", "ofType": null}}, {"defaultValue": null, "description": "", "name": "startDate", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "inspectionsConnection", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "InspectionRecordConnection", "ofType": null}}}, {"args": [{"defaultValue": null, "description": "", "name": "fromTimestamp", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}, {"defaultValue": null, "description": "", "name": "toTimestamp", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "issScores", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "ISSScore", "ofType": null}}}}}, {"args": [{"defaultValue": null, "description": "", "name": "scoreCount", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "issScoresByCount", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "ISSScore", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "name", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [{"defaultValue": null, "description": "", "name": "after", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "before", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "filterText", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "filterTextFields", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}}, {"defaultValue": null, "description": "", "name": "filterType", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "first", "type": {"kind": "SCALAR", "name": "int64", "ofType": null}}, {"defaultValue": null, "description": "", "name": "impact", "type": {"kind": "ENUM", "name": "RecommendationImpact", "ofType": null}}, {"defaultValue": null, "description": "", "name": "last", "type": {"kind": "SCALAR", "name": "int64", "ofType": null}}, {"defaultValue": null, "description": "", "name": "sortBy", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "sortOrder", "type": {"kind": "ENUM", "name": "SortOrder", "ofType": null}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "recommendations", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "RecommendationConnection", "ofType": null}}}, {"args": [{"defaultValue": null, "description": "", "name": "after", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "before", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "filterText", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "filterTextFields", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}}, {"defaultValue": null, "description": "", "name": "filterType", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "first", "type": {"kind": "SCALAR", "name": "int64", "ofType": null}}, {"defaultValue": null, "description": "", "name": "impact", "type": {"kind": "ENUM", "name": "RecommendationImpact", "ofType": null}}, {"defaultValue": null, "description": "", "name": "last", "type": {"kind": "SCALAR", "name": "int64", "ofType": null}}, {"defaultValue": null, "description": "", "name": "sortBy", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"defaultValue": null, "description": "", "name": "sortOrder", "type": {"kind": "ENUM", "name": "SortOrder", "ofType": null}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "recommendations_subset", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "RecommendationConnection", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "starred", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "state", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [{"defaultValue": null, "description": "", "name": "endTime", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}, {"defaultValue": null, "description": "", "name": "startTime", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "telematicsRiskFleetPercentiles", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "ScoreTrendItem", "ofType": null}}}}}, {"args": [{"defaultValue": null, "description": "", "name": "endTime", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}, {"defaultValue": null, "description": "", "name": "startTime", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "telematicsRiskVinPercentiles", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "telematicsRiskVinPercentile", "ofType": null}}}}}, {"args": [{"defaultValue": null, "description": "", "name": "endTime", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}, {"defaultValue": null, "description": "", "name": "startTime", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "telematicsVehicles", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "TelematicsVehicle", "ofType": null}}}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "fleetSafetyReport", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "shareID", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "fleetUserInvite", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [], "inputFields": [], "interfaces": [], "kind": "SCALAR", "name": "float64", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "handleID", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "location", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "state", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "tsp", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "TSP", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "initiateTelematicsResponse", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "combinationVehicleGrossWeight", "type": {"kind": "SCALAR", "name": "int", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "countyCode", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "countyName", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "dotNumber", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int64", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "driver", "type": {"kind": "OBJECT", "name": "driver", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "driverOOSTotal", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "hazmatOOSTotal", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "hazmatPlacardReq", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "hazmatViolationsSent", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "inspectionDate", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "inspectionID", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int64", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "inspectionLevel", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "InspectionLevel", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "location", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "publicVINs", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "publishedDate", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "region", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "reportNumber", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "reportState", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "rowID", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "shipperName", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "telematicsAssignments", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "telematicsAssignment", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "timeWeight", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "totalBASICViols", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "totalOOSViolations", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "vehicleOOSTotal", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "vehicles", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "vehicle", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "violations", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "violation", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "wasLocalEnforcement", "type": {"kind": "SCALAR", "name": "bool", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "wasPostAccident", "type": {"kind": "SCALAR", "name": "bool", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "wasSizeWeightEnforcement", "type": {"kind": "SCALAR", "name": "bool", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "wasTrafficEnforcement", "type": {"kind": "SCALAR", "name": "bool", "ofType": null}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "inspection", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [], "inputFields": [], "interfaces": [], "kind": "SCALAR", "name": "int", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [], "inputFields": [], "interfaces": [], "kind": "SCALAR", "name": "int64", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "coverages", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "PolicyCoverageEnums", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "documentID", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "drivers", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "PolicyDriver", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "endDate", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "endorsements", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Endorsement", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "insuredDOTNumber", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "insuredName", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "isTest", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "policyNumber", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "programType", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "ProgramType", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "signedLink", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "ExpirableLink", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "startDate", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "state", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "PolicyState", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "subCoverages", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "CoverageWithSymbols", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "underwriter", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "BasicUser", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "vehicles", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "PolicyVehicle", "ofType": null}}}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "policy", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "engine", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "impact", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "RecommendationImpact", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "sections", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "RecommendationSection", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "title", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "recommendation", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "agency", "type": {"kind": "OBJECT", "name": "agency", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "agencyID", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "createdAt", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "domain", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "fleetID", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "group", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "RoleGroupEnum", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "updatedAt", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "userID", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "role", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [], "inputFields": [], "interfaces": [], "kind": "SCALAR", "name": "string", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "assignedDurationMs", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int64", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "driver", "type": {"kind": "OBJECT", "name": "telematicsDriver", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "vehicle", "type": {"kind": "OBJECT", "name": "TelematicsVehicle", "ofType": null}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "telematicsAssignment", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "licenseNumber", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "licenseState", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "name", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "telematicsDriver", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "scores", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "vehicleScoreTrendItem", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "vin", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "telematicsRiskVinPercentile", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "agencies", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "agency", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "createdAt", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "deletedAt", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "email", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "firstName", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "id", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "lastLoginAt", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "lastName", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "phoneNumber", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "profilePicture", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "roles", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "role", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "safetyReports", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "fleetSafetyReport", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "title", "type": {"kind": "SCALAR", "name": "string", "ofType": null}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "updatedAt", "type": {"kind": "SCALAR", "name": "Time", "ofType": null}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "user", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "company", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "make", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "model", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "vin", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "vehicle", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "fleetID", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "handleID", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "score", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "float64", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "telematicsAssignments", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "telematicsAssignment", "ofType": null}}}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "timestamp", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "vin", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "vehicleScoreTrendItem", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "category", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "Category", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "code", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "description", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "group", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "violationGroup", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "humanReadableCode", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "isDSMS", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "oosIndicator", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "bool", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "oosWeight", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "publishedDate", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Time", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "rowID", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "severityWeight", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "timeWeight", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "totalSeverityWeight", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "violationID", "type": {"kind": "SCALAR", "name": "int", "ofType": null}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "violation", "possibleTypes": []}, {"description": "", "enumValues": [], "fields": [{"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "category", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "Category", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "humanReadable", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "name", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "string", "ofType": null}}}, {"args": [], "deprecationReason": null, "description": "", "isDeprecated": false, "name": "severity", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "int", "ofType": null}}}], "inputFields": [], "interfaces": [], "kind": "OBJECT", "name": "violationGroup", "possibleTypes": []}]}}