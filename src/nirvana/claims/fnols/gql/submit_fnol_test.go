package gql_test

import (
	"bytes"
	"context"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"
	"github.com/google/uuid"

	claim_enums "nirvanatech.com/nirvana/claims/enums"
	fnols_db "nirvanatech.com/nirvana/claims/fnols/db"
	"nirvanatech.com/nirvana/claims/fnols/enums"
	"nirvanatech.com/nirvana/claims/fnols/gql"
	"nirvanatech.com/nirvana/common-go/file_upload_lib"
	file_enums "nirvanatech.com/nirvana/common-go/file_upload_lib/enums"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/common-go/uuid_utils"
	"nirvanatech.com/nirvana/infra/authz"
	policy_constants "nirvanatech.com/nirvana/policy/constants"
	policy_enums "nirvanatech.com/nirvana/policy/enums"
)

func (f *fnolsResolverTestSuite) TestSubmitFnol() {
	ctx := context.Background()
	s3Keygen := new(file_upload_lib.DefaultS3Keygen)

	dotNumber, err := strconv.ParseInt(f.fleetFixture.Fleet.DotNumber, 10, 64)
	f.Require().NoError(err)

	policyNumber := "NISTK1222221-25"

	startDate := time_utils.NewDate(2025, 1, 1).ToTime()
	endDate := startDate.AddDate(1, 0, 0)

	_ = f.seedPolicy(
		ctx,
		f.agencyFixture.Agency.ID,
		dotNumber,
		policy_enums.PolicyStateActive,
		"NISTK",
		"1222221",
		startDate,
		endDate,
	)

	now := time.Now().UTC().Round(time.Millisecond)

	// This fn simulates the pre-condition that assumes that the FE has already uploaded the attachment to S3.
	newUploadedAttachmentKey := func(accountId uuid.UUID, handleId uuid.UUID) string {
		key := s3Keygen.Path(accountId, handleId, "attachment", handleId.String())
		_, err = f.s3Client.UploadWithContext(ctx, &s3manager.UploadInput{
			Body:   bytes.NewReader([]byte("contents")),
			Bucket: aws.String(file_upload_lib.GetBucketName(f.cfg, file_enums.FileDestinationGroupClaims)),
			Key:    aws.String(key),
		})
		f.Require().NoError(err)
		return key
	}

	draftFnol, err := fnols_db.NewClaimFnolBuilder(claim_enums.ClaimsProviderNars).
		WithDefaultMockData().
		WithPolicyNumber(&policyNumber).
		WithNoticeType(pointer_utils.ToPointer(enums.FnolNoticeTypeClaim)).
		WithLossState(pointer_utils.String("CA")).
		WithSubmittedFrom(enums.FnolSourceFinolaEmail).
		WithStatus(enums.FnolStatusDraft).
		WithCreatedBy(uuid.New()).
		Build()
	f.Require().NoError(err)
	f.Require().NoError(f.fnolWrapper.UpsertFnol(ctx, *draftFnol))

	superuser := f.usersFixture.Superuser.AuthzUser()

	validMinimalRequest := gql.CreateFNOLArgs{
		PolicyNumber: policyNumber,
		NoticeType:   enums.FnolNoticeTypeClaim,
		LossState:    "CA",
		LossDate:     now,
		Reporter: gql.Reporter{
			FirstName: pointer_utils.String("John"),
			LastName:  pointer_utils.String("Doe"),
			Email:     pointer_utils.String("<EMAIL>"),
		},
		Provider: pointer_utils.ToPointer(claim_enums.ClaimsProviderSnapsheet),
	}

	wantFnolForMinimalRequest := fnols_db.ClaimFnol{
		PolicyNumber:        &policyNumber,
		InsuredName:         pointer_utils.String("Nirvana Trucking"), // Falls back to policy's insured name
		IncidentDescription: pointer_utils.String("N/A"),
		NoticeType:          pointer_utils.ToPointer(enums.FnolNoticeTypeClaim),
		LossLocation:        pointer_utils.String("N/A"),
		LossState:           pointer_utils.String("CA"),
		LossDatetime:        pointer_utils.ToPointer(now),
		InjuriesInvolved:    pointer_utils.Bool(false),
		Contacts: []fnols_db.FnolContact{
			{
				FirstName: "John",
				LastName:  "Doe",
				Phone:     "N/A",
				Email:     pointer_utils.String("<EMAIL>"),
			},
		},
		Status:        enums.FnolStatusSent,
		CreatedBy:     f.usersFixture.Superuser.ID,
		SubmittedFrom: enums.FnolSourceUnknown,
		Source:        claim_enums.ClaimsProviderSnapsheet,
	}

	expectedUrl := "https://test.snapsheetvice.com/claims/112233"

	testCases := []struct {
		name            string
		user            authz.User
		argsFactory     func() gql.CreateFNOLArgs
		wantFnolFactory func() fnols_db.ClaimFnol
		wantUrl         *string
		wantExternalId  string
		wantErr         bool
	}{
		{
			name: "With minimal required fields",
			user: superuser,
			argsFactory: func() gql.CreateFNOLArgs {
				return validMinimalRequest
			},
			wantFnolFactory: func() fnols_db.ClaimFnol {
				return wantFnolForMinimalRequest
			},
			wantExternalId: "112233", // Stubbed value for Snapsheet
			wantUrl:        &expectedUrl,
			wantErr:        false,
		},
		{
			name: "With minimal required fields, but user is unauthorized",
			user: f.usersFixture.BillingAdmin.AuthzUser(),
			argsFactory: func() gql.CreateFNOLArgs {
				return validMinimalRequest
			},
			wantExternalId: "",
			wantErr:        true,
		},
		{
			name: "With all fields filled (non-draft)",
			user: superuser,
			argsFactory: func() gql.CreateFNOLArgs {
				req := validMinimalRequest
				req.InsuredName = pointer_utils.String("Narvini")
				req.Description = pointer_utils.String("Accident")
				req.LossLocation = pointer_utils.String("123 Main St")
				req.InjuriesInvolved = pointer_utils.Bool(true)
				req.Police = &gql.Police{
					AgencyName:   pointer_utils.String("LAPD"),
					ReportNumber: pointer_utils.String("RPT-2025-001"),
				}
				req.InsuredVehicles = pointer_utils.ToPointer([]gql.ClaimVehicle{
					{
						VIN: pointer_utils.String("1HGCM82633A123456"),
					},
				})
				req.OtherVehicles = pointer_utils.ToPointer([]gql.ClaimVehicle{
					{
						RegistrationNumber: pointer_utils.String("XYZ789"),
					},
				})
				req.AttachmentKeys = pointer_utils.ToPointer([]string{
					newUploadedAttachmentKey(superuser.ID, uuid_utils.StableUUID("attachment")),
				})
				req.LineOfBusiness = pointer_utils.ToPointer(claim_enums.LineOfBusinessAutoLiability)
				req.Source = pointer_utils.ToPointer(enums.FnolSourceFinolaEmail)
				return req
			},
			wantFnolFactory: func() fnols_db.ClaimFnol {
				want := wantFnolForMinimalRequest
				want.InsuredName = pointer_utils.String("Narvini")
				want.IncidentDescription = pointer_utils.String("Accident")
				want.LossLocation = pointer_utils.String("123 Main St")
				want.InjuriesInvolved = pointer_utils.Bool(true)
				want.PoliceAgencyName = pointer_utils.String("LAPD")
				want.PoliceReportNumber = pointer_utils.String("RPT-2025-001")
				want.Vehicles = []fnols_db.FnolVehicle{
					{
						VIN:              pointer_utils.String("1HGCM82633A123456"),
						IsInsuredVehicle: true,
					},
					{
						RegistrationNumber: pointer_utils.String("XYZ789"),
						IsInsuredVehicle:   false,
					},
				}
				want.Attachments = []fnols_db.FnolAttachment{
					{
						HandleId: uuid_utils.StableUUID("attachment"),
					},
				}
				want.SubmittedFrom = enums.FnolSourceFinolaEmail
				want.Source = claim_enums.ClaimsProviderSnapsheet
				return want
			},
			wantExternalId: "112233", // Stubbed value for Snapsheet
			wantUrl:        &expectedUrl,
			wantErr:        false,
		},
		{
			name: "With all fields filled (draft)",
			user: superuser,
			argsFactory: func() gql.CreateFNOLArgs {
				req := validMinimalRequest
				req.DraftFnolId = &draftFnol.Id
				req.Description = pointer_utils.String("Accident")
				req.LossLocation = pointer_utils.String("123 Main St")
				req.InjuriesInvolved = pointer_utils.Bool(true)
				req.Police = &gql.Police{
					AgencyName:   pointer_utils.String("LAPD"),
					ReportNumber: pointer_utils.String("RPT-2025-001"),
				}
				req.InsuredVehicles = pointer_utils.ToPointer([]gql.ClaimVehicle{
					{
						VIN: pointer_utils.String("1HGCM82633A123456"),
					},
				})
				req.OtherVehicles = pointer_utils.ToPointer([]gql.ClaimVehicle{
					{
						RegistrationNumber: pointer_utils.String("XYZ789"),
					},
				})
				req.AttachmentKeys = pointer_utils.ToPointer([]string{
					newUploadedAttachmentKey(superuser.ID, uuid_utils.StableUUID("attachment")),
				})
				req.LineOfBusiness = pointer_utils.ToPointer(claim_enums.LineOfBusinessAutoLiability)
				req.Source = pointer_utils.ToPointer(enums.FnolSourceFinolaEmail)
				return req
			},
			wantFnolFactory: func() fnols_db.ClaimFnol {
				want := wantFnolForMinimalRequest
				want.IncidentDescription = pointer_utils.String("Accident")
				want.LossLocation = pointer_utils.String("123 Main St")
				want.InjuriesInvolved = pointer_utils.Bool(true)
				want.PoliceAgencyName = pointer_utils.String("LAPD")
				want.PoliceReportNumber = pointer_utils.String("RPT-2025-001")
				want.Vehicles = []fnols_db.FnolVehicle{
					{
						VIN:              pointer_utils.String("1HGCM82633A123456"),
						IsInsuredVehicle: true,
					},
					{
						RegistrationNumber: pointer_utils.String("XYZ789"),
						IsInsuredVehicle:   false,
					},
				}
				want.Attachments = []fnols_db.FnolAttachment{
					{
						HandleId: uuid_utils.StableUUID("attachment"),
					},
				}
				want.Source = claim_enums.ClaimsProviderSnapsheet // Updated from args.Provider

				// The following fields should not be updated (retain same original values)
				want.Id = draftFnol.Id
				want.SubmittedFrom = draftFnol.SubmittedFrom
				want.CreatedBy = draftFnol.CreatedBy
				return want
			},
			wantExternalId: "112233", // Stubbed value for Snapsheet
			wantUrl:        &expectedUrl,
			wantErr:        false,
		},
		{
			name: "Missing policy number",
			user: superuser,
			argsFactory: func() gql.CreateFNOLArgs {
				req := validMinimalRequest
				req.PolicyNumber = ""
				return req
			},
			wantExternalId: "",
			wantErr:        true,
		},
		{
			name: "Lowercase policy number",
			user: superuser,
			argsFactory: func() gql.CreateFNOLArgs {
				req := validMinimalRequest
				req.PolicyNumber = strings.ToLower(policyNumber)
				return req
			},
			wantFnolFactory: func() fnols_db.ClaimFnol {
				return wantFnolForMinimalRequest
			},
			wantExternalId: "112233", // Stubbed value for Snapsheet
			wantUrl:        &expectedUrl,
			wantErr:        false,
		},
		{
			name: "Business Auto policy number",
			user: superuser,
			argsFactory: func() gql.CreateFNOLArgs {
				req := validMinimalRequest
				req.PolicyNumber = policy_constants.NirvanaBusinessAutoALPrefix + "1000001-25"
				return req
			},
			wantExternalId: "",
			wantErr:        true,
		},
	}
	for _, tc := range testCases {
		f.Run(tc.name, func() {
			request := tc.argsFactory()
			result, err := f.submitFnol(ctx, tc.user, request)
			if tc.wantErr {
				f.Require().Error(err)
				return
			}

			f.Require().NoError(err)
			f.Require().NotNil(result)

			f.Equal(tc.wantExternalId, result.SubmitFnol.ExternalId)
			assertPointerEqual(f, tc.wantUrl, result.SubmitFnol.Url)

			gotFnol, err := f.fnolWrapper.GetFnol(ctx, result.SubmitFnol.Id)
			f.Require().NoError(err)
			f.Require().NotNil(gotFnol)

			want := tc.wantFnolFactory()
			if request.DraftFnolId != nil {
				f.Equal(request.DraftFnolId.String(), gotFnol.Id.String())
			}
			assertPointerEqual(f, want.PolicyNumber, gotFnol.PolicyNumber)
			assertPointerEqual(f, want.InsuredName, gotFnol.InsuredName)
			assertPointerEqual(f, want.IncidentDescription, gotFnol.IncidentDescription)
			assertPointerEqual(f, want.NoticeType, gotFnol.NoticeType)
			assertPointerEqual(f, want.LossLocation, gotFnol.LossLocation)
			assertPointerEqual(f, want.LossState, gotFnol.LossState)
			assertPointerEqual(f, want.LossDatetime, gotFnol.LossDatetime)
			assertPointerEqual(f, want.InjuriesInvolved, gotFnol.InjuriesInvolved)
			assertPointerEqual(f, want.PoliceAgencyName, gotFnol.PoliceAgencyName)
			assertPointerEqual(f, want.PoliceReportNumber, gotFnol.PoliceReportNumber)
			f.Equal(want.Status.String(), gotFnol.Status.String())
			f.Equal(want.CreatedBy, gotFnol.CreatedBy)
			f.Equal(want.Source.String(), gotFnol.Source.String())

			f.Require().Len(gotFnol.Contacts, len(want.Contacts))
			sortContactsFn := func(contacts []fnols_db.FnolContact) {
				sort.Slice(contacts, func(i, j int) bool {
					return contacts[i].FirstName < contacts[j].FirstName
				})
			}
			sortContactsFn(want.Contacts)
			sortContactsFn(gotFnol.Contacts)
			for i, wantContact := range want.Contacts {
				gotContact := gotFnol.Contacts[i]
				f.Equal(wantContact.FirstName, gotContact.FirstName)
				f.Equal(wantContact.LastName, gotContact.LastName)
				f.Equal(wantContact.Phone, gotContact.Phone)
				f.Equal(wantContact.Email, gotContact.Email)
			}

			f.Require().Len(gotFnol.Vehicles, len(want.Vehicles))
			sortVehiclesFn := func(vehicles []fnols_db.FnolVehicle) {
				sort.Slice(vehicles, func(i, j int) bool {
					iRegistrationNumber := pointer_utils.StringValOr(vehicles[i].RegistrationNumber, "")
					iVal := pointer_utils.StringValOr(vehicles[i].VIN, iRegistrationNumber)

					jRegistrationNumber := pointer_utils.StringValOr(vehicles[j].RegistrationNumber, "")
					jVal := pointer_utils.StringValOr(vehicles[j].VIN, jRegistrationNumber)

					return iVal < jVal
				})
			}
			sortVehiclesFn(want.Vehicles)
			sortVehiclesFn(gotFnol.Vehicles)
			for i, wantVehicle := range want.Vehicles {
				gotVehicle := gotFnol.Vehicles[i]
				f.Equal(wantVehicle.RegistrationNumber, gotVehicle.RegistrationNumber)
				f.Equal(wantVehicle.VIN, gotVehicle.VIN)
			}

			gotHandleIds := slice_utils.Map(gotFnol.Attachments, func(a fnols_db.FnolAttachment) uuid.UUID {
				return a.HandleId
			})
			wantHandleIds := slice_utils.Map(want.Attachments, func(a fnols_db.FnolAttachment) uuid.UUID {
				return a.HandleId
			})
			f.ElementsMatch(wantHandleIds, gotHandleIds)
		})
	}
}

type submitFnolOutput struct {
	SubmitFnol gql.SubmitFnolResponse
}

func (f *fnolsResolverTestSuite) submitFnol(ctx context.Context, user authz.User, args gql.CreateFNOLArgs) (*submitFnolOutput, error) {
	mutation := `mutation(
		$attachmentKeys: [string!],
		$policyNumber: string!,
		$insuredName: string,
		$description: string,
		$noticeType: FnolNoticeType!,
		$lossLocation: string,
		$lossState: string!,
		$lossDate: Time!,
		$injuriesInvolved: bool,
		$police: Police_InputObject,
		$reporter: Reporter_InputObject!,
		$insuredVehicles: [ClaimVehicle_InputObject!],
		$otherVehicles: [ClaimVehicle_InputObject!],
		$insuredName: string,
		$lineOfBusiness: LineOfBusiness,
		$draftFnolId: string,
		$source: FnolSource,
		$provider: ClaimsProvider
	) {
		submitFnol(
			attachmentKeys:$attachmentKeys,
			policyNumber:$policyNumber,
			insuredName:$insuredName,
			description:$description,
			noticeType:$noticeType,
			lossLocation:$lossLocation,
			lossState:$lossState,
			lossDate:$lossDate,
			injuriesInvolved:$injuriesInvolved,
			police:$police,
			reporter:$reporter,
			insuredVehicles:$insuredVehicles,
			otherVehicles:$otherVehicles,
			lineOfBusiness:$lineOfBusiness,
			draftFnolId:$draftFnolId,
			source:$source,
			provider:$provider
		) {
			id
			externalId
			url
		}
	}`

	mutationVars := map[string]any{
		"attachmentKeys":   args.AttachmentKeys,
		"policyNumber":     args.PolicyNumber,
		"insuredName":      args.InsuredName,
		"description":      args.Description,
		"noticeType":       args.NoticeType,
		"lossLocation":     args.LossLocation,
		"lossState":        args.LossState,
		"lossDate":         args.LossDate,
		"injuriesInvolved": args.InjuriesInvolved,
		"police":           args.Police,
		"reporter":         args.Reporter,
		"insuredVehicles":  args.InsuredVehicles,
		"otherVehicles":    args.OtherVehicles,
		"lineOfBusiness":   args.LineOfBusiness,
		"draftFnolId":      args.DraftFnolId,
		"source":           args.Source,
		"provider":         args.Provider,
	}

	var result submitFnolOutput
	if err := f.gqlClient.Mutation(ctx, user, mutation, mutationVars).ResultAs(&result); err != nil {
		return nil, err
	}
	return &result, nil
}
