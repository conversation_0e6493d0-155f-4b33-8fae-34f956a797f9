package gql

import (
	"context"
	"fmt"
	"strings"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"nirvanatech.com/nirvana/claims/client"
	claims_enums "nirvanatech.com/nirvana/claims/enums"
	"nirvanatech.com/nirvana/claims/fnols/db"
	fnol_enums "nirvanatech.com/nirvana/claims/fnols/enums"
	"nirvanatech.com/nirvana/claims/metrics"
	file_enums "nirvanatech.com/nirvana/common-go/file_upload_lib/enums"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	"nirvanatech.com/nirvana/infra/authz"
)

type SubmitFnolResponse struct {
	Id         uuid.UUID
	ExternalId string
	Url        *string
}

func (r *Resolver) submitFnol(
	ctx context.Context,
	args CreateFNOLArgs,
) (response *SubmitFnolResponse, err error) {
	args.PolicyNumber = strings.ToUpper(strings.Trim(args.PolicyNumber, " "))

	ctx = log.ContextWithFields(
		ctx,
		log.String("operation", "submitFnol"),
		log.String("policyNumber", args.PolicyNumber),
		log.Stringer("user", authz.UserFromContext(ctx).ID),
	)

	defer func() {
		r.deps.MetricsClient.IncOperation(ctx, metrics.OperationCreateFNOLRequest, err)
	}()

	policyAllowed, err := client.IsPolicyAllowed(args.PolicyNumber)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to check if policy %s is allowed", args.PolicyNumber)
	}
	if !policyAllowed {
		return nil, errors.Newf("cannot create FNOL for policy %s", args.PolicyNumber)
	}

	p, err := r.deps.PolicyClient.GetLatestPolicy(ctx, args.PolicyNumber, false)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to fetch policy %s", args.PolicyNumber)
	}

	if err := r.deps.AuthzClient.CanCreateFnols(ctx, p.PolicyNumber.String()); err != nil {
		return nil, err
	}

	var attachmentKeys []string
	if args.AttachmentKeys != nil {
		attachmentKeys = *args.AttachmentKeys
	}

	fnol, err := r.argsToFnol(ctx, args, *p, attachmentKeys)
	if err != nil {
		return nil, errors.Wrap(err, "failed to create new FNOL from args")
	}

	// We commit files before persisting FNOL to avoid the risk of first persisting the FNOL and
	// then files failing to be committed, which would result in a "sendable" FNOL with attachment
	// records pointing to file handle IDs that do not exist.
	if err := r.commitAttachmentKeys(ctx, attachmentKeys); err != nil {
		return nil, errors.Wrap(err, "failed to commit attachment keys")
	}

	if fnol.Status == fnol_enums.FnolStatusDraft {
		err = fnol.ConvertFromDraft(func() (string, error) {
			return r.deps.FnolClient.GenerateClaimNumber(ctx, *fnol.PolicyNumber, fnol.Source)
		})
		if err != nil {
			return nil, errors.Wrap(err, "failed to convert FNOL from draft")
		}
	}

	if _, err := r.deps.FnolClient.UpsertFnol(ctx, fnol, attachmentKeys); err != nil {
		return nil, errors.Wrap(err, "failed to upsert FNOL")
	}
	externalId, err := r.deps.FnolClient.SendToProvider(ctx, fnol.Id)
	if err != nil {
		return nil, errors.Wrap(err, "failed to send FNOL to provider")
	}
	return r.createSubmitFnolResponse(ctx, fnol, externalId)
}

func (r *Resolver) createSubmitFnolResponse(
	ctx context.Context,
	fnol *db.ClaimFnol,
	externalId string,
) (*SubmitFnolResponse, error) {
	var url *string

	if fnol.Source == claims_enums.ClaimsProviderSnapsheet {
		policy, err := r.deps.PolicyClient.GetLatestPolicy(ctx, *fnol.PolicyNumber, false)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to get policy %s for FNOL %s", *fnol.PolicyNumber, fnol.Id)
		}

		snapsheetUrl := r.deps.Cfg.ProductTools.Snapsheet.ProductionHost
		isSandbox, err := r.deps.SnapsheetClient.ShouldSandboxRequest(ctx, *policy)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to determine if request should be sandboxed for FNOL %s", fnol.Id)
		}
		if isSandbox {
			snapsheetUrl = r.deps.Cfg.ProductTools.Snapsheet.SandboxHost
		}
		url = pointer_utils.ToPointer(fmt.Sprintf("%s/claims/%s", snapsheetUrl, externalId))
	}

	return &SubmitFnolResponse{
		Id:         fnol.Id,
		ExternalId: externalId,
		Url:        url,
	}, nil
}

// argsToFnol instantiates a new FNOL from the given args, provided there is no draft FNOL ID.
// If there is a draft FNOL ID, then the draft FNOL is used as a base for the new FNOL.
func (r *Resolver) argsToFnol(
	ctx context.Context,
	args CreateFNOLArgs,
	p policy.Policy,
	attachmentKeys []string,
) (*db.ClaimFnol, error) {
	if args.Provider == nil || *args.Provider != claims_enums.ClaimsProviderSnapsheet {
		return nil, errors.New("only Snapsheet is supported for submitting FNOLs for now")
	}

	createdBy := authz.UserFromContext(ctx).ID

	insuredName := pointer_utils.StringValOr(args.InsuredName, p.InsuredName)
	description := fallbackToString(args.Description)
	lossLocation := fallbackToString(args.LossLocation)
	injuries := pointer_utils.FromPointerOr(args.InjuriesInvolved, false)

	var policeAgencyName *string
	if args.Police != nil && isNonEmptyString(args.Police.AgencyName) {
		policeAgencyName = args.Police.AgencyName
	}

	var policeReportNumber *string
	if args.Police != nil && isNonEmptyString(args.Police.ReportNumber) {
		policeReportNumber = args.Police.ReportNumber
	}

	var fnolBuilder *db.ClaimFnolBuilder
	if args.DraftFnolId != nil {
		draftFnol, err := r.deps.FnolWrapper.GetFnol(ctx, *args.DraftFnolId)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to get draft FNOL %s", args.DraftFnolId)
		}
		draftFnol.Source = *args.Provider
		fnolBuilder = db.NewClaimFnolBuilderFromDraft(draftFnol)
	} else {
		ccn, err := r.deps.FnolClient.GenerateClaimNumber(ctx, args.PolicyNumber, *args.Provider)
		if err != nil {
			return nil, errors.Wrap(err, "failed to generate client claim number")
		}

		fnolBuilder = db.
			NewClaimFnolBuilder(*args.Provider).
			WithClientClaimNumber(pointer_utils.String(ccn)).
			WithSubmittedFrom(pointer_utils.FromPointerOr(args.Source, fnol_enums.FnolSourceUnknown)).
			WithStatus(fnol_enums.FnolStatusSendable).
			WithCreatedBy(createdBy)
	}

	fnol, err := fnolBuilder.
		WithNoticeType(&args.NoticeType).
		WithPolicyNumber(pointer_utils.String(args.PolicyNumber)).
		WithInsuredName(&insuredName).
		WithIncidentDescription(&description).
		WithLossLocation(&lossLocation).
		WithLossState(&args.LossState).
		WithLossDatetime(&args.LossDate).
		WithInjuriesInvolved(&injuries).
		WithPoliceAgencyName(policeAgencyName).
		WithPoliceReportNumber(policeReportNumber).
		Build()
	if err != nil {
		return nil, errors.Wrap(err, "failed to build FNOL")
	}

	reporter, err := db.
		NewFnolContactBuilder().
		WithFnolId(fnol.Id).
		WithContactType(fnol_enums.FnolContactTypeReporter).
		WithFirstName(pointer_utils.StringValOr(args.Reporter.FirstName, "")).
		WithLastName(pointer_utils.StringValOr(args.Reporter.LastName, "")).
		WithPhone(pointer_utils.StringValOr(args.Reporter.Phone, "")).
		WithEmail(args.Reporter.Email).
		Build()
	if err != nil {
		return nil, errors.Wrap(err, "failed to build FNOL contact")
	}
	fnol.Contacts = append(fnol.Contacts, *reporter)

	if args.InsuredVehicles != nil {
		insuredVehicles, err := vehiclesFromArgs(fnol.Id, *args.InsuredVehicles, vehiclesFromArgsOptions{IsInsured: true})
		if err != nil {
			return nil, errors.Wrap(err, "failed to add insured vehicles to FNOL")
		}
		fnol.Vehicles = append(fnol.Vehicles, insuredVehicles...)
	}
	if args.OtherVehicles != nil {
		nonInsuredVehicles, err := vehiclesFromArgs(fnol.Id, *args.OtherVehicles, vehiclesFromArgsOptions{IsInsured: false})
		if err != nil {
			return nil, errors.Wrap(err, "failed to add other vehicles to FNOL")
		}
		fnol.Vehicles = append(fnol.Vehicles, nonInsuredVehicles...)
	}

	accountId := createdBy
	for _, key := range attachmentKeys {
		fileHandleId, err := r.deps.FnolClient.ParseAttachmentKeyToHandleId(accountId, key)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to parse attachment key %s", key)
		}
		attachment, err := db.NewFnolAttachmentBuilder().
			WithFnolId(fnol.Id).
			WithHandleId(fileHandleId).
			Build()
		if err != nil {
			return nil, errors.Wrapf(err, "failed to build FNOL attachment for key %s", key)
		}
		fnol.Attachments = append(fnol.Attachments, *attachment)
	}

	return fnol, nil
}

func (r *Resolver) commitAttachmentKeys(ctx context.Context, attachmentKeys []string) error {
	accountId := authz.UserFromContext(ctx).ID
	for _, key := range attachmentKeys {
		// We check the fileHandleId not just corresponds to the key, but also to the same account.
		// If we do not do this check, then a malicious user could commit files from another FNOL,
		// as actual uploads happen in the FE.
		if _, err := r.deps.FnolClient.ParseAttachmentKeyToHandleId(accountId, key); err != nil {
			return errors.Wrapf(err, "failed to parse attachment key %s", key)
		}
		if err := r.deps.FileUploadManager.CommitKey(ctx, file_enums.FileDestinationGroupClaims, key); err != nil {
			return errors.Wrapf(err, "failed to commit key %s", key)
		}
	}
	return nil
}

type vehiclesFromArgsOptions struct {
	IsInsured bool
}

func vehiclesFromArgs(fnolId uuid.UUID, argsVehicles []ClaimVehicle, opts vehiclesFromArgsOptions) ([]db.FnolVehicle, error) {
	vehicles := make([]db.FnolVehicle, 0, len(argsVehicles))
	for _, v := range argsVehicles {
		vehicle, err := db.NewFnolVehicleBuilder().
			WithFnolId(fnolId).
			WithIsInsuredVehicle(opts.IsInsured).
			WithVIN(v.VIN).
			WithRegistrationNumber(v.RegistrationNumber).
			Build()
		if err != nil {
			return nil, errors.Wrapf(err, "failed to build FNOL vehicle for VIN %s", v.VIN)
		}
		vehicles = append(vehicles, *vehicle)
	}
	return vehicles, nil
}

func fallbackToString(s *string) string {
	if s == nil || *s == "" {
		return "N/A"
	}
	return *s
}

func isNonEmptyString(s *string) bool {
	return s != nil && *s != ""
}
