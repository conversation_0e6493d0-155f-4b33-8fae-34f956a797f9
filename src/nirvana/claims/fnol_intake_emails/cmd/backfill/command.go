package backfill

import (
	"github.com/spf13/cobra"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/common-go/log"
	db_api "nirvanatech.com/nirvana/db-api"
	"nirvanatech.com/nirvana/infra/fx/appfx/cobrafx"
)

type env struct {
	fx.In

	NirvanaRW db_api.NirvanaRW
}

var Command = cobrafx.NewCLI(
	&cobra.Command{
		Use:     "backfill",
		Short:   "Backfill claims.fnol_intake_emails from nars.fnol_intake_emails",
		Example: "bazel run //nirvana/claims/fnol_intake_emails/cmd -- backfill",
	},
	execute,
)

func execute(cmd *cobra.Command, args []string, env env) error {
	ctx := cmd.Context()

	log.Info(ctx, "Backfilling nars.fnol_intake_emails to claims.fnol_intake_emails")

	nirvanaDB := env.NirvanaRW.DB

	_, err := nirvanaDB.ExecContext(ctx, `
		INSERT INTO claims.fnol_intake_emails (
			id,
			message_id,
			fnol_id,
			sent_at,
			created_at
		)
		SELECT
			nf.id,
			nf.message_id,
			nf.fnol_id,
			nf.sent_at,
			nf.created_at
		FROM
			nars.fnol_intake_emails nf
		WHERE NOT EXISTS (
			SELECT 1
			FROM claims.fnol_intake_emails cf
			WHERE cf.message_id = nf.message_id
		)
		AND nf.id NOT IN (
			'96e14200-6c26-4d17-952a-38c4132d22ff',
			'fa02ae97-8321-463b-be58-c0328116ee60',
			'51ee8c82-93cb-4a5d-b08f-929f73143765',
			'3716c4f5-bafa-47d6-867e-94dae50f85e0',
			'2a1399fa-aac3-4a4d-a0e0-ea6ed6b8a42b'
		);
	`)
	if err != nil {
		log.Fatal(ctx, "update claim.fnol_intake_emails table failed...", log.Err(err))
		return err
	}

	log.Info(
		ctx,
		"command finished",
	)

	return nil
}
