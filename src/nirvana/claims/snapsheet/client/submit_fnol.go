package client

import (
	"context"
	"fmt"
	"slices"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	claims_enums "nirvanatech.com/nirvana/claims/enums"
	"nirvanatech.com/nirvana/claims/fnols/db"
	fnol_enums "nirvanatech.com/nirvana/claims/fnols/enums"
	"nirvanatech.com/nirvana/claims/snapsheet/client/policy_type"
	"nirvanatech.com/nirvana/claims/snapsheet/internal"
	oapi "nirvanatech.com/nirvana/claims/snapsheet/internal/openapi/components"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/uuid_utils"
	db_policy "nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	policy_constants "nirvanatech.com/nirvana/policy_common/constants"
)

const placeholderUnknown = "unknown"

// SubmitFnol creates a claim record in Snapsheet for the given FNOL.
func (c *Client) SubmitFnol(ctx context.Context, fnol db.ClaimFnol) (string, error) {
	if err := fnol.CanSendTo(claims_enums.ClaimsProviderSnapsheet); err != nil {
		return "", errors.Wrapf(err, "FNOL %s is not sendable to Snapsheet", fnol.Id)
	}

	p, err := c.deps.PolicyClient.GetLatestPolicy(ctx, *fnol.PolicyNumber, false)
	if err != nil {
		return "", errors.Wrapf(err, "failed to get policy %s", *fnol.PolicyNumber)
	}

	sandboxed, err := c.ShouldSandboxRequest(ctx, *p)
	if err != nil {
		return "", errors.Wrapf(err, "failed to determine if request should be sandboxed for FNOL %s", fnol.Id)
	}
	opts := &internal.ReqOptions{Sandboxed: sandboxed}

	snapsheetId, err := c.createClaim(ctx, fnol, *p, opts)
	if err != nil {
		return "", errors.Wrapf(err, "failed to create claim for FNOL %s", fnol.Id)
	}

	if err := c.setClaimCustomFields(ctx, fnol, snapsheetId, *p, opts); err != nil {
		return "", errors.Wrapf(err, "failed to set custom fields for claim %s", snapsheetId)
	}

	if err := c.uploadAttachments(ctx, fnol, snapsheetId, *p, opts); err != nil {
		return "", errors.Wrapf(err, "failed to upload attachments for FNOL %s", fnol.Id)
	}

	return snapsheetId, nil
}

func (c *Client) ShouldSandboxRequest(ctx context.Context, p db_policy.Policy) (bool, error) {
	agency, err := c.deps.AgencyWrapper.FetchAgency(ctx, p.AgencyID)
	if err != nil {
		return false, errors.Wrapf(err, "failed to get agency %s", p.AgencyID)
	}
	return agency.IsTestAgency, nil
}

func (c *Client) createClaim(
	ctx context.Context,
	fnol db.ClaimFnol,
	p db_policy.Policy,
	opts *internal.ReqOptions,
) (string, error) {
	createClaimRequest, err := newCreateClaimRequest(fnol, p)
	if err != nil {
		return "", errors.Wrapf(err, "failed to create claim request for FNOL %s", fnol.Id)
	}

	createClaimResponse, err := c.deps.SnapsheetOapiClient.CreateClaim(ctx, *createClaimRequest, opts)
	if err != nil {
		return "", errors.Wrapf(err, "failed to create claim for FNOL %s", fnol.Id)
	}
	snapsheetId := fmt.Sprintf("%d", createClaimResponse.ClaimReferenceNumber)
	return snapsheetId, nil
}

// setClaimCustomFields updates an already-created claim with custom fields added by us, and
// therefore not supported by the creation endpoint.
// TODO(IE-990): update this to also send the correct attribute field names for production (not just UAT).
func (c *Client) setClaimCustomFields(
	ctx context.Context,
	fnol db.ClaimFnol,
	snapsheetId string,
	p db_policy.Policy,
	opts *internal.ReqOptions,
) error {
	cfFrontingCarrier := getInsuranceCarrierInitials(p.InsuranceCarrier)
	cfInjuriesInvolved := pointer_utils.BoolValOr(fnol.InjuriesInvolved, false)
	cfInsuredName := fnol.InsuredName
	cfReportOnly := pointer_utils.Bool(fnol.NoticeType != nil && *fnol.NoticeType == fnol_enums.FnolNoticeTypeReport)
	dotNumber := fmt.Sprintf("%d", p.CompanyInfo.DOTNumber)
	return c.deps.SnapsheetOapiClient.UpdateClaim(
		ctx,
		snapsheetId,
		oapi.UpdateClaimRequest{
			Data: oapi.UpdateClaimRequestData{
				Id:   snapsheetId,
				Type: "claim",
				Attributes: oapi.UpdateClaimRequestDataAttributes{
					CfFrontingCarrierUat7b39:   cfFrontingCarrier,
					CfFrontingCarrierProdE809:  cfFrontingCarrier,
					CfInjuriesInvolvedUatB662:  cfInjuriesInvolved,
					CfInjuriesInvolvedProd6a0b: cfInjuriesInvolved,
					CfInsuredDotUatF4a0:        dotNumber,
					CfInsuredNameUatCbbb:       cfInsuredName,
					CfInsuredNameProd2ee5:      cfInsuredName,
					CfReportOnlyUat915a:        cfReportOnly,
					CfReportOnlyProd7595:       cfReportOnly,
					CfInsuredDotProdCa0d:       dotNumber,
				},
			},
		},
		opts,
	)
}

func (c *Client) uploadAttachments(
	ctx context.Context,
	fnol db.ClaimFnol,
	snapsheetId string,
	p db_policy.Policy,
	opts *internal.ReqOptions,
) error {
	programResolver, err := c.getProgramResolver(p.ProgramType)
	if err != nil {
		return errors.Wrapf(err, "failed to get program resolver for policy %s", p.PolicyNumber)
	}
	endorsements, err := programResolver.GetEndorsements(ctx, p)
	if err != nil {
		return errors.Wrapf(err, "failed to get endorsements for policy %s", p.PolicyNumber)
	}

	handleIds := []uuid.UUID{p.DocumentHandleId}
	for _, attachment := range fnol.Attachments {
		handleIds = append(handleIds, attachment.HandleId)
	}
	var endorsementFileHandleIds []uuid.UUID
	for _, end := range endorsements {
		if end.FileHandleId != nil {
			handleIds = append(handleIds, *end.FileHandleId)
			endorsementFileHandleIds = append(endorsementFileHandleIds, *end.FileHandleId)
		}
	}

	var attachments []oapi.ClaimAttachment
	for _, handleId := range handleIds {
		fileName, err := c.deps.FileUploadManager.GetFileNameByHandleID(ctx, handleId)
		if err != nil {
			return errors.Wrapf(err, "failed to get file name for file handle %s", handleId)
		}

		downloadUrl, err := c.deps.FileUploadManager.GenerateTemporaryDownloadLink(
			ctx,
			handleId,
			time.Minute*60,
		)
		if err != nil {
			return errors.Wrapf(err, "failed to generate download link for file handle %s", handleId)
		}

		isEndorsement := slices.Contains(endorsementFileHandleIds, handleId)
		attachmentName := fileName
		if isEndorsement {
			attachmentName = fmt.Sprintf("END-%s", attachmentName)
		}

		attachments = append(attachments, oapi.ClaimAttachment{
			Url:          downloadUrl,
			Name:         attachmentName,
			OriginatedAt: time.Now(),
		})
	}

	for _, attachment := range attachments {
		_, err := c.deps.SnapsheetOapiClient.CreateAttachment(
			ctx,
			oapi.CreateAttachmentRequest{
				Data: &oapi.CreateAttachmentData{
					Attributes: oapi.ClaimAttachmentAttribute{
						Name:         attachment.Name,
						OriginatedAt: &attachment.OriginatedAt,
						Url:          attachment.Url,
					},
					Relationships: oapi.ClaimAttachmentRelationship{
						AttachmentTarget: &oapi.ClaimAttachmentTarget{
							Data: oapi.ClaimAttachmentRelationshipData{
								Id:   snapsheetId,
								Type: oapi.Claim,
							},
						},
					},
					Type: "attachment",
				},
			},
			opts,
		)
		if err != nil {
			return errors.Wrapf(err, "failed to create attachment %s", attachment.Name)
		}
	}
	return nil
}

func newCreateClaimRequest(fnol db.ClaimFnol, p db_policy.Policy) (*oapi.CreateClaimRequest, error) {
	policyType, err := policy_type.New(*p.PolicyNumber)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get policy type from policy number %s", p.PolicyNumber)
	}

	claimType, err := policyType.ToClaimType()
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get claim type from policy type %s", policyType)
	}

	defaultClaimLossType, err := policyType.ToDefaultClaimLossType()
	if err != nil {
		return nil, errors.Wrapf(
			err,
			"failed to get default claim loss type from policy type %s",
			policyType,
		)
	}

	reporter := fnol.GetReporter()
	if reporter == nil {
		return nil, errors.Newf("failed to get reporter for FNOL %s", fnol.Id)
	}

	contactMethods := []oapi.ClaimPartyContactMethod{
		{
			Type:  oapi.ClaimPartyContactMethodTypeEmail,
			Value: *reporter.Email,
		},
	}
	if reporter.Phone != "" && reporter.Phone != "N/A" {
		contactMethods = append(contactMethods, oapi.ClaimPartyContactMethod{
			Type:        oapi.ClaimPartyContactMethodTypePhone,
			Value:       reporter.Phone,
			Country:     pointer_utils.String("us"),
			CountryCode: pointer_utils.String("1"),
		})
	}

	notifierClaimParty := oapi.ClaimParty{
		PartyType:      oapi.PERSON,
		Id:             *reporter.Email,
		FirstName:      reporter.FirstName,
		LastName:       reporter.LastName,
		ContactMethods: &contactMethods,
	}

	var vehicles []oapi.ClaimVehicle
	var exposures []oapi.ClaimExposure
	for _, v := range fnol.Vehicles {
		claimVehicle := newClaimVehicle(v)
		vehicles = append(vehicles, claimVehicle)
		exposures = append(exposures, newClaimExposure(claimVehicle, claimExposureOptions{
			isInsuredVehicle: v.IsInsuredVehicle,
		}))
	}
	// We can only create claims on Snapsheet that have at least one exposure. Because vehicles are
	// not required when submitting Nirvana FNOLs, we add a default one when there are none.
	if len(exposures) == 0 {
		defaultVehicle := newDefaultClaimVehicle(fnol.Id)
		vehicles = append(vehicles, defaultVehicle)
		exposures = append(exposures, newClaimExposure(defaultVehicle, claimExposureOptions{
			isInsuredVehicle: true,
		}))
	}

	hasPoliceAgencyName := isStringFilled(fnol.PoliceAgencyName)
	hasPoliceReportNumber := isStringFilled(fnol.PoliceReportNumber)

	return &oapi.CreateClaimRequest{
		PolicyNumber:       *fnol.PolicyNumber,
		DatetimeOfLoss:     *fnol.LossDatetime,
		LossType:           defaultClaimLossType,
		ClaimType:          claimType,
		ClaimNumber:        *fnol.ClientClaimNumber,
		ClaimParties:       []oapi.ClaimParty{notifierClaimParty},
		AccountCode:        getAccountCodeFromInsuranceCarrier(p.InsuranceCarrier),
		Exposures:          &exposures,
		Vehicles:           &vehicles,
		NotificationMethod: oapi.ONLINE,
		Notifier: &oapi.ClaimNotifier{
			ClaimPartyId: notifierClaimParty.Id,
		},
		ClaimIncidentDetails: &oapi.ClaimIncidentDetails{
			IncidentLocationType:        pointer_utils.String("other"),
			IncidentLocationDescription: fnol.LossLocation,
			IncidentLocationAddress: &oapi.ClaimIncidentLocationAddress{
				Region:  fnol.LossState,
				Country: pointer_utils.String("US"),
			},
			AutoClaimCollisionWithMotorVehicleIncidentDetail: &oapi.ClaimIncidentDetail{
				FactsOfLoss: pointer_utils.StringValOr(fnol.IncidentDescription, "N/A"),
			},
		},
		EmergencyServicesDetail: &oapi.ClaimEmergencyServicesDetail{
			PoliceAttended:     pointer_utils.Bool(hasPoliceAgencyName || hasPoliceReportNumber),
			PoliceStation:      fnol.PoliceAgencyName,
			PoliceReport:       &hasPoliceReportNumber,
			PoliceReportNumber: fnol.PoliceReportNumber,
		},
	}, nil
}

func newClaimVehicle(v db.FnolVehicle) oapi.ClaimVehicle {
	// Snapsheet requires make & model to be set, but the FNOL form only sets VIN & reg number.
	model := placeholderUnknown
	make := placeholderUnknown
	if isStringFilled(v.VIN) {
		make = fmt.Sprintf("%s (VIN)", *v.VIN)
	} else if isStringFilled(v.RegistrationNumber) {
		make = fmt.Sprintf("%s (Reg. Number)", *v.RegistrationNumber)
	}
	return oapi.ClaimVehicle{
		Id:                 v.Id.String(),
		Make:               make,
		Model:              model,
		VinNumber:          v.VIN,
		RegistrationNumber: v.RegistrationNumber,
	}
}

func newDefaultClaimVehicle(fnolId uuid.UUID) oapi.ClaimVehicle {
	return oapi.ClaimVehicle{
		Id:    uuid_utils.StableUUID(fmt.Sprintf("default_vehicle_%s", fnolId)).String(),
		Make:  placeholderUnknown,
		Model: placeholderUnknown,
	}
}

type claimExposureOptions struct {
	isInsuredVehicle bool
}

func newClaimExposure(v oapi.ClaimVehicle, options claimExposureOptions) oapi.ClaimExposure {
	lossParty := oapi.ThirdParty
	if options.isInsuredVehicle {
		lossParty = oapi.Insured
	}

	return oapi.ClaimExposure{
		Id:           fmt.Sprintf("exposure_%s", v.Id),
		ExposureType: oapi.Vehicle,
		LossParty:    lossParty,
		Vehicle: &oapi.ClaimExposureVehicle{
			VehicleId: v.Id,
		},
	}
}

func getAccountCodeFromInsuranceCarrier(carrier policy_constants.InsuranceCarrier) oapi.FinancialAccountCode {
	switch carrier {
	case policy_constants.InsuranceCarrierMSTSpeciality, policy_constants.InsuranceCarrierMSTransverse:
		return oapi.InsuranceCarrierMsTransverse
	case policy_constants.InsuranceCarrierSiriusPoint, policy_constants.InsuranceCarrierSiriusPointSpeciality:
		return oapi.InsuranceCarrierSiriusPoint
	default:
		return oapi.FinancialAccountCode("")
	}
}

func getInsuranceCarrierInitials(carrier policy_constants.InsuranceCarrier) string {
	switch carrier {
	case policy_constants.InsuranceCarrierFalseLake:
		return "FLI"
	case policy_constants.InsuranceCarrierMSTSpeciality, policy_constants.InsuranceCarrierMSTransverse:
		return "MST"
	case policy_constants.InsuranceCarrierSiriusPoint, policy_constants.InsuranceCarrierSiriusPointSpeciality:
		return "SP"
	default:
		return carrier.String()
	}
}

func isStringFilled(s *string) bool {
	return s != nil && *s != ""
}
