package gql_test

import (
	"testing"

	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	brdrxdb "nirvanatech.com/nirvana/claims/reporting/internal/db/postgres/bordereaux_reports"
	"nirvanatech.com/nirvana/claims/reporting/internal/db/snowflake"
	files "nirvanatech.com/nirvana/db-api/db_wrappers/file_upload"
	"nirvanatech.com/nirvana/graphql-server/queryclient"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/users_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

type resolverTestSuiteEnv struct {
	fx.In

	BordereauxDataWrapper *brdrxdb.DataWrapper
	FileUploadDataWrapper files.DataWrapper
	QueryClient           *queryclient.QueryClient
	SnowflakeWrapper      *snowflake.MockDataWrapper

	*users_fixture.UsersFixture
}

type resolverTestSuite struct {
	suite.Suite
	resolverTestSuiteEnv

	fxapp *fxtest.App
}

func TestResolverTestSuite(t *testing.T) {
	suite.Run(t, new(resolverTestSuite))
}

func (s *resolverTestSuite) SetupTest() {
	s.fxapp = testloader.RequireStart(s.T(), &s.resolverTestSuiteEnv)
}

func (s *resolverTestSuite) TearDownTest() {
	s.fxapp.RequireStop()
}
