package gql_test

import (
	"context"
	"sort"
	"time"

	"github.com/google/uuid"

	"nirvanatech.com/nirvana/claims/reporting/gql"
	db "nirvanatech.com/nirvana/claims/reporting/internal/db/postgres/bordereaux_reports"
	files_enums "nirvanatech.com/nirvana/common-go/file_upload_lib/enums"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	files "nirvanatech.com/nirvana/db-api/db_wrappers/file_upload"
	"nirvanatech.com/nirvana/infra/authz"
)

func (s *resolverTestSuite) TestGetBordereauxReports() {
	ctx := context.Background()

	claimsAdmin := s.ClaimsAdmin.AuthzUser()
	report1 := s.seedBordereauxReport(
		ctx,
		claimsAdmin.ID,
		time_utils.NewDate(2025, time.May, 5),
		"keys/report1.zip",
	)
	wantReport1 := gql.BordereauxReport{
		Id:                report1.Id,
		Carrier:           report1.Carrier,
		DownloadURL:       pointer_utils.ToPointer("https://s3.amazonaws.com/nirvana-claims/keys/report1.zip"),
		GeneratedByUserId: report1.GeneratedBy,
		GeneratedBy:       &claimsAdmin,
		GeneratedAt:       report1.GeneratedAt,
	}

	superuser := s.Superuser.AuthzUser()
	report2 := s.seedBordereauxReport(
		ctx,
		superuser.ID,
		time_utils.NewDate(2025, time.August, 15),
		"keys/report2.zip",
	)
	wantReport2 := gql.BordereauxReport{
		Id:                report2.Id,
		Carrier:           report2.Carrier,
		DownloadURL:       pointer_utils.ToPointer("https://s3.amazonaws.com/nirvana-claims/keys/report2.zip"),
		GeneratedByUserId: report2.GeneratedBy,
		GeneratedBy:       &superuser,
		GeneratedAt:       report2.GeneratedAt,
	}

	testCases := []struct {
		name    string
		user    authz.User
		want    []gql.BordereauxReport
		wantErr bool
	}{
		{
			name: "allowed user - returns sorted list according to generatedAt",
			user: s.ClaimsAdmin.AuthzUser(),
			want: []gql.BordereauxReport{
				wantReport2,
				wantReport1,
			},
			wantErr: false,
		},
		{
			name:    "disallowed user - cannot get reports",
			user:    s.FleetAdmin.AuthzUser(),
			wantErr: true,
		},
	}
	for _, tc := range testCases {
		s.Run(tc.name, func() {
			response, err := s.getBordereauxReports(ctx, tc.user)
			if tc.wantErr {
				s.Require().Error(err)
				s.Empty(response)
				return
			}

			s.Require().NoError(err)
			s.Require().NotEmpty(response)
			got := response.GetBordereauxReports
			s.Require().Len(got, len(tc.want))
			sort.Slice(got, func(i, j int) bool {
				return got[i].GeneratedAt.Before(got[j].GeneratedAt)
			})
			sort.Slice(tc.want, func(i, j int) bool {
				return tc.want[i].GeneratedAt.Before(tc.want[j].GeneratedAt)
			})

			for i, report := range got {
				s.Equal(tc.want[i].Id, report.Id)
				s.Equal(tc.want[i].Carrier, report.Carrier)
				s.Equal(tc.want[i].DownloadURL, report.DownloadURL)
				s.Equal(tc.want[i].GeneratedByUserId.String(), report.GeneratedByUserId.String())
				s.Equal(tc.want[i].GeneratedAt.UTC().Round(time.Second), report.GeneratedAt.UTC().Round(time.Second))
				s.Equal(tc.want[i].ErrorMessage, report.ErrorMessage)
				s.Equal(tc.want[i].GeneratedByUserId.String(), report.GeneratedBy.ID.String())
				s.Equal(tc.want[i].GeneratedBy.FirstName, report.GeneratedBy.FirstName)
				s.Equal(tc.want[i].GeneratedBy.LastName, report.GeneratedBy.LastName)
				s.Equal(tc.want[i].GeneratedBy.Email, report.GeneratedBy.Email)
			}
		})
	}
}

func (s *resolverTestSuite) seedBordereauxReport(
	ctx context.Context,
	generatedBy uuid.UUID,
	generatedAt time_utils.Date,
	fileKey string,
) db.BordereauxReport {
	report := db.NewBordereauxReportBuilder().
		WithDefaultMockData().
		WithGeneratedBy(generatedBy).
		WithGeneratedAt(generatedAt.ToTime()).
		Build()
	s.Require().NoError(s.BordereauxDataWrapper.Insert(ctx, report))

	err := s.FileUploadDataWrapper.InsertFile(ctx, files.FileObject{
		Handle:           report.FileHandleId,
		Key:              fileKey,
		DestinationGroup: files_enums.FileDestinationGroupClaims,
		AccountID:        uuid.New(),
	})
	s.Require().NoError(err)

	return report
}

type getBordereauxReportsResult struct {
	GetBordereauxReports []gql.BordereauxReport
}

func (s *resolverTestSuite) getBordereauxReports(
	ctx context.Context,
	user authz.User,
) (*getBordereauxReportsResult, error) {
	query := `query {
		getBordereauxReports {
			id
			carrier
			downloadURL
			generatedByUserId
			generatedBy {
				id
				firstName
				lastName
				email
			}
			generatedAt
			errorMessage
		}
	}`

	var result getBordereauxReportsResult
	if err := s.QueryClient.Query(ctx, user, query, nil).ResultAs(&result); err != nil {
		return nil, err
	}
	return &result, nil
}
