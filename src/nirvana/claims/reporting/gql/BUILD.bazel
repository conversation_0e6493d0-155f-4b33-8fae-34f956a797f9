load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "gql",
    srcs = [
        "generate_bordereaux_report.go",
        "get_bordereaux_reports.go",
        "resolver.go",
    ],
    importpath = "nirvanatech.com/nirvana/claims/reporting/gql",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/claims/authz",
        "//nirvana/claims/metrics",
        "//nirvana/claims/reporting/client",
        "//nirvana/claims/reporting/enums",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/tracing",
        "//nirvana/db-api/db_wrappers/auth",
        "//nirvana/gqlschema/resolver",
        "//nirvana/infra/authz",
        "@com_github_cactus_go_statsd_client_v5//statsd",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_samsarahq_thunder//batch",
        "@com_github_samsarahq_thunder//graphql",
        "@com_github_samsarahq_thunder//graphql/schemabuilder",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "gql_test",
    srcs = [
        "generate_bordereaux_report_test.go",
        "get_bordereaux_reports_test.go",
        "resolver_test.go",
    ],
    deps = [
        ":gql",
        "//nirvana/claims/reporting/enums",
        "//nirvana/claims/reporting/internal/db/postgres/bordereaux_reports",
        "//nirvana/claims/reporting/internal/db/snowflake",
        "//nirvana/common-go/file_upload_lib/enums",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/db-api/db_wrappers/file_upload",
        "//nirvana/graphql-server/queryclient",
        "//nirvana/infra/authz",
        "//nirvana/infra/fx/testfixtures/users_fixture",
        "//nirvana/infra/fx/testloader",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//suite",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
        "@org_uber_go_mock//gomock",
    ],
)
