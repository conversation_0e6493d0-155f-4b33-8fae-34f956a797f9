package snowflake

import (
	"context"

	"nirvanatech.com/nirvana/claims/reporting/enums"
	"nirvanatech.com/nirvana/claims/reporting/internal/db/snowflake/primitives"
)

const bordereauxClaimsTableName = "REPORTING_BORDEREAUX_CLAIMS"

type BordereauxClaimRow struct {
	CLIENT_NAME                string               `csv:"Client Name"`
	CLIENT_LOCATION            string               `csv:"Client Location"`
	ORG_LOCATION_NAME          string               `csv:"Org Location Name"`
	LOB                        string               `csv:"LOB"`
	AGENCY_NAME                *string              `csv:"Retail Broker/Agent"`
	INSURED_NAME               *string              `csv:"Insured"`
	INSURED_STATE              *string              `csv:"Insured State"`
	INSURER_DBA_NAME           string               `csv:"Insurer DBA Name"`
	DRIVER                     *string              `csv:"Driver"`
	VIN                        *string              `csv:"Vehicle VIN"`
	INSURED_VEHICLE_TOTAL_LOSS primitives.YesNoBool `csv:"Insured Vehicle Total Loss (Y/N)"`
	POLICY_NUMBER              string               `csv:"Policy Nbr"`
	RISK_STATE                 string               `csv:"Risk St"`
	EFFECTIVE_DATE             primitives.UsDate    `csv:"Eff Date"`
	EXPIRATION_DATE            primitives.UsDate    `csv:"Exp Date"`
	CANCELLED_AT               primitives.UsDate    `csv:"Policy Cancel Date"`
	CLAIM_NUMBER               string               `csv:"Claim Nbr"`
	CLAIM_TYPE                 string               `csv:"Claim Type"`
	PREV_CLAIM_NUMBER          *string              `csv:"Prev Claim Nbr"` // Left empty on purpose - do not read from Snowflake.
	IS_DENIED                  primitives.YesNoBool `csv:"Is Denied"`
	DENIAL_REASON              *string              `csv:"Denial Reason"`
	IS_CONTESTED               primitives.YesNoBool `csv:"Contested (Y/N)"`
	INCIDENT_ONLY              primitives.YesNoBool `csv:"Incident Only"`
	IN_LITIGATION              primitives.YesNoBool `csv:"Litigation (Y/N)"`
	LOSS_DATE                  primitives.UsDate    `csv:"Acc Date"`
	LOSS_DESCRIPTION           string               `csv:"Acc Desc"`
	LINE_OF_BUSINESS           string               `csv:"Acc Code"`
	LOSS_LOCATION              string               `csv:"Acc Location"`
	LOSS_CITY                  string               `csv:"Acc City"`
	LOSS_COUNTY                string               `csv:"Acc County"`
	LOSS_STATE                 string               `csv:"Acc State"`
	STATUS                     string               `csv:"Claim Status"`
	ADJUSTER_NAME              *string              `csv:"Claim Adjuster"`
	SUPERVISOR_NAME            *string              `csv:"Claim Team"`
	REPORTED_AT                primitives.UsDate    `csv:"Dt Reported"`
	REPORTED_METHOD            string               `csv:"Reported Method"`
	LAG_TIME                   float64              `csv:"Lag Time"`
	NOTIFIED_AT                primitives.UsDate    `csv:"Dt Open"`
	CLOSED_AT                  primitives.UsDate    `csv:"Dt Closed"`
	REOPENED_AT                primitives.UsDate    `csv:"Dt Reopen"`
	DAYS_OPEN                  float64              `csv:"Days Open"`
	CATASTROPHY_NUMBER         string               `csv:"Catastrophe Nbr"`
	EXCEEDS_CLIENT_AUTHORITY   primitives.YesNoBool `csv:"Exceeds Client Authority (Y/N)"`
	LOSS_PAID                  float64              `csv:"Ind Paid"`
	MEDICAL_PAID               float64              `csv:"Med Paid"`
	EXPENSES_PAID              float64              `csv:"Exp Paid"`
	DCC_PAID                   float64              `csv:"DCC Paid"`
	AOE_PAID                   float64              `csv:"AOE Paid"`
	LOSS_RESERVES              float64              `csv:"Ind Res"`
	MEDICAL_RESERVES           float64              `csv:"Med Res"`
	EXPENSES_RESERVES          float64              `csv:"Exp Res"`
	DCC_RESERVES               float64              `csv:"DCC Res"`
	AOE_RESERVES               float64              `csv:"AOE Res"`
	GROSS_INCURRED             float64              `csv:"Gross Incurred"`
	TOTAL_RECOVERIES           float64              `csv:"Total Recoveries"`
	SUBROGATION_RECOVERIES     float64              `csv:"Subro Rec"`
	SALVAGE_RECOVERIES         float64              `csv:"Salvage Rec"`
	DEDUCTIBLE_RECOVERIES      float64              `csv:"Deduct Rec"`
	OTHER_RECOVERIES           float64              `csv:"Other Recoveries"`
	NET_INCURRED               float64              `csv:"Net Incurred"`
}

func (c *DataWrapperImpl) GetBordereauxClaimsRows(
	ctx context.Context,
	carrier enums.Carrier,
) ([]BordereauxClaimRow, error) {
	cols := []string{
		// "CLIENT_NAME", // TODO(IE-1240): uncomment once available in Snowflake.
		// "CLIENT_LOCATION", // TODO(IE-1240): uncomment once available in Snowflake.
		// "ORG_LOCATION_NAME", // TODO(IE-1240): uncomment once available in Snowflake.
		// "LOB", // TODO(IE-1240): uncomment once available in Snowflake.
		"AGENCY_NAME",
		"INSURED_NAME",
		"INSURED_STATE",
		// "INSURER_DBA_NAME", // TODO(IE-1240): uncomment once available in Snowflake.
		"DRIVER",
		"VIN",
		// "INSURED_VEHICLE_TOTAL_LOSS", // TODO(IE-1240): uncomment once available in Snowflake.
		"POLICY_NUMBER",
		// "RISK_STATE", // TODO(IE-1240): uncomment once available in Snowflake.
		"EFFECTIVE_DATE",
		"EXPIRATION_DATE",
		"CANCELLED_AT",
		"CLAIM_NUMBER",
		"CLAIM_TYPE",
		"IS_DENIED",
		"DENIAL_REASON",
		// "IS_CONTESTED", // TODO(IE-1240): uncomment once available in Snowflake.
		"INCIDENT_ONLY",
		"IN_LITIGATION",
		"LOSS_DATE",
		"LOSS_DESCRIPTION",
		"LINE_OF_BUSINESS",
		"LOSS_LOCATION",
		"LOSS_CITY",
		"LOSS_COUNTY",
		"LOSS_STATE",
		"STATUS",
		"ADJUSTER_NAME",
		"SUPERVISOR_NAME",
		"REPORTED_AT",
		"REPORTED_METHOD",
		"LAG_TIME",
		"NOTIFIED_AT",
		"CLOSED_AT",
		"REOPENED_AT",
		"DAYS_OPEN",
		// "CATASTROPHY_NUMBER", // TODO(IE-1240): uncomment once available in Snowflake.
		// "EXCEEDS_CLIENT_AUTHORITY", // TODO(IE-1240): uncomment once available in Snowflake.
		"LOSS_PAID",
		"MEDICAL_PAID",
		"EXPENSES_PAID",
		"DCC_PAID",
		"AOE_PAID",
		"LOSS_RESERVES",
		"MEDICAL_RESERVES",
		"EXPENSES_RESERVES",
		"DCC_RESERVES",
		"AOE_RESERVES",
		"GROSS_INCURRED",
		"TOTAL_RECOVERIES",
		"SUBROGATION_RECOVERIES",
		"SALVAGE_RECOVERIES",
		"DEDUCTIBLE_RECOVERIES",
		"OTHER_RECOVERIES",
		"NET_INCURRED",
	}

	scanTargets := func(r *BordereauxClaimRow) []any {
		return []any{
			// &r.CLIENT_NAME, // TODO(IE-1240): uncomment once available in Snowflake.
			// &r.CLIENT_LOCATION, // TODO(IE-1240): uncomment once available in Snowflake.
			// &r.ORG_LOCATION_NAME, // TODO(IE-1240): uncomment once available in Snowflake.
			// &r.LOB, // TODO(IE-1240): uncomment once available in Snowflake.
			&r.AGENCY_NAME,
			&r.INSURED_NAME,
			&r.INSURED_STATE,
			// &r.INSURER_DBA_NAME, // TODO(IE-1240): uncomment once available in Snowflake.
			&r.DRIVER,
			&r.VIN,
			// &r.INSURED_VEHICLE_TOTAL_LOSS, // TODO(IE-1240): uncomment once available in Snowflake.
			&r.POLICY_NUMBER,
			// &r.RISK_STATE, // TODO(IE-1240): uncomment once available in Snowflake.
			&r.EFFECTIVE_DATE,
			&r.EXPIRATION_DATE,
			&r.CANCELLED_AT,
			&r.CLAIM_NUMBER,
			&r.CLAIM_TYPE,
			&r.IS_DENIED,
			&r.DENIAL_REASON,
			// &r.IS_CONTESTED, // TODO(IE-1240): uncomment once available in Snowflake.
			&r.INCIDENT_ONLY,
			&r.IN_LITIGATION,
			&r.LOSS_DATE,
			&r.LOSS_DESCRIPTION,
			&r.LINE_OF_BUSINESS,
			&r.LOSS_LOCATION,
			&r.LOSS_CITY,
			&r.LOSS_COUNTY,
			&r.LOSS_STATE,
			&r.STATUS,
			&r.ADJUSTER_NAME,
			&r.SUPERVISOR_NAME,
			&r.REPORTED_AT,
			&r.REPORTED_METHOD,
			&r.LAG_TIME,
			&r.NOTIFIED_AT,
			&r.CLOSED_AT,
			&r.REOPENED_AT,
			&r.DAYS_OPEN,
			// &r.CATASTROPHY_NUMBER, // TODO(IE-1240): uncomment once available in Snowflake.
			// &r.EXCEEDS_CLIENT_AUTHORITY, // TODO(IE-1240): uncomment once available in Snowflake.
			&r.LOSS_PAID,
			&r.MEDICAL_PAID,
			&r.EXPENSES_PAID,
			&r.DCC_PAID,
			&r.AOE_PAID,
			&r.LOSS_RESERVES,
			&r.MEDICAL_RESERVES,
			&r.EXPENSES_RESERVES,
			&r.DCC_RESERVES,
			&r.AOE_RESERVES,
			&r.GROSS_INCURRED,
			&r.TOTAL_RECOVERIES,
			&r.SUBROGATION_RECOVERIES,
			&r.SALVAGE_RECOVERIES,
			&r.DEDUCTIBLE_RECOVERIES,
			&r.OTHER_RECOVERIES,
			&r.NET_INCURRED,
		}
	}

	return selectAllFrom(
		ctx,
		c.snapsheetDB,
		bordereauxClaimsTableName,
		cols,
		scanTargets,
		// TODO(IE-1184): add qm.Where("CARRIER = ?", carrier.String())
	)
}
