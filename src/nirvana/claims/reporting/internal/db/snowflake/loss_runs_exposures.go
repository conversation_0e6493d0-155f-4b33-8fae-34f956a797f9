package snowflake

import (
	"context"

	"github.com/volatiletech/sqlboiler/v4/queries/qm"
)

type LossRunsRow struct {
	CLAIM_ID               string
	CLAIM_NUMBER           string
	CLAIMANT_NAME          *string
	DEDUCTIBLE_RECOVERIES  float64
	DRIVER                 *string
	EXPENSES_PAID          float64
	EXPENSES_RESERVES      float64
	GROSS_INCURRED         float64
	LINE_CODE              string
	LOSS_PAID              float64
	LOSS_RESERVES          float64
	MEDICAL_PAID           float64
	MEDICAL_RESERVES       float64
	OTHER_RECOVERIES       float64
	SALVAGE_RECOVERIES     float64
	STATUS                 string
	SUBROGATION_RECOVERIES float64
	TYPE                   string
}

func (c *DataWrapperImpl) GetLossRunsRows(
	ctx context.Context,
	policyNumber string,
) ([]LossRunsRow, error) {
	cols := []string{
		"CLAIM_ID",
		"CLAIM_NUMBER",
		"CLAIMANT_NAME",
		"DEDUCTIBLE_RECOVERIES",
		"DRIVER",
		"EXPENSES_PAID",
		"EXPENSES_RESERVES",
		"GROSS_INCURRED",
		"LINE_CODE",
		"LOSS_PAID",
		"LOSS_RESERVES",
		"MEDICAL_PAID",
		"MEDICAL_RESERVES",
		"OTHER_RECOVERIES",
		"SALVAGE_RECOVERIES",
		"STATUS",
		"SUBROGATION_RECOVERIES",
		"TYPE",
	}

	scanTargets := func(r *LossRunsRow) []any {
		return []any{
			&r.CLAIM_ID,
			&r.CLAIM_NUMBER,
			&r.CLAIMANT_NAME,
			&r.DEDUCTIBLE_RECOVERIES,
			&r.DRIVER,
			&r.EXPENSES_PAID,
			&r.EXPENSES_RESERVES,
			&r.GROSS_INCURRED,
			&r.LINE_CODE,
			&r.LOSS_PAID,
			&r.LOSS_RESERVES,
			&r.MEDICAL_PAID,
			&r.MEDICAL_RESERVES,
			&r.OTHER_RECOVERIES,
			&r.SALVAGE_RECOVERIES,
			&r.STATUS,
			&r.SUBROGATION_RECOVERIES,
			&r.TYPE,
		}
	}

	return selectAllFrom(
		ctx,
		c.snapsheetDB,
		bordereauxExposuresTableName,
		cols,
		scanTargets,
		qm.Where("POLICY_NUMBER = ?", policyNumber),
	)
}
