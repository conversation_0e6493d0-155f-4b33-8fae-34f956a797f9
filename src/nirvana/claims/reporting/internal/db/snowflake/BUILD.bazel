load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "snowflake",
    srcs = [
        "bordereaux_claims.go",
        "bordereaux_exposures.go",
        "connection.go",
        "fx.go",
        "loss_runs_exposures.go",
        "mock.go",
        "wrapper.go",
    ],
    importpath = "nirvanatech.com/nirvana/claims/reporting/internal/db/snowflake",
    visibility = ["//nirvana/claims/reporting:__subpackages__"],
    deps = [
        "//nirvana/claims/reporting/enums",
        "//nirvana/claims/reporting/internal/db/snowflake/primitives",
        "//nirvana/common-go/crypto_utils",
        "//nirvana/common-go/log",
        "//nirvana/common-go/snowflake_utils",
        "//nirvana/common-go/tracing",
        "//nirvana/infra/config",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/pricing/explainability/snowflake_utils",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_volatiletech_sqlboiler_v4//boil",
        "@com_github_volatiletech_sqlboiler_v4//queries",
        "@com_github_volatiletech_sqlboiler_v4//queries/qm",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_mock//gomock",
    ],
)
