package client_test

import (
	"archive/zip"
	"bytes"
	"context"
	"encoding/csv"
	"io"
	"net/url"
	"time"

	"github.com/google/uuid"
	"github.com/jszwec/csvutil"
	"go.uber.org/mock/gomock"
	"nirvanatech.com/nirvana/claims/reporting/enums"
	"nirvanatech.com/nirvana/claims/reporting/internal/db/snowflake"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/infra/authz"
)

func (s *clientTestSuite) TestGenerateBordereauxReport() {
	wantGeneratedAt := time_utils.NewDate(2025, time.August, 15).ToTime()
	s.Clk.Set(wantGeneratedAt)

	wantUserId := uuid.New()
	ctx := authz.WithUser(context.Background(), authz.User{
		UserInfo: authz.UserInfo{ID: wantUserId},
	})

	wantCarrier := enums.CarrierMSTransverse

	wantBordereauxClaims := []snowflake.BordereauxClaimRow{
		{
			POLICY_NUMBER: "TINCA0100010-24",
			CLAIM_NUMBER:  "CLAIM-111",
		},
		{
			POLICY_NUMBER: "TINCA0200020-25",
			CLAIM_NUMBER:  "CLAIM-222",
		},
	}
	s.SnowflakeWrapper.
		EXPECT().
		GetBordereauxClaimsRows(gomock.Any(), enums.CarrierMSTransverse).
		Return(wantBordereauxClaims, nil).
		Times(1)

	wantBordereauxExposures := []snowflake.BordereauxExposureRow{
		{
			CLAIM_NUMBER:  "CLAIM-111",
			AOE_PAID:      1_000,
			VIN:           pointer_utils.ToPointer("12345678901234567"),
			VEHICLE_MAKE:  pointer_utils.ToPointer("Toyota"),
			VEHICLE_MODEL: pointer_utils.ToPointer("Camry"),
		},
		{
			CLAIM_NUMBER:  "CLAIM-222",
			AOE_PAID:      2_000,
			VIN:           pointer_utils.ToPointer("12345678901234568"),
			VEHICLE_MAKE:  pointer_utils.ToPointer("Honda"),
			VEHICLE_MODEL: pointer_utils.ToPointer("Civic"),
		},
	}
	s.SnowflakeWrapper.
		EXPECT().
		GetBordereauxExposuresRows(gomock.Any(), enums.CarrierMSTransverse).
		Return(wantBordereauxExposures, nil).
		Times(1)

	reportId, downloadURL, err := s.Client.GenerateBordereauxReport(ctx, wantCarrier)
	s.Require().NoError(err)

	storedReports, err := s.BordereauxReportDataWrapper.Get(ctx)
	s.Require().NoError(err)
	s.Require().Equal(1, len(storedReports))

	gotReport := storedReports[0]
	s.Equal(reportId, gotReport.Id)
	s.Equal(wantCarrier, gotReport.Carrier)
	s.Equal(wantUserId, gotReport.GeneratedBy)
	s.Equal(wantGeneratedAt, gotReport.GeneratedAt)
	s.True(isValidURL(downloadURL.String()))

	gotZipFileName, err := s.FileUploadManager.GetFileNameByHandleID(ctx, gotReport.FileHandleId)
	s.Require().NoError(err)
	s.Contains(gotZipFileName, "bordereaux_report_MSTransverse_")
	s.Contains(gotZipFileName, ".zip")

	gotZippedContents := s.getZippedFiles(ctx, gotReport.FileHandleId)
	s.Require().NoError(err)
	s.Equal(2, len(gotZippedContents))

	getZipFileByName := func(name string) *zip.File {
		return slice_utils.Find(gotZippedContents, func(f zip.File) bool {
			return f.Name == name
		})
	}

	gotBordereauxClaimsFile := getZipFileByName("bordereaux_claims.csv")
	s.Require().NotNil(gotBordereauxClaimsFile)
	gotBordereauxClaimsFileContents, err := s.readZippedFile(gotBordereauxClaimsFile)
	s.Require().NoError(err)
	s.assertHeaders(gotBordereauxClaimsFileContents, wantSortedClaimsHeaders)
	var gotUploadedBordereauxClaims []snowflake.BordereauxClaimRow
	s.Require().NoError(csvutil.Unmarshal(gotBordereauxClaimsFileContents, &gotUploadedBordereauxClaims))
	s.ElementsMatch(wantBordereauxClaims, gotUploadedBordereauxClaims)

	gotBordereauxExposuresFile := getZipFileByName("bordereaux_exposures.csv")
	s.Require().NotNil(gotBordereauxExposuresFile)
	gotBordereauxExposuresFileContents, err := s.readZippedFile(gotBordereauxExposuresFile)
	s.Require().NoError(err)
	s.assertHeaders(gotBordereauxExposuresFileContents, wantSortedExposuresHeaders)
	var gotUploadedBordereauxExposures []snowflake.BordereauxExposureRow
	s.Require().NoError(csvutil.Unmarshal(gotBordereauxExposuresFileContents, &gotUploadedBordereauxExposures))
	s.ElementsMatch(wantBordereauxExposures, gotUploadedBordereauxExposures)
}

func (s *clientTestSuite) getZippedFiles(
	ctx context.Context,
	zipFileHandleId uuid.UUID,
) []zip.File {
	fileReader, err := s.FileUploadManager.DownloadFile(ctx, zipFileHandleId)
	s.Require().NoError(err)

	fileBytes, err := io.ReadAll(fileReader)
	s.Require().NoError(err)

	zipReader, err := zip.NewReader(bytes.NewReader(fileBytes), int64(len(fileBytes)))
	s.Require().NoError(err)

	return slice_utils.FromSliceOfPointers(zipReader.File)
}

func (s *clientTestSuite) readZippedFile(zipFile *zip.File) ([]byte, error) {
	reader, err := zipFile.Open()
	s.Require().NoError(err)
	defer reader.Close()

	contents, err := io.ReadAll(reader)
	s.Require().NoError(err)

	return contents, nil
}

// assertHeaders checks that the input CSV contents have the same headers in the same order as the
// wantSortedHeaders.
func (s *clientTestSuite) assertHeaders(contents []byte, wantSortedHeaders []string) {
	reader := csv.NewReader(bytes.NewReader(contents))
	headers, err := reader.Read()
	s.Require().NoError(err)
	s.Require().Equal(len(wantSortedHeaders), len(headers), "Amount of headers mismatch")
	s.Equal(wantSortedHeaders, headers)
}

func isValidURL(str string) bool {
	u, err := url.ParseRequestURI(str)
	return err == nil && u.Scheme != "" && u.Host != ""
}

var wantSortedClaimsHeaders = []string{
	"Client Name",
	"Client Location",
	"Org Location Name",
	"LOB",
	"Retail Broker/Agent",
	"Insured",
	"Insured State",
	"Insurer DBA Name",
	"Driver",
	"Vehicle VIN",
	"Insured Vehicle Total Loss (Y/N)",
	"Policy Nbr",
	"Risk St",
	"Eff Date",
	"Exp Date",
	"Policy Cancel Date",
	"Claim Nbr",
	"Claim Type",
	"Prev Claim Nbr",
	"Is Denied",
	"Denial Reason",
	"Contested (Y/N)",
	"Incident Only",
	"Litigation (Y/N)",
	"Acc Date",
	"Acc Desc",
	"Acc Code",
	"Acc Location",
	"Acc City",
	"Acc County",
	"Acc State",
	"Claim Status",
	"Claim Adjuster",
	"Claim Team",
	"Dt Reported",
	"Reported Method",
	"Lag Time",
	"Dt Open",
	"Dt Closed",
	"Dt Reopen",
	"Days Open",
	"Catastrophe Nbr",
	"Exceeds Client Authority (Y/N)",
	"Ind Paid",
	"Med Paid",
	"Exp Paid",
	"DCC Paid",
	"AOE Paid",
	"Ind Res",
	"Med Res",
	"Exp Res",
	"DCC Res",
	"AOE Res",
	"Gross Incurred",
	"Total Recoveries",
	"Subro Rec",
	"Salvage Rec",
	"Deduct Rec",
	"Other Recoveries",
	"Net Incurred",
}

var wantSortedExposuresHeaders = []string{
	"Client Name",
	"Client Location",
	"Org Location Name",
	"LOB",
	"Retail Broker/Agent",
	"Insured",
	"Insured State",
	"Insurer DBA Name",
	"Driver",
	"Vehicle VIN",
	"Insured Vehicle Total Loss (Y/N)",
	"Vehicle Make",
	"Vehicle Model",
	"Policy Nbr",
	"Risk St",
	"Eff Date",
	"Exp Date",
	"Policy Cancel Date",
	"Reserve Nbr",
	"Reserve Status",
	"Claim Nbr",
	"Reserve Reference Nbr",
	"Claim Type",
	"Prev Claim Nbr",
	"Is Denied",
	"Denial Reason",
	"Contested (Y/N)",
	"Litigation (Y/N)",
	"Acc Date",
	"Acc Desc",
	"Acc Code",
	"Acc Location",
	"Acc City",
	"Acc County",
	"Acc State",
	"Jurisdiction State",
	"Claim Status",
	"Line Code",
	"Coverage Code",
	"Coverage Type",
	"Claimant",
	"Reserve Adjuster",
	"Reserve Supervisor",
	"Dt Reported",
	"Reported Method",
	"Lag Time",
	"Dt Open",
	"Dt Closed",
	"Dt Reopen",
	"Reserve Dt Open",
	"Reserve Dt Closed",
	"Reserve Dt Reopen",
	"Days Reserve Open",
	"Catastrophe Nbr",
	"Exceeds Client Authority (Y/N)",
	"Ind Paid",
	"Med Paid",
	"Exp Paid",
	"DCC Paid",
	"AOE Paid",
	"Ind Res",
	"Med Res",
	"Exp Res",
	"DCC Res",
	"AOE Res",
	"Gross Incurred",
	"Total Recoveries",
	"Subro Rec",
	"Salvage Rec",
	"Deduct Rec",
	"Other Recoveries",
	"Net Incurred",
}
