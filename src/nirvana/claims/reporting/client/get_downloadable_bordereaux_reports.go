package client

import (
	"context"
	"net/url"

	"github.com/cockroachdb/errors"
	db "nirvanatech.com/nirvana/claims/reporting/internal/db/postgres/bordereaux_reports"
)

// DownloadableBordereauxReport is a BordereauxReport with a temporary download link.
// If the report is not downloadable, the Error field is set, containing the reason why.
type DownloadableBordereauxReport struct {
	Report      db.BordereauxReport
	DownloadURL *url.URL
	Error       error
}

// GetDownloadableBordereauxReports returns a list of BordereauxReports with their temporary
// download links (valid for 8 hours), ordered by generation date descending.
func (c *Client) GetDownloadableBordereauxReports(ctx context.Context) (
	[]DownloadableBordereauxReport,
	error,
) {
	reports, err := c.deps.BordereauxReportWrapper.Get(ctx, db.OrderByGeneratedAtDesc())
	if err != nil {
		return nil, err
	}

	downloadableReports := make([]DownloadableBordereauxReport, 0, len(reports))
	for _, report := range reports {
		downloadURL, err := c.generateDownloadURL(ctx, report.FileHandleId)
		if err != nil {
			downloadableReports = append(downloadableReports, DownloadableBordereauxReport{
				Report: report,
				Error:  errors.Wrapf(err, "failed to generate download URL for report %s", report.Id),
			})
			continue
		}

		downloadableReports = append(downloadableReports, DownloadableBordereauxReport{
			Report:      report,
			DownloadURL: downloadURL,
		})
	}

	return downloadableReports, nil
}
