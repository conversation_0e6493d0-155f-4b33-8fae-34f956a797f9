package client

import (
	"archive/zip"
	"bytes"
	"context"
	"fmt"
	"net/url"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/jszwec/csvutil"
	"nirvanatech.com/nirvana/claims/reporting/enums"
	db "nirvanatech.com/nirvana/claims/reporting/internal/db/postgres/bordereaux_reports"
	file_enums "nirvanatech.com/nirvana/common-go/file_upload_lib/enums"
	"nirvanatech.com/nirvana/common-go/tracing"
	"nirvanatech.com/nirvana/common-go/uuid_utils"
	"nirvanatech.com/nirvana/infra/authz"
)

// GenerateBordereauxReport generates a bordereaux report for the given carrier, and returns the
// resulting report ID and a temporary download URL.
func (c *Client) GenerateBordereauxReport(
	ctx context.Context,
	carrier enums.Carrier,
) (uuid.UUID, *url.URL, error) {
	ctx, span := tracing.Start(ctx, "client.GenerateBordereauxReport")
	defer span.End()

	claimsRowsCSV, err := c.getBordereauxClaimsCSV(ctx, carrier)
	if err != nil {
		return uuid.Nil, nil, errors.Wrap(err, "failed to get bordereaux claims CSV")
	}

	exposuresRowsCSV, err := c.getBordereauxExposuresCSV(ctx, carrier)
	if err != nil {
		return uuid.Nil, nil, errors.Wrap(err, "failed to get bordereaux exposures CSV")
	}

	zipBuffer, err := zipContents(
		[]zipFile{
			{
				fileName: "bordereaux_claims.csv",
				contents: claimsRowsCSV,
			},
			{
				fileName: "bordereaux_exposures.csv",
				contents: exposuresRowsCSV,
			},
		},
	)
	if err != nil {
		return uuid.Nil, nil, errors.Wrap(err, "failed to generate zip")
	}

	generatedAt := c.deps.Clk.Now()
	zipName := fmt.Sprintf("bordereaux_report_%s_%d.zip", carrier, generatedAt.UnixNano())
	fileHandleId, err := c.uploadZip(ctx, zipBuffer, zipName, carrier)
	if err != nil {
		return uuid.Nil, nil, errors.Wrap(err, "failed to upload zip file")
	}

	reportId, err := c.storeBordereauxReport(ctx, carrier, fileHandleId, generatedAt)
	if err != nil {
		return uuid.Nil, nil, errors.Wrap(err, "failed to store bordereaux report")
	}

	downloadURL, err := c.generateDownloadURL(ctx, fileHandleId)
	if err != nil {
		return uuid.Nil, nil, errors.Wrap(err, "failed to generate temporary download URL")
	}

	return reportId, downloadURL, nil
}

func (c *Client) getBordereauxClaimsCSV(ctx context.Context, carrier enums.Carrier) ([]byte, error) {
	claimsRows, err := c.deps.SnowflakeWrapper.GetBordereauxClaimsRows(ctx, carrier)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get bordereaux claims rows")
	}
	return csvutil.Marshal(claimsRows)
}

func (c *Client) getBordereauxExposuresCSV(ctx context.Context, carrier enums.Carrier) ([]byte, error) {
	exposuresRows, err := c.deps.SnowflakeWrapper.GetBordereauxExposuresRows(ctx, carrier)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get bordereaux exposures rows")
	}
	return csvutil.Marshal(exposuresRows)
}

type zipFile struct {
	fileName string
	contents []byte
}

// zipContents zips the given files into a single zip file, and returns the resulting buffer.
func zipContents(files []zipFile) (*bytes.Buffer, error) {
	zipBuffer := new(bytes.Buffer)
	zipWriter := zip.NewWriter(zipBuffer)
	defer zipWriter.Close()

	for _, file := range files {
		fileToAdd, err := zipWriter.Create(file.fileName)
		if err != nil {
			return nil, errors.Wrap(err, "failed to create file in zip")
		}

		if _, err := fileToAdd.Write(file.contents); err != nil {
			return nil, errors.Wrap(err, "failed to write data to zip")
		}
	}
	return zipBuffer, nil
}

// uploadZip uploads the given zip buffer to our object store, and returns the file handle ID.
func (c *Client) uploadZip(
	ctx context.Context,
	zipBuffer *bytes.Buffer,
	zipName string,
	carrier enums.Carrier,
) (uuid.UUID, error) {
	fileHandleId := uuid.New()
	accountId := uuid_utils.StableUUID(carrier.String())
	err := c.deps.FilesManager.UploadFile(
		ctx,
		bytes.NewReader(zipBuffer.Bytes()),
		fileHandleId,
		file_enums.FileTypeZip,
		zipName,
		accountId,
		file_enums.FileDestinationGroupClaims,
	)
	if err != nil {
		return uuid.Nil, errors.Wrap(err, "failed to upload zip file")
	}
	return fileHandleId, nil
}

func (c *Client) storeBordereauxReport(
	ctx context.Context,
	carrier enums.Carrier,
	fileHandleId uuid.UUID,
	generatedAt time.Time,
) (uuid.UUID, error) {
	generatedBy := authz.UserFromContext(ctx).ID
	report := db.NewBordereauxReportBuilder().
		WithCarrier(carrier).
		WithGeneratedAt(generatedAt).
		WithGeneratedBy(generatedBy).
		WithFileHandleId(fileHandleId).
		Build()
	if err := c.deps.BordereauxReportWrapper.Insert(ctx, report); err != nil {
		return uuid.Nil, errors.Wrap(err, "failed to insert bordereaux report")
	}
	return report.Id, nil
}
