package client_test

import (
	"context"
	"time"

	"github.com/google/uuid"
	db "nirvanatech.com/nirvana/claims/reporting/internal/db/postgres/bordereaux_reports"
	files_enums "nirvanatech.com/nirvana/common-go/file_upload_lib/enums"
	"nirvanatech.com/nirvana/common-go/time_utils"
	files "nirvanatech.com/nirvana/db-api/db_wrappers/file_upload"
)

func (s *clientTestSuite) TestGetDownloadableBordereauxReports() {
	ctx := context.Background()

	muchLaterReportWithBrokenFile := db.NewBordereauxReportBuilder().
		WithDefaultMockData().
		WithGeneratedAt(time_utils.NewDate(2024, time.December, 2).ToTime()).
		Build()
	s.Require().NoError(s.BordereauxReportDataWrapper.Insert(ctx, muchLaterReportWithBrokenFile))

	earlierReport := s.seedBordereauxReport(ctx, time_utils.NewDate(2024, time.May, 5), "keys/earlier-report.zip")
	laterReport := s.seedBordereauxReport(ctx, time_utils.NewDate(2024, time.August, 15), "keys/later-report.zip")

	got, err := s.Client.GetDownloadableBordereauxReports(ctx)
	s.Require().NoError(err)
	s.Require().Equal(3, len(got))

	assertResult := func(
		index int,
		wantReport db.BordereauxReport,
		wantDownloadURL string,
		wantErrorMessage string,
	) {
		s.T().Helper()

		s.Equal(wantReport, got[index].Report)
		if wantErrorMessage != "" {
			s.Nil(got[index].DownloadURL)
			s.Contains(got[index].Error.Error(), wantErrorMessage)
			return
		}
		s.Equal(wantDownloadURL, got[index].DownloadURL.String())
		s.Nil(got[index].Error)
	}

	assertResult(0, muchLaterReportWithBrokenFile, "", "failed to generate download URL for report")
	assertResult(1, laterReport, "https://s3.amazonaws.com/nirvana-claims/keys/later-report.zip", "")
	assertResult(2, earlierReport, "https://s3.amazonaws.com/nirvana-claims/keys/earlier-report.zip", "")
}

func (s *clientTestSuite) seedBordereauxReport(
	ctx context.Context,
	generatedAt time_utils.Date,
	fileKey string,
) db.BordereauxReport {
	report := db.NewBordereauxReportBuilder().
		WithDefaultMockData().
		WithGeneratedAt(generatedAt.ToTime()).
		Build()
	s.Require().NoError(s.BordereauxReportDataWrapper.Insert(ctx, report))

	err := s.FileUploadDataWrapper.InsertFile(ctx, files.FileObject{
		Handle:           report.FileHandleId,
		Key:              fileKey,
		DestinationGroup: files_enums.FileDestinationGroupClaims,
		AccountID:        uuid.New(),
	})
	s.Require().NoError(err)

	return report
}
