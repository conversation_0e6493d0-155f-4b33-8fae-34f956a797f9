load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "client",
    srcs = [
        "client.go",
        "download_urls.go",
        "fx.go",
        "generate_bordereaux_report.go",
        "get_downloadable_bordereaux_reports.go",
    ],
    importpath = "nirvanatech.com/nirvana/claims/reporting/client",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/claims/reporting/enums",
        "//nirvana/claims/reporting/internal/db/postgres/bordereaux_reports",
        "//nirvana/claims/reporting/internal/db/snowflake",
        "//nirvana/common-go/file_upload_lib",
        "//nirvana/common-go/file_upload_lib/enums",
        "//nirvana/common-go/tracing",
        "//nirvana/common-go/uuid_utils",
        "//nirvana/infra/authz",
        "//nirvana/infra/fx/fxregistry",
        "@com_github_benb<PERSON><PERSON><PERSON>_clock//:clock",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_jszwec_csvutil//:csvutil",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "client_test",
    srcs = [
        "client_test.go",
        "generate_bordereaux_report_test.go",
        "get_downloadable_bordereaux_reports_test.go",
    ],
    deps = [
        ":client",
        "//nirvana/claims/reporting/enums",
        "//nirvana/claims/reporting/internal/db/postgres/bordereaux_reports",
        "//nirvana/claims/reporting/internal/db/snowflake",
        "//nirvana/common-go/file_upload_lib",
        "//nirvana/common-go/file_upload_lib/enums",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/db-api/db_wrappers/file_upload",
        "//nirvana/infra/authz",
        "//nirvana/infra/fx/testloader",
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_google_uuid//:uuid",
        "@com_github_jszwec_csvutil//:csvutil",
        "@com_github_stretchr_testify//suite",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
        "@org_uber_go_mock//gomock",
    ],
)
