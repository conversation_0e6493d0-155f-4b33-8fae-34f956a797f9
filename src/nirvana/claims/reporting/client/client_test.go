package client_test

import (
	"testing"

	"github.com/benbjohnson/clock"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"nirvanatech.com/nirvana/claims/reporting/client"
	brdrxdb "nirvanatech.com/nirvana/claims/reporting/internal/db/postgres/bordereaux_reports"
	"nirvanatech.com/nirvana/claims/reporting/internal/db/snowflake"
	uploads "nirvanatech.com/nirvana/common-go/file_upload_lib"
	files "nirvanatech.com/nirvana/db-api/db_wrappers/file_upload"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

func TestClient(t *testing.T) {
	suite.Run(t, new(clientTestSuite))
}

type clientTestSuiteEnv struct {
	fx.In

	Clk                         *clock.Mock
	Client                      *client.Client
	BordereauxReportDataWrapper *brdrxdb.DataWrapper
	FileUploadDataWrapper       files.DataWrapper
	FileUploadManager           uploads.FileUploadManager[uploads.DefaultS3Keygen]
	SnowflakeWrapper            *snowflake.MockDataWrapper
}

type clientTestSuite struct {
	suite.Suite
	fxapp *fxtest.App

	clientTestSuiteEnv
}

func (s *clientTestSuite) SetupTest() {
	s.fxapp = testloader.RequireStart(s.T(), &s.clientTestSuiteEnv)
}

func (s *clientTestSuite) TearDownTest() {
	s.fxapp.RequireStop()
}
