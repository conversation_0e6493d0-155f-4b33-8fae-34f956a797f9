package client

import (
	"github.com/benb<PERSON><PERSON><PERSON>/clock"
	"go.uber.org/fx"
	brdrxdb "nirvanatech.com/nirvana/claims/reporting/internal/db/postgres/bordereaux_reports"
	"nirvanatech.com/nirvana/claims/reporting/internal/db/snowflake"
	files "nirvanatech.com/nirvana/common-go/file_upload_lib"
)

type deps struct {
	fx.In

	BordereauxReportWrapper *brdrxdb.DataWrapper
	Clk                     clock.Clock
	FilesManager            files.FileUploadManager[files.DefaultS3Keygen]
	SnowflakeWrapper        snowflake.DataWrapper
}

type Client struct {
	deps deps
}

func newClient(deps deps) *Client {
	return &Client{
		deps: deps,
	}
}
