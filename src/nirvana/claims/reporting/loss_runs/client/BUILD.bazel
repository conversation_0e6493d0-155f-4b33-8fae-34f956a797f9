load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "client",
    srcs = [
        "client.go",
        "deps.go",
        "fx.go",
        "generate_provisional_loss_runs_report.go",
        "generate_provisional_loss_runs_report_from_dot.go",
        "get_loss_runs_report_data.go",
        "get_loss_runs_reports.go",
        "get_policy.go",
        "nars.go",
        "policy.go",
        "snapsheet.go",
    ],
    importpath = "nirvanatech.com/nirvana/claims/reporting/loss_runs/client",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/claims/claim_parties/db/nars",
        "//nirvana/claims/claim_postings/recoveries",
        "//nirvana/claims/claim_reserves/db/nars",
        "//nirvana/claims/client",
        "//nirvana/claims/db",
        "//nirvana/claims/enums",
        "//nirvana/claims/reporting/internal/db/snowflake",
        "//nirvana/claims/reporting/loss_runs/db",
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/db-api/db_wrappers/agency",
        "//nirvana/db-api/db_wrappers/policy",
        "//nirvana/infra/authz",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/pdfgen",
        "//nirvana/policy",
        "//nirvana/policy/enums",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_shopspring_decimal//:decimal",
        "@org_golang_google_protobuf//types/known/durationpb",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "client_test",
    srcs = [
        "client_test.go",
        "generate_provisional_loss_runs_report_from_dot_test.go",
        "generate_provisional_loss_runs_report_test.go",
        "get_loss_runs_report_data_test.go",
        "get_loss_runs_reports_test.go",
        "get_policy_test.go",
    ],
    deps = [
        ":client",
        "//nirvana/claims/claim_parties/builders",
        "//nirvana/claims/claim_parties/db/nars",
        "//nirvana/claims/claim_postings/db/nars",
        "//nirvana/claims/claim_reserves/builders",
        "//nirvana/claims/claim_reserves/db/nars",
        "//nirvana/claims/claim_reserves/enums",
        "//nirvana/claims/client",
        "//nirvana/claims/db",
        "//nirvana/claims/enums",
        "//nirvana/claims/reporting/internal/db/snowflake",
        "//nirvana/claims/reporting/loss_runs/db",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/test_utils/builders/policy",
        "//nirvana/common-go/time_utils",
        "//nirvana/db-api/db_wrappers/policy",
        "//nirvana/infra/authz",
        "//nirvana/infra/fx/testfixtures/agency_fixture",
        "//nirvana/infra/fx/testfixtures/users_fixture",
        "//nirvana/infra/fx/testloader",
        "//nirvana/policy/enums",
        "//nirvana/policy_common/constants",
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_google_uuid//:uuid",
        "@com_github_shopspring_decimal//:decimal",
        "@com_github_stretchr_testify//suite",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
    ],
)
