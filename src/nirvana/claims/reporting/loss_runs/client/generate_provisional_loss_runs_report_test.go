package client_test

import (
	"context"

	"nirvanatech.com/nirvana/infra/authz"
)

func (s *lossRunsRenewalsClientTestSuite) TestGenerateProvisionalLossRunsReport() {
	ctx := context.Background()

	testCases := []struct {
		name         string
		policyNumber string
		user         authz.User
		wantErr      bool
	}{
		{
			name:         "policy number not found",
			policyNumber: "NISTK0000000-invalid",
			user:         s.usersFixture.Superuser.AuthzUser(),
			wantErr:      true,
		},
		{
			name:         "valid policy number",
			policyNumber: s.expiredPolicyNumberWithNarsClaimsOnly,
			user:         s.usersFixture.Underwriters[0].AuthzUser(),
			wantErr:      false,
		},
		{
			name:         "invalid user",
			policyNumber: s.expiredPolicyNumberWithNarsClaimsOnly,
			user:         authz.User{},
			wantErr:      true,
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			ctx = authz.WithUser(ctx, tc.user)

			report, err := s.client.GenerateProvisionalLossRunsReport(ctx, tc.policyNumber)
			if tc.wantErr {
				s.Require().Error(err)
				s.Require().Nil(report)
				return
			}

			s.Require().NoError(err)
			s.Require().NotNil(report)
			s.Require().Equal(tc.policyNumber, report.PolicyNumber)
			s.Require().Equal(tc.user.ID, report.RequestedBy)
			s.Require().NotEmpty(report.DownloadLink)
		})
	}
}
