package client_test

import (
	"context"
	"database/sql"
	"slices"
	"strings"

	"nirvanatech.com/nirvana/claims/reporting/internal/db/snowflake"
	"nirvanatech.com/nirvana/claims/reporting/loss_runs/client"
	"nirvanatech.com/nirvana/claims/reporting/loss_runs/db"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/infra/authz"
)

func (s *lossRunsRenewalsClientTestSuite) TestGenerateProvisionalLossRunsReportsFromDOT() {
	ctx := context.Background()

	testCases := []struct {
		name             string
		dotNumber        int64
		user             authz.User
		wantErr          error
		want             []client.LossRunsReport
		setExpectedCalls func(ctx context.Context)
	}{
		{
			name:             "dot number without policies",
			dotNumber:        -1,
			user:             s.usersFixture.Superuser.AuthzUser(),
			wantErr:          sql.ErrNoRows,
			setExpectedCalls: func(ctx context.Context) {},
		},
		{
			name:      "valid dot number",
			dotNumber: s.dotNumberWithPolicies,
			user:      s.usersFixture.Underwriters[0].AuthzUser(),
			want: []client.LossRunsReport{
				{
					LossRunsReport: db.LossRunsReport{
						PolicyNumber: s.activePolicyNumberWithNoClaims,
					},
					DownloadLink: "https://s3.amazonaws.com/nirvana-pdfgen",
				},
				{
					LossRunsReport: db.LossRunsReport{
						PolicyNumber: s.activePolicyNumberWithNarsClaimsOnly,
					},
					DownloadLink: "https://s3.amazonaws.com/nirvana-pdfgen",
				},
				{
					LossRunsReport: db.LossRunsReport{
						PolicyNumber: s.activePolicyNumberWithSnapsheetAndNarsClaims,
					},
					DownloadLink: "https://s3.amazonaws.com/nirvana-pdfgen",
				},
			},
			setExpectedCalls: func(ctx context.Context) {
				s.snowflakeWrapper.
					EXPECT().
					GetLossRunsRows(ctx, s.activePolicyNumberWithSnapsheetAndNarsClaims).
					Return([]snowflake.LossRunsRow{
						{
							CLAIM_NUMBER:           "SS-CLAIM10040405",
							CLAIM_ID:               "123456",
							CLAIMANT_NAME:          pointer_utils.ToPointer("Jimmy's Trucking LLC"),
							DEDUCTIBLE_RECOVERIES:  130,
							DRIVER:                 pointer_utils.ToPointer("James Smith"),
							EXPENSES_PAID:          200.75,
							EXPENSES_RESERVES:      0,
							GROSS_INCURRED:         1300,
							LINE_CODE:              "19.4",
							LOSS_PAID:              1000.25,
							LOSS_RESERVES:          0,
							MEDICAL_PAID:           0,
							MEDICAL_RESERVES:       0,
							OTHER_RECOVERIES:       0,
							SALVAGE_RECOVERIES:     70,
							STATUS:                 "CLOSED",
							SUBROGATION_RECOVERIES: 1000,
							TYPE:                   "VEHICLE",
						},
					}, nil).
					Times(1)
			},
		},
	}

	sortReportsFn := func(a, b client.LossRunsReport) int {
		return strings.Compare(a.PolicyNumber, b.PolicyNumber)
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			ctx = authz.WithUser(ctx, tc.user)
			tc.setExpectedCalls(ctx)

			reports, err := s.client.GenerateProvisionalLossRunsReportsFromDOT(ctx, tc.dotNumber)
			if tc.wantErr != nil {
				s.Require().ErrorIs(err, tc.wantErr)
				s.Require().Nil(reports)
				return
			}

			s.Require().NoError(err)
			s.Require().NotNil(reports)
			s.Require().Len(reports, len(tc.want))

			slices.SortFunc(tc.want, sortReportsFn)
			slices.SortFunc(reports, sortReportsFn)

			for i, report := range reports {
				s.Require().Contains(report.DownloadLink, tc.want[i].DownloadLink)
				s.Require().Equal(tc.want[i].PolicyNumber, report.PolicyNumber)
			}
		})
	}
}
