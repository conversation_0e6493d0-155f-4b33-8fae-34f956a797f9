package client_test

import (
	"context"

	"nirvanatech.com/nirvana/claims/reporting/internal/db/snowflake"
	"nirvanatech.com/nirvana/claims/reporting/loss_runs/client"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
)

func (s *lossRunsRenewalsClientTestSuite) TestGetLossRunsReportData() {
	ctx := context.Background()

	s.snowflakeWrapper.
		EXPECT().
		GetLossRunsRows(ctx, s.expiredPolicyNumberWithSnapsheetClaimsOnly).
		Return([]snowflake.LossRunsRow{
			{
				CLAIM_NUMBER:           "SS-CLAIM10000004",
				CLAIM_ID:               "123457",
				CLAIMANT_NAME:          pointer_utils.ToPointer("UNK UNK"),
				DEDUCTIBLE_RECOVERIES:  0,
				DRIVER:                 pointer_utils.ToPointer("UNK UNK"),
				EXPENSES_PAID:          200,
				EXPENSES_RESERVES:      0,
				GROSS_INCURRED:         1200,
				LINE_CODE:              "19.4",
				LOSS_PAID:              1000,
				LOSS_RESERVES:          0,
				MEDICAL_PAID:           0,
				MEDICAL_RESERVES:       0,
				OTHER_RECOVERIES:       0,
				SALVAGE_RECOVERIES:     500,
				STATUS:                 "CLOSED",
				SUBROGATION_RECOVERIES: 0,
				TYPE:                   "VEHICLE",
			},
			{
				CLAIM_NUMBER:           "SS-CLAIM1000004",
				CLAIM_ID:               "123457",
				CLAIMANT_NAME:          pointer_utils.ToPointer("Juanita's Trucking LLC"),
				DEDUCTIBLE_RECOVERIES:  0,
				DRIVER:                 pointer_utils.ToPointer("UNK UNK"),
				EXPENSES_PAID:          0,
				EXPENSES_RESERVES:      0,
				GROSS_INCURRED:         0,
				LINE_CODE:              "19.4",
				LOSS_PAID:              0,
				LOSS_RESERVES:          0,
				MEDICAL_PAID:           0,
				MEDICAL_RESERVES:       0,
				OTHER_RECOVERIES:       0,
				SALVAGE_RECOVERIES:     0,
				STATUS:                 "CLOSED",
				SUBROGATION_RECOVERIES: 0,
				TYPE:                   "VEHICLE",
			},
		}, nil).
		Times(1)

	s.snowflakeWrapper.
		EXPECT().
		GetLossRunsRows(ctx, s.activePolicyNumberWithSnapsheetAndNarsClaims).
		Return([]snowflake.LossRunsRow{
			{
				CLAIM_NUMBER:           "SS-CLAIM10040405",
				CLAIM_ID:               "123459",
				CLAIMANT_NAME:          pointer_utils.ToPointer("Jimmy's Trucking LLC"),
				DEDUCTIBLE_RECOVERIES:  130,
				DRIVER:                 pointer_utils.ToPointer("James Smith"),
				EXPENSES_PAID:          200.75,
				EXPENSES_RESERVES:      0,
				GROSS_INCURRED:         1300,
				LINE_CODE:              "19.4",
				LOSS_PAID:              1000.25,
				LOSS_RESERVES:          0,
				MEDICAL_PAID:           0,
				MEDICAL_RESERVES:       0,
				OTHER_RECOVERIES:       0,
				SALVAGE_RECOVERIES:     70,
				STATUS:                 "CLOSED",
				SUBROGATION_RECOVERIES: 1000,
				TYPE:                   "VEHICLE",
			},
		}, nil).
		Times(1)

	testCases := []struct {
		name                   string
		policyNumber           string
		wantErr                bool
		wantLossRunsReportData *client.LossRunsReportData
	}{
		{
			name:         "policy number not found",
			policyNumber: "NISTK0000000-invalid",
			wantErr:      true,
		},
		{
			name:         "valid policy number with no claims",
			policyNumber: s.activePolicyNumberWithNoClaims,
			wantErr:      false,
			wantLossRunsReportData: &client.LossRunsReportData{
				Policy:         s.policyNumberToRenewalsPolicyMap[s.activePolicyNumberWithNoClaims],
				AgencyName:     "TrucksRUs",
				Claims:         nil,
				IsDataComplete: true,
			},
		},
		{
			name:         "valid policy number with claims & recoveries data - NARS only",
			policyNumber: s.expiredPolicyNumberWithNarsClaimsOnly,
			wantErr:      false,
			wantLossRunsReportData: &client.LossRunsReportData{
				Policy:     s.policyNumberToRenewalsPolicyMap[s.expiredPolicyNumberWithNarsClaimsOnly],
				AgencyName: "TrucksRUs",
				// For this set of claims, one will have full recoveries data, and one will have
				// none, since it does not have any reserve summaries "yet".
				Claims:         s.policyNumberToRenewalsClaimsMap[s.expiredPolicyNumberWithNarsClaimsOnly],
				IsDataComplete: true,
			},
		},
		{
			name:         "valid policy number with claims & incomplete recoveries data - NARS only",
			policyNumber: s.activePolicyNumberWithNarsClaimsOnly,
			wantErr:      false,
			wantLossRunsReportData: &client.LossRunsReportData{
				Policy:         s.policyNumberToRenewalsPolicyMap[s.activePolicyNumberWithNarsClaimsOnly],
				AgencyName:     "TrucksRUs",
				Claims:         s.policyNumberToRenewalsClaimsMap[s.activePolicyNumberWithNarsClaimsOnly],
				IsDataComplete: false,
			},
		},
		{
			name:         "valid policy number with claims & recoveries data - Snapsheet only",
			policyNumber: s.expiredPolicyNumberWithSnapsheetClaimsOnly,
			wantErr:      false,
			wantLossRunsReportData: &client.LossRunsReportData{
				Policy:         s.policyNumberToRenewalsPolicyMap[s.expiredPolicyNumberWithSnapsheetClaimsOnly],
				AgencyName:     "TrucksRUs",
				Claims:         s.policyNumberToRenewalsClaimsMap[s.expiredPolicyNumberWithSnapsheetClaimsOnly],
				IsDataComplete: true,
			},
		},
		{
			name:         "valid policy number with claims & recoveries data - Snapsheet and NARS",
			policyNumber: s.activePolicyNumberWithSnapsheetAndNarsClaims,
			wantErr:      false,
			wantLossRunsReportData: &client.LossRunsReportData{
				Policy:         s.policyNumberToRenewalsPolicyMap[s.activePolicyNumberWithSnapsheetAndNarsClaims],
				AgencyName:     "TrucksRUs",
				Claims:         s.policyNumberToRenewalsClaimsMap[s.activePolicyNumberWithSnapsheetAndNarsClaims],
				IsDataComplete: true,
			},
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			lrrd, err := s.client.GetLossRunsReportData(ctx, tc.policyNumber)
			if tc.wantErr {
				s.Require().Error(err)
				s.Require().Nil(lrrd)
				return
			}
			s.Require().NoError(err)
			s.Require().NotNil(lrrd)

			s.Equal(tc.wantLossRunsReportData.IsDataComplete, lrrd.IsDataComplete)

			want := tc.wantLossRunsReportData.Policy
			got := lrrd.Policy

			s.Equal(want.PolicyNumber, got.PolicyNumber)
			s.Equal(want.InsuredName, got.InsuredName)
			s.Equal(want.InsuranceCarrier, got.InsuranceCarrier)
			s.Equal(want.EffectiveDate, got.EffectiveDate)
			s.Equal(want.ExpirationDate, got.ExpirationDate)

			s.Equal(tc.wantLossRunsReportData.AgencyName, lrrd.AgencyName)
			s.assertClaimsEqual(tc.wantLossRunsReportData.Claims, lrrd.Claims)
		})
	}
}

func (s *lossRunsRenewalsClientTestSuite) assertClaimsEqual(want, got []client.Claim) {
	s.Len(got, len(want), "claims arrays have different lengths")
	wantClaimNumbers := slice_utils.Map(want, func(c client.Claim) string {
		return c.ClaimNumber
	})
	gotClaimNumbers := slice_utils.Map(got, func(c client.Claim) string {
		return c.ClaimNumber
	})
	s.False(slice_utils.HasDuplicates(wantClaimNumbers))
	s.False(slice_utils.HasDuplicates(gotClaimNumbers))
	s.ElementsMatch(wantClaimNumbers, gotClaimNumbers, "claim numbers do not match")

	// Compare each claim in detail
	for _, wantClaim := range want {
		gotClaim := slice_utils.Find(
			got,
			func(c client.Claim) bool {
				return c.ClaimNumber == wantClaim.ClaimNumber
			},
		)
		s.NotNil(gotClaim, "claim %s not found in the 'got' array", wantClaim.ClaimNumber)

		// Compare the fields of the claim
		s.Equal(
			wantClaim.DateOpened,
			gotClaim.DateOpened,
			"dateOpened mismatch for claim %s",
			wantClaim.ClaimNumber,
		)
		s.Equal(
			wantClaim.DateClosed,
			gotClaim.DateClosed,
			"dateClosed mismatch for claim %s",
			wantClaim.ClaimNumber,
		)
		s.Equal(
			wantClaim.LossDate,
			gotClaim.LossDate,
			"lossDate mismatch for claim %s",
			wantClaim.ClaimNumber,
		)
		s.Equal(
			wantClaim.LossState,
			gotClaim.LossState,
			"lossState mismatch for claim %s",
			wantClaim.ClaimNumber,
		)
		s.Equal(
			wantClaim.LossDescription,
			gotClaim.LossDescription,
			"lossDescription mismatch for claim %s",
			wantClaim.ClaimNumber,
		)
		s.Equal(
			wantClaim.Status,
			gotClaim.Status,
			"status mismatch for claim %s",
			wantClaim.ClaimNumber,
		)

		// Compare Drivers array
		s.ElementsMatch(
			wantClaim.Drivers,
			gotClaim.Drivers,
			"drivers mismatch for claim %s",
			wantClaim.ClaimNumber,
		)

		// Compare ReservesSummaries array
		s.ElementsMatch(
			wantClaim.ReservesSummaries,
			gotClaim.ReservesSummaries,
			"reservesSummaries mismatch for claim %s",
			wantClaim.ClaimNumber,
		)
	}
}
