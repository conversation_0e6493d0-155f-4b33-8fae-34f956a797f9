package client

import (
	"context"
	"database/sql"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/policy"
	policy_enums "nirvanatech.com/nirvana/policy/enums"
)

// GenerateProvisionalLossRunsReportsFromDOT generated Loss Runs Reports for all active policies
// for the given DOT number. It returns only the download link for each policy number.
func (c *Client) GenerateProvisionalLossRunsReportsFromDOT(
	ctx context.Context,
	dotNumber int64,
) ([]LossRunsReport, error) {
	policies, err := c.getActivePoliciesForDotNumber(ctx, dotNumber)
	if err != nil {
		return nil, err
	}
	if len(policies) == 0 {
		return nil, sql.ErrNoRows
	}

	lossRunsReports := make([]LossRunsReport, 0, len(policies))
	for _, p := range policies {
		lrr, err := c.GenerateProvisionalLossRunsReport(ctx, p.PolicyNumber)
		if err != nil {
			return nil, errors.Wrapf(
				err,
				"unable to generate loss runs report for policy %s",
				p.PolicyNumber,
			)
		}

		lossRunsReports = append(lossRunsReports, *lrr)
	}

	return lossRunsReports, nil
}

// getActivePoliciesForDotNumber retrieves all active policies for the given DOT number.
func (c *Client) getActivePoliciesForDotNumber(ctx context.Context, dotNumber int64) ([]Policy, error) {
	policies, err := c.deps.PolicyClient.GetPolicies(
		ctx,
		policy.GetPoliciesFilter{
			DotNumber: &dotNumber,
			States: &[]policy_enums.PolicyState{
				policy_enums.PolicyStateActive,
				policy_enums.PolicyStateCancellationFiled,
			},
			SkipTestAgencies: true,
		},
	)
	if err != nil {
		return nil, err
	}

	return slice_utils.FromSliceOfPointers(slice_utils.Map(policies, serializePolicy)), nil
}
