package client_test

import (
	"context"

	"nirvanatech.com/nirvana/claims/reporting/loss_runs/client"
	"nirvanatech.com/nirvana/common-go/time_utils"
)

func (s *lossRunsRenewalsClientTestSuite) TestGetPolicy() {
	ctx := context.Background()

	testCases := []struct {
		name         string
		policyNumber string
		expected     *client.Policy
		wantErr      bool
	}{
		{
			name:         "get expired policy",
			policyNumber: s.expiredPolicyNumberWithNarsClaimsOnly,
			expected: &client.Policy{
				PolicyNumber:     s.expiredPolicyNumberWithNarsClaimsOnly,
				InsuredName:      s.expiredPolicyWithNarsClaimsOnly.InsuredName,
				InsuranceCarrier: s.expiredPolicyWithNarsClaimsOnly.InsuranceCarrier.String(),
				EffectiveDate:    time_utils.DateFromTime(s.expiredPolicyWithNarsClaimsOnly.EffectiveDate),
				ExpirationDate:   time_utils.DateFromTime(s.expiredPolicyWithNarsClaimsOnly.EffectiveDateTo),
			},
			wantErr: false,
		},
		{
			name:         "get inexistent policy",
			policyNumber: "NISTK0000000-20",
			wantErr:      true,
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			got, err := s.client.GetPolicy(ctx, tc.policyNumber)
			if tc.wantErr {
				s.Require().Error(err)
				return
			}
			s.Require().NoError(err)
			s.Require().NotNil(got)

			want := tc.expected
			s.Equal(want.PolicyNumber, got.PolicyNumber)
			s.Equal(want.InsuredName, got.InsuredName)
			s.Equal(want.InsuranceCarrier, got.InsuranceCarrier)
			s.Equal(want.EffectiveDate, got.EffectiveDate)
			s.Equal(want.ExpirationDate, got.ExpirationDate)
		})
	}
}
