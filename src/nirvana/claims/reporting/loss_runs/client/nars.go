package client

import (
	"context"
	"database/sql"
	"strings"

	"github.com/cockroachdb/errors"
	"github.com/shopspring/decimal"

	cp "nirvanatech.com/nirvana/claims/claim_parties/db/nars"
	recoveries_client "nirvanatech.com/nirvana/claims/claim_postings/recoveries"
	cr "nirvanatech.com/nirvana/claims/claim_reserves/db/nars"
	"nirvanatech.com/nirvana/claims/db"
	"nirvanatech.com/nirvana/claims/enums"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
)

// isNarsReportDataComplete checks if the recoveries data is complete for a set of claims (provided
// by NARS). It only checks recoveries because we can assume all other data (reserves, parties,
// etc.) to be complete. We also assume that any claim not provided by NARS is complete.
func (c *Client) isNarsReportDataComplete(ctx context.Context, claims []db.Claim) (bool, error) {
	for _, claim := range claims {
		if claim.Source != enums.ClaimsProviderNars {
			continue // Skip claims that are not from NARS
		}

		isRecoveriesDataComplete, err := c.deps.RecoveriesClient.IsRecoveriesDataComplete(
			ctx, claim,
		)
		if err != nil {
			return false, errors.Wrapf(
				err,
				"failed to check recoveries data completeness for %s claim with claim number %s",
				claim.Source,
				claim.ClaimNumber,
			)
		}
		if !isRecoveriesDataComplete {
			return false, nil
		}
	}
	return true, nil
}

func (c *Client) newNarsReservesSummary(
	ctx context.Context, reserveSummary cr.ReserveSummary, recoveries *RecoveriesSummary,
) (*ReservesSummary, error) {
	gross := reserveSummary.IndemnityPaid.
		Add(reserveSummary.MedicalPaid).
		Add(reserveSummary.ExpensePaid).
		Add(reserveSummary.IndemnityAvailable).
		Add(reserveSummary.MedicalAvailable).
		Add(reserveSummary.ExpenseAvailable)

	var claimParty *cp.ClaimParty
	var err error
	if reserveSummary.ClaimPartyExternalId != nil {
		// TODO: think whether we can avoid this N+1 DB call, since we retrieved all claim's parties before
		claimParty, err = c.deps.ClaimPartiesWrapper.GetClaimPartyByExternalId(
			ctx, *reserveSummary.ClaimPartyExternalId,
		)
		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			return nil, err
		}
	}

	var claimant string
	if claimParty != nil {
		claimant = claimParty.Claimant()
	}

	return &ReservesSummary{
		Claimant:       strings.TrimSpace(claimant),
		LineOfBusiness: reserveSummary.LOBCode,
		Coverage:       reserveSummary.LOBCoverage,
		Status:         reserveSummary.Status.String(),
		Amounts: Amounts{
			LossPaid:               reserveSummary.IndemnityPaid,
			MedicalPaid:            reserveSummary.MedicalPaid,
			ExpensesPaid:           reserveSummary.ExpensePaid,
			CurrentLossReserve:     reserveSummary.IndemnityAvailable,
			CurrentExpensesReserve: reserveSummary.ExpenseAvailable,
			CurrentMedicalReserve:  reserveSummary.MedicalAvailable,
			GrossIncurred:          gross,
			OtherRecoveries:        recoveries.OtherRecoveries,
			DeductibleRecoveries:   recoveries.DeductibleRecoveries,
			SalvageRecoveries:      recoveries.SalvageRecoveries,
			SubrogationRecoveries:  recoveries.SubrogationRecoveries,
		},
	}, nil
}

// newNarsClaim creates a Claim from a db.Claim with provider Nars
func (c *Client) newNarsClaim(ctx context.Context, dbClaim db.Claim) (*Claim, error) {
	if dbClaim.Source != enums.ClaimsProviderNars {
		return nil, errors.Newf("claim with external id %s is not from NARS", dbClaim.ExternalId)
	}

	claim := Claim{
		ClaimNumber:     dbClaim.ExternalId,
		DateOpened:      dbClaim.CreatedAt,
		LossDate:        dbClaim.LossDatetime,
		LossState:       dbClaim.LossState,
		LossDescription: dbClaim.LossDescription,
		Status:          dbClaim.Status.String(),
	}

	// The status enum has a misspelling, so we need to fix it here
	if dbClaim.Status == enums.ClaimStatusReopen {
		claim.Status = "Reopened"
	}

	dbReserveSummaries, err := c.deps.ClaimReservesWrapper.GetReserveSummaries(
		ctx, cr.ReserveSummaryClaimExternalIdIs(dbClaim.ExternalId))
	if err != nil {
		return nil, err
	}

	claimParties, err := c.deps.ClaimPartiesWrapper.GetClaimParties(
		ctx, cp.ClaimPartyClaimIdIs(dbClaim.Id.String()))
	if err != nil {
		return nil, err
	}

	claim.Drivers = slice_utils.Map(
		slice_utils.Filter(
			claimParties,
			func(p cp.ClaimParty) bool {
				return strings.ToLower(p.Entity) == driverEntity
			},
		),
		func(p cp.ClaimParty) Driver {
			return Driver{
				FirstName:  pointer_utils.StringValOr(p.FirstName, ""),
				MiddleName: pointer_utils.StringValOr(p.MiddleName, ""),
				LastName:   pointer_utils.StringValOr(p.LastName, ""),
				// TODO: Unit number
			}
		},
	)

	recoveriesByReserveSummaryId, err := c.getNarsRecoveries(ctx, dbClaim, dbReserveSummaries)
	if err != nil {
		return nil, errors.Wrapf(
			err,
			"failed to get recoveries for %s claim with claim number %s",
			dbClaim.Source,
			dbClaim.ClaimNumber,
		)
	}

	// we explicitly set the reserve summaries as a slice, to avoid it being null in template
	claim.ReservesSummaries = make([]ReservesSummary, 0, len(dbReserveSummaries))
	for _, rs := range dbReserveSummaries {
		recoveries, ok := recoveriesByReserveSummaryId[rs.Id.String()]
		if !ok {
			recoveries = &RecoveriesSummary{}
		}
		summary, err := c.newNarsReservesSummary(ctx, rs, recoveries)
		if err != nil {
			return nil, errors.Wrapf(
				err,
				"failed to create reserves summary for %s claim with claim number %s",
				dbClaim.Source,
				dbClaim.ClaimNumber,
			)
		}

		claim.ReservesSummaries = append(claim.ReservesSummaries, *summary)

		// Update the total amounts
		claim.TotalAmounts.Add(summary.Amounts)
	}

	if len(claim.ReservesSummaries) == 0 {
		// If there are no reserve summaries, we want to set all total amounts to zero.
		claim.TotalAmounts = Amounts{
			LossPaid:               decimal.Zero,
			MedicalPaid:            decimal.Zero,
			ExpensesPaid:           decimal.Zero,
			CurrentLossReserve:     decimal.Zero,
			CurrentExpensesReserve: decimal.Zero,
			CurrentMedicalReserve:  decimal.Zero,
			GrossIncurred:          decimal.Zero,
			OtherRecoveries:        pointer_utils.ToPointer(decimal.Zero),
			DeductibleRecoveries:   pointer_utils.ToPointer(decimal.Zero),
			SalvageRecoveries:      pointer_utils.ToPointer(decimal.Zero),
			SubrogationRecoveries:  pointer_utils.ToPointer(decimal.Zero),
		}
	}

	return &claim, nil
}

// getNarsRecoveries will check if the recoveries data is complete in our database for the given
// claim with source NARS. If the data is complete, it will fetch the recoveries postings and
// summarize them for each reserve summary. If the recoveries data is not complete, it will return
// an empty map of ReserveSummaryId -> RecoveriesSummaries.
func (c *Client) getNarsRecoveries(
	ctx context.Context, claim db.Claim, reserveSummaries []cr.ReserveSummary,
) (map[string]*RecoveriesSummary, error) {
	if claim.Source != enums.ClaimsProviderNars {
		return nil, errors.Newf("claim with external id %s is not from NARS", claim.ExternalId)
	}

	isRecoveriesDataComplete, err := c.deps.RecoveriesClient.IsRecoveriesDataComplete(ctx, claim)
	if err != nil {
		return nil, err
	}

	if !isRecoveriesDataComplete {
		return make(map[string]*RecoveriesSummary), nil
	}

	reserveSummaryIdToRecoveriesSummary := slice_utils.ToMap(
		reserveSummaries,
		func(rs cr.ReserveSummary) string {
			return rs.Id.String()
		},
		func(_ cr.ReserveSummary) *RecoveriesSummary {
			return &RecoveriesSummary{
				OtherRecoveries:       pointer_utils.ToPointer(decimal.NewFromInt(0)),
				DeductibleRecoveries:  pointer_utils.ToPointer(decimal.NewFromInt(0)),
				SalvageRecoveries:     pointer_utils.ToPointer(decimal.NewFromInt(0)),
				SubrogationRecoveries: pointer_utils.ToPointer(decimal.NewFromInt(0)),
			}
		},
	)

	options := recoveries_client.GetNarsRecoveriesForClaimOptions{
		UseDbOnly: true,
	}
	recoveriesPostings, err := c.deps.RecoveriesClient.GetNarsRecoveriesForClaim(
		ctx, claim.ExternalId, options,
	)
	if err != nil {
		return nil, errors.Wrapf(
			err,
			"failed to get recoveries for %s claim %s",
			claim.Source,
			claim.ClaimNumber,
		)
	}

	for _, posting := range recoveriesPostings {
		reserveSummaryId := posting.ReserveSummaryId.String()
		recoverySummary, ok := reserveSummaryIdToRecoveriesSummary[reserveSummaryId]
		if !ok {
			// Not finding the reserve summary should not happen, but if it does, we'll just skip
			// that posting for the report.
			log.Warn(
				ctx,
				"reserve summary not found",
				log.Stringer("postingId", posting.Id),
				log.Stringer("reserveSummaryId", posting.ReserveSummaryId),
				log.String("claimExternalId", claim.ExternalId),
			)
			continue
		}

		switch posting.Type {
		// TODO: Confirm rcvy type is correct
		case "Rcvy-Other":
			recoverySummary.OtherRecoveries = addDecimalPointers(
				recoverySummary.OtherRecoveries, &posting.Amount,
			)
		case "Rcvy-Deductible":
			recoverySummary.DeductibleRecoveries = addDecimalPointers(
				recoverySummary.DeductibleRecoveries, &posting.Amount,
			)
		case "Rcvy-Salvage":
			recoverySummary.SalvageRecoveries = addDecimalPointers(
				recoverySummary.SalvageRecoveries, &posting.Amount,
			)
		case "Rcvy-Subrogation":
			recoverySummary.SubrogationRecoveries = addDecimalPointers(
				recoverySummary.SubrogationRecoveries, &posting.Amount,
			)
		default:
			log.Warn(
				ctx,
				"unexpected recovery type",
				log.Stringer("postingId", posting.Id),
				log.String("postingType", posting.Type),
				log.String("claimExternalId", claim.ExternalId),
			)
			recoverySummary.OtherRecoveries = addDecimalPointers(
				recoverySummary.OtherRecoveries, &posting.Amount,
			)
		}
	}

	return reserveSummaryIdToRecoveriesSummary, nil
}
