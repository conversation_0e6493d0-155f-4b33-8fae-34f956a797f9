package client_test

import (
	"context"
	"time"

	cf_db "nirvanatech.com/nirvana/claims/claim_feedbacks/db"
	claim_builder "nirvanatech.com/nirvana/claims/db"
	claim_enums "nirvanatech.com/nirvana/claims/enums"
)

func (c *claimFeedbacksClientTestSuite) TestGetStats() {
	ctx := context.Background()

	stats, err := c.client.GetStats(ctx)
	c.Require().NoError(err)
	c.Equal(0, stats.TotalRatings)
	c.Equal(0.0, stats.AvgRating)
	c.Equal(0, stats.OneToFourStarRatings)
	c.Equal(0, stats.FiveStarRatings)
	c.Equal(0.0, stats.Avg30DaysRating)

	claim, err := claim_builder.New(claim_enums.ClaimsProviderNars).
		WithDefaultMockData().
		Build()
	c.Require().NoError(err)
	c.Require().NoError(c.claimsClient.InsertClaim(ctx, claim))

	feedback, err := cf_db.New(claim_enums.ClaimsProviderNars).
		WithDefaultMockData().
		WithRating(1).
		WithClaimId(claim.Id).
		Build()
	c.Require().NoError(err)
	c.Require().NoError(c.client.Insert(ctx, *feedback))

	feedback2, err := cf_db.New(claim_enums.ClaimsProviderNars).
		WithDefaultMockData().
		WithRating(5).
		WithClaimId(claim.Id).
		Build()
	c.Require().NoError(err)
	c.Require().NoError(c.client.Insert(ctx, *feedback2))

	oldFeedback, err := cf_db.New(claim_enums.ClaimsProviderNars).
		WithDefaultMockData().
		WithRating(3).
		WithClaimId(claim.Id).
		WithCreatedAt(time.Now().AddDate(0, 0, -29)).
		Build()
	c.Require().NoError(err)
	c.Require().NoError(c.client.Insert(ctx, *oldFeedback))

	snapsheetClaim, err := claim_builder.New(claim_enums.ClaimsProviderSnapsheet).
		WithDefaultMockData().
		Build()
	c.Require().NoError(err)
	c.Require().NoError(c.claimsClient.InsertClaim(ctx, snapsheetClaim))

	snapsheetFeedback, err := cf_db.New(claim_enums.ClaimsProviderSnapsheet).
		WithDefaultMockData().
		WithRating(4).
		WithClaimId(snapsheetClaim.Id).
		Build()
	c.Require().NoError(err)
	c.Require().NoError(c.client.Insert(ctx, *snapsheetFeedback))

	stats, err = c.client.GetStats(ctx)
	c.Require().NoError(err)

	c.Equal(3, stats.TotalRatings)
	c.Equal(3.33, stats.AvgRating) // (5 + 1 + 4)/3
	c.Equal(2, stats.OneToFourStarRatings)
	c.Equal(1, stats.FiveStarRatings)
	c.Equal(3.25, stats.Avg30DaysRating) // (5 + 1 + 3 + 4)/3
}
