package client

import (
	"context"

	"github.com/volatiletech/sqlboiler/v4/queries"

	cf_db "nirvanatech.com/nirvana/claims/claim_feedbacks/db"
)

// GetFeedbackStats returns the claim feedback stats for the last 7 days, and the average rating for the last 30 days.
// Stats include: avg rating, avg rating for the last 30 days, number of 1-4 star ratings, number of 5 star ratings, and total ratings.
func (c *Client) GetStats(ctx context.Context) (*cf_db.Stats, error) {
	stats := cf_db.NewStats()

	query := `
		WITH recent_feedbacks AS (
			SELECT rating, created_at
			FROM claims.feedbacks
			WHERE created_at >= NOW() - INTERVAL '7 days'
		),
		last_30_days_feedbacks_avg AS (
			SELECT ROUND(COALESCE(AVG(rating), 0.0), 2)
			FROM claims.feedbacks
			WHERE created_at >= NOW() - INTERVAL '30 days'
		)
		SELECT
			COUNT(*) AS total_ratings,
			ROUND(COALESCE(AVG(rating), 0.0), 2) AS avg_rating,
			(SELECT * FROM last_30_days_feedbacks_avg) AS avg_30_days_rating,
			COALESCE(SUM(CASE WHEN rating > 0 AND rating < 5 THEN 1 ELSE 0 END), 0) AS one_to_four_star_ratings,
			COALESCE(SUM(CASE WHEN rating = 5 THEN 1 ELSE 0 END), 0) AS five_star_ratings
		FROM recent_feedbacks;
	`

	err := queries.Raw(query).Bind(ctx, c.deps.NirvanaRW, stats)
	if err != nil {
		return nil, err
	}
	return stats, nil
}
