package db_test

import (
	"context"
	"database/sql"
	"testing"

	"github.com/benbjo<PERSON>son/clock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/claims/claim_summaries/db"
	claims_client "nirvanatech.com/nirvana/claims/client"
	claims_db "nirvanatech.com/nirvana/claims/db"
	claim_enums "nirvanatech.com/nirvana/claims/enums"
	"nirvanatech.com/nirvana/common-go/test_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	db_api "nirvanatech.com/nirvana/db-api"
	"nirvanatech.com/nirvana/db-api/db_models/claims"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

func Test_InsertManyClaimSummaries(t *testing.T) {
	var env struct {
		fx.In

		Clk          clock.Clock
		DB           db_api.NirvanaRW
		Wrapper      *db.DataWrapper
		ClaimsClient *claims_client.Client
	}
	defer testloader.RequireStart(t, &env).RequireStop()

	ctx := context.Background()

	claim, err := claims_db.New(claim_enums.ClaimsProviderNars).WithDefaultMockData().Build()
	require.NoError(t, err)
	require.NoError(t, env.ClaimsClient.InsertClaim(ctx, claim))

	interval := time_utils.Interval{
		Start: env.Clk.Now().AddDate(0, 0, -7),
		End:   env.Clk.Now(),
	}

	tests := []struct {
		name        string
		summaries   []db.ClaimSummary
		expectErr   bool
		expectedLen int
	}{
		{
			name: "valid claim summary",
			summaries: []db.ClaimSummary{
				*db.NewClaimSummary().
					WithClaimId(claim.Id).
					WithTitle("valid").
					WithInterval(interval).
					WithSummary("valid summary").
					WithSource(claim.Source),
			},
			expectErr:   false,
			expectedLen: 1,
		},
		{
			name: "invalid title",
			summaries: []db.ClaimSummary{
				*db.NewClaimSummary().
					WithClaimId(claim.Id).
					WithTitle("").
					WithInterval(interval).
					WithSummary("no title").
					WithSource(claim.Source),
			},
			expectErr: true,
		},
		{
			name: "invalid summary",
			summaries: []db.ClaimSummary{
				*db.NewClaimSummary().
					WithClaimId(claim.Id).
					WithTitle("invalid summary").
					WithInterval(interval).
					WithSummary("").
					WithSource(claim.Source),
			},
			expectErr: true,
		},
		{
			name: "nil UUID",
			summaries: []db.ClaimSummary{
				*db.NewClaimSummary().
					WithId(uuid.Nil).
					WithTitle("no id").
					WithClaimId(claim.Id).
					WithSource(claim.Source),
			},
			expectErr: true,
		},
		{
			name: "invalid interval",
			summaries: []db.ClaimSummary{
				*db.NewClaimSummary().
					WithClaimId(claim.Id).
					WithTitle("bad interval").
					WithInterval(time_utils.Interval{
						Start: env.Clk.Now().AddDate(1, 0, 0),
						End:   env.Clk.Now(),
					}).
					WithSource(claim.Source),
			},
			expectErr: true,
		},
		{
			name: "mix of valid and invalid interval",
			summaries: []db.ClaimSummary{
				*db.NewClaimSummary().
					WithClaimId(claim.Id).
					WithTitle("valid").
					WithInterval(interval).
					WithSource(claim.Source),
				*db.NewClaimSummary().
					WithClaimId(claim.Id).
					WithTitle("bad interval").
					WithInterval(time_utils.Interval{
						Start: env.Clk.Now().AddDate(1, 0, 0),
						End:   env.Clk.Now(),
					}).
					WithSource(claim.Source),
			},
			expectErr: true,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			err := env.Wrapper.InsertManyClaimSummaries(ctx, tc.summaries)
			if tc.expectErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
			}

			claimSummaries, err := claims.Summaries().All(ctx, env.DB)
			require.NoError(t, err)
			if !tc.expectErr {
				assert.Len(t, claimSummaries, tc.expectedLen)
			}
		})
	}
}

func Test_GetClaimSummaries(t *testing.T) {
	var env struct {
		fx.In

		Clk          clock.Clock
		DB           db_api.NirvanaRW
		ClaimsClient *claims_client.Client
		Wrapper      *db.DataWrapper
	}
	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := context.Background()

	claim, err := claims_db.New(claim_enums.ClaimsProviderNars).WithDefaultMockData().Build()
	require.NoError(t, err)
	require.NoError(t, env.ClaimsClient.InsertClaim(ctx, claim))

	interval := time_utils.Interval{
		Start: env.Clk.Now().AddDate(0, 0, -7),
		End:   env.Clk.Now(),
	}

	claimSummary := db.
		NewClaimSummary().
		WithClaimId(claim.Id).
		WithTitle("summary title for get").
		WithInterval(interval).
		WithSummary("a summary for get").
		WithSource(claim.Source)
	require.NoError(
		t,
		env.Wrapper.InsertManyClaimSummaries(
			ctx,
			[]db.ClaimSummary{*claimSummary},
		),
	)

	claimSummaries, err := env.Wrapper.GetClaimSummaries(
		ctx,
		db.ClaimIdIs(claim.Id),
		// Shouldn't affect if no feedbacks present in the DB
		db.WithFeedbacks(),
	)
	require.NoError(t, err)
	require.Len(t, claimSummaries, 1)
	summary := claimSummaries[0]
	assert.Equal(t, claim.Id.String(), summary.ClaimId.String())

	now := env.Clk.Now()
	superuser := test_utils.Superuser()
	err = env.Wrapper.UpsertFeedback(ctx, db.Feedback{
		Id:             uuid.New(),
		ClaimSummaryId: claimSummary.Id,
		CreatedBy:      superuser.ID,
		Rating:         -1,
		CreatedAt:      now,
		UpdatedAt:      now,
	})
	require.NoError(t, err)

	ctx = authz.WithUser(ctx, superuser)
	claimSummaries, err = env.Wrapper.GetClaimSummaries(
		ctx,
		db.ClaimIdIs(claim.Id),
		// We'll now have a feedback
		db.WithFeedbacks(),
	)
	require.Len(t, claimSummaries, 1)
	summary = claimSummaries[0]
	assert.Equal(t, claim.Id.String(), summary.ClaimId.String())
	require.Len(t, summary.Feedbacks, 1)
	feedback := summary.Feedbacks[0]
	assert.Equal(t, -1, feedback.Rating)

	claimSummaries, err = env.Wrapper.GetClaimSummaries(ctx, db.ClaimIdIs(uuid.Nil))
	require.Len(t, claimSummaries, 0)
}

func Test_UpsertFeedback(t *testing.T) {
	var env struct {
		fx.In

		Clk          clock.Clock
		DB           db_api.NirvanaRW
		ClaimsClient *claims_client.Client
		Wrapper      *db.DataWrapper
	}
	defer testloader.RequireStart(t, &env).RequireStop()

	ctx := context.Background()

	claim, err := claims_db.New(claim_enums.ClaimsProviderNars).WithDefaultMockData().Build()
	require.NoError(t, err)
	require.NoError(t, env.ClaimsClient.InsertClaim(ctx, claim))

	interval := time_utils.Interval{
		Start: env.Clk.Now().AddDate(0, 0, -7),
		End:   env.Clk.Now(),
	}

	claimSummary := db.NewClaimSummary().
		WithClaimId(claim.Id).
		WithTitle("feedback test").
		WithInterval(interval).
		WithSummary("summary with feedback").
		WithSource(claim.Source)

	require.NoError(t, env.Wrapper.InsertManyClaimSummaries(ctx, []db.ClaimSummary{*claimSummary}))

	now := env.Clk.Now()
	feedbackId := uuid.New()
	userId := uuid.New()

	tests := []struct {
		name     string
		feedback db.Feedback
		wantErr  bool
	}{
		{
			name: "invalid rating",
			feedback: db.Feedback{
				Id:             uuid.New(),
				ClaimSummaryId: claimSummary.Id,
				CreatedBy:      uuid.New(),
				Rating:         28,
				CreatedAt:      now,
				UpdatedAt:      now,
			},
			wantErr: true,
		},
		{
			name: "foreign key violation",
			feedback: db.Feedback{
				Id:             uuid.New(),
				ClaimSummaryId: uuid.New(),
				CreatedBy:      uuid.New(),
				Rating:         1,
				CreatedAt:      now,
				UpdatedAt:      now,
			},
			wantErr: true,
		},
		{
			name: "valid insert",
			feedback: db.Feedback{
				Id:             feedbackId,
				ClaimSummaryId: claimSummary.Id,
				CreatedBy:      userId,
				Rating:         1,
				CreatedAt:      now,
				UpdatedAt:      now,
			},
			wantErr: false,
		},
		{
			name: "valid update",
			feedback: db.Feedback{
				Id:             feedbackId,
				ClaimSummaryId: claimSummary.Id,
				CreatedBy:      userId,
				Rating:         -1,
				CreatedAt:      now,
				UpdatedAt:      now,
			},
			wantErr: false,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			err := env.Wrapper.UpsertFeedback(ctx, tc.feedback)
			if tc.wantErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func Test_GetClaimSummary(t *testing.T) {
	var env struct {
		fx.In

		Clk          clock.Clock
		DB           db_api.NirvanaRW
		ClaimsClient *claims_client.Client
		Wrapper      *db.DataWrapper
	}
	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := context.Background()

	claim, err := claims_db.New(claim_enums.ClaimsProviderNars).WithDefaultMockData().Build()
	require.NoError(t, err)
	require.NoError(t, env.ClaimsClient.InsertClaim(ctx, claim))

	interval := time_utils.Interval{
		Start: env.Clk.Now().AddDate(0, 0, -7),
		End:   env.Clk.Now(),
	}
	claimSummary := db.NewClaimSummary().
		WithClaimId(claim.Id).
		WithTitle("get summary").
		WithInterval(interval).
		WithSummary("summary").
		WithSource(claim.Source)

	require.NoError(t, env.Wrapper.InsertManyClaimSummaries(ctx, []db.ClaimSummary{*claimSummary}))

	tests := []struct {
		name    string
		id      uuid.UUID
		wantErr error
	}{
		{
			name:    "existing summary",
			id:      claimSummary.Id,
			wantErr: nil,
		},
		{
			name:    "non-existent summary",
			id:      uuid.Nil,
			wantErr: sql.ErrNoRows,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			got, err := env.Wrapper.GetClaimSummary(ctx, tc.id)
			if tc.wantErr != nil {
				assert.ErrorIs(t, err, tc.wantErr)
			} else {
				require.NoError(t, err)
				assert.Equal(t, claimSummary.Id, got.Id)
			}
		})
	}
}

func Test_GetFeedback(t *testing.T) {
	var env struct {
		fx.In

		Clk          clock.Clock
		DB           db_api.NirvanaRW
		ClaimsClient *claims_client.Client
		Wrapper      *db.DataWrapper
	}
	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := context.Background()

	claim, err := claims_db.New(claim_enums.ClaimsProviderNars).WithDefaultMockData().Build()
	require.NoError(t, err)
	require.NoError(t, env.ClaimsClient.InsertClaim(ctx, claim))
	require.NoError(t, err)

	interval := time_utils.Interval{
		Start: env.Clk.Now().AddDate(0, 0, -7),
		End:   env.Clk.Now(),
	}

	claimSummary := db.
		NewClaimSummary().
		WithClaimId(claim.Id).
		WithTitle("summary title for get feedback").
		WithInterval(interval).
		WithSummary("a summary for get feedback").
		WithSource(claim.Source)
	require.NoError(
		t,
		env.Wrapper.InsertManyClaimSummaries(
			ctx,
			[]db.ClaimSummary{*claimSummary},
		),
	)

	now := env.Clk.Now()
	feedbackId := uuid.New()
	userId := uuid.New()
	err = env.Wrapper.UpsertFeedback(ctx, db.Feedback{
		Id:             feedbackId,
		ClaimSummaryId: claimSummary.Id,
		CreatedBy:      userId,
		Rating:         1,
		CreatedAt:      now,
		UpdatedAt:      now,
	})
	require.NoError(t, err)

	feedback, err := env.Wrapper.GetFeedback(ctx, claimSummary.Id, userId)
	require.NoError(t, err)
	assert.Equal(t, feedbackId.String(), feedback.Id.String())
	assert.Equal(t, claimSummary.Id.String(), feedback.ClaimSummaryId.String())
	assert.Equal(t, userId.String(), feedback.CreatedBy.String())
	assert.Equal(t, 1, feedback.Rating)
}
