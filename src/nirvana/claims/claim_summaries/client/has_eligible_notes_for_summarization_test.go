package client_test

import (
	"context"
	"time"

	"github.com/google/uuid"

	claim_enums "nirvanatech.com/nirvana/claims/enums"
	"nirvanatech.com/nirvana/common-go/time_utils"
)

func (s *claimSummariesClientTestSuite) TestHasEligibleNotesForSummarization() {
	ctx := context.Background()
	p := s.seedPolicy(ctx, "0011101", s.dotNumber)
	claimWithNotesNars := s.seedClaim(ctx, p.PolicyNumber.String(), 4, claim_enums.ClaimStatusOpen, claim_enums.ClaimsProviderNars)
	claimWithoutNotesNars := s.seedClaim(ctx, p.PolicyNumber.String(), 6, claim_enums.ClaimStatusOpen, claim_enums.ClaimsProviderNars)
	claimWithoutNotesSnapsheet := s.seedClaim(ctx, p.PolicyNumber.String(), 8532, claim_enums.ClaimStatusOpen, claim_enums.ClaimsProviderSnapsheet)

	now := s.deps.Clk.Now()
	s.seedClaimNotes(
		ctx,
		claimWithNotesNars.ExternalId,
		now,
		"We received a letter from the insured's lawyer, claiming it was the other vehicle's fault.",
		claim_enums.ClaimsProviderNars,
	)
	s.seedClaimNotes(
		ctx,
		claimWithNotesNars.ExternalId,
		time_utils.NewDate(2022, time.April, 5).ToTime(),
		"We received a letter from the insured's lawyer, claiming it was the other vehicle's fault.",
		claim_enums.ClaimsProviderNars,
	)
	s.seedClaimNotes(
		ctx,
		claimWithNotesNars.ExternalId,
		time_utils.NewDate(2023, time.January, 6).ToTime(),
		"We received a letter from the insured's lawyer, claiming it was the other vehicle's fault.",
		claim_enums.ClaimsProviderNars,
	)
	s.seedClaimNotes(
		ctx,
		claimWithNotesNars.ExternalId,
		now.AddDate(0, -1, -1),
		"We received a letter from the insured's lawyer, claiming it was the other vehicle's fault.",
		claim_enums.ClaimsProviderNars,
	)

	testCases := []struct {
		name                 string
		claimId              uuid.UUID
		expectedSummarizable bool
		wantErr              bool
	}{
		{
			name:                 "Nonexistent claim should throw error",
			claimId:              uuid.New(),
			expectedSummarizable: false,
			wantErr:              true,
		},
		{
			name:                 "Claim from Narswithout eligible notes should say false",
			claimId:              claimWithoutNotesNars.Id,
			expectedSummarizable: false,
			wantErr:              false,
		},
		{
			name:                 "Claim from Nars with notes should say true",
			claimId:              claimWithNotesNars.Id,
			expectedSummarizable: true,
			wantErr:              false,
		},
		{
			name:                 "Claim from Snapsheet without notes should say false",
			claimId:              claimWithoutNotesSnapsheet.Id,
			expectedSummarizable: false,
			wantErr:              false,
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			ctx := context.Background()

			summarizable, err := s.deps.Client.HasEligibleNotesForSummarization(ctx, tc.claimId, now)
			if tc.wantErr {
				s.Require().Error(err)
				return
			}
			s.Require().NoError(err)

			s.Equal(tc.expectedSummarizable, summarizable)
		})
	}
}
