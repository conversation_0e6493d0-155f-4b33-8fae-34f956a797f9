package config

import (
	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/map_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"

	"nirvanatech.com/nirvana/telematics"
)

// dataProviderTspConfig links TSPs to their respective configurations.
//
//nolint:exhaustive
var dataProviderTspConfig = map[telematics.DataProvider]map[telematics.TSP]*DefaultDataPipelineConfig{
	telematics.DataProviderTerminal: {
		// PremiumTSPConfig
		telematics.TSPGeotab:                  &premiumTSPConfig,
		telematics.TSPBigRoad:                 &premiumTSPConfig,
		telematics.TSPFleetComplete:           &premiumTSPConfig,
		telematics.TSPATAndTFleetComplete:     &premiumTSPConfig,
		telematics.TSPZonar:                   &premiumTSPConfig,
		telematics.TSPTTELD:                   &premiumTSPConfig,
		telematics.TSPEROAD:                   &premiumTSPConfig,
		telematics.TSPGoFleet:                 &premiumTSPConfig,
		telematics.TSPGoGPS:                   &premiumTSPConfig,
		telematics.TSPTransflo:                &premiumTSPConfig,
		telematics.TSPATAndTFleet:             &premiumTSPConfig,
		telematics.TSPEnVueTelematics:         &premiumTSPConfig,
		telematics.TSPFleetistics:             &premiumTSPConfig,
		telematics.TSPEVOELD:                  &premiumTSPConfig,
		telematics.TSPZippyELD:                &premiumTSPConfig,
		telematics.TSPOntimeELD:               &premiumTSPConfig,
		telematics.TSPAdvantageOne:            &premiumTSPConfig,
		telematics.TSPFleetProfitCenter:       &premiumTSPConfig,
		telematics.TSPBlueArrowTelematics:     &premiumTSPConfig,
		telematics.TSPGridline:                &premiumTSPConfig,
		telematics.TSPBlueInkTechnology:       &premiumTSPConfig,
		telematics.TSPIntellishift:            &premiumTSPConfig,
		telematics.TSPOneStepGPS:              &premiumTSPConfig,
		telematics.TSPIoTab:                   &premiumTSPConfig,
		telematics.TSPArgosConnectedSolutions: &premiumTSPConfig,
		telematics.TSPHighPointGPS:            &premiumTSPConfig,
		telematics.TSPAttriX:                  &premiumTSPConfig,
		telematics.TSPEagleWireless:           &premiumTSPConfig,
		telematics.TSPGrayboxSolutions:        &premiumTSPConfig,
		telematics.TSPNoorELD:                 &premiumTSPConfig,
		telematics.TSPATELD:                   &premiumTSPConfig,
		telematics.TSPFirstELD:                &premiumTSPConfig,
		telematics.TSPMGKELD:                  &premiumTSPConfig,
		telematics.TSPOrientELD:               &premiumTSPConfig,

		// nonPremiumTSP180Config
		telematics.TSPContiGO:           &nonPremiumTSP180Config,
		telematics.TSPGPSTab:            &nonPremiumTSP180Config,
		telematics.TSPMasterELD:         &nonPremiumTSP180Config,
		telematics.TSPKSKELD:            &nonPremiumTSP180Config,
		telematics.TSPMountainELD:       &nonPremiumTSP180Config,
		telematics.TSPRightTruckingELD:  &nonPremiumTSP180Config,
		telematics.TSPRoadStarELD:       &nonPremiumTSP180Config,
		telematics.TSPTFMELD:            &nonPremiumTSP180Config,
		telematics.TSPFMELD:             &nonPremiumTSP180Config,
		telematics.TSPIDELD:             &nonPremiumTSP180Config,
		telematics.TSPApexUltima:        &nonPremiumTSP180Config,
		telematics.TSPBlueHorseELD:      &nonPremiumTSP180Config,
		telematics.TSPELDBooks:          &nonPremiumTSP180Config,
		telematics.TSPELDOne:            &nonPremiumTSP180Config,
		telematics.TSPLightAndTravelELD: &nonPremiumTSP180Config,
		telematics.TSPMaxELD:            &nonPremiumTSP180Config,
		telematics.TSPMonarchGPS:        &nonPremiumTSP180Config,
		telematics.TSPRadicalELD:        &nonPremiumTSP180Config,
		telematics.TSPMotionELD:         &nonPremiumTSP180Config,
		telematics.TSPSmartelds:         &nonPremiumTSP180Config,
		telematics.TSPVulcansols:        &nonPremiumTSP180Config,
		telematics.TSPOmnitracs:         &nonPremiumTSP180Config,
		telematics.TSPOmnitracsXRS:      &nonPremiumTSP180Config,
		telematics.TSPOmnitracsES:       &nonPremiumTSP180Config,

		// nonPremiumTSP365Config
		telematics.TSPVerizonConnect:       &nonPremiumTSP365Config,
		telematics.TSPVerizonConnectReveal: &nonPremiumTSP365Config,
		telematics.TSPVerizonConnectFleet:  &nonPremiumTSP365Config,
		telematics.TSPRealELD:              &nonPremiumTSP365Config,
		telematics.TSPAgilisLinxup:         &nonPremiumTSP365Config,
		telematics.TSPClearPathGPS:         &nonPremiumTSP365Config,
		telematics.TSPGPSInsight:           &nonPremiumTSP365Config,
		telematics.TSPNetradyneInc:         &nonPremiumTSP365Config,
		telematics.TSPAirELD:               &nonPremiumTSP365Config,
	},
	telematics.DataProviderNative: {
		telematics.TSPSamsara:           &samsaraConfig,
		telematics.TSPKeepTruckin:       &keeptruckInConfig,
		telematics.TSPSamsaraSafety:     &samsaraConfig,
		telematics.TSPKeepTruckinSafety: &keeptruckInConfig,
	},
	telematics.DataProviderSmartDrive: {
		telematics.TSPSmartDrive: &smartDriveConfig,
	},
}

// GetConfigForDataProviderTSP retrieves the final configuration for a given TSP and data provider.
// Returns an error if the dataProviderTSP is not supported.
func GetConfigForDataProviderTSP(
	dataProviderTSP telematics.DataProviderTSP,
) (*DefaultDataPipelineConfig, error) {
	// Return base test config for mock TSP
	if dataProviderTSP.TSP == telematics.TSPMock {
		return &baseTestConfig, nil
	}

	// Check if the data provider is supported
	tspConfigMap, providerSupported := dataProviderTspConfig[dataProviderTSP.DataProvider]
	if !providerSupported {
		return nil, errors.Newf(
			"%s data provider is not supported by data platform",
			dataProviderTSP.DataProvider.String(),
		)
	}

	// Check if the TSP is supported for the given data provider
	dataProviderTSPConfig, tspSupported := tspConfigMap[dataProviderTSP.TSP]
	if !tspSupported {
		return nil, errors.Newf(
			"%s dataprovider, tsp is not supported by data platform",
			dataProviderTSP,
		)
	}

	// Return the DataProviderTSP config if found
	return dataProviderTSPConfig, nil
}

// GetAllSupportedDataProviderTSP returns a slice of all supported DataProvider-TSP
func GetAllSupportedDataProviderTSP() []telematics.DataProviderTSP {
	var supportedDataProviderTSPs []telematics.DataProviderTSP
	for dataProvider, tspConfig := range dataProviderTspConfig {
		supportedDataProviderTSPs = append(
			supportedDataProviderTSPs,
			slice_utils.Map(
				map_utils.Keys(tspConfig),
				func(tsp telematics.TSP) telematics.DataProviderTSP {
					return telematics.NewDataProviderTSP(dataProvider, tsp)
				},
			)...)
	}
	return supportedDataProviderTSPs
}
