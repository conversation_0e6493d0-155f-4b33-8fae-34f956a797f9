package internal

import (
	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/telematics/data_platform"
)

//go:generate go run github.com/dmarkham/enumer -type=DataType -trimprefix=DataType
type DataType int

const (
	DataTypeGPS DataType = iota
	DataTypeHarshBraking
	DataTypeHarshAcceleration
)

func convertStatKindToDataType(statKind data_platform.NormalizedVehicleStat) (DataType, error) {
	switch statKind {
	case data_platform.NormalizedVehicleStatGPSLogWithGeography:
		return DataTypeGPS, nil
	case data_platform.NormalizedVehicleStatHarshBrakingEventLog:
		return DataTypeHarshBraking, nil
	case data_platform.NormalizedVehicleStatHarshAccelerationEventLog:
		return DataTypeHarshAcceleration, nil
	default:
		return 0, errors.Newf("unsupported stat kind %s", statKind.String())
	}
}

// FileInfo contains information necessary to copy a file to CMT.
type FileInfo struct {
	VehicleId  string
	VIN        data_platform.VIN
	StatKind   DataType
	S3FilePath string
	SeqId      int
}

// MetadataInfo contains vin and its corresponding NHTSA marshalled info
type MetadataInfo struct {
	VIN  data_platform.VIN
	Data *VehicleMetadata
}

// CMT constraints on certain vehicle attributes
const (
	minModelYear      = 1886
	minDisplacementCC = 0
	minCylinders      = 1
	minAxles          = 1
	metadataFileName  = "vehicle_metadata.json"
)

// VehicleMetadata is the golang type for CMT requested json metadata. Structuring is exactly according to CMT
// specified format, including those in children fields.
type VehicleMetadata struct {
	Braking        *BrakingObject           `json:"braking"`
	DataCollection *DataCollectionObject    `json:"data_collection,omitempty"`
	Identification *IdentificationObject    `json:"identification"`
	Engine         *EngineObject            `json:"engine"`
	Attributes     *VehicleAttributesObject `json:"attributes"`
}

type BrakingObject struct {
	BrakeSystemType string `json:"brake_system_type,omitempty"`
}

type DataCollectionObject struct {
	TSPName string `json:"tsp_name,omitempty"`
}

type IdentificationObject struct {
	Make      string `json:"make,omitempty"`
	Model     string `json:"model,omitempty"`
	ModelYear *int   `json:"model_year,omitempty"` // minimum 1886
	Series    string `json:"series,omitempty"`
}

type EngineObject struct {
	Model               string   `json:"model,omitempty"`
	DisplacementCC      *float64 `json:"displacement_cc,omitempty"` // minimum zero
	Cylinders           *int     `json:"cylinders,omitempty"`       // minimum one
	FuelType            string   `json:"fuel_type,omitempty"`
	EngineConfiguration string   `json:"engine_configuration,omitempty"`
}

type VehicleAttributesObject struct {
	Type              string `json:"type,omitempty"`
	BodyClass         string `json:"body_class,omitempty"`
	Axles             *int   `json:"axles,omitempty"` // minimum one
	AxleConfiguration string `json:"axle_configuration,omitempty"`
	DriveType         string `json:"drive_type,omitempty"`
	GVWR              string `json:"gvwr,omitempty"`
}
