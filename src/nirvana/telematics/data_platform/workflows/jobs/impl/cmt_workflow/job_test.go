package cmt_workflow

import (
	"bytes"
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"
	"github.com/benb<PERSON><PERSON><PERSON>/clock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"github.com/volatiletech/null/v8"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/common-go/map_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/data_infra_jobber"
	"nirvanatech.com/nirvana/db-api/db_wrappers/telematics"
	cmt_scoring_wrapper "nirvanatech.com/nirvana/db-api/db_wrappers/telematics/cmt"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/cmt_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/jobber/jtypes"
	"nirvanatech.com/nirvana/jobber/registry"
	"nirvanatech.com/nirvana/telematics/data_platform"
	"nirvanatech.com/nirvana/telematics/data_platform/workflows/jobs"
)

// A simple smoke test to simulate the entire workflow. We set up some files in normalized file metadata table, along
// with entities,etc. We then trigger the CMT workflow and lastly verify that the handle id is scored.
// Other intricate details have been tested in smaller util functions, this smoke test is only to check that things
// are running properly end to end.
func TestSmoke(t *testing.T) {
	var env struct {
		fx.In
		Jobber   data_infra_jobber.Client
		Deps     CMTDeps
		Registry *data_infra_jobber.Registry
		Tf       *cmt_fixture.Cmt
		Clk      *clock.Mock
	}
	defer testloader.RequireStart(t, &env).RequireStop()
	env.Tf.RegisterGoldenDataset()

	ctx := context.Background()
	telematicsBucket := sourceBucket
	handleId := uuid.New()
	pipelineId := uuid.New()
	testInterval := time_utils.Interval{
		Start: time_utils.DateUTC(2025, 2, 1),
		End:   time_utils.DateUTC(2025, 4, 1),
	}
	isoWeeks := time_utils.GetISOWeekInterval(testInterval).GetISOWeekList()

	// 1. Upload some sample files to S3 using s3 client in s3_utils. These can be dummy files as they
	// will not be read anywhere. They are just for copy purposes.
	// 2. Insert respective rows for the files (with stats, num rows, vehicle id, etc) into normalized metadata table.
	// 3. Also insert those vehicles with sample VINs using entities wrapper.
	vehiclesToVin := map[string]string{
		"vehicle_id_1": "VIN1",
		"vehicle_id_2": "VIN2",
		"vehicle_id_3": "VIN3",
	}
	for vehicleId, vin := range vehiclesToVin {
		for _, week := range isoWeeks {
			filePath := fmt.Sprintf("%s/%s/data.csv", vin, week)
			_, err := env.Deps.S3Client.UploadWithContext(ctx, &s3manager.UploadInput{
				Body:   bytes.NewReader([]byte("testdata")),
				Bucket: aws.String(telematicsBucket),
				Key:    aws.String(filePath),
			})
			require.NoError(t, err)
			require.NoError(t, env.Deps.NormalizedFileMetaWrapper.StoreMetadata(ctx,
				&telematics.NormalizedFileMetadatum{
					HandleId:            handleId,
					VehicleId:           vehicleId,
					StatKind:            data_platform.NormalizedVehicleStatGPSLogWithGeography,
					StatVersion:         null.IntFrom(0),
					Interval:            week.GetInterval(time.UTC),
					CreatedByPipelineId: pipelineId.String(),
					NumDataRows:         3,
					FileFormat:          "csv",
					S3Path:              filePath,
				}),
			)
		}
	}
	require.NoError(t, env.Deps.EntitiesWrapper.UpsertVehicles(
		ctx,
		slice_utils.Map(map_utils.Keys(vehiclesToVin), func(vehicle string) data_platform.Vehicle {
			return data_platform.Vehicle{
				HandleId: handleId,
				TspId:    vehicle,
				VIN:      data_platform.VIN(vehiclesToVin[vehicle]),
			}
		},
		)...,
	))

	// 4. Finally, trigger the CMT workflow on the interval for the handle id.
	cmtJob, err := NewJob(env.Deps)
	require.NoError(t, err)
	require.NoError(t, registry.AddJob(env.Registry, cmtJob))
	jrId, err := env.Jobber.AddJobRun(ctx, jtypes.NewAddJobRunParams(
		jobs.CMTWorkflow,
		&jobs.CMTWorkflowMessage{
			HandleID: handleId,
			Interval: testInterval,
			TSP:      "TSPMock",
		},
		jtypes.NewMetadata(jtypes.Immediate),
	))
	require.NoError(t, err)

	// we need to increment our mock clock continuously as our CMT state machine's polling logic is dependent on the
	// ticker which polls in 30s intervals. CMT mock API returns a success status after the 5th poll.
	doneChan := make(chan struct{})
	defer close(doneChan)
	go func() {
		for {
			select {
			case <-doneChan:
				return
			default:
				env.Clk.Add(1 * time.Minute)
			}
		}
	}()
	require.NoError(t, env.Jobber.WaitForJobRunCompletion(ctx, jrId))
	jRun, err := env.Jobber.GetJobRun(ctx, jrId)
	require.NoError(t, err)
	require.Equal(t, jtypes.JobRunStatusSucceeded, jRun.Status)

	// 5. Verify that the handleId is now upserted with Scoring Status as Scored
	rows, err := env.Deps.ScoringWrapper.GetScoringDetails(ctx, cmt_scoring_wrapper.WhereHandleID(handleId))
	require.NoError(t, err)
	require.Len(t, rows, 1)
	require.Equal(t, handleId, rows[0].HandleID)
	require.Equal(t, cmt_scoring_wrapper.ScoringStateScored, rows[0].ScoringState)
}
