load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "cmt_workflow",
    srcs = [
        "copier.go",
        "fx.go",
        "job.go",
        "persist_cmt_scores.go",
        "trigger_cmt_scoring.go",
        "trigger_cmt_upload.go",
    ],
    importpath = "nirvanatech.com/nirvana/telematics/data_platform/workflows/jobs/impl/cmt_workflow",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/errgroup",
        "//nirvana/common-go/log",
        "//nirvana/common-go/s3_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/db-api/db_wrappers/telematics",
        "//nirvana/db-api/db_wrappers/telematics/cmt",
        "//nirvana/distsem",
        "//nirvana/external_data_management/nhtsa",
        "//nirvana/infra/config",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/jobber/job_utils",
        "//nirvana/jobber/job_utils/jobber_distsem",
        "//nirvana/jobber/jtypes",
        "//nirvana/telematics",
        "//nirvana/telematics/data_platform",
        "//nirvana/telematics/data_platform/workflows/common",
        "//nirvana/telematics/data_platform/workflows/jobs",
        "//nirvana/telematics/data_platform/workflows/jobs/impl/cmt_workflow/internal",
        "//nirvana/telematics/integrations/cmt",
        "//nirvana/telematics/integrations/cmt/encryption_utils",
        "//nirvana/telematics/integrations/cmt/score_utils",
        "@com_github_aws_aws_sdk_go//aws",
        "@com_github_aws_aws_sdk_go//service/s3",
        "@com_github_aws_aws_sdk_go//service/s3/s3manager",
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_cactus_go_statsd_client_v5//statsd",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "cmt_workflow_test",
    srcs = [
        "copier_test.go",
        "job_test.go",
        "persist_cmt_scores_test.go",
    ],
    embed = [":cmt_workflow"],
    deps = [
        "//nirvana/common-go/map_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/data_infra_jobber",
        "//nirvana/db-api/db_wrappers/telematics",
        "//nirvana/db-api/db_wrappers/telematics/cmt",
        "//nirvana/infra/fx/testfixtures/cmt_fixture",
        "//nirvana/infra/fx/testloader",
        "//nirvana/jobber/jtypes",
        "//nirvana/jobber/registry",
        "//nirvana/telematics/data_platform",
        "//nirvana/telematics/data_platform/workflows/jobs",
        "//nirvana/telematics/data_platform/workflows/jobs/impl/cmt_workflow/internal",
        "//nirvana/telematics/integrations/cmt/score_utils",
        "@com_github_aws_aws_sdk_go//aws",
        "@com_github_aws_aws_sdk_go//service/s3/s3manager",
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//require",
        "@com_github_volatiletech_null_v8//:null",
        "@org_uber_go_fx//:fx",
    ],
)
