package cmt_workflow

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"
	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/common-go/errgroup"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/s3_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	telematics_wrapper "nirvanatech.com/nirvana/db-api/db_wrappers/telematics"
	cmt_scoring_wrapper "nirvanatech.com/nirvana/db-api/db_wrappers/telematics/cmt"
	"nirvanatech.com/nirvana/external_data_management/nhtsa"
	"nirvanatech.com/nirvana/infra/config"
	"nirvanatech.com/nirvana/jobber/job_utils"
	"nirvanatech.com/nirvana/jobber/jtypes"
	"nirvanatech.com/nirvana/telematics"
	"nirvanatech.com/nirvana/telematics/data_platform"
	"nirvanatech.com/nirvana/telematics/data_platform/workflows/common"
	"nirvanatech.com/nirvana/telematics/data_platform/workflows/jobs"
	"nirvanatech.com/nirvana/telematics/data_platform/workflows/jobs/impl/cmt_workflow/internal"
	"nirvanatech.com/nirvana/telematics/integrations/cmt/encryption_utils"
)

const (
	copierTaskId         = "DataCopierTask"
	copyOrUploadRoutines = 100
	sourceBucket         = "nirvana-telematics-default"
)

type (
	copyHandlerFunc               = func(ctx context.Context, info internal.FileInfo) error
	uploadHandlerFunc             = func(ctx context.Context, metadata internal.MetadataInfo) error
	copyUploadSuccessCallbackFunc = func(ctx context.Context, handleId uuid.UUID, interval time_utils.Interval) error
	spanFinderFunc                = func(ctx context.Context, handleId uuid.UUID) (time_utils.Interval, error)
)

type s3CopierTask struct {
	cfg                       *config.Config
	s3Client                  s3_utils.Client
	vinEncryptor              *encryption_utils.VinParser
	normalizedFileMetaWrapper *telematics_wrapper.NormFileMetaWrapper
	entitiesWrapper           *telematics_wrapper.EntitiesWrapper
	cmtScoringWrapper         *cmt_scoring_wrapper.ScoringWrapper
	nhtsaClient               nhtsa.CmtClient
	common.Retryable[*jobs.CMTWorkflowMessage]
	job_utils.NoopUndoTask[*jobs.CMTWorkflowMessage]
}

func (task *s3CopierTask) ID() string {
	return copierTaskId
}

func (task *s3CopierTask) Run(ctx jtypes.Context, message *jobs.CMTWorkflowMessage) error {
	handleId := message.HandleID
	tsp, err := telematics.TSPString(message.TSP)
	if err != nil {
		return errors.Wrapf(err, "unsupported tsp received %v", message.TSP)
	}
	logCtx := log.ContextWithFields(ctx, log.Stringer("handleId", handleId))
	// find intervals to copy
	intervals, err := findUncopiedIntervals(logCtx, handleId, message.Interval, task.cmtScoringWrapper.FindSpan)
	if err != nil {
		return errors.Wrap(err, "failed to find uncopied intervals")
	}
	copyBucket := task.cfg.GetTelematics().GetIntegrations().GetCmt().GetInputBucket()
	for idx, interval := range intervals {
		if !interval.IsValid() {
			continue // necessary logging is done in findUncopiedIntervals
		}
		// generate file details necessary for copy
		fileDetails, metadata, err := internal.GenerateFileAndMetadataDetails(
			ctx,
			task.normalizedFileMetaWrapper,
			task.entitiesWrapper,
			task.nhtsaClient,
			handleId, interval, tsp.Trimmed(),
		)
		if err != nil {
			return errors.Wrapf(err, "failed to generate vehicle details for interval %v", interval)
		}
		encryptedFileInfo, encryptedMetadataInfo, err := encryptVinsInFileDetails(
			task.vinEncryptor, handleId.String(), fileDetails, metadata,
		)
		if err != nil {
			return errors.Wrapf(err, "failed to encrypt vehicle details for interval %v", interval)
		}
		copyPrefix := fmt.Sprintf("%s-%d", ctx.GetJobRunId().String(), idx+1)
		err = processDataCopyAndUpload(
			logCtx, handleId, interval, encryptedFileInfo, encryptedMetadataInfo,
			s3CopierFn(task.s3Client, copyPrefix, copyBucket, sourceBucket),
			s3UploaderFn(task.s3Client, copyPrefix, copyBucket),
			dbPersistSuccessCallback(
				task.cmtScoringWrapper, internal.ToS3URI(copyBucket, copyPrefix), ctx.GetJobRunId(),
			),
		)
		if err != nil {
			return err
		}
	}
	return nil
}

// encryptVinsInFileDetails encrypts the VINs in received fileInfo and metadataInfo. This function is a simple
// VIN converter, and encryption is already well tested
func encryptVinsInFileDetails(
	vinEncryptor *encryption_utils.VinParser,
	handleId string,
	fileInfo []internal.FileInfo,
	metadataInfo []internal.MetadataInfo,
) ([]internal.FileInfo, []internal.MetadataInfo, error) {
	// cache encrypted vins
	vinToEncrypted := make(map[data_platform.VIN]data_platform.VIN)
	var encryptedFileInfo []internal.FileInfo
	var encryptedMetadataInfo []internal.MetadataInfo
	for _, metadata := range metadataInfo {
		encryptedVin, ok := vinToEncrypted[metadata.VIN]
		if !ok {
			enc, err := vinEncryptor.EncryptVIN(handleId, string(metadata.VIN))
			if err != nil {
				return nil, nil, errors.Wrapf(err, "failed to encrypt VIN %v", metadata.VIN)
			}
			encryptedVin = data_platform.VIN(enc)
			vinToEncrypted[metadata.VIN] = encryptedVin
		}
		metadata.VIN = encryptedVin
		encryptedMetadataInfo = append(encryptedMetadataInfo, metadata)
	}

	for _, file := range fileInfo {
		encryptedVin, ok := vinToEncrypted[file.VIN]
		if !ok {
			enc, err := vinEncryptor.EncryptVIN(handleId, string(file.VIN))
			if err != nil {
				return nil, nil, errors.Wrapf(err, "failed to encrypt VIN %v", file.VIN)
			}
			encryptedVin = data_platform.VIN(enc)
			vinToEncrypted[file.VIN] = encryptedVin
		}
		file.VIN = encryptedVin
		encryptedFileInfo = append(encryptedFileInfo, file)
	}
	return encryptedFileInfo, encryptedMetadataInfo, nil
}

// findUncopiedIntervals finds the intervals that needs to be copied for the given handle id, by performing
// necessary truncations and validations using spanFinderFunc.
func findUncopiedIntervals(
	ctx context.Context,
	handleId uuid.UUID,
	originalInterval time_utils.Interval,
	spanFinder spanFinderFunc,
) ([]time_utils.Interval, error) {
	var uncopiedIntervals []time_utils.Interval
	interval := originalInterval
	if !interval.IsValid() || interval.Start.Equal(interval.End) {
		log.Info(ctx, "invalid interval", log.Any("requestedInterval", originalInterval))
		return uncopiedIntervals, nil
	}
	// Find data interval to copy
	spanInterval, err := spanFinder(ctx, handleId)
	if err != nil {
		return uncopiedIntervals, errors.Wrap(err, "failed to find span interval")
	}

	if !spanInterval.IsValid() {
		// no valid spanInterval means this is the first entry, we can directly return the
		// initial interval after truncation
		interval.End = time_utils.StartOfMonthFor(interval.End.Add(time.Nanosecond))
		if !interval.IsValid() || interval.Start.Equal(interval.End) {
			log.Info(ctx, "invalid interval", log.Any("requestedInterval", originalInterval))
			return uncopiedIntervals, nil
		}
		uncopiedIntervals = append(uncopiedIntervals, interval)
		return uncopiedIntervals, nil
	}

	// span interval exists, hence there can be atmost two intervals that are uncopied. One at the start, that we refer
	// to as backfill interval, and one at the end, that is the incremental interval
	if interval.Start.Before(spanInterval.Start) {
		// calculate backfill interval. Interval is not rounded off yet to check for backfill accurately.
		if interval.End.Before(spanInterval.Start) {
			// data hole found
			log.Error(ctx, "Data hole found for backfill",
				log.Any("requestedInterval", interval),
				log.Any("spanInterval", spanInterval),
			)
			return uncopiedIntervals, errors.Newf(
				"data hole found for backfill, span interval: %v, requested interval: %v",
				spanInterval, interval,
			)
		}
		backfillInterval := time_utils.Interval{
			Start: interval.Start,
			End:   time_utils.EndOfMonthFor(spanInterval.Start.Add(-time.Nanosecond)).Add(time.Nanosecond),
			// we need the end as ceil of span interval's start (rounded off to month), hence we subtract a nanosecond
			// from span interval's start, find its end of month, and then add the nanosecond back
		}
		uncopiedIntervals = append(uncopiedIntervals, backfillInterval)
	}

	// we can now truncate the interval for incremental interval calculation
	interval.End = time_utils.StartOfMonthFor(interval.End.Add(time.Nanosecond))
	if interval.End.After(spanInterval.End) {
		// incremental interval
		if interval.Start.After(spanInterval.End) {
			// data hole found
			log.Error(ctx, "Data hole found for incremental",
				log.Any("requestedInterval", interval),
				log.Any("spanInterval", spanInterval),
			)
			return uncopiedIntervals, errors.Newf(
				"data hole found for incremental, span interval: %v, requested interval: %v",
				spanInterval, interval,
			)
		}
		incrementalInterval := time_utils.Interval{
			Start: spanInterval.End,
			End:   interval.End,
		}
		uncopiedIntervals = append(uncopiedIntervals, incrementalInterval)
	}
	return uncopiedIntervals, nil
}

// processDataCopyAndUpload processed data copies in parallel using copyHandlerFunc. Upon successful completion of all
// copies, it invokes the copySuccessCallbackFunc.
func processDataCopyAndUpload(
	ctx context.Context,
	handleId uuid.UUID,
	interval time_utils.Interval,
	fileInfos []internal.FileInfo,
	metadataInfos []internal.MetadataInfo,
	copyHandler copyHandlerFunc,
	uploadHandler uploadHandlerFunc,
	copyUploadSuccessCallback copyUploadSuccessCallbackFunc,
) error {
	if len(fileInfos) == 0 && len(metadataInfos) == 0 {
		return nil
	}
	copyOrUploadChan := make(chan any, copyOrUploadRoutines)

	copyOrUploadWorker := func(ctx context.Context) error {
		for {
			select {
			case <-ctx.Done():
				return ctx.Err()
			case data, ok := <-copyOrUploadChan:
				if !ok { // channel closed and empty
					return nil
				}
				switch data := data.(type) {
				case internal.FileInfo:
					err := copyHandler(ctx, data)
					if err != nil {
						log.Error(ctx, "failed to copy file", log.Err(err), log.Any("fileInfo", data))
						return err
					}
				case internal.MetadataInfo:
					err := uploadHandler(ctx, data)
					if err != nil {
						log.Error(ctx, "failed to upload file", log.Err(err), log.Any("metaInfo", data))
						return err
					}
				default:
					log.Error(ctx, "unknown data type received", log.Any("data", data))
					return errors.New("unknown data type")
				}
			}
		}
	}
	sender := func(ctx context.Context) error {
		defer close(copyOrUploadChan)
		for _, fileInfo := range fileInfos {
			select {
			case <-ctx.Done():
				return ctx.Err()
			case copyOrUploadChan <- fileInfo:
			}
		}
		for _, metadataInfo := range metadataInfos {
			select {
			case <-ctx.Done():
				return ctx.Err()
			case copyOrUploadChan <- metadataInfo:
			}
		}
		return nil
	}
	g, groupCtx := errgroup.WithContext(ctx)
	// start sender routine
	g.Go(func() error {
		return sender(groupCtx)
	})
	// start worker routines
	for i := 0; i < copyOrUploadRoutines; i++ {
		g.Go(func() error {
			return copyOrUploadWorker(groupCtx)
		})
	}
	// wait for all routines to finish
	if err := g.Wait(); err != nil {
		return errors.Wrap(err, "failed to copy or upload files for vehicles")
	}
	log.Info(ctx, "Successfully copied and uploaded all files", log.Any("interval", interval))
	if err := copyUploadSuccessCallback(ctx, handleId, interval); err != nil {
		return errors.Wrap(err, "failed to execute copy-upload success callback")
	}
	log.Info(ctx, "Successfully executed copy-upload success callback", log.Any("interval", interval))
	return nil
}

// s3CopierFn returns a copyHandlerFunc to copies data from Nirvana S3 to CMT S3. We do not need to test this as
// s3 utils and cmt internal utils are well tested.
func s3CopierFn(s3Client s3_utils.Client, prefix, targetBucket, sourceBucket string) copyHandlerFunc {
	return func(ctx context.Context, info internal.FileInfo) error {
		s3URI := fmt.Sprintf("%s/%s", sourceBucket, info.S3FilePath)
		source, err := internal.ParseS3URIToCopySource(s3URI)
		if err != nil {
			return errors.Wrap(err, "failed to parse s3 uri to copy source")
		}
		copyKey := internal.GenerateS3KeyForCopy(prefix, info)
		_, err = s3Client.CopyObjectWithContext(ctx, &s3.CopyObjectInput{
			Bucket:     aws.String(targetBucket),
			Key:        aws.String(copyKey),
			CopySource: aws.String(source),
		})
		if err != nil {
			return errors.Wrapf(err, "failed to copy object %s", copyKey)
		}
		return nil
	}
}

// s3UploaderFn returns an uploadHandlerFunc which uploads metadata files to CMT S3. This util is well tested.
func s3UploaderFn(s3Client s3_utils.Client, prefix, targetBucket string) uploadHandlerFunc {
	return func(ctx context.Context, metadata internal.MetadataInfo) error {
		key := internal.GenerateS3KeyForMetadataUpload(prefix, string(metadata.VIN))
		data, err := json.Marshal(metadata.Data)
		if err != nil {
			return errors.Wrap(err, "failed to marshal metadata data")
		}
		_, err = s3Client.UploadWithContext(ctx, &s3manager.UploadInput{
			Body:   bytes.NewReader(data),
			Bucket: aws.String(targetBucket),
			Key:    aws.String(key),
		})
		if err != nil {
			return errors.Wrapf(err, "failed to upload object %s", key)
		}
		return nil
	}
}

// dbPersistSuccessCallback returns a copySuccessCallbackFunc to persist the scoring details in the database.
// We do not need to test this function as it is well tested in the wrapper tests.
func dbPersistSuccessCallback(
	cmtWrapper *cmt_scoring_wrapper.ScoringWrapper, s3URI string, uploadedBy jtypes.JobRunId,
) copyUploadSuccessCallbackFunc {
	return func(ctx context.Context, handleId uuid.UUID, interval time_utils.Interval) error {
		return cmtWrapper.InsertScoringDetails(ctx, cmt_scoring_wrapper.ScoringDetails{
			HandleID:       handleId,
			Interval:       interval,
			InputS3URI:     s3URI,
			DataUploadedBy: uploadedBy,
			ScoringState:   cmt_scoring_wrapper.ScoringStateDataCopied,
		})
	}
}

func (task *s3CopierTask) Retry(ctx jtypes.Context, message *jobs.CMTWorkflowMessage) error {
	return task.Run(ctx, message)
}
