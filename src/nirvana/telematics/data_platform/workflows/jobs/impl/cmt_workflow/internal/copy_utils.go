package internal

import (
	"context"
	"fmt"
	"net/url"
	"strings"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/volatiletech/null/v8"

	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/map_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/telematics"
	"nirvanatech.com/nirvana/external_data_management/nhtsa"
	"nirvanatech.com/nirvana/telematics/data_platform"
)

// GenerateFileAndMetadataDetails generates list of file details for all vehicles in the given interval and handle id,
// using the given normalized file metadata and entities wrappers. It also generates vin metadata info for all vins
// affected using NHTSA Client
func GenerateFileAndMetadataDetails(
	ctx context.Context,
	normFileMetaWrapper *telematics.NormFileMetaWrapper,
	entitiesWrapper *telematics.EntitiesWrapper,
	nhtsaClient nhtsa.CmtClient,
	handleId uuid.UUID,
	interval time_utils.Interval,
	tsp string,
) ([]FileInfo, []MetadataInfo, error) {
	// find normalized metadata files for the interval per vehicle
	rowsGroupedByVehicleId, err := getNormalizedMetadataRowsForInterval(
		ctx, normFileMetaWrapper, handleId, interval,
	)
	if err != nil {
		return nil, nil, err
	}
	// find vin for each vehicle id
	vehicleIdToVinMap, err := getVehicleIdToVinMap(ctx, entitiesWrapper, handleId)
	if err != nil {
		return nil, nil, err
	}
	fileDetails, err := getFileDetails(rowsGroupedByVehicleId, vehicleIdToVinMap)
	if err != nil {
		return nil, nil, err
	}
	vins := slice_utils.ToExistsMap(slice_utils.Map(fileDetails, func(detail FileInfo) data_platform.VIN {
		return detail.VIN
	}))
	metadata, err := generateVinMetadataFromList(ctx, nhtsaClient, tsp, map_utils.Keys(vins)...)
	if err != nil {
		return nil, nil, err
	}
	return fileDetails, metadata, nil
}

// getNormalizedMetadataRowsForInterval fetches normalized metadata rows for the given interval and handle id, for
// stat kinds GPSLogWithGeography, HarshBrakingEventLog, and HarshAccelerationEventLog. Also filters out rows with
// no data and rows with vehicle id starting with "vin:", as we are only interested in rows of device level files.
// We do not need to test this function as NormalizedFileMetaWrapper is already well tested.
func getNormalizedMetadataRowsForInterval(
	ctx context.Context,
	wrapper *telematics.NormFileMetaWrapper,
	handleId uuid.UUID,
	interval time_utils.Interval,
) (map[string]telematics.NormFileMetaLog, error) {
	rows, err := wrapper.FetchMetadata(
		ctx,
		telematics.WhereHandleId(handleId),
		telematics.WhereIntersectionExists(interval),
		telematics.WhereStatIn(
			data_platform.NormalizedVehicleStatWithVersion{
				Kind:    data_platform.NormalizedVehicleStatGPSLogWithGeography,
				Version: null.IntFrom(0),
			},
			data_platform.NormalizedVehicleStatWithVersion{
				Kind:    data_platform.NormalizedVehicleStatHarshBrakingEventLog,
				Version: null.IntFrom(0),
			},
			data_platform.NormalizedVehicleStatWithVersion{
				Kind:    data_platform.NormalizedVehicleStatHarshAccelerationEventLog,
				Version: null.IntFrom(0),
			},
		),
	)
	if err != nil {
		return nil, err
	}
	// filter out rows with no data and rows with vehicle id starting with "vin:"
	rows = slice_utils.Filter(
		rows,
		func(row *telematics.NormalizedFileMetadatum) bool {
			return row.NumDataRows > 0 && !strings.HasPrefix(row.VehicleId, "vin:")
		},
	)
	return telematics.GroupMetadata(rows, telematics.GroupByVehicleId), nil
}

// getVehicleIdToVinMap fetches vehicles from entities wrapper to get VINs for the given handle id and vehicle ids.
// We do not need to test this function as EntitiesWrapper is already well tested.
func getVehicleIdToVinMap(
	ctx context.Context,
	wrapper *telematics.EntitiesWrapper,
	handleId uuid.UUID,
) (map[string]data_platform.VIN, error) {
	fetchedVehicles, err := wrapper.FetchVehicles(ctx, handleId)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to fetch vehicles to get VINs for handle %s", handleId)
	}
	return slice_utils.ToMap(
		fetchedVehicles,
		func(v data_platform.Vehicle) string {
			return v.TspId
		},
		func(v data_platform.Vehicle) data_platform.VIN {
			return v.VIN
		},
	), nil
}

// getFileDetails transforms the given normalized metadata map and VIN map
// into a list of file details.
func getFileDetails(
	vehicleIdToNormData map[string]telematics.NormFileMetaLog,
	vehicleIdToVinMap map[string]data_platform.VIN,
) ([]FileInfo, error) {
	var fileDetails []FileInfo
	for vehicleId, normData := range vehicleIdToNormData {
		dataTypeToSeqId := make(map[DataType]int)
		vin, ok := vehicleIdToVinMap[vehicleId]
		if !ok { // this will never happen as entities wrapper will always fetch all vehicles for the handle id
			return nil, errors.Newf("failed to find vin for vehicle id %s", vehicleId)
		}
		for _, normDatum := range normData {
			dataType, err := convertStatKindToDataType(normDatum.StatKind)
			if err != nil {
				return nil, err
			}
			seq, ok := dataTypeToSeqId[dataType]
			if !ok {
				seq = 1
			}
			fileDetails = append(fileDetails, FileInfo{
				VehicleId:  vehicleId,
				VIN:        vin,
				StatKind:   dataType,
				S3FilePath: normDatum.S3Path,
				SeqId:      seq,
			})
			seq++
			dataTypeToSeqId[dataType] = seq
		}
	}
	return fileDetails, nil
}

// generateVinMetadataFromList returns a list of metadata files to upload, given a list of VINs
func generateVinMetadataFromList(ctx context.Context, client nhtsa.CmtClient, tsp string, vins ...data_platform.VIN) (
	[]MetadataInfo, error,
) {
	var metadataInfo []MetadataInfo
	for _, vin := range vins {
		metadata, err := generateVinMetadata(ctx, client, tsp, vin)
		if err != nil {
			if !errors.IsAny(err, nhtsa.ErrNoData, nhtsa.ErrVinDetailsParse) {
				return nil, err
			}
			log.Warn(ctx, "No VIN Info found", log.String("vin", string(vin)), log.Err(err))
			continue
		}
		metadataInfo = append(metadataInfo, MetadataInfo{
			VIN:  vin,
			Data: metadata,
		})
	}
	return metadataInfo, nil
}

// generateVinMetadata returns vehicle attributes for a vin in CMT specified format using NHTSA wrapper. Testing is
// not needed, pretty straightforward function. NHTSA client is already tested.
func generateVinMetadata(ctx context.Context, client nhtsa.CmtClient, tsp string, vin data_platform.VIN) (
	*VehicleMetadata, error,
) {
	vinDetails, err := client.GetVINRecord(ctx, string(vin))
	if err != nil {
		return nil, errors.Wrapf(err, "error getting vin record for %s", vin)
	}
	braking := &BrakingObject{BrakeSystemType: vinDetails.BrakeSystemType}
	dataColl := &DataCollectionObject{TSPName: tsp}

	identification := &IdentificationObject{
		Make:   vinDetails.Make,
		Model:  vinDetails.Model,
		Series: vinDetails.Series,
	}
	if vinDetails.ModelYear != nil && *vinDetails.ModelYear >= minModelYear {
		identification.ModelYear = vinDetails.ModelYear
	}

	engine := &EngineObject{
		Model:               vinDetails.EngineModel,
		FuelType:            vinDetails.FuelTypePrimary,
		EngineConfiguration: vinDetails.EngineConfiguration,
	}
	if vinDetails.DisplacementCC != nil && *vinDetails.DisplacementCC >= minDisplacementCC {
		engine.DisplacementCC = vinDetails.DisplacementCC
	}
	if vinDetails.EngineCylinders != nil && *vinDetails.EngineCylinders >= minCylinders {
		engine.Cylinders = vinDetails.EngineCylinders
	}

	attributes := &VehicleAttributesObject{
		Type:              vinDetails.VehicleType,
		BodyClass:         vinDetails.BodyClass,
		AxleConfiguration: vinDetails.AxleConfiguration,
		DriveType:         vinDetails.DriveType,
		GVWR:              vinDetails.GVWR,
	}
	if vinDetails.Axles != nil && *vinDetails.Axles >= minAxles {
		attributes.Axles = vinDetails.Axles
	}

	return &VehicleMetadata{
		Braking:        braking,
		DataCollection: dataColl,
		Identification: identification,
		Engine:         engine,
		Attributes:     attributes,
	}, nil
}

// ParseS3URIToCopySource parses the given S3 URI to a copy source string that can be used in s3.CopyObjectInput.
func ParseS3URIToCopySource(s3URI string) (string, error) {
	bucket, key, err := ParseS3URIToBucketAndKey(s3URI)
	if err != nil {
		return "", err
	}
	return url.QueryEscape(fmt.Sprintf("%s/%s", bucket, key)), nil
}

func ParseS3URIToBucketAndKey(s3URI string) (string, string, error) {
	parts := strings.SplitN(
		strings.TrimPrefix(s3URI, "s3://"),
		"/", 2,
	)
	if len(parts) != 2 {
		return "", "", errors.Newf("s3 uri must have a bucket and a key. Got %s", s3URI)
	}
	return parts[0], parts[1], nil
}

// GenerateS3KeyForCopy generates an S3 key for copying a file for a given vehicle and sequence id.
// The key is of the format <prefix>/<vin>/<vehicle_id>/<stat_type>/<seq_id>.csv
func GenerateS3KeyForCopy(prefix string, fileInfo FileInfo) string {
	return fmt.Sprintf("%s/%s/%s/%s/%d.csv",
		prefix, fileInfo.VIN, fileInfo.VehicleId, fileInfo.StatKind.String(), fileInfo.SeqId,
	)
}

// GenerateS3KeyForMetadataUpload generates s3 key to upload vehicle metadata file.
// The key is of format <prefix>/<vin>/vehicle_metadata.json
func GenerateS3KeyForMetadataUpload(prefix, vin string) string {
	return fmt.Sprintf("%s/%s/%s", prefix, vin, metadataFileName)
}

func ToS3URI(bucket, key string) string {
	return fmt.Sprintf("s3://%s/%s", bucket, key)
}
