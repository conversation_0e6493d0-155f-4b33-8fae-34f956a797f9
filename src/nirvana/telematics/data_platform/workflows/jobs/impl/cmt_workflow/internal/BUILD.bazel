load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "internal",
    srcs = [
        "copy_utils.go",
        "datatype_enumer.go",
        "state_machine.go",
        "types.go",
    ],
    importpath = "nirvanatech.com/nirvana/telematics/data_platform/workflows/jobs/impl/cmt_workflow/internal",
    visibility = ["//nirvana/telematics/data_platform/workflows/jobs/impl/cmt_workflow:__subpackages__"],
    deps = [
        "//nirvana/common-go/log",
        "//nirvana/common-go/map_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/db-api/db_wrappers/telematics",
        "//nirvana/db-api/db_wrappers/telematics/cmt",
        "//nirvana/external_data_management/nhtsa",
        "//nirvana/jobber/jtypes",
        "//nirvana/telematics/data_platform",
        "//nirvana/telematics/integrations/cmt",
        "@com_github_ben<PERSON><PERSON><PERSON><PERSON>_clock//:clock",
        "@com_github_cactus_go_statsd_client_v5//statsd",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_volatiletech_null_v8//:null",
    ],
)

go_test(
    name = "internal_test",
    srcs = [
        "copy_utils_test.go",
        "state_machine_test.go",
    ],
    embed = [":internal"],
    deps = [
        "//nirvana/common-go/time_utils",
        "//nirvana/db-api/db_wrappers/telematics/cmt",
        "//nirvana/jobber/jtypes",
        "//nirvana/jobber/test_utils",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//require",
    ],
)
