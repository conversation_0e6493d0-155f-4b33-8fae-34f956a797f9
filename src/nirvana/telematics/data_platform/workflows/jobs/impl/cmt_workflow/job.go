package cmt_workflow

import (
	"fmt"

	"github.com/benb<PERSON><PERSON><PERSON>/clock"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/common-go/s3_utils"
	telematics_wrapper "nirvanatech.com/nirvana/db-api/db_wrappers/telematics"
	cmt_scoring_wrapper "nirvanatech.com/nirvana/db-api/db_wrappers/telematics/cmt"
	"nirvanatech.com/nirvana/distsem"
	"nirvanatech.com/nirvana/external_data_management/nhtsa"
	"nirvanatech.com/nirvana/infra/config"
	"nirvanatech.com/nirvana/jobber/job_utils/jobber_distsem"
	"nirvanatech.com/nirvana/jobber/jtypes"
	"nirvanatech.com/nirvana/telematics/data_platform"
	"nirvanatech.com/nirvana/telematics/data_platform/workflows/jobs"
	cmt_integration "nirvanatech.com/nirvana/telematics/integrations/cmt"
	"nirvanatech.com/nirvana/telematics/integrations/cmt/encryption_utils"
)

type CMTDeps struct {
	fx.In
	Clk                       clock.Clock
	Cfg                       *config.Config
	ApiClient                 *cmt_integration.Client
	ScoringWrapper            *cmt_scoring_wrapper.ScoringWrapper
	S3Client                  s3_utils.Client `name:"cmts3"`
	VinParser                 *encryption_utils.VinParser
	NormalizedFileMetaWrapper *telematics_wrapper.NormFileMetaWrapper
	EntitiesWrapper           *telematics_wrapper.EntitiesWrapper
	NHTSAClient               nhtsa.CmtClient
	StatSender                data_platform.Statter
	DistsemManager            distsem.Manager
}

func NewJob(deps CMTDeps) (*jtypes.Job[*jobs.CMTWorkflowMessage], error) {
	return jtypes.NewJob(
		jobs.CMTWorkflow,
		jobber_distsem.TaskSetWithResourceRequirement(
			"cmt-workflow",
			[]jtypes.TaskCreator[*jobs.CMTWorkflowMessage]{
				func() jtypes.Task[*jobs.CMTWorkflowMessage] {
					return &s3CopierTask{
						cfg:                       deps.Cfg,
						s3Client:                  deps.S3Client,
						normalizedFileMetaWrapper: deps.NormalizedFileMetaWrapper,
						entitiesWrapper:           deps.EntitiesWrapper,
						cmtScoringWrapper:         deps.ScoringWrapper,
						vinEncryptor:              deps.VinParser,
						nhtsaClient:               deps.NHTSAClient,
					}
				},
				func() jtypes.Task[*jobs.CMTWorkflowMessage] {
					return &triggerCMTUploadTask{
						clk:            deps.Clk,
						apiClient:      deps.ApiClient,
						scoringWrapper: deps.ScoringWrapper,
						statSender:     deps.StatSender,
					}
				},
				func() jtypes.Task[*jobs.CMTWorkflowMessage] {
					return &triggerCMTScoringTask{
						clk:            deps.Clk,
						apiClient:      deps.ApiClient,
						scoringWrapper: deps.ScoringWrapper,
						statSender:     deps.StatSender,
					}
				},
				func() jtypes.Task[*jobs.CMTWorkflowMessage] {
					return &persistCMTScoresTask{
						scoringWrapper: deps.ScoringWrapper,
						s3Client:       deps.S3Client,
						cfg:            deps.Cfg,
						vinDecryptor:   deps.VinParser,
					}
				},
			},
			deps.DistsemManager,
			func(msg *jobs.CMTWorkflowMessage) jobber_distsem.PartialAllocParams {
				return jobber_distsem.PartialAllocParams{
					ResourceId: distsem.ResourceId(
						fmt.Sprintf("cmt-workflow-handle-%s", msg.HandleID.String()),
					),
					Count:                         1,
					DynamicCreationCountIfMissing: 1,
					Priority:                      msg.Priority,
				}
			},
		),
		jobs.UnmarshalCMTWorkflowMessage,
	)
}
