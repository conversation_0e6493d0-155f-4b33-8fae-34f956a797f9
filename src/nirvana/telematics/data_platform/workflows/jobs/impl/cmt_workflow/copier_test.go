package cmt_workflow

import (
	"context"
	"fmt"
	"math/rand"
	"sync"
	"testing"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"

	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/telematics/data_platform"
	"nirvanatech.com/nirvana/telematics/data_platform/workflows/jobs/impl/cmt_workflow/internal"
)

func TestFindUncopiedInterval(t *testing.T) {
	var spanInterval time_utils.Interval
	ctx := context.Background()
	handleId := uuid.New()
	spanFinder := func(ctx context.Context, handleId uuid.UUID) (time_utils.Interval, error) {
		return spanInterval, nil
	}

	testcases := []struct {
		name             string
		originalInterval time_utils.Interval
		spanInterval     time_utils.Interval
		expectErr        bool
		expectIntervals  []time_utils.Interval
	}{
		{
			name:             "empty interval",
			originalInterval: time_utils.Interval{},
			spanInterval:     time_utils.Interval{},
			expectIntervals:  nil,
			expectErr:        false,
		},
		{
			name: "No span interval",
			originalInterval: time_utils.Interval{
				Start: time_utils.DateUTC(2024, time.January, 1),
				End:   time_utils.DateUTC(2024, time.May, 1),
			},
			spanInterval: time_utils.Interval{},
			expectErr:    false,
			expectIntervals: []time_utils.Interval{
				{
					Start: time_utils.DateUTC(2024, time.January, 1),
					End:   time_utils.DateUTC(2024, time.May, 1),
				},
			},
		},
		{
			name: "No span interval, with end interval as end of month",
			originalInterval: time_utils.Interval{
				Start: time_utils.DateUTC(2024, time.January, 1),
				End:   time_utils.DateUTC(2024, time.May, 1).Add(-time.Nanosecond),
			},
			spanInterval: time_utils.Interval{},
			expectErr:    false,
			expectIntervals: []time_utils.Interval{
				{
					Start: time_utils.DateUTC(2024, time.January, 1),
					End:   time_utils.DateUTC(2024, time.May, 1),
				},
			},
		},
		{
			name: "no span interval and invalid post truncation interval",
			originalInterval: time_utils.Interval{
				Start: time_utils.DateUTC(2024, time.April, 1),
				End:   time_utils.DateUTC(2024, time.April, 14),
			},
			spanInterval:    time_utils.Interval{},
			expectErr:       false,
			expectIntervals: nil,
		},
		{
			name: "backfill interval only",
			originalInterval: time_utils.Interval{
				Start: time_utils.DateUTC(2024, time.January, 1),
				End:   time_utils.DateUTC(2024, time.February, 15),
			},
			spanInterval: time_utils.Interval{
				Start: time_utils.DateUTC(2024, time.February, 15),
				End:   time_utils.DateUTC(2024, time.May, 1),
			},
			expectErr: false,
			expectIntervals: []time_utils.Interval{
				{
					Start: time_utils.DateUTC(2024, time.January, 1),
					End:   time_utils.DateUTC(2024, time.March, 1),
				},
			},
		},
		{
			name: "backfill data hole not supported",
			originalInterval: time_utils.Interval{
				Start: time_utils.DateUTC(2024, time.January, 1),
				End:   time_utils.DateUTC(2024, time.February, 14),
			},
			spanInterval: time_utils.Interval{
				Start: time_utils.DateUTC(2024, time.February, 15),
				End:   time_utils.DateUTC(2024, time.April, 1),
			},
			expectErr:       true,
			expectIntervals: []time_utils.Interval{},
		},
		{
			name: "incremental interval only",
			originalInterval: time_utils.Interval{
				Start: time_utils.DateUTC(2024, time.March, 25),
				End:   time_utils.DateUTC(2024, time.June, 14),
			},
			spanInterval: time_utils.Interval{
				Start: time_utils.DateUTC(2024, time.January, 1),
				End:   time_utils.DateUTC(2024, time.April, 1),
			},
			expectErr: false,
			expectIntervals: []time_utils.Interval{
				{
					Start: time_utils.DateUTC(2024, time.April, 1),
					End:   time_utils.DateUTC(2024, time.June, 1),
				},
			},
		},
		{
			name: "incremental data hole not supported",
			originalInterval: time_utils.Interval{
				Start: time_utils.DateUTC(2024, time.May, 1),
				End:   time_utils.DateUTC(2024, time.June, 1),
			},
			spanInterval: time_utils.Interval{
				Start: time_utils.DateUTC(2024, time.January, 1),
				End:   time_utils.DateUTC(2024, time.April, 1),
			},
			expectErr:       true,
			expectIntervals: []time_utils.Interval{},
		},
		{
			name: "smaller than a month interval",
			originalInterval: time_utils.Interval{
				Start: time_utils.DateUTC(2024, time.May, 1),
				End:   time_utils.DateUTC(2024, time.May, 15),
			},
			spanInterval: time_utils.Interval{
				Start: time_utils.DateUTC(2024, time.January, 1),
				End:   time_utils.DateUTC(2024, time.May, 1),
			},
			expectErr:       false,
			expectIntervals: nil,
		},
		{
			name: "Valid interval with both backfill and incremental",
			originalInterval: time_utils.Interval{
				Start: time_utils.DateUTC(2024, time.January, 15),
				End:   time_utils.DateUTC(2024, time.July, 12),
			},
			spanInterval: time_utils.Interval{
				Start: time_utils.DateUTC(2024, time.March, 4),
				End:   time_utils.DateUTC(2024, time.June, 1),
			},
			expectErr: false,
			expectIntervals: []time_utils.Interval{
				{ // backfill interval
					Start: time_utils.DateUTC(2024, time.January, 15),
					End:   time_utils.DateUTC(2024, time.April, 1),
				},
				{ // incremental interval
					Start: time_utils.DateUTC(2024, time.June, 1),
					End:   time_utils.DateUTC(2024, time.July, 1),
				},
			},
		},
	}
	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			spanInterval = tc.spanInterval
			intervals, err := findUncopiedIntervals(ctx, handleId, tc.originalInterval, spanFinder)
			if tc.expectErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, tc.expectIntervals, intervals)
			}
		})
	}
}

func TestProcessDataUploads(t *testing.T) {
	ctx := context.Background()
	var copyWarehouse []internal.FileInfo
	var uploadWarehouse []internal.MetadataInfo
	var mu sync.Mutex
	successfulState := false

	mockCopyHandler := func(ctx context.Context, info internal.FileInfo) error {
		if info.VIN == "MUST_FAIL" {
			return errors.New("mock error")
		}
		mu.Lock()
		defer mu.Unlock()
		copyWarehouse = append(copyWarehouse, info)
		return nil
	}
	mockUploadHandler := func(ctx context.Context, meta internal.MetadataInfo) error {
		if meta.VIN == "MUST_FAIL" {
			return errors.New("mock error")
		}
		mu.Lock()
		defer mu.Unlock()
		uploadWarehouse = append(uploadWarehouse, meta)
		return nil
	}
	mockSuccessCallback := func(ctx context.Context, handleId uuid.UUID, interval time_utils.Interval) error {
		successfulState = true
		return nil
	}

	testcases := []struct {
		name          string
		fileInfos     []internal.FileInfo
		metaInfos     []internal.MetadataInfo
		expectErr     bool
		expectSuccess bool
	}{
		{
			name:          "No files",
			fileInfos:     nil,
			metaInfos:     nil,
			expectErr:     false,
			expectSuccess: false,
		},
		{
			name:          "10 files, 10 metadata",
			fileInfos:     generateNFileInfo(10),
			metaInfos:     generateNMetaInfo(10),
			expectErr:     false,
			expectSuccess: true,
		},
		{
			name:          "100 files, 50 metadata",
			fileInfos:     generateNFileInfo(100),
			metaInfos:     generateNMetaInfo(50),
			expectErr:     false,
			expectSuccess: true,
		},
		{
			name:          "1000 files, 500 metadata",
			fileInfos:     generateNFileInfo(1000),
			metaInfos:     generateNMetaInfo(500),
			expectErr:     false,
			expectSuccess: true,
		},
		{
			name:          "1000 files with file error. 500 metadata",
			fileInfos:     append(generateNFileInfo(1000), internal.FileInfo{VIN: "MUST_FAIL"}),
			metaInfos:     generateNMetaInfo(500),
			expectErr:     true,
			expectSuccess: false,
		},
		{
			name:      "1000 files. 500 metadata with one error",
			fileInfos: generateNFileInfo(1000),
			metaInfos: append(generateNMetaInfo(500), internal.MetadataInfo{
				VIN:  "MUST_FAIL",
				Data: &internal.VehicleMetadata{},
			}),
			expectErr:     true,
			expectSuccess: false,
		},
	}
	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			copyWarehouse = []internal.FileInfo{}
			uploadWarehouse = []internal.MetadataInfo{}
			successfulState = false
			err := processDataCopyAndUpload(
				ctx,
				uuid.New(),
				time_utils.Interval{},
				tc.fileInfos,
				tc.metaInfos,
				mockCopyHandler,
				mockUploadHandler,
				mockSuccessCallback,
			)
			if tc.expectErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, tc.expectSuccess, successfulState)
				require.ElementsMatch(t, copyWarehouse, tc.fileInfos)
				require.ElementsMatch(t, uploadWarehouse, tc.metaInfos)
			}
		})
	}
}

func generateNFileInfo(count int) []internal.FileInfo {
	var subs []internal.FileInfo
	stats := []internal.DataType{
		internal.DataTypeGPS,
		internal.DataTypeHarshBraking,
		internal.DataTypeHarshAcceleration,
	}
	for i := 0; i < count; i++ {
		subs = append(subs, internal.FileInfo{
			VehicleId:  fmt.Sprintf("vehicle-%d", i),
			VIN:        data_platform.VIN(fmt.Sprintf("vin-%d", i)),
			StatKind:   stats[rand.Intn(len(stats))],
			S3FilePath: fmt.Sprintf("s3://bucket/key-%d", i),
			SeqId:      i,
		})
	}
	return subs
}

func generateNMetaInfo(count int) []internal.MetadataInfo {
	var metas []internal.MetadataInfo
	for i := 0; i < count; i++ {
		metas = append(metas, internal.MetadataInfo{
			VIN: data_platform.VIN(fmt.Sprintf("vin-%d", i)),
			Data: &internal.VehicleMetadata{
				Braking:        &internal.BrakingObject{BrakeSystemType: "test"},
				DataCollection: &internal.DataCollectionObject{TSPName: "test"},
				Identification: &internal.IdentificationObject{Model: "test"},
				Engine:         &internal.EngineObject{Model: "test"},
				Attributes:     &internal.VehicleAttributesObject{GVWR: "test"},
			},
		})
	}
	return metas
}
