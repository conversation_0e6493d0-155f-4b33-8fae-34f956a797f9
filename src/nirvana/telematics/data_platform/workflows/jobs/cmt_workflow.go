package jobs

import (
	"encoding/json"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/distsem"
	"nirvanatech.com/nirvana/jobber/jtypes"
)

type CMTWorkflowMessage struct {
	HandleID uuid.UUID
	Interval time_utils.Interval
	TSP      string
	Priority distsem.Priority
}

func (message *CMTWorkflowMessage) MarshalJSON() ([]byte, error) {
	return json.Marshal(*message)
}

func (message *CMTWorkflowMessage) Version() jtypes.MessageVersion {
	return 0
}

func UnmarshalCMTWorkflowMessage(
	data []byte, version jtypes.MessageVersion,
) (message *CMTWorkflowMessage, err error) {
	if version != 0 {
		return nil, errors.Newf("unidentified message version: %d", version)
	}
	err = json.Unmarshal(data, &message)
	return
}
