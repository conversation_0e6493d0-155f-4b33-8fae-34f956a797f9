package tests

import (
	"context"
	"testing"
	"time"

	"nirvanatech.com/nirvana/telematics/connections/oauth"

	"github.com/benb<PERSON><PERSON><PERSON>/clock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"github.com/volatiletech/null/v8"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/keeptruckin_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/jobber/jtypes"
	"nirvanatech.com/nirvana/telematics"
	"nirvanatech.com/nirvana/telematics/connections"
	"nirvanatech.com/nirvana/telematics/data_platform"
	dp_test_utils "nirvanatech.com/nirvana/telematics/data_platform/test_utils"
	"nirvanatech.com/nirvana/telematics/data_platform/workflows/jobs"
	"nirvanatech.com/nirvana/telematics/data_platform/workflows/test_utils"
	kt_test_utils "nirvanatech.com/nirvana/telematics/integrations/keeptruckin_lib/test_utils"
)

type multiVehicleWithSameVINMillimanScoringWorkflowTestCase struct {
	// name of the test case
	name string
	// The number of vehicles to create
	vehicleCount int
	// [OPTIONAL] The "custom" VIN map to use.
	vinMap map[int]string
	// The total number of unique VINs
	uniqueVINs int
}

// TestMillimanScoringWorkflow_MultipleVehiclesWithSameVIN_KeepTruckin tests the
// MillimanScoringWorkflow for cases where multiple vehicles _may_ have the same
// VIN. This test in particular only focuses on the KeepTruckin integration.
// All test cases are executed with and without the SpawnByTspIds flag, and we
// validate that the correct number of vehicle workflows are spawned:
//   - SpawnByTspIds = false: 1 vehicle workflow per unique VIN
//   - SpawnByTspIds = true: 1 vehicle workflow per vehicle
func TestMillimanScoringWorkflow_MultipleVehiclesWithSameVIN_KeepTruckin(t *testing.T) {
	testCases := []multiVehicleWithSameVINMillimanScoringWorkflowTestCase{
		{
			name:         "7 total vehicles, with 3 with same VIN",
			vehicleCount: 7,
			vinMap: map[int]string{
				1: "ABCDE12345XYZ42",
				3: "ABCDE12345XYZ42",
				5: "ABCDE12345XYZ42",
			},
			uniqueVINs: 5,
		},
		{
			name:         "7 total vehicles, with no custom VINs",
			vehicleCount: 7,
			uniqueVINs:   7,
		},
		{
			name:         "5 total vehicles, with 1 custom VIN",
			vehicleCount: 5,
			vinMap:       map[int]string{1: "ABCDE12345XYZ42"},
			uniqueVINs:   5,
		},
		{
			name:         "5 total vehicles, with 2 unique VINs",
			vehicleCount: 5,
			vinMap: map[int]string{
				// The only two unique VINs are: FGHIJ12345XYZ43 and ABCDE12345XYZ42
				1: "ABCDE12345XYZ42",
				2: "FGHIJ12345XYZ43",
				3: "ABCDE12345XYZ42",
				4: "FGHIJ12345XYZ43",
				5: "ABCDE12345XYZ42",
			},
			uniqueVINs: 2,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			runMultiVehicleWithSameVINMillimanScoringWorkflowTestCaseKT(t, tc)
		})
	}
}

func runMultiVehicleWithSameVINMillimanScoringWorkflowTestCaseKT(
	t *testing.T, tc multiVehicleWithSameVINMillimanScoringWorkflowTestCase,
) {
	connHandleId := uuid.NewSHA1(dp_test_utils.GoldenHandleId(telematics.TSPKeepTruckin), []byte("multiVehicle"))
	/* Setup dependencies and load fixtures */
	ctx := context.Background()
	var env struct {
		fx.In
		Harness *test_utils.Harness
		*keeptruckin_fixture.KeepTruckin
		TSPConnManager   *connections.TSPConnManager
		OauthConnManager *oauth.OauthConnManager
	}
	defer testloader.RequireStart(t, &env).RequireStop()
	defer func(Harness *test_utils.Harness) {
		require.NoError(t, Harness.Close())
	}(env.Harness)
	require.NoError(t, oauth.PreloadHandleForTest(ctx, env.OauthConnManager, connHandleId, telematics.TSPKeepTruckin))
	require.NoError(t, env.KeepTruckin.RegisterDataset(
		ctx, connHandleId,
		kt_test_utils.GoldenDatasetOptions.
			WithNumVehicles(tc.vehicleCount).
			WithVehicleOptions(kt_test_utils.SetVINOnVehicle(tc.vinMap)),
	))

	/* Run the workflow and assert that it succeeds */
	message := jobs.RunMillimanScoringMessage{
		PipelineId:          null.StringFrom(uuid.New().String()),
		HandleID:            connHandleId,
		TSP:                 telematics.TSPKeepTruckin.String(),
		AllowConfigurations: true,
		Config: &jobs.PipelineConfig{
			ForceInterval: &time_utils.Interval{
				Start: time.Date(2020, time.December, 1, 0, 0, 0, 0, time.UTC),
				End:   time.Date(2020, time.December, 21, 0, 0, 0, 0, time.UTC),
			},
			KindsToNormalise: []data_platform.NormalizedVehicleStat{
				data_platform.NormalizedVehicleStatGPSLogWithGeography,
			},
		},
	}
	scoringWorkflow, err := env.Harness.RunMillimanScoringWorkflow(ctx, &message)
	require.NoError(t, err)
	// increment wall clock by 1 minute every 10 ms
	// TODO: de-duplicate this (also present in msw_e2e_test.go) to scoringWorkflow.Finish()?
	doneCh := make(chan bool)
	waitCh := make(chan bool)
	go func() {
		timer := time.NewTicker(10 * time.Millisecond)
		defer timer.Stop()
		for {
			select {
			case <-doneCh:
				close(waitCh)
				return
			case <-timer.C:
				env.Harness.Deps.Clk.(*clock.Mock).Add(5 * time.Minute)
			}
		}
	}()
	require.NoError(t, scoringWorkflow.WaitForCompletion(ctx))
	close(doneCh)
	<-waitCh
	require.NoError(t, env.Harness.CheckJobRunStatus(ctx, scoringWorkflow.JobRunId(), jtypes.JobRunStatusSucceeded))
	// assert that workflow completed successfully
	handleWorkflow, err := scoringWorkflow.MillimanHandleWorkflow(ctx)
	require.NoError(t, err)
	require.NoError(t, handleWorkflow.ValidateStatus(ctx, jtypes.JobRunStatusSucceeded))

	// Number of workflows should be N + M
	// N = number of vehicleIds
	// M = number of unique VINs
	spawnedVehicleWorkflows, err := handleWorkflow.VehicleWorkflows(ctx)
	require.NoError(t, err)
	byVin := slice_utils.Filter(spawnedVehicleWorkflows, func(wrapper *test_utils.NativeMillimanVehicleWorkflowWrapper) bool {
		return wrapper.InputMessage.VINVehicleIds != nil
	})
	byTspId := slice_utils.Filter(spawnedVehicleWorkflows, func(wrapper *test_utils.NativeMillimanVehicleWorkflowWrapper) bool {
		return wrapper.InputMessage.VehicleId.Valid
	})
	require.Len(t, byTspId, tc.vehicleCount)
	require.Len(t, byVin, tc.uniqueVINs)
}
