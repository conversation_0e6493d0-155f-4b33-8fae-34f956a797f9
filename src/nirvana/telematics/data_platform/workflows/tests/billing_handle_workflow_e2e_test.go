package tests

import (
	"bytes"
	"context"
	"testing"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/benb<PERSON><PERSON><PERSON>/clock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"github.com/volatiletech/null/v8"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/keeptruckin_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/samsara_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/jobber/jtypes"
	"nirvanatech.com/nirvana/telematics"
	"nirvanatech.com/nirvana/telematics/data_platform"
	"nirvanatech.com/nirvana/telematics/data_platform/stream"
	dp_test_utils "nirvanatech.com/nirvana/telematics/data_platform/test_utils"
	"nirvanatech.com/nirvana/telematics/data_platform/workflows/jobs"
	"nirvanatech.com/nirvana/telematics/data_platform/workflows/test_utils"
	kt_test_utils "nirvanatech.com/nirvana/telematics/integrations/keeptruckin_lib/test_utils"
	samsara_test_utils "nirvanatech.com/nirvana/telematics/integrations/samsara_lib/test_utils"
)

// TestBillingHandleWorkflowTestSuite tests the API handlers related to telematics
// pipeline executions.
func TestBillingHandleWorkflowTestSuiteInMemoryPersistence(t *testing.T) {
	suite.Run(t, new(billingHandleWorkflowTestSuite))
}

// billingHandleWorkflowTestSuite represents the test suite to validate
// a billing JobRun behavior. At the moment, we only
// validate the success case.
type billingHandleWorkflowTestSuite struct {
	suite.Suite

	suiteCtx context.Context
	clk      *clock.Mock
	harness  *test_utils.Harness
	testApp  *fxtest.App
}

func (n *billingHandleWorkflowTestSuite) SetupTest() {
	n.suiteCtx = context.Background()
	var env struct {
		fx.In
		*keeptruckin_fixture.KeepTruckin
		*samsara_fixture.Samsara
		Harness *test_utils.Harness
	}
	n.testApp = testloader.RequireStart(n.T(), &env)
	n.harness = env.Harness
	n.clk = n.harness.Deps.Clk.(*clock.Mock)

	// Configure golden datasets for KT & Samsara
	n.Require().NoError(env.KeepTruckin.RegisterDataset(
		n.suiteCtx, dp_test_utils.GoldenHandleId(telematics.TSPKeepTruckin),
		kt_test_utils.GoldenDatasetOptions),
	)
	env.Samsara.RegisterGoldenDataset(samsara_test_utils.GoldenDatasetOptions)
}

func (n *billingHandleWorkflowTestSuite) TearDownTest() {
	n.Require().NoError(n.harness.Close())
}

func (n *billingHandleWorkflowTestSuite) TestSuccess() {
	ctx := n.suiteCtx

	type vehicleRowCountSummary struct {
		tspId string
		stats map[data_platform.NormalizedVehicleStat]int // map of stat kind to row count
	}
	type testCase struct {
		name            string
		tsp             telematics.TSP
		handleId        uuid.UUID
		year            int
		month           time.Month
		policyStartDate null.Time
		// want is the map of vin(s) to vehicleRowCountSummary(s) for the vin
		want map[data_platform.VIN]vehicleRowCountSummary
	}

	testCases := []testCase{
		{
			name:     "Dec-2020 for KT000000000000001",
			tsp:      telematics.TSPKeepTruckin,
			handleId: dp_test_utils.GoldenHandleId(telematics.TSPKeepTruckin),
			year:     2020,
			month:    time.December,
			want: map[data_platform.VIN]vehicleRowCountSummary{
				"KT000000000000001": {
					tspId: "1",
					stats: map[data_platform.NormalizedVehicleStat]int{
						data_platform.NormalizedVehicleStatGPSLogWithGeography: 50 * (365 + 1), // 50 GPS points per day
						data_platform.NormalizedVehicleStatOdometerLog:         50 * (365 + 1), // 50 Odo readings per day
					},
				},
			},
		},
		{
			name:     "Dec-2020 for SM000000000000001",
			tsp:      telematics.TSPSamsara,
			handleId: dp_test_utils.GoldenHandleId(telematics.TSPSamsara),
			year:     2020,
			month:    time.December,
			want: map[data_platform.VIN]vehicleRowCountSummary{
				"SM000000000000001": {
					tspId: "1",
					stats: map[data_platform.NormalizedVehicleStat]int{
						data_platform.NormalizedVehicleStatGPSLogWithGeography: 293 * (365 + 1), // 293 GPS points per day
						data_platform.NormalizedVehicleStatOdometerLog:         293 * (365 + 1), // One Odo readings per GPS & Eng State
					},
				},
			},
		},
	}

	for idx := range testCases {
		tc := testCases[idx]
		n.Run(tc.name, func() {
			var vehicles []data_platform.VIN
			for vin := range tc.want {
				vehicles = append(vehicles, vin)
			}
			message := jobs.NewConfigurableBillingWorkflow(&jobs.BasePayload{
				ConnHandleId:    tc.handleId,
				Tsp:             tc.tsp.String(),
				PolicyStartDate: tc.policyStartDate,
				USState:         us_states.AR,
			}, time_utils.TimeToYearAndMonth(time.Date(tc.year, tc.month, 1, 0, 0, 0, 0, time.UTC)))
			message.Config.OnlyVINs = vehicles

			// run the billing workflow
			billingWorkflow, err := n.harness.RunConfigurableWorkflow(ctx, message)
			n.Require().NoError(err)

			// increment wall clock by 1 minute every 10 ms
			doneCh := make(chan bool)
			waitCh := make(chan bool)
			go func() {
				timer := time.NewTicker(10 * time.Millisecond)
				defer timer.Stop()
				for {
					select {
					case <-doneCh:
						close(waitCh)
						return
					case <-timer.C:
						n.harness.Deps.Clk.(*clock.Mock).Add(5 * time.Minute)
					}
				}
			}()
			n.Require().NoError(billingWorkflow.WaitForCompletion(ctx))
			close(doneCh)
			<-waitCh

			// assert that workflow completed successfully
			n.Require().NoError(n.harness.CheckJobRunStatus(ctx, billingWorkflow.JobRunId(), jtypes.JobRunStatusSucceeded))

			// verify that pipeline id and info is persisted properly
			pipeline, err := billingWorkflow.GetPipelineRecord(ctx, message.HandleID)
			n.Require().NoError(err)
			n.Require().Equal(message.PipelineId.String, pipeline.PipelineID)
			n.Require().True(pipeline.FinishedAt.Valid)
			n.Require().Equal(message.PipelineId.String, pipeline.R.PipelineInfo.PipelineID)
			n.Require().Equal(tc.tsp.String(), pipeline.R.PipelineInfo.TSP)

			handleWorkflow, err := billingWorkflow.MillimanHandleWorkflow(ctx)
			n.Require().NoError(err)

			// Get all spawned Vehicle workflows from artifactory and assert
			// that its length is equal to the number of vehicles in input params
			spawnedVehicleWorkflows, err := handleWorkflow.VehicleWorkflows(ctx)
			n.Require().NoError(err)
			workflowsByVin := slice_utils.Filter(spawnedVehicleWorkflows, func(wrapper *test_utils.NativeMillimanVehicleWorkflowWrapper) bool {
				return wrapper.InputMessage.VehicleId.Valid
			})
			workflowsByTspId := slice_utils.Filter(spawnedVehicleWorkflows, func(wrapper *test_utils.NativeMillimanVehicleWorkflowWrapper) bool {
				return wrapper.InputMessage.VINVehicleIds != nil
			})
			n.Require().Len(workflowsByVin, len(message.Config.OnlyVINs))
			n.Require().Len(workflowsByTspId, len(workflowsByVin))

			// validate each of the spawned vehicle workflows, comparing their
			// artifacts and compiled stats with expected values
			for _, vinWorkflow := range spawnedVehicleWorkflows {
				n.Require().Equal(jtypes.JobRunStatusSucceeded, vinWorkflow.Status)

				// Verify that stats are persisted properly
				// verify that for each vehicle its stats are properly persisted
				var vid string
				var forTspId bool
				var rowCountsSummary vehicleRowCountSummary
				if vinWorkflow.InputMessage.VINVehicleIds != nil {
					vid = string(vinWorkflow.InputMessage.VINVehicleIds.VIN)
					rowCountsSummary = tc.want[data_platform.VIN(vid)]
				} else {
					vid = vinWorkflow.InputMessage.VehicleId.String
					forTspId = true
					for _, summary := range tc.want {
						if summary.tspId == vid {
							rowCountsSummary = summary
						}
					}
				}

				for stat, wantCount := range rowCountsSummary.stats {
					weeklyFiles := n.harness.ListWeeklyStatsFiles(ctx, message.HandleID, vid, stat.String(), 0, tc.year, forTspId)
					odFiles := n.harness.ListOnDemandFiles(ctx, message.HandleID, vid, stat.String(), 0, forTspId)
					currCount := 0
					for _, filePath := range append(weeklyFiles, odFiles...) {
						var versionedObj aws.WriteAtBuffer
						_, err = n.harness.Deps.S3Client.DownloadWithContext(ctx, &versionedObj, &s3.GetObjectInput{
							Bucket: pointer_utils.String("nirvana-telematics-default"),
							Key:    pointer_utils.String(filePath),
						})
						n.Require().NoError(err)

						var items data_platform.ResultStream[data_platform.NormalizedStat]
						items, err := stream.FromCSV(bytes.NewReader(versionedObj.Bytes()), func() data_platform.NormalizedStat {
							switch stat {
							case data_platform.NormalizedVehicleStatGPSLogWithGeography:
								return new(data_platform.NormalizedGPSLogWithGeography)
							case data_platform.NormalizedVehicleStatOdometerLog:
								return new(data_platform.NormalizedOdometerLogItem)
							default:
								n.FailNowf("unsupported stat kind %s", stat.String())
							}
							return nil
						})
						n.Require().NoError(err)
						itemsSlice, err := stream.GetAll(items)
						n.Require().NoError(err)
						currCount += len(itemsSlice)
					}
					n.Require().Equal(wantCount, currCount)
				}
			}

			dsw, err := billingWorkflow.DataScienceWorkflow(ctx)
			n.Require().NoError(err)

			// verify that metaflow task artifact is properly persisted
			dsArt, err := dsw.GetArtifact(ctx)
			n.Require().NoError(err)
			n.Require().False(dsArt.Error.Valid)
			n.Require().Len(dsArt.MetaflowRuns, 1)
			run := dsArt.MetaflowRuns["UWDataFlow"]
			n.Require().Equal("metaflow-mock", run.RunID)
			n.Require().True(run.FinishedAt.Valid)
			n.Require().Equal("FINISHED", run.State)
		})
	}
}
