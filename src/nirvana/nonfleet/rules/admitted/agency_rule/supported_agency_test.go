package agency_rule

import (
	"context"
	"reflect"
	"testing"

	"github.com/launchdarkly/go-sdk-common/v3/ldvalue"

	"nirvanatech.com/nirvana/common-go/pointer_utils"

	"nirvanatech.com/nirvana/policy_common/constants"

	"github.com/google/uuid"
	"go.uber.org/fx"
	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/rule_runs"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/users_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/nonfleet/rule_engine/registry"
)

func TestSupportedAgenciesRule_Run(t *testing.T) {
	ctx := context.Background()
	var env struct {
		fx.In
		AuthWrapper auth.DataWrapper
		FFClient    *feature_flag_lib.MockClient
		*users_fixture.UsersFixture
	}
	defer testloader.RequireStart(t, &env).RequireStop()

	type args struct {
		input *registry.RuleInput[*admitted_app.AdmittedApp]
	}

	tests := []struct {
		name string
		args args
		mock func()
		want *registry.RuleOutput
	}{
		{
			name: "Pass for supported agency with valid FilingType",
			args: args{
				input: registry.NewRuleInput[*admitted_app.AdmittedApp](
					&application.Application[*admitted_app.AdmittedApp]{
						ID:         uuid.New(),
						MarketerId: env.Superuser.ID,
						FilingType: pointer_utils.ToPointer(constants.FilingTypeNonAdmitted),
					},
					nil,
				),
			},
			want: registry.NewRuleOutput(enums.Pass, rule_runs.Reason{}, nil),
		},
		{
			name: "Fail for unsupported agency",
			args: args{
				input: registry.NewRuleInput[*admitted_app.AdmittedApp](
					&application.Application[*admitted_app.AdmittedApp]{
						ID:         uuid.New(),
						MarketerId: env.Superuser.ID,
						FilingType: pointer_utils.ToPointer(constants.FilingTypeNonAdmitted),
					},
					nil,
				),
			},
			mock: func() {
				// Mock the feature flag to return false
				env.FFClient.SetValue(feature_flag_lib.FeatureNFStateTX, ldvalue.Bool(false))
			},
			want: registry.NewRuleOutput(
				enums.Fail,
				rule_runs.Reason{
					PrimaryReason:   pointer_utils.ToPointer(rule_runs.PrimaryDeclineReasonOperations.String()),
					SecondaryReason: pointer_utils.ToPointer(rule_runs.SecondaryDeclineState.String()),
					Text:            "We are not yet available in Texas.\nPlease contact your Business Development Representative for more information",
				},
				nil,
			),
		},
		{
			name: "Pass when FilingType is not NonAdmitted",
			args: args{
				input: registry.NewRuleInput[*admitted_app.AdmittedApp](
					&application.Application[*admitted_app.AdmittedApp]{
						ID:         uuid.New(),
						MarketerId: env.Superuser.ID,
						FilingType: pointer_utils.ToPointer(constants.FilingTypeAdmitted),
					},
					nil,
				),
			},
			want: registry.NewRuleOutput(enums.Pass, rule_runs.Reason{}, nil),
		},
		{
			name: "Pass when FilingType is missing",
			args: args{
				input: registry.NewRuleInput[*admitted_app.AdmittedApp](
					&application.Application[*admitted_app.AdmittedApp]{
						ID:         uuid.New(),
						MarketerId: env.Superuser.ID,
						FilingType: nil,
					},
					nil,
				),
			},
			mock: func() {},
			want: registry.NewRuleOutput(enums.Pass, rule_runs.Reason{}, nil),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}

			b := SupportedAgenciesRule{
				AuthWrapper:       env.AuthWrapper,
				FeatureFlagClient: env.FFClient,
			}
			if got := b.Run(ctx, tt.args.input); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Run() = %v, want %v", got, tt.want)
			}
		})
	}
}
