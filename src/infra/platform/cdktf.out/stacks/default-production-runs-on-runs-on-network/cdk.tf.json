{"//": {"metadata": {"backend": "s3", "overrides": {"stack": ["terraform"]}, "stackName": "default-production-runs-on-runs-on-network", "version": "0.20.10"}, "outputs": {"default-production-runs-on-runs-on-network": {"availability-zones": "availability-zones", "bastion-security-group-id": "bastion-security-group-id", "cf-stack-region": "cf-stack-region", "cross-stack-output-aws_security_group.bastion-sg.id": "cross-stack-output-aws_security_groupbastion-sgid", "cross-stack-output-data.aws_availability_zones.availability_zones.names": "cross-stack-output-dataaws_availability_zonesavailability_zonesnames", "cross-stack-output-data.aws_security_group.sg.id": "cross-stack-output-dataaws_security_groupsgid", "cross-stack-output-data.aws_subnet.private-subnet-a.id": "cross-stack-output-dataaws_subnetprivate-subnet-aid", "cross-stack-output-data.aws_subnet.private-subnet-b.id": "cross-stack-output-dataaws_subnetprivate-subnet-bid", "cross-stack-output-data.aws_subnet.private-subnet-c.id": "cross-stack-output-dataaws_subnetprivate-subnet-cid", "cross-stack-output-data.aws_subnet.public-subnet-a.id": "cross-stack-output-dataaws_subnetpublic-subnet-aid", "cross-stack-output-data.aws_subnet.public-subnet-b.id": "cross-stack-output-dataaws_subnetpublic-subnet-bid", "cross-stack-output-data.aws_subnet.public-subnet-c.id": "cross-stack-output-dataaws_subnetpublic-subnet-cid", "cross-stack-output-data.aws_vpc.vpc.id": "cross-stack-output-dataaws_vpcvpcid", "nat-gateway-ids": "nat-gateway-ids", "private-subnet-ids": "private-subnet-ids", "public-subnet-ids": "public-subnet-ids", "runs-on-security-group-id": "runs-on-security-group-id", "vpc-id": "vpc-id"}}}, "data": {"aws_availability_zones": {"availability_zones": {"//": {"metadata": {"path": "default-production-runs-on-runs-on-network/availability_zones", "uniqueId": "availability_zones"}}, "filter": [{"name": "region-name", "values": ["${data.aws_cloudformation_stack.cf-stack.outputs[\"RunsOnRegion\"]}"]}], "state": "available"}}, "aws_cloudformation_stack": {"cf-stack": {"//": {"metadata": {"path": "default-production-runs-on-runs-on-network/cf-stack", "uniqueId": "cf-stack"}}, "name": "runs-on"}}, "aws_route_table": {"priv-subnet-a-route-table": {"//": {"metadata": {"path": "default-production-runs-on-runs-on-network/priv-subnet-a-route-table", "uniqueId": "priv-subnet-a-route-table"}}, "subnet_id": "${data.aws_subnet.private-subnet-a.id}", "vpc_id": "${data.aws_vpc.vpc.id}"}, "priv-subnet-b-route-table": {"//": {"metadata": {"path": "default-production-runs-on-runs-on-network/priv-subnet-b-route-table", "uniqueId": "priv-subnet-b-route-table"}}, "subnet_id": "${data.aws_subnet.private-subnet-b.id}", "vpc_id": "${data.aws_vpc.vpc.id}"}, "priv-subnet-c-route-table": {"//": {"metadata": {"path": "default-production-runs-on-runs-on-network/priv-subnet-c-route-table", "uniqueId": "priv-subnet-c-route-table"}}, "subnet_id": "${data.aws_subnet.private-subnet-c.id}", "vpc_id": "${data.aws_vpc.vpc.id}"}}, "aws_security_group": {"sg": {"//": {"metadata": {"path": "default-production-runs-on-runs-on-network/sg", "uniqueId": "sg"}}, "id": "${data.aws_cloudformation_stack.cf-stack.outputs[\"RunsOnSecurityGroupId\"]}", "vpc_id": "${data.aws_vpc.vpc.id}"}}, "aws_subnet": {"private-subnet-a": {"//": {"metadata": {"path": "default-production-runs-on-runs-on-network/private-subnet-a", "uniqueId": "private-subnet-a"}}, "id": "${data.aws_cloudformation_stack.cf-stack.outputs[\"RunsOnPrivateSubnet1\"]}", "vpc_id": "${data.aws_vpc.vpc.id}"}, "private-subnet-b": {"//": {"metadata": {"path": "default-production-runs-on-runs-on-network/private-subnet-b", "uniqueId": "private-subnet-b"}}, "id": "${data.aws_cloudformation_stack.cf-stack.outputs[\"RunsOnPrivateSubnet2\"]}", "vpc_id": "${data.aws_vpc.vpc.id}"}, "private-subnet-c": {"//": {"metadata": {"path": "default-production-runs-on-runs-on-network/private-subnet-c", "uniqueId": "private-subnet-c"}}, "id": "${data.aws_cloudformation_stack.cf-stack.outputs[\"RunsOnPrivateSubnet3\"]}", "vpc_id": "${data.aws_vpc.vpc.id}"}, "public-subnet-a": {"//": {"metadata": {"path": "default-production-runs-on-runs-on-network/public-subnet-a", "uniqueId": "public-subnet-a"}}, "id": "${data.aws_cloudformation_stack.cf-stack.outputs[\"RunsOnPublicSubnet1\"]}", "vpc_id": "${data.aws_vpc.vpc.id}"}, "public-subnet-b": {"//": {"metadata": {"path": "default-production-runs-on-runs-on-network/public-subnet-b", "uniqueId": "public-subnet-b"}}, "id": "${data.aws_cloudformation_stack.cf-stack.outputs[\"RunsOnPublicSubnet2\"]}", "vpc_id": "${data.aws_vpc.vpc.id}"}, "public-subnet-c": {"//": {"metadata": {"path": "default-production-runs-on-runs-on-network/public-subnet-c", "uniqueId": "public-subnet-c"}}, "id": "${data.aws_cloudformation_stack.cf-stack.outputs[\"RunsOnPublicSubnet3\"]}", "vpc_id": "${data.aws_vpc.vpc.id}"}}, "aws_vpc": {"vpc": {"//": {"metadata": {"path": "default-production-runs-on-runs-on-network/vpc", "uniqueId": "vpc"}}, "id": "${data.aws_cloudformation_stack.cf-stack.outputs[\"RunsOnVPCId\"]}"}}}, "output": {"availability-zones": {"description": "The availability zones used by runs-on.", "value": "${data.aws_availability_zones.availability_zones.names}"}, "bastion-security-group-id": {"description": "The ID of the security group used by runs-on bastion.", "value": "${aws_security_group.bastion-sg.id}"}, "cf-stack-region": {"description": "The region where the runs-on CloudFormation stack is deployed.", "value": "${data.aws_cloudformation_stack.cf-stack.outputs[\"RunsOnRegion\"]}"}, "cross-stack-output-aws_security_groupbastion-sgid": {"sensitive": true, "value": "${aws_security_group.bastion-sg.id}"}, "cross-stack-output-dataaws_availability_zonesavailability_zonesnames": {"sensitive": true, "value": "${data.aws_availability_zones.availability_zones.names}"}, "cross-stack-output-dataaws_security_groupsgid": {"sensitive": true, "value": "${data.aws_security_group.sg.id}"}, "cross-stack-output-dataaws_subnetprivate-subnet-aid": {"sensitive": true, "value": "${data.aws_subnet.private-subnet-a.id}"}, "cross-stack-output-dataaws_subnetprivate-subnet-bid": {"sensitive": true, "value": "${data.aws_subnet.private-subnet-b.id}"}, "cross-stack-output-dataaws_subnetprivate-subnet-cid": {"sensitive": true, "value": "${data.aws_subnet.private-subnet-c.id}"}, "cross-stack-output-dataaws_subnetpublic-subnet-aid": {"sensitive": true, "value": "${data.aws_subnet.public-subnet-a.id}"}, "cross-stack-output-dataaws_subnetpublic-subnet-bid": {"sensitive": true, "value": "${data.aws_subnet.public-subnet-b.id}"}, "cross-stack-output-dataaws_subnetpublic-subnet-cid": {"sensitive": true, "value": "${data.aws_subnet.public-subnet-c.id}"}, "cross-stack-output-dataaws_vpcvpcid": {"sensitive": true, "value": "${data.aws_vpc.vpc.id}"}, "nat-gateway-ids": {"description": "The IDs of the NAT gateways used by runs-on.", "value": ["${aws_nat_gateway.nat-gateway-a.id}", "${aws_nat_gateway.nat-gateway-b.id}", "${aws_nat_gateway.nat-gateway-c.id}"]}, "private-subnet-ids": {"description": "The IDs of the private subnets used by runs-on.", "value": ["${data.aws_subnet.private-subnet-a.id}", "${data.aws_subnet.private-subnet-b.id}", "${data.aws_subnet.private-subnet-c.id}"]}, "public-subnet-ids": {"description": "The IDs of the public subnets used by runs-on.", "value": ["${data.aws_subnet.public-subnet-a.id}", "${data.aws_subnet.public-subnet-b.id}", "${data.aws_subnet.public-subnet-c.id}"]}, "runs-on-security-group-id": {"description": "The ID of the security group used by runs-on.", "value": "${data.aws_security_group.sg.id}"}, "vpc-id": {"description": "The ID of runs-on VPC.", "value": "${data.aws_vpc.vpc.id}"}}, "provider": {"aws": [{"allowed_account_ids": ["************"], "assume_role": [{"role_arn": "arn:aws:iam::************:role/AdministratorAccessForManagementAccountUsers"}], "default_tags": [{"tags": {"environment": "production", "group": "runs-on", "infraWorkspace": "default", "stackName": "runs-on-network"}}]}]}, "resource": {"aws_eip": {"nat-gateway-a-eip": {"//": {"metadata": {"path": "default-production-runs-on-runs-on-network/nat-gateway-a-eip", "uniqueId": "nat-gateway-a-eip"}}, "tags": {"NatGatewayName": "nat-gateway-a"}, "vpc": true}, "nat-gateway-b-eip": {"//": {"metadata": {"path": "default-production-runs-on-runs-on-network/nat-gateway-b-eip", "uniqueId": "nat-gateway-b-eip"}}, "tags": {"NatGatewayName": "nat-gateway-b"}, "vpc": true}, "nat-gateway-c-eip": {"//": {"metadata": {"path": "default-production-runs-on-runs-on-network/nat-gateway-c-eip", "uniqueId": "nat-gateway-c-eip"}}, "tags": {"NatGatewayName": "nat-gateway-c"}, "vpc": true}}, "aws_nat_gateway": {"nat-gateway-a": {"//": {"metadata": {"path": "default-production-runs-on-runs-on-network/nat-gateway-a", "uniqueId": "nat-gateway-a"}}, "allocation_id": "${aws_eip.nat-gateway-a-eip.id}", "subnet_id": "${data.aws_subnet.public-subnet-a.id}", "tags": {"Name": "nat-gateway-a"}}, "nat-gateway-b": {"//": {"metadata": {"path": "default-production-runs-on-runs-on-network/nat-gateway-b", "uniqueId": "nat-gateway-b"}}, "allocation_id": "${aws_eip.nat-gateway-b-eip.id}", "subnet_id": "${data.aws_subnet.public-subnet-b.id}", "tags": {"Name": "nat-gateway-b"}}, "nat-gateway-c": {"//": {"metadata": {"path": "default-production-runs-on-runs-on-network/nat-gateway-c", "uniqueId": "nat-gateway-c"}}, "allocation_id": "${aws_eip.nat-gateway-c-eip.id}", "subnet_id": "${data.aws_subnet.public-subnet-c.id}", "tags": {"Name": "nat-gateway-c"}}}, "aws_route": {"priv-subnet-a-allow-internet-access": {"//": {"metadata": {"path": "default-production-runs-on-runs-on-network/priv-subnet-a-allow-internet-access", "uniqueId": "priv-subnet-a-allow-internet-access"}}, "destination_cidr_block": "0.0.0.0/0", "nat_gateway_id": "${aws_nat_gateway.nat-gateway-a.id}", "route_table_id": "${data.aws_route_table.priv-subnet-a-route-table.id}"}, "priv-subnet-b-allow-internet-access": {"//": {"metadata": {"path": "default-production-runs-on-runs-on-network/priv-subnet-b-allow-internet-access", "uniqueId": "priv-subnet-b-allow-internet-access"}}, "destination_cidr_block": "0.0.0.0/0", "nat_gateway_id": "${aws_nat_gateway.nat-gateway-b.id}", "route_table_id": "${data.aws_route_table.priv-subnet-b-route-table.id}"}, "priv-subnet-c-allow-internet-access": {"//": {"metadata": {"path": "default-production-runs-on-runs-on-network/priv-subnet-c-allow-internet-access", "uniqueId": "priv-subnet-c-allow-internet-access"}}, "destination_cidr_block": "0.0.0.0/0", "nat_gateway_id": "${aws_nat_gateway.nat-gateway-c.id}", "route_table_id": "${data.aws_route_table.priv-subnet-c-route-table.id}"}}, "aws_security_group": {"bastion-sg": {"//": {"metadata": {"path": "default-production-runs-on-runs-on-network/bastion-sg", "uniqueId": "bastion-sg"}}, "name": "runs-on-bastion-sg", "vpc_id": "${data.aws_vpc.vpc.id}"}}, "aws_security_group_rule": {"sg_rule_egress_bastion_to_internet": {"//": {"metadata": {"path": "default-production-runs-on-runs-on-network/sg_rule_egress_bastion_to_internet", "uniqueId": "sg_rule_egress_bastion_to_internet"}}, "cidr_blocks": ["0.0.0.0/0"], "from_port": 0, "protocol": "-1", "security_group_id": "${aws_security_group.bastion-sg.id}", "to_port": 0, "type": "egress"}, "sg_rule_ingress_ssh_to_bastion": {"//": {"metadata": {"path": "default-production-runs-on-runs-on-network/sg_rule_ingress_ssh_to_bastion", "uniqueId": "sg_rule_ingress_ssh_to_bastion"}}, "cidr_blocks": ["0.0.0.0/0"], "from_port": 22, "protocol": "tcp", "security_group_id": "${aws_security_group.bastion-sg.id}", "to_port": 22, "type": "ingress"}}}, "terraform": {"backend": {"s3": {"bucket": "cloud.nirvanatech.com", "key": "private/deployment/cdktf/default/production/default-production-runs-on-runs-on-network.json", "region": "us-east-2"}}, "required_providers": {"aws": {"source": "aws", "version": "5.88.0"}}, "required_version": "1.7.5"}}